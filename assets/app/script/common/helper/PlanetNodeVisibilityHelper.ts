/**
 * 星球节点可见性辅助工具
 * 用于手动刷新和调试节点显示状态
 */
import { gameHelper } from "./GameHelper";

export class PlanetNodeVisibilityHelper {
    
    /**
     * 刷新当前地图所有节点的可见性
     */
    public static refreshAllNodesVisibility() {
        let planet = gameHelper.planet.getCurPlanet()
        if (!planet) {
            console.warn("当前没有星球")
            return
        }

        let map = planet.getBranchCurMap()
        if (!map) {
            console.warn("当前没有地图")
            return
        }

        let nodes = map.getNodes()
        console.log(`开始刷新 ${nodes.length} 个节点的可见性`)

        // 更新所有节点的控制标志
        for (let node of nodes) {
            let oldFlag = node.controlFlag
            node.updateContorlFlag()
            let newFlag = node.controlFlag
            let canShow = node.checkShow()
            
            console.log(`节点 ${node.index}: controlFlag ${oldFlag} -> ${newFlag}, canShow: ${canShow}`)
        }

        console.log("节点可见性刷新完成")
    }

    /**
     * 强制显示指定索引的节点
     */
    public static forceShowNode(nodeIndex: number) {
        let planet = gameHelper.planet.getCurPlanet()
        if (!planet) {
            console.warn("当前没有星球")
            return
        }

        let map = planet.getBranchCurMap()
        if (!map) {
            console.warn("当前没有地图")
            return
        }

        let node = map.getNodeByIndex(nodeIndex)
        if (!node) {
            console.warn(`找不到索引为 ${nodeIndex} 的节点`)
            return
        }

        // 强制设置控制标志为true
        node.controlFlag = true
        console.log(`强制显示节点 ${nodeIndex}`)
    }

    /**
     * 获取所有节点的可见性状态
     */
    public static getNodesVisibilityStatus() {
        let planet = gameHelper.planet.getCurPlanet()
        if (!planet) {
            console.warn("当前没有星球")
            return []
        }

        let map = planet.getBranchCurMap()
        if (!map) {
            console.warn("当前没有地图")
            return []
        }

        let nodes = map.getNodes()
        let status = []

        for (let node of nodes) {
            status.push({
                index: node.index,
                id: node.getId(),
                eventName: node.eventName,
                controlFlag: node.controlFlag,
                canShow: node.checkShow(),
                isPass: node.isPass(),
                dead: node.dead,
                isEnd: node.isEnd,
                nodeType: node.nodeType
            })
        }

        return status
    }

    /**
     * 打印所有节点的详细状态
     */
    public static debugNodesStatus() {
        let status = this.getNodesVisibilityStatus()
        console.table(status)
        return status
    }

    /**
     * 查找隐藏的节点
     */
    public static findHiddenNodes() {
        let status = this.getNodesVisibilityStatus()
        let hiddenNodes = status.filter(node => !node.canShow)
        
        console.log("隐藏的节点:")
        console.table(hiddenNodes)
        
        return hiddenNodes
    }

    /**
     * 修复洞口节点显示问题
     * 针对你提到的洞口由两个节点组成但有一个没显示的问题
     */
    public static fixCaveNodes() {
        console.log("开始修复洞口节点显示问题...")
        
        // 先刷新所有节点
        this.refreshAllNodesVisibility()
        
        // 查找可能的洞口节点（通常是连续的空节点）
        let planet = gameHelper.planet.getCurPlanet()
        let map = planet.getBranchCurMap()
        let nodes = map.getNodes()
        
        for (let i = 0; i < nodes.length - 1; i++) {
            let currentNode = nodes[i]
            let nextNode = nodes[i + 1]
            
            // 如果是连续的空节点，可能是洞口组合
            if (currentNode.eventName && nextNode.eventName) {
                let currentCanShow = currentNode.checkShow()
                let nextCanShow = nextNode.checkShow()
                
                // 如果一个显示一个不显示，尝试修复
                if (currentCanShow !== nextCanShow) {
                    console.log(`发现可能的洞口节点组合: ${currentNode.index}(${currentCanShow}) - ${nextNode.index}(${nextCanShow})`)
                    
                    // 强制让两个节点都显示
                    currentNode.controlFlag = true
                    nextNode.controlFlag = true
                    
                    console.log(`已修复洞口节点 ${currentNode.index} 和 ${nextNode.index}`)
                }
            }
        }
        
        console.log("洞口节点修复完成")
    }
}

// 添加到全局调试工具
if (typeof window !== 'undefined') {
    (window as any).planetNodeHelper = PlanetNodeVisibilityHelper
}
