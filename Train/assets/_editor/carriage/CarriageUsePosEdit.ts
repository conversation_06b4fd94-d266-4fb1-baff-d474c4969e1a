import { CarriageUsePosType, RoleDir } from "../../app/script/common/constant/Enums";

const { ccclass, requireComponent, executionOrder, menu, property, executeInEditMode } = cc._decorator;

@ccclass
@menu("车厢编辑/点位")
export default class CarriageUsePosEdit extends cc.Component {
    @property({visible: false, serializable: true})
    cname: String = "CarriageUsePosEdit";

    @property
    id: string = ""

    @property({
        type: cc.Enum(CarriageUsePosType),
    })
    type: CarriageUsePosType = CarriageUsePosType.NORMAL

    @property({
        type: cc.Enum(RoleDir),
        displayName: "角色朝向"
    })
    dir: RoleDir = RoleDir.NONE

    @property({
        displayName: "强制移动点"
    })
    force: boolean = false

    @property({
        displayName: "依赖的设施点位"
    })
    buildOrder: number = 0

    // @property({
    //     displayName: "扩展"
    // })
    // extra: string = null
}