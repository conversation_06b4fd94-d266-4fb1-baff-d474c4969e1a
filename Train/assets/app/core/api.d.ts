type SwihNodeCallback = (it: cc.Node) => any;
type SwihToggleCallback = (it: cc.Toggle) => any;

type Point = {
    x: number;
    y: number;
}

type KeyParams = {
    key: string;//约定以.开头不需要读取配置
    params?: any[];
    // key和params得到的val作为rich的第一个参
    rich?: string;
    richParams?: any[];
}

var wx: any
var qq: any
var wxPro: any //微信接口promise化

declare namespace cc {

    declare namespace js {
        function isArray(val: any): boolean;
        function isTable(val: any): boolean;
        function isFunction(val: any): boolean;
        function getDumpStr(objIn: any, descript?: string): string;
        function dump(objIn: any, descript?: string);
    }

    interface Vec2 {
        set2(x: number, y: number): Vec2;
        equals2(x: number, y: number): boolean;
        Join(separator?: string): string;
        toVec3(): Vec3;
        newVec3(): Vec3;
        toJson(): Point;
        swapSelf(): Vec2;
        edistance(Vec2): number;
        flipY(): Vec2;
        xAngle(): number;
    }

    interface Button {
        _onTouchEnded: (ev) => void
    }

    interface Vec3 {
        set2(x: number, y: number, z: number): Vec3;
        equals2(x: number, y: number, z?: number): boolean;
        Join(separator?: string): string;
        toVec2(): Vec2;
        newVec2(): Vec2;
        xAngle(): number;
    }

    interface Director {
        _speedRate: number
        calculateDeltaTime: Function
    }

    interface _BaseNode {
        Data: any;
        initialSlbling: number,
        initialActive: boolean
        _hitTest(vec: Vec2 | Vec3, listener?): boolean;
        Child(name: string | number): Node;
        Child(name: string | number, className: string): any;
        Child<T extends Component>(name: string | number, type: { prototype: T }): T;
        FindChild(name: string | number): Node;
        FindChild(name: string | number, className: string): any;
        FindChild<T extends Component>(name: string | number, type: { prototype: T }): T;
        Component<T extends Component>(type: { prototype: T }): T;
        Component(className: string): any;
        Items<T>(list: T[], item: Node | Prefab, setItemData: (it: cc.Node, data: T, i: number) => void, target?: any): void;
        Items<T>(list: T[], setItemData: (it: cc.Node, data: T, i: number) => void, target?: any): void;
        Items<T>(list: T[]): void;
        AddItem(item: Node | Prefab, setItemData: (it: cc.Node, i: number) => void, target?: any): void;
        AddItem(setItemData: (it: cc.Node, i: number) => void, target?: any): void;
        getItems(): Node[];
        Swih(val: string | number | SwihNodeCallback): Node[];
        SetColor(val: string | Color): Node;
        SetSwallowTouches(val: boolean): void;
        IsSwallowTouches(): boolean;
        setLocaleKey(key: string, ...params: any[]);
        setLocaleUpdate(func: Function)
        getRect(postion?: cc.Vec2): cc.Rect
        getWorldRect(postion?: cc.Vec2): cc.Rect
        getWorldScale(out: cc.Vec2): cc.Vec2
        getPath()
        setGray(bol: boolean)
        setDark(darkness: number, recur?: boolean)
        _onAddAction(action: cc.Action) //node执行action时触发
        removeAndDestroyAllChildren()
        clearCacheField()
        hitTestByWorldPos(worldPos: cc.Vec2),
        removeAndDestroy()
    }

    interface Component {
        getActive(): boolean;
        setActive(val: boolean): void;
        getPosition(out?: cc.Vec2): cc.Vec2;
        Child(name: string | number): Node;
        Child(name: string | number, className: string): any;
        Child<T extends Component>(name: string | number, type: { prototype: T }): T;
        FindChild(name: string | number): Node;
        FindChild(name: string | number, className: string): any;
        FindChild<T extends Component>(name: string | number, type: { prototype: T }): T;
        Component<T extends Component>(type: { prototype: T }): T;
        Component(className: string): any;
        SetColor(val: string | Color): Component;
        GetColor(): Color;
        scheduleUpdate(callback: Function)
        _onAddAction(action: cc.Action) //target执行action时触发
        _onSchedule(timer) //target执行schedule时触发
    }

    interface Animation {
        playAsync(name?: string): Promise<void>;
        playToFinished(callback: Function, name?: string): AnimationState;
    }

    interface Label {
        _forceUpdateRenderData(): void;// v2.2.0
        setLocaleKey(key: string, ...params: any[]);
        setLocaleUpdate(func: Function)
        getLineCount(): number
    }

    interface Sprite {
        setLocaleKey(key: string);
    }

    interface ScrollView {
        Items<T>(list: T[], setItemData?: (it: cc.Node, data: T, i: number) => void, target?: any): void;
        AddItem(setItemData: (it: cc.Node, i: number) => void, target?: any): void;
        Find(predicate: (value: Node, index: number, obj: Node[]) => unknown, thisArg?: any): Node;
        IsEmpty(): boolean;
        List(len: number, setItemData?: (it: cc.Node, i: number) => void, target?: any): void;
        JumpToItem(anchor: cc.Vec2): void;
    }

    interface ToggleContainer {
        Swih(val: string | number | SwihToggleCallback): Toggle[];
        Tabs(val: string | number): Toggle;
    }

    // 按钮扩展
    export class ButtonEx extends Component {
        interactable: boolean;
        static DefaultClickPath: string;
    }

    export class WidgetEx extends Component {
    }

    export class RichTextSizeAdapter extends Component {
    }

    export class LabelSizeAdapter extends Component {
    }

    // scrollview扩展
    export class ScrollViewEx extends Component {
    }

    // 播放等待的点
    export class LabelWaitDot extends Component {
        play(val?: string): void;
        stop(val?: string): void;
    }

    // 滚动数字
    export class LabelRollNumber extends Component {
        setPrefix(val: string): LabelRollNumber;
        set(val: number): LabelRollNumber;
        to(end: number, duration?: number): void;
        by(val: number, duration?: number): void;
    }

    // 时间文本
    export class LabelTimer extends Component {
        string: string;
        setPrefix(val: string): LabelTimer;
        setFormat(val: string | ((time: number) => string)): LabelTimer;
        setEndTime(val: number): LabelTimer;
        setPause(val: boolean): void;
        run(time: number, callback?: Function): void;
    }

    export class LabelPlayer extends Component {
        speed: number;
        play(onEnd?: Function);
        end();
        isEnd(): boolean
    }

    // 多选颜色
    export class MultiColor extends Component {
        setColor(idx: number | boolean): void;
        setOutlineColor(idx: number | boolean): void;
        getColor(idx: number | boolean): Color;
    }

    // 多选精灵
    export class MultiFrame extends Component {
        setFrame(idx: number | boolean): void;
        getFrame(idx: number | boolean): SpriteFrame;
        getIndex(): number;
        frameCount(): number;
        getSpriteFrame(): SpriteFrame;
        random(): number;
    }

    export class MultiFont extends Component {
        setFont(idx: number | boolean): void;
    }

    export class MultiAtlas extends Component {
        setAtlas(idx: number | boolean): void;
    }

    export class LocaleLang extends Component {
        string: string;
        getKey();
        setKey(key: string, ...params: any[]);
        setUpdate(func: Function)
        updateLang();
        updateString();
    }

    // 多语言label
    export class LocaleLabel extends LocaleLang {
    }

    // 多语言RichText
    export class LocaleRichText extends Component {
        setKey(key: string, ...params: any[]);
    }

    // 多语言Sprite
    export class LocaleSprite extends Component {
        setKey(key: string);
        addSpriteFrame(val: cc.SpriteFrame);
    }

    function instantiate2(item: Node | Prefab, parent: Node | Component): Node;
    function setEnumAttr(obj, propName, enumDef)
    function setClassAttr(obj, propName, defKey, defValue)

    interface Tween {
        wait(func: Function)
        promise(): Promise<void>
        progress(dur: number, func: (percent: number) => void)
        update(dt)
    }

    interface Widget {
        getAlignByPos(pos?: cc.Vec2)
    }

    export let _widgetManager = {
        updateAlignment(node: cc.Node)
    }

    interface RichText {
        _lineCount: number
        public getLineCount(): number
        public setLocaleKey(key: string, ...params: any[])
        public setKeyParams(kp: KeyParams | string)
        setLocaleUpdate(func: Function)
    }
}
declare namespace sp {
    interface Skeleton {
        playAnimation(name: string, loop?: boolean, elapsed: number = 0): Promise<boolean>;
        playBackAnimation(name: string, timeScale: number = 1): Promise<boolean>;
        tryPlayAnimations(names: string[], loop = false, elapsed: number = 0): Promise<boolean>;
        getAnimationDuration(name: string): number;
        findSkin(skinName?: string)
        getSkinAttachment(slotName: string, attachmentName: string, skinName?: string)
        setSkinAttachment(slotName: string, attachmentName: string, skinName?: string)
        setSlotActive(slotName: string, active: boolean)
        switchSlot(slotName: string)
        attachUtil: { generateAttachedNodes: (name?: string) => cc.Node, destroyAllAttachedNodes: (name?: string) => void }
        getAttachedNode(nodeName: string, syncImmediate: boolean = true)
        getEvent(animName: string, eventName?: string): sp.spine.Event
        getEvents(animName: string): sp.spine.Event[]
        getEventTime(animName: string, eventName?: string): number
        mix(anim1: string, anim2: string, dur: number = 0.15)
        mix2(anim1: string, anim2: string, dur1: number = 0.15, dur2?: number)
        addTime(num: number)
        getAnimationTime(trackIndex: number = 0): number
        setSlotAttachment(slotName: string, spf: cc.Texture2D | cc.SpriteFrame, opt?: { anchorX?: number, anchorY?: number, flipY?: boolean })
        setAccTimeScale(timeScale)
        getAnimations(): string[]
    }
}

type ProcessCallback = (completedCount: number, totalCount: number, item?: any) => void;

type EventItem = {
    callback: Function;
    target: any;
}

type AudioAsset = {
    mod: string;// 模块名
    url: string;
    audio: cc.AudioClip;
}

type PnlParam = {
    isClean?: boolean;
    isAct?: boolean;
    isMask?: boolean;
    Index?: number;
    adaptWidth?: number;
    adIndex?: number;
    maskName?: string
    maskOpacity?: number
}

type WindParam = {
    isClean?: boolean;
}

// 加载pnl信息
type LoadPnlInfo = {
    id: number;
    name: string; //传入名
    url: string; //实际pnl路径
    params?: any[]; //参数
}

class BaseMvcCtrl extends cc.Component {
    public loadProperty();

    public __newEventHandler(handler: string, data: string);
}

class BaseViewCtrl extends BaseMvcCtrl {
    readonly _state: string;
    addListener(type: string, cb: Function, target?: any);
    removeListener(type: string);
    emit(type: string | number, ...params: any);
    addClickEvent(cmpt: cc.Component, handler: string, data?: string);
    // addWdtCtrl(path: string, type: typeof mc.BaseWdtCtrl): mc.BaseWdtCtrl;
    listenEventMaps(): { tag?: string }[];
    getModel<T>(key: string): T;
    getTag(): string;
}


declare namespace mc {

    export const GameNameSpace: string;
    export var currWind: BaseWindCtrl;
    export var currWindName: string;
    export var preWindName: string;
    export var isGoingtoNextWind: boolean;
    export var currScene: string;// 当前场景 start - loading - main
    export var lang: string; //当前语言
    export function init(name: string, root: cc.Node, lang?: string, changeLang?: boolean): void;
    export function lockTouch(val: boolean);
    export function addmodel(type: string, priority?: number);
    export function getWindNode(): cc.Node;
    export function getViewNode(): cc.Node;
    export function getNoticeNode(): cc.Node;
    export function getViewMgr(): ViewCtrlMgr;
    export function getPnl<T extends BasePnlCtrl>(name: string): T
    export function isPnlInQueue(name: string): boolean
    export function isPressed(): boolean
    export enum Event {
        MVC_ERROR_MSG,
        /** (key: string | BasePnlCtrl, ...params: any) */
        OPEN_PNL,
        /** (key: string | BasePnlCtrl) */
        HIDE_PNL,
        /** (val?: string, ignores: string) */
        HIDE_ALL_PNL,
        /** (key: string | BasePnlCtrl) */
        CLOSE_PNL,
        /** (val?: string) */
        CLOSE_ALL_PNL,
        /** (mod: string) */
        CLOSE_MOD_PNL,
        /** (key: string, complete?: Function, progress?: (done: number, total: number) => void) */
        PRELOAD_PNL,
        LOAD_BEGIN_PNL,
        LOAD_END_PNL,
        PNL_ENTER,
        PNL_LEAVE,
        CLEAN_ALL_UNUSED,
        /** (id: number) */
        GIVEUP_LOAD_PNL,
        /** (key: string, ...params: any) */
        GOTO_WIND,
        /** (key: string, complete?: Function, progress?: (done: number, total: number) => void) */
        PRELOAD_WIND,
        WIND_ENTER,
        CLEAN_CACHE_WIND,
        LOAD_BEGIN_WIND,    // 这个事件只能用于转场动画的播放，唯一的，其他地方不要监听同名的
        LOAD_END_WIND,// 这个事件只能用于转场动画的播放，唯一的，其他地方不要监听同名的
        /** (complete?: Function, progress?: (done: number, total: number) => void) */
        LOAD_ALL_NOTICE,
        /** 热更新事件 */
        HOT_UPDATE_EVENT,
    }

    export class BasePnlCtrl extends BaseViewCtrl {
        readonly key: string; //传入名
        readonly mod: string; //所属模块名
        readonly url: string; //路径
        readonly Index: number; //层级
        mask: cc.Node; //当前的遮照
        async onCreate(...params: any): Promise<void>;
        onEnter(...params: any);
        onRemove();
        onClean();
        hide(...params: any);
        close();
        setOpacity(val: number); //设置UI的透明度
        setParam(opts: PnlParam);
    }

    export class BaseWindCtrl extends BaseViewCtrl {
        readonly key: string; //传入名 即模块名
        async onCreate(...params: any): Promise<void>;
        onEnter(...params: any);
        onLeave();
        onClean();
        setParam(opts: WindParam);
    }

    export class BaseNoticeCtrl extends BaseViewCtrl {
        async onCreate(): Promise<void>;
        onClean();
        open();
        hide();
    }

    export class BaseWdtCtrl extends BaseViewCtrl {
        onCreate();
        onClean();
    }

    export class BaseCmptCtrl extends BaseViewCtrl {
        onCreate();
        onClean();
        onEnter();
        onRemove();
    }
    export class BaseLogCtrl extends cc.Component {
        onCreate();
        onClean();
        onLoggerListener(type: string, content: string);
        close();
    }

    export class BaseModel {
        readonly type: string;
        constructor(type: string);
        protected onCreate();
        protected onClean();
        protected emit(type: string | number, ...params: any);
        protected getModel<T>(key: string): T;
        public init()
        public onEnable()
        public update(dt)
    }

    export class modelMgr {
        static add(...params: BaseModel[]): void;
        static get<T>(key: string): T;
        static reset(model: BaseModel): void;
        static getModels(): Map<string, BaseModel>
        static initModels()
    }
}

var eventCenter: {
    emit(type: number | string, ...params: any): void;
    get(type: number | string, ...params: any): any;
    async req(type: number | string, ...params: any): any;
    on(type: number | string, callback: Function, target?: any): void;
    once(type: number | string, callback: Function, target?: any): void;
    async wait(type: number | string, checkEnd?: (...params) => boolean): Promise<any>;
    off(type: number | string, callback?: Function, target?: any): void;
    clean();
}

var storageMgr: {
    async init(): void
    register(key: string, callback: Function, target: any): void;
    loadString(key: string): string;
    saveString(key: string, val: string): void;
    loadNumber(key: string): number;
    saveNumber(key: string, val: number): void;
    loadBool(key: string): boolean;
    saveBool(key: string, val: boolean): void;
    loadJson(key: string): any;
    saveJson(key: string, val: any): void;
    loadBigJson(key: string): any;
    saveBigJson(key: string, val: any): void;
    save(): void;
    clear(): void;
    reset(): void;
    getStorageInfo();
    setStorageInfo(info: any, ver: number);
    syncSaveLazy(): void
    public loadObject(key: string): any
    public saveObject(key: string, val: object)
    getOrgItem(key: string)
    setOrgItem(key, data: string)
}

var hotUpdateMgr: {
    start(packageUrl: string, manifestUrl: string, version: string);
    abort();
    redownload();
    getVersion(): string;
    cleanCache();
    convertBytesToString(bytes: number): string;
    isUpdating(): boolean;
}

declare namespace ut {

    export var Time: {
        readonly Year: number,
        readonly Month: number,
        readonly Week: number
        readonly Day: number;
        readonly Hour: number;
        readonly Minute: number;
        readonly Second: number;
    }

    export function now(): number;

    export function getTimeAgo(time: number | string): string

    export function millisecondToString(msd: number): string;

    /**
     * 将一个毫秒数格式化 format('hh:mm:ss')
     * @param msd
     * @param format 默认'mm:ss'
     */
    export function millisecondFormat(msd: number, format?: string): string;

    /**
     * 将一个秒格式化
     * @param val
     * @param format 默认'mm:ss'
     */
    export function secondFormat(val: number, format?: string): string;

    /**
     * 将一个时间 format('yyyy-MM-dd hh:mm:ss')
     * @param format
     * @param msd
     */
    export function dateFormat(format: string, msd?: number): string;

    /**
     * 首字母变成大写
     * @param str
     */
    export function initialUpperCase(str: string): string;

    /**
     * 将数字转换为String 中文
     * @param money
     * @param num
     */
    export function simplifyMoneyCh(money: number, num?: number): string;

    /**
     * 将数字转换为String 英文
     * @param money
     * @param num
     */
    export function simplifyMoneyEn(money: number, num?: number): string;

    /**
     * 名字省略
     * @param name
     * @param max
     * @param extra
     */
    export function nameFormator(name: string, max: number, extra?: string): string;

    /**
     * 将数字以逗号隔开
     * @param num
     */
    export function formatNumberByComma(num: number): string;

    /**
     * 随机一个整数 包括min和max
     * @param min [最小值]
     * @param max [最大值]
     */
    export function random(min: number, max?: number): number;

    /**
     * 随机排序一个队列
     * @param array 必须是一个队列
     */
    export function randomArray<T>(array: T[]): T[];

    /**
     *
     * @param min 最小值
     * @param max 最大值
     * @param isFloat 是否要小数
     * @param decimal 小数后面位数
     */
    export function getRandomNum(min: number, max: number, isFloat: boolean = false, decimal: number = 2): number;

    /**
     * 获取对象中的基本数据类型
     * @param data
     * @param attribute
     */
    export function getBaseAttribute(data: Object, attribute: Array<string>): any

    /**
     * 是否为基础数据类型
     * @param value
     */
    export function isBasicType(value: any): boolean
    /**
     * 角度转弧度
     * @param value
     */
    function angle2radian(value: number): number


    /**弧度转角度 */
    function radian2angle(value: number): number

    /**
     *
     * @param graphics
     * @param from 起始点
     * @param to 结束点
     * @param color 虚线颜色
     * @param width 虚线宽度
     * @param length 每段虚线的长度
     * @param interval 每段虚线的间隔
     */
    export function drawDottedLine(graphics: cc.Graphics, from: cc.Vec2, to: cc.Vec2, color: cc.Color, width: number = 10, length: number = 10, interval: number = 5);

    export function drawLine(graphics: cc.Graphics, from: cc.Vec2, to: cc.Vec2, color: cc.Color, width: number = 10);

    /**
     * 是否有概率
     * @param odds 概率值必须是100内的数字
     * @param mul 概率值倍数
     */
    export function chance(odds: number, mul: number = 1): boolean

    /**
     * 随机一个小数
     */
    export function randomRange(min: number, max: number): number;

    /**
     * 从数组范围中随机
     */
    export function randomInAry(ary: number[]): number;

    /**
     * 随机一个下标出来
     * @param len    [数组长度]
     * @param count  [需要随机的个数](可不填)
     * @param ignore [需要忽略的下标](可不填)
     */
    export function randomIndex(len: number);

    /**
     * 新的获取角度
     * 以a为圆点开始顺时针方向旋转到b点的角度
     * @param a [圆点]
     * @param b [目标点]
     */
    export function getAngle(a: cc.Vec2 | cc.Vec3, b: cc.Vec2 | cc.Vec3): number;
    export function normAngle(angle: number): number;
    export function simplifyMoney(money: number): string
    /**
     * Math.sin 返回Y坐标
     * @param angle
     */
    export function sin(angle: number): number;

    /**
     * Math.cos 返回X坐标
     * @param angle
     */
    export function cos(angle: number): number;

    /**
    * @param angle
    */
    export function tan(angle: number): number;

    /**
     * 同父级下两点之间的距离
     * @param localPos
     * @param tarPos
     */
    export function calculationDis(localPos: cc.Vec2, tarPos: cc.Vec2)

    /**
     * 根据角度和距离 获取坐标
     * @param angle
     * @param dis
     * @param out
     */
    export function angleToPoint(angle: number, dis: number, out?: cc.Vec2): cc.Vec2;

    /**
    * 获取某个节点的某个坐标在某个节点里面的坐标
    * @param node 需要转换的节点
    * @param targetNode 要转换到的目标节点
    * @param nodePoint 第一个节点的某个坐标，默认原点
    * @param withCamera 是否需要相机转换（两个节点在不同摄像机下), 默认为true
    */
    export function convertToNodeAR(node: cc.Node, targetNode: cc.Node, nodePoint?: cc.Vec2, out?: cc.Vec2, withCamera: boolean = true): cc.Vec2;

    /**
     * 改变父节点但保持相对位置不变
     * @param node
     * @param parent
     */
    export function convertParent(node: cc.Node, parent: cc.Node, useCamera: boolean = false);

    /**
    * 数字 字符串补0,根据长度补出前面差的0
    * @param num 需要补的数字
    * @param length 要补的长度默认为2
    */
    export function pad(num: number, length: number = 2): string;

    /**
     * 将一个数字 分解成多个类型的数字
     * @param num   [数字]
     * @param types [你想分解的类型列表 可不填]
     */
    export function decomposeNumberToTypes(num: number, types: number[] = [100000, 10000, 1000, 100, 10, 1], out?): any;

    /**
     * 将一个字符串转换成向量
     * @param str 一个字符串必须满足以逗号隔开
     * @param separator 分隔符默认','
     */
    export function stringToVec2(str: string, separator?: string): cc.Vec2;

    /**
     * 将一个字符串拆分为数组
     * @param str
     * @param separator 默认|
     */
    export function stringToNumbers(str: string, separator?: string): number[];

    /**
     * //将时间格式转成毫秒
     * @param str
     */
    export function stringToMs(str: string): number;

    /**
     * 将一个常数变成1 并保留正负
     * @param val
     */
    export function normalizeNumber(val: number): number;

    /**
     * 将一个数字转换为带正负符号的字符串
     * @param val
     */
    export function numberToString(val: number): string;

    /**
     * 填充一个带参数的字符串
     * @param text
     * @param params
     */
    export function stringFormat(text: string, params: any[]): string;

    export function stringRichTextFormat(text: string, params: any[]): string;

    /**
     * 同步等待时间 (单位秒)
     * @param delay
     */
    export function wait(delay: number, target?: cc.Component): Promise<void>;

    /**
     * 同步等待时间 (单位毫秒)，用setTimeout实现
     * @param delay
     */
    export function waitTimeout(delay: number): Promise<void>;

    /**
     * 等待下一帧
     * @param frames (需要等待的帧数,默认1)
     */
    export function waitNextFrame(frames?: number, target?: cc.Component): Promise<void>;

    /**
     * 读取 16 进制颜色
     * color.fromHEX("#FFFF33");
     * @param hexString
     */
    export function colorFromHEX(hexString: string): cc.Color;

    /**
     * 生成一个唯一ID
     */
    export function uid(): string;

    /**
     * 是否对象
     * @param o
     */
    export function isObject(o: any): boolean;

    /**
     * 判断是否空对象
     * @param o
     */
    export function isEmptyObject(o: any);

    /**
     * 拷贝对象
     * @param obj
     */
    export function cloneObject<T>(obj: T): T;

    /**
    * 深度拷贝对象
    * @param obj
    */
    export function deepClone(obj: any, inDeep?: boolean): any;

    /**
     * 深度比较两个对象是否相等
     * @param x
     * @param y
     */
    export function compareObject(x: any, y: any): boolean;

    /**
     * 组装列表
     * @param arr
     * @param datas
     * @param item
     * @param parent
     * @param cb
     */
    export function items(arr: cc.Node[], datas: any[], item: cc.Node | cc.Prefab, parent: cc.Node, cb: Function): void;

    /**
     * 循环值
     * @param val
     * @param len [数组长度]
     */
    export function loopValue(val: number, len: number);

    /**
     * 设置屏幕常亮
     * @param val
     */
    export function setKeepScreenOn(val: boolean);

    /**
     * 将一个bool值转成1和0
     * @param val
     */
    export function boolToNumber(val: boolean): number;

    /**
     * 对象给对象赋值
     * @param target
     * @param value
     * @param fields
     */
    export function setValue(fields: string, data: any, target?: any): any;

    /**
     * http请求
     * @param method
     * @param url
     * @param data
     * @param cb
     */
    export function httpRequest(method: string, url: string, data?: any): Promise<any>;

    /**
     * 是否手机平台
     */
    export function isMobile(): boolean;

    /**
     * 判断是否是小程序
     */
    export function isMiniGame(): boolean

    export function isWechatGame(): boolean

    export function isQQGame(): boolean

    /**
     * 是否ios
     */
    export function isIos(): boolean;

    /**
     * 判断是安卓
     */
    export function isAndroid(): boolean

    /**
     * 获取随机字符串
     */
    export function getRandomString(len: number): string

    /**
     * 创建一个数组
     * @param count
     * @param val
     */
    export function newArray(count: number, val?: any): any[];

    /**
       * Array.map的异步版本
       */
    export function promiseMap<T, R>(arr: T[], callback?: (T, index) => Promise<R> | any): Promise<R[]>;

    /**
     * 对方法进行加锁的修饰器
     * 加锁的方法必须以promise作为返回值，可以接收一个锁名作为参数
     * @example addLock  addLock("isWork")
     */
    export function addLock(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor

    /**
    * 处理一个异步方法被多次调用的情况，会按照调用顺序依次进行调用 (处理完第一个调用的结果再处理第二次调用)
    * 使用场景如 日志上报，需要上报每一条日志
    */
    export function queue(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor

    /** 节流
     * 处理一个异步方法被多次调用的情况，只处理最后一次调用
     * 使用场景如 上报蜡烛，只需要上报最新的就好了
     */
    export function throttle(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor

    /**
     * 保证函数全局只会被调用一次，直到退出游戏
     * 用于多个地方await一个初始化结果
     * *****注意：目前没办法递归
     */
    export function callOnce(target?: Object | string, propertyName?: string, propertyDescriptor?: PropertyDescriptor): PropertyDescriptor

    //按比例缩放节点
    export function scaleNode(width: number, height: number, target: cc.Node)

    //保留小数
    export function toFixed(val: number, precision: number = 2)
    export function toRound(val: number, precision: number = 2)

    //安全foreach，支持一边循环一边删除
    export function forEach<T>(array: T[], callback: (el: T, index: number) => {})

    //平均拆分数字
    export function numAvgSplit(num: number, count: number, random: boolean = true): Array<number>

    //随机拆分数字
    export function numRandomSplit(num: number, count: number): Array<number>

    export function lineLine(a1: cc.Vec2, a2: cc.Vec2, b1: cc.Vec2, b2: cc.Vec2, retP?: cc.Vec2): boolean
    export function lineRect(a1: cc.Vec2, a2: cc.Vec2, b: cc.Rect, retP?: cc.Vec2): boolean

    /**
     * 获取两个矩形相交面积
     * @param rect1
     * @param rect2
     */
    export function getIntersectionArea(rect1: cc.Rect, rect2: cc.Rect): number

    /**
     * 用于代替setTimeout 内部使用scheduleOnce
     * @param cb
     * @param delay
     * @param target
     */
    export function setTimeout(cb: Function, delay: number, target?: cc.Component): any;
    export function clearTimeout(cb: Function, target?: cc.Component): void;

    // 当实例化有Light的组件时，需要重置color
    export function resetLightItem(node: cc.Node);

    //16进制颜色代码，归一化rgba，返回[r, g, b, a]
    export function colorCodeToNormAry(code: string): number[]

    export function combination<T>(datas: T[], count: number, startIndex = 0, path = [], result: T[] = []): T[][]
    export function permutation<T>(datas: T[], count?: number, step = 0, path: any[] = [], result: T[] = []): T[][]
}

// 配置表结构
type JsonConfData<T> = {
    datas: T[]
    // dataIdMap: any
    getById(id: string | number): T
    get(key: string, value: any): T[]
    set(key: string, value: T)
}

// 临时资源结构
type TempAssetData = {
    name: string
    asset: cc.Asset
    url: string
    type: typeof cc.Asset
    tagCount: number
    refs: {} //引用列表
}

// 资源管理
var assetsMgr: {

    debug: boolean;

    localConfigMd5: { [key: string]: string }

    // 初始化
    async init(onProgess?: (percent: number) => void): Promise<void>;

    /**
     * 获取配置表数据
     * @param name
     */
    getJson<T>(name: string): JsonConfData<T>;

    /**
     * 获取配置表数据2
     * @param name
     * @param id
     */
    getJsonData<T>(name: string, id: string | number): T;

    checkJsonData<T>(name: string, id: string | number): T;

    /**
     * 获取全局图片
     * @param name
     */
    getImage(name: string): cc.SpriteFrame;

    /**
     * 获取全局预制体
     * @param name
     */
    getPrefab(name: string): cc.Prefab;

    /**
     * 获取声音
     * @param name
     */
    getAudio(name: string): cc.AudioClip;

    /**
     * 获取材质
     * @param name
     */
    getMaterial(name: string): cc.Material;

    /**
     * 获取字体
     * @param name
     */
    getFont(name: string): cc.Font;

    /**
     * 获取动画
     * @param name
     */
    getAnim(name: string): cc.AnimationClip;

    /**
     * 转换文本
     * @param key
     * @param params
     */
    lang(key: string, ...params: any[]): string;

    getLangKey(key: string): string;

    /**
     * 加载临时资源 (需要手动释放)
     * 会先从内存中寻找 如果有就会直接获取 没有将加载进来放入内存 缓存
     * @param name
     * @param type
     * @param tag
     * @param context 上下文环境，一般就传this
     */
    async loadTempRes(name: string, type: typeof cc.Asset, tag?: cc.Component | string): Promise<any>;
    async loadTempRseDir(key: string, type: typeof cc.Asset, tag?: cc.Component | string): Promise<any[]>;

    /**
     * 加载远程图片 (需要手动释放)
     * @param url
     * @param ext 图片后缀 （.png .jpg）
     * @param tag
     * @param context 上下文环境，一般就传this
     */
    async loadRemote(url: string, ext: string, context: cc.Component, tag?: string): Promise<cc.SpriteFrame>;

    /**
     * 释放临时资源
     * @param name
     * @param tag
     */
    releaseTempRes(name: string, tag?: string);
    releaseTempAsset(name: string);

    /**
     * 释放所有标记的临时资源
     * @param tag
     */
    releaseTempResByTag(tag: string);

    /**
     * 根据path，uuid等找资源url, 更详细的参数参考源码urlTransformer
     * @param params eg: {path: "manifest/project", bundle: "resources"}
     * @param option eg: {ext: .manifest}
     */
    public transform(params: { path: string, bundle: string } | string, option: { ext?: string, __isNative__?: boolean } | any = {}): string
    public getInfoWithPath(bundleName: string, path: string)
};

// 声音
var audioMgr: {

    bgmVolume: number;
    sfxVolume: number;

    // 初始化
    init(): void;
    // 暂停所有声音
    pauseAll(): void;
    // 恢复所有声音
    resumeAll(): void;
    //
    stopAll(): void;
    clean(): void;

    /**
     * 加载声音
     * @param urls
     */
    async load(urls: string | string[]): Promise<void>;

    /**
     * 预加载
     * @param url
     */
    async preload(url: string): Promise<void>;

    /**
     * 加载声音 根据模块来
     * @param mod
     */
    async loadByMod(mod?: string): Promise<void>;

    /**
     * 释放单个声音
     * @param val
     */
    release(val: string): void;

    /**
     * 释放对应mod的声音
     * @param mod
     */
    releaseByMod(mod?: string): void;

    /**
     * 释放所有声音
     */
    releaseAll(): void;

    /**
     * 播放背景音乐
     * @param url
     */
    playBGM(url: string, volume?: number): void;

    /**
     * 停止播放背景音乐
     */
    stopBGM(): void;

    /**
     * 播放音效
     * @param url
     * @param loop [是否循环]
     * @param cb [播放完成后的回调]
     */
    async playSFX(url: string, opts?: { volume?: number, startTime?: number, loop?: boolean, onComplete?: Function, tag?: string }): Promise<number>;

    /**
     * 停止播放音效
     * @param val
     */
    stopSFX(val: number | string | cc.AudioClip, tag: string = ''): void;

    /**
     * 设置临时的背景音乐声音
     * @param val
     */
    setTempBgmVolume(val: number): void;

}

// 日志
var twlog: {
    open: boolean;
    /**
     * 打印信息
     * @param params
     */
    info(...params: any[]): void;

    /**
     * 打印错误日志
     * @param params
     */
    error(...params: any[]): void;

    /**
     * 打印debug信息
     * @param params
     */
    debug(...params: any[]): void;

    upLog: {
        info(...params: any);
        warn(...params: any);
        error(...params: any);
        setFilterMsg(msg: any);
        addFilterMsg(msg: any);
    }
}

// 数组
interface Array<T> {

    /**
     * 删除数组一个元素并返回这个元素
     * @param key
     * @param value
     */
    remove(key: any, value?: any): T

    /**
     * 删除满足条件的数组元素
     * @param cb
     */
    delete(cb: (value: T, index: number) => boolean): T[]

    /**
     * 返回一个随机元素
     */
    random(): T

    /**
     * 是否有这个元素
     */
    has(key: any, value?: any): boolean

    /**
     * 添加一个元素并返回这个数组
     */
    append(val: T): T[]

    /**
     * 添加一个元素并返回这个元素
     */
    add(val: T): T

    /**如果数组没有这个元素,则添加 ,返回是否添加*/
    push2(val: any): boolean

    /**
     * 返回最后一个元素
     */
    last(): T

    /**
     * 拼接数组 对象
     */
    join2(cb: (value: T, index: number) => string, separator?: string): string

    /**
     * push数组
     */
    pushArr(arr: T[]): number;

    /**
     * 重新设置这个数组
     */
    set(arr: T[]): T[]

    /**
     * 从后面查找index
     * @param cb
     */
    findLastIndex(cb: (value: T, index: number) => boolean): number

    /**
     * 返回数组中的最小值
     * @param calFunc 根据当前值计算出一个用于比较的数字，对于纯数字可以不传入
     * 对于更复杂的需求，如求字符串/对象的最小值，只能通过sort
     */
    min(calFunc?: (value: T, index: number) => number): T
    minList(calFunc?: (value: T, index: number) => number): T[]
    max(calFunc?: (value: T, index: number) => number): T
    maxList(calFunc?: (value: T, index: number) => number): T[]
    for(calFunc?: (value: T, index: number) => void): void
}

interface ArrayConstructor {
    // 生成start-end的序列
    range(start: number, end: number)
}


// promise
interface PromiseConstructor {
    /**
     * @param iterators
     * @description 返回第一个成功的
     */
    any<T>(iterators: readonly (T | PromiseLike<T>)[]): Promise<T>;
}
declare class BFS {
    /**
     * 广度优先搜索 给定一个二维数组，起点和终点，找到一条从起点到终点的最短路径
     * @param grid 二维网格
     * @param start 起点
     * @param target 终点
     * @returns Point数组表示的路径，如果不可达则返回null
     */
    public static find(grid: number[][], start: Point, target: Point): Point[] | null
    /**
     * 广度优先搜索 给定一个二维数组，起点和终点，判断终点是否可达
     * @param grid 二维网格
     * @param start 起点
     * @param target 终点
     * @returns 是否可达
     */
    public static isReachableWithStart(grid: number[][], start: Point, target: Point): boolean
    /**
     * 广度优先搜索 给定一个二维数组和终点，判断终点是否可达
     * @param grid 二维网格
     * @param target 终点
     * @returns 是否可达
     */
    public static isReachable(grid: number[][], target: Point): boolean
    /**
    * 找到网格中可达的最深点
    * @param grid 二维网格
    * @returns [x, y] 最深点的坐标
    */
    public static findDeepestPoint(grid: number[][]): [number, number]
}
