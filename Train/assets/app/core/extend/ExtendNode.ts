
/**
 * Node扩展方法
 */

import BaseLocale from "../base/BaseLocale";

cc.Node.prototype.Data = null;

cc.Node.prototype.FindChild = function (name: string | number, className?: any): any {
    name = String(name)
    let val = this;
    const arr = name.split('/');
    for (let i = 0, l = arr.length; i < l; i++) {
        val = val.getChildByName(arr[i]);
        if (!val) {
            return null;
        }
    }
    if (className) {
        val = val.getComponent(className);
    }
    return val;
};

const CACHE_PREFIX = "__@"

cc.Node.prototype.Child = function (name: string | number, className?: any): any {
    name = String(name)
    const cls = typeof className === 'function' ? '_' + cc.js.getClassName(className).replace('.', '') : (className ? '_' + className : '');
    const field = CACHE_PREFIX + name.replace(/\//g, '_') + cls;
    let val = this[field];
    if (!cc.isValid(val)) {
        val = this;
        const arr = name.split('/');
        for (let i = 0, l = arr.length; i < l; i++) {
            val = val.getChildByName(arr[i]);
            if (!val) {
                break
            }
        }
        if (val && className) {
            val = val.getComponent(className);
        }
        this[field] = !!val ? val : null;
    }
    return val;
};

cc.Node.prototype.Component = function (className: any): any {
    if (!className) {
        return null
    }
    const cls = typeof className === 'function' ? cc.js.getClassName(className).replace('.', '') : className;
    const field = CACHE_PREFIX + cls
    let val = this[field];
    if (!val) {
        val = this.getComponent(className);
        this[field] = val;
    }
    return val;
};

cc.Node.prototype.clearCacheField = function (): any {
    for (let field in this) {
        if (field.startsWith(CACHE_PREFIX)) {
            delete this[field]
        }
    }
};

cc.Node.prototype.Items = function <T>(list: T[], prefab?: any, cb?: (it: cc.Node, data: T, i: number) => void | any, target?: any) {
    let i = 0, count = list.length, childs = this.children
    let prefabKey = "__itemsPrefab"
    let item = this[prefabKey] || childs[0]
    if (typeof (prefab) === 'function') {
        target = cb
        cb = prefab
    } else if (prefab instanceof cc.Node || prefab instanceof cc.Prefab) {
        item = prefab
    }
    if (!item) {
        return twlog.error('Items error, not item')
    }
    if (this[prefabKey] != item) {
        item = this[prefabKey] = cc.instantiate(item)
    }
    for (; i < this.childrenCount; i++) {
        const it = childs[i]
        if (i < count) {
            it.active = true
            setItemData(it, list[i], i, cb, target)
        } else {
            it.Data = null
            it.active = false
        }
    }
    for (; i < count; i++) {
        const it = cc.instantiate2(item, this)
        it.active = true
        setItemData(it, list[i], i, cb, target)
    }
}

// 添加一个
cc.Node.prototype.AddItem = function (prefab?: any, cb?: (it: cc.Node, i: number) => void, target?: any) {
    let item = null
    if (typeof (prefab) === 'function') {
        target = cb
        cb = prefab
    } else if (prefab instanceof cc.Node || prefab instanceof cc.Prefab) {
        item = prefab
    }
    let i = this.children.findIndex(m => !m.active), it = null
    if (item) {
        it = cc.instantiate2(item, this)
    } else if (i !== -1) {
        it = this.children[i]
    } else {
        it = cc.instantiate2(this.children[0], this)
    }
    it.active = true
    setItemData(it, i === -1 ? this.childrenCount : i, undefined, cb, target)
}

function setItemData(it: cc.Node, data: any, i: number, cb: Function, target: any) {
    it.Data = data;
    if (!cb) {
        return
    }
    if (target) {
        cb.call(target, it, data, i)
    } else {
        cb(it, data, i)
    }
}

async function setItemDataAsync(it: cc.Node, data: any, i: number, cb: Function, target: any) {
    it.Data = data;
    if (!cb) {
        return
    }
    if (target) {
        await cb.call(target, it, data, i)
    } else {
        await cb(it, data, i)
    }
}

cc.Node.prototype.getItems = function () {
    return this.children.filter(it => it.active)
}

// 切换节点
cc.Node.prototype.Swih = function (val: string | number | SwihNodeCallback): cc.Node[] {
    let name: string, cb: SwihNodeCallback
    if (typeof (val) === 'function') {
        cb = val
    } else if (typeof (val) === 'number' || typeof (val) === 'string') {
        name = String(val)
    } else {
        return []
    }
    let arr: cc.Node[] = []
    for (let i = 0, l = this.childrenCount; i < l; i++) {
        const m = this.children[i]
        m.active = cb ? !!cb(m) : (m.name === name)
        if (m.active) {
            arr.push(m)
        }
    }
    return arr
}

// 设置颜色
cc.Node.prototype.SetColor = function (val: string | cc.Color) {
    if (val instanceof cc.Color) {
        this.color = val
    } else {
        let color = new cc.Color().fromHEX(val)
        if (color.a != 255) {
            this.opacity = color.a
            color.a = 255
        }
        this.color = color
    }
    return this
}

// 设置触摸事件穿透
cc.Node.prototype.SetSwallowTouches = function (val: boolean) {
    if (this._touchListener) {
        this._touchListener.setSwallowTouches(val)
    }
}

cc.Node.prototype.IsSwallowTouches = function () {
    return this._touchListener && this._touchListener.isSwallowTouches()
}

// 设置多语言key
cc.Node.prototype.setLocaleKey = function (key: string, ...params: any[]) {
    const locale = this.Component(BaseLocale)
    if (locale) {
        locale.setKey(key, ...params)
    } else {
        twlog.error('setLocaleKey error, not LocaleComponent!')
    }
}

cc.Node.prototype.setLocaleUpdate = function(func: Function) {
    const localeLabel = this.Component(BaseLocale)
    if (localeLabel) {
        localeLabel.setUpdate(func);
    } else {
        twlog.error('setLocaleUpdate error, not LocaleComponent!')
    }
}

cc.Node.prototype.getRect = function (position?: cc.Vec2) {
    let width = this._contentSize.width;
    let height = this._contentSize.height;
    let rect = cc.rect(
        -this._anchorPoint.x * width + (position?.x || 0),
        -this._anchorPoint.y * height + (position?.y || 0),
        width,
        height);
    return rect
}

cc.Node.prototype.getWorldRect = function (position?: cc.Vec2) {
    this._calculWorldMatrix();
    let rect = cc.rect(0, 0, this._contentSize.width, this._contentSize.height)
    rect.transformMat4(rect, this._worldMatrix);
    return rect
}

cc.Node.prototype.getPath = function () {
    let paths = [this.name]
    let node = this.parent
    while (node) {
        paths.push(node.name)
        node = node.parent
    }
    return paths.reverse().join("/")
}

function setOneGray(node: cc.Node, bol: boolean) {
    let cmpt = node.getComponent(cc.RenderComponent)
    if (cmpt instanceof sp.Skeleton) {
        cmpt.setMaterial(0, bol ? assetsMgr.getMaterial("gray_spine") : cc.Material.getBuiltinMaterial("2d-spine"))
    }
    else if (cmpt) {
        cmpt.setMaterial(0, cc.Material.getBuiltinMaterial(bol ? "2d-gray-sprite" : "2d-sprite"))
    }
}

// 精灵及其子精灵全部变灰
cc.Node.prototype.setGray = function (bol: boolean) {
    setOneGray(this, bol)
    this.children.forEach(node => {
        node.setGray(bol)
    })
}

function setOneDarkness(node: cc.Node, darkness) {
    let cmpt = node.getComponent(cc.RenderComponent)
    if (!cmpt) return

    darkness = (1 - darkness)
    
    let onColorChange = function() {
        let node = this
        node[key] = node.color.clone()
        update.call(this)
    }

    let update = function(){
        let node = this
        let orgColor = node[key]
        cc.Color.set(node["_color"], orgColor.r * darkness, orgColor.g * darkness, orgColor.b * darkness)
        node["_renderFlag"] |= cc["RenderFlow"]["FLAG_COLOR"]
        let cmpt = node.getComponent(cc.Label)
        if (cmpt) {
            cmpt["_nodeColorChanged"]()
        }
    }

    let key = "__darknessOrgColor"
    if (!node[key]) {
        node[key] = node.color.clone()
        node.on(cc.Node.EventType.COLOR_CHANGED, onColorChange, node)
    }

    update.call(node)
}

//变暗
cc.Node.prototype.setDark = function(darkness = 0.5, recur: boolean = false) {
    setOneDarkness(this, darkness)
    if (recur) {
        this.children.forEach(node => {
            node.setDark(darkness, recur)
        })
    }
}

cc.Node.prototype.removeAndDestroyAllChildren = function() {
    this.destroyAllChildren()
    this.removeAllChildren()
}

cc.Node.prototype.hitTestByWorldPos = function(worldPos: cc.Vec2) {
    let pos = this.convertToNodeSpaceAR(worldPos)
    return this.getRect().contains(pos)
}

cc.Node.prototype.removeAndDestroy = function() {
    this.parent = null
    this.destroy()
}
