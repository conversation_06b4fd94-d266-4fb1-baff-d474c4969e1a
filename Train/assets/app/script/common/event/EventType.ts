import { PASSENGER_QUALITY_COLOR } from "../constant/Constant";

/**
 * 全局事件（全大写单词间用下划线隔开）
 */
export default {
    //common
    UPDATE_HEART: "UPDATE_HEART",
    UPDATE_DIAMOND: "UPDATE_DIAMOND",
    UPDATE_STARDUST: "UPDATE_STARDUST",
    UPDATE_PAPER_CRANES: "UPDATE_PAPER_CRANES",
    SHOW_TOP: 'SHOW_TOP',
    HIDE_TOP: 'HIDE_TOP',
    RESTORE_TOP: 'RESTORE_TOP',
    LAST_TOP: 'LAST_TOP',
    STOP_MOVE_CLOUD: 'STOP_MOVE_CLOUD',
    START_MOVE_CLOUD: 'START_MOVE_CLOUD',
    SHOW_SPEECH_BUBBLE: 'SPEECH_BUBBLE',
    HIDE_SPEECH_BUBBLE: 'HIDE_SPEECH_BUBBLE',
    CHANGE_NUM_PROP: 'CHANGE_NUM_PROP', //道具数量有变化时
    TIME_ACCELERATE_START: 'TIME_ACCELERATE_START',
    TIME_ACCELERATE_END: 'TIME_ACCELERATE_END',
    CAMERA_MOVE: 'CAMERA_MOVE',
    CAMERA_MOVE_START: 'CAMERA_MOVE_START',
    CAMERA_MOVE_END: 'CAMERA_MOVE_END',
    INIT_SHOW_TIME: 'INIT_SHOW_TIME',
    UPDATE_ENERGY: 'UPDATE_ENERGY',
    TRAIN_MOVING_PLANET: 'TRAIN_MOVING_PLANET',//前往某星球
    UPDATE_ONE_CURRENCY: 'UPDATE_ONE_CURRENCY',
    FORBIDDEN_UPDATE_CURRENCY: 'FORBIDDEN_UPDATE_CURRENCY',
    DAILY_REFRESH: 'DAILY_REFRESH', //每日刷新
    INIT_PAY_FINISH: "INIT_PAY_FINISH",
    REWARDPNL_CLOSE: "REWARDPNL_CLOSE",
    DEEP_REWARDPNL_CLOSE: "DEEP_REWARDPNL_CLOSE",
    DAY_NIGHT_EXCHANGE: "DAY_NIGHT_EXCHANGE",//游戏中白天黑夜交替
    UPDATE_CURRENCY: "UPDATE_CURRENCY",
    SHOW_CURRENCY_UI: "SHOW_CURRENCY_UI",
    TRAIN_ACTIVITY_STATE_CHANGE: "TRAIN_ACTIVITY_STATE_CHANGE", // 主界面左上角的时间背景随活动变动
    GAME_DAY_CHANGE: "GAME_DAY_CHANGE", //游戏日期变化

    //列车活动
    TRAIN_ACTIVITY_PNL_SELECT_CHANGE: "TRAIN_ACTIVITY_PNL_SELECT_CHANGE", // 列车活动 - 进行活动安排选中item时
    TRAIN_ACTIVITY_ARRANGE: "TRAIN_ACTIVITY_ARRANGE", // 列车活动 - 活动安排成功
    TRAIN_ACTIVITY_REWARD_SHOW: "TRAIN_ACTIVITY_REWARD_SHOW", // 列车活动 - 车厢展示奖励
    TRAIN_ACTIVITY_ANIM_SHOW: "TRAIN_ACTIVITY_ANIM_SHOW", // 列车活动 - 开始展示动画
    UI_TIP_TRAIN_ACTIVITY: "UI_TIP_TRAIN_ACTIVITY", // 活动提示
    TRAIN_ACTIVITY_GATHER: "TRAIN_ACTIVITY_GATHER", // 列车活动开始聚集

    //星球相关
    REACH_PLANET: 'REACH_PLANET', //到达星球
    PLANET_MOVE_CHANGE: 'PLANET_MOVE_CHANGE', //星球航行变更
    UPDATE_PLANET_ROBOT: 'UPDATE_PLANET_ROBOT', //星球机器人状态更新
    ENTER_COLLECT: 'ENTER_COLLECT', //进入采集状态
    EXIT_COLLECT: 'EXIT_COLLECT',
    ON_COLLECT: 'ON_COLLECT',   //每次进行实际采集操作
    COLLECT_COMBO: 'COLLECT_COMBO',
    CHANGE_PLANET_MAP: 'CHANGE_PLANET_MAP', //更换星球地图
    PLANET_NODE_COMPLETE: 'PLANET_NODE_COMPLETE', //星球进度变化
    PLANET_NODE_SEVER_DIE: 'PLANET_NODE_SEVER_DIE', //星球战斗节点通关(服务器)
    PLANET_COMPLETE: 'PLANET_COMPLETE', //星球探索完成
    PLANET_DONE: 'PLANET_DONE', //视图层面
    PLANET_NODE_END: 'PLANET_NODE_END', //一个星球节点结束
    SHOW_PLANET_GUIDE: 'SHOW_PLANET_GUIDE', //
    HIDE_PLANET_GUIDE: 'HIDE_PLANET_GUIDE', //
    TARGET_PLANET_NODE: 'TARGET_PLANET_NODE', //
    REACH_PLANET_NODE: 'REACH_PLANET_NODE', //
    READY_PLANET_NODE: 'READY_PLANET_NODE',
    PLANET_SAVE_CAT_START: 'PLANET_SAVE_CAT', //救猫
    PLANET_SAVE_CAT_END: 'PLANET_SAVE_CAT', //救猫
    PLANET_STAMINA_CHANGE: 'PLANET_STAMINA_CHANGE', //星球体力更新
    PLANET_GEN_REWARD: 'PLANET_GEN_REWARD', //抛出奖励
    PLANET_GEN_BAG_ASSETS1: 'PLANET_GEN_BAG_ASSETS1', //抛出奖励(材料掉落)
    PLANET_GEN_BAG_ASSETS2: 'PLANET_GEN_BAG_ASSETS2', //抛出奖励(飞完)
    PLANET_GEN_MINE_TIPS: 'PLANET_GEN_MINE_TIPS', //奖励tips
    PLANET_SHOW_HIT_TIPS: 'PLANET_SHOW_HIT_TIPS', //伤害数值tips
    CLAIM_MINE_REWARD_START: 'CLAIM_MINE_REWARD_START', //开始收取采集奖励
    ON_GET_NEW_MINE: 'ON_GET_NEW_MINE',
    CLAIM_MINE_REWARD_END: 'CLAIM_MINE_REWARD_END',
    BACK_SELECT_PLANET_PNL: 'BACK_SELECT_PLANET_PNL',
    PLANET_MINE_QTE_START: 'PLANET_MINE_QTE_START',
    PLANET_MINE_QTE_END: 'PLANET_MINE_QTE_END',
    PLAENT_CONTROL_TOUCH_START: 'PLAENT_CONTROL_TOUCH_START', //星球右下角按钮的生命周期
    PLAENT_CONTROL_TOUCH_END: 'PLAENT_CONTROL_TOUCH_END',
    PLAENT_CONTROL_TOUCH_CANCEL: 'PLAENT_CONTROL_TOUCH_CANCEL',
    PLAENT_CONTROL_TIP_CHANGE: 'PLAENT_CONTROL_TIP_CHANGE',
    PLAENT_CAN_CONTROL_JUMP: 'PLAENT_CAN_CONTROL_JUMP',
    PLAENT_CAN_JUMP_TO_END: 'PLAENT_CAN_JUMP_TO_END',//能跳到终点
    PLAENT_CHANGE_JUMP_END: 'PLAENT_CHANGE_JUMP_END',//改变跳的终点
    READY_ADD_PLANET_ITEM: 'READY_ADD_PLANET_ITEM', //准备获得星球道具
    ADD_PLANET_ITEM: 'ADD_PLANET_ITEM', //获得星球道具
    REMOVE_PLANET_ITEM: 'REMOVE_PLANET_ITEM', //移除星球道具
    GARDEN_FIRST_LAND_OVER: 'GARDEN_FIRST_LAND_OVER',
    PLANET_HIDE_CUR_MINE_UI: 'PLANET_HIDE_CUR_MINE_UI',
    EXPLORE_SPEED_UP_START: "EXPLORE_SPEED_UP_START", //星球探索加速开始
    EXPLORE_SPEED_UP_END: "EXPLORE_SPEED_UP_END", //星球探索加速结束
    UPDATE_AUTO_EXPLORE: "UPDATE_AUTO_EXPLORE", //解锁自动采集类型
    UPDATE_AUTO_EXPLORE_TIME: "UPDATE_AUTO_EXPLORE_TIME", //更新自动探索时间
    RELOAD_PLANET_WIND: "RELOAD_PLANET_WIND", //重新加载星球场景
    PLANET_SHOW_TIPS: "PLANET_SHOW_TIPS",
    PLANET_HIDE_TIPS: "PLANET_HIDE_TIPS",
    PLANET_MINE_HP_CHANGE: "PLANET_MINE_HP_CHANGE",
    TIME_STONE_APPEAR: "TIME_STONE_APPEAR",
    TIME_STONE_USED: "TIME_STONE_USED",
    HEAD_BACK_PLAY_OVER: "HEAD_BACK_PLAY_OVER",//车头倒放演绎结束
    PLANET_MOVE_PAUSE: "PLANET_MOVE_PAUSE",
    CHECK_USE_PLANET_ITEM: "CHECK_USE_PLANET_ITEM",
    USE_PLANET_ITEM: "USE_PLANET_ITEM",
    PLANET_DEEP_EXPLORE_START: 'PLANET_DEEP_EXPLORE_START', //开始深度探索
    PLANET_DEEP_EXPLORE_END: 'PLANET_DEEP_EXPLORE_END', //结束深度探索
    PLANET_DEEP_EXPLORE_SHIP_CHANGED: 'PLANET_DEEP_EXPLORE_SHIP_CHANGED', //探索飞船数量改变
    PLANET_PROFILE_UNLOCK: 'PLANET_PROFILE_UNLOCK', //星球资料解锁
    PLANET_LAND_ANIM_END: 'PLANET_LAND_ANIM_END', //星球登录动画结束

    //星球问答
    ENTER_PLANET_QUESTION: "ENTER_PLANET_QUESTION",
    EXIT_PLANET_QUESTION: "EXIT_PLANET_QUESTION",
    UNLOCK_PLANET_QUESTION: "UNLOCK_PLANET_QUESTION",

    //主角相关
    CHANGE_TOOL: 'CHANGE_TOOL', //更换武器
    HERO_CHANGE_STATE: 'HERO_CHANGE_STATE',
    HERO_PICK_ANIM_END: 'HERO_PICK_ANIM_END',
    HERO_COLLECT_NOEFFECT: 'HERO_COLLECT_NOEFFECT',
    HERO_COLLECT: 'HERO_COLLECT',
    HERO_QTE: 'HERO_QTE',
    HERO_RAGE_MODE_COLLECT: 'HERO_RAGE_MODE_COLLECT',

    //战斗相关
    ENTER_BATTLE_PREVIEW: 'ENTER_BATTLE_PREVIEW', //进入战斗预览
    EXIT_BATTLE_PREVIEW: 'EXIT_BATTLE_PREVIEW', //退出战斗预览
    ENTER_BATTLE: 'ENTER_BATTLE', //进入战斗
    EXIT_BATTLE: 'EXIT_BATTLE',
    BATTLE_SHOW_START_ROUND: 'BATTLE_SHOW_START_ROUND',
    BATTLE_ROUND_START: 'BATTLE_ROUND_START',
    ON_ROUND_START: 'ON_ROUND_START',

    //车厢相关
    SHOW_FLUTTER_MONEY: 'SHOW_FLUTTER_MONEY', //捡地上星尘时的飘字
    GET_UI_MONEY_ICON: 'GET_UI_MONEY_ICON', //获取星尘ui节点
    ROLE_DROP_MONEY: 'ROLE_DROP_MONEY', //乘客丢星尘
    UPDATE_ALL_DROP: 'UPDATE_ALL_DROP', //刷新某车厢的所有丢出物
    CARRIAGE_DANCE_START: 'CARRIAGE_DANCE_START', //开始跳舞
    CARRIAGE_DANCE_END: 'CARRIAGE_DANCE_END', //跳舞结束
    CARRIAGE_CAR_LEAVE: 'CARRIAGE_CAR_LEAVE', //车离开
    READY_UNLOCK_FUNCTION: 'READY_UNLOCK_FUNCTION', //准备解锁功能
    UNLOCK_FUNTION: 'UNLOCK_FUNTION', //解锁功能
    DROP_ITEM_ANIM_END: 'DROP_ITEM_END', //丢东西动画结束
    SELECT_BUILD_ENTER: 'SELECT_BUILD_ENTER',
    CREATE_NEW_CARRIAGE: "CREATE_NEW_CARRIAGE", //创建车厢
    CARRIAGE_BUILD_END: "CARRIAGE_BUILD_END", //修建车厢动画结束
    FOCUS_CARRIAGE: 'FOCUS_CARRIAGE',
    BACK_CAMERA_FROM_EDIT: 'BACK_CAMERA_FROM_EDIT', //编辑完成后缩放和视角回归
    HIDE_CARRIAGE: 'HIDE_CARRIAGE',
    SHOW_CARRIAGE: 'SHOW_CARRIAGE',
    FOCUS_CARRIAGE_END: 'FOCUS_CARRIAGE_END',
    SHOW_CARRIAGECOST_TIPS: 'SHOW_CARRIAGECOST_TIPS',
    SHOW_CARRIAGE_DEBUG_MAP: 'SHOW_CARRIAGE_DEBUG_MAP',
    HIDE_CARRIAGE_DEBUG_MAP: 'HIDE_CARRIAGE_DEBUG_MAP',
    TRAIN_CHANGE_WORK: 'TRAIN_CHANGE_WORK',
    TRAIN_CHANGE_WORK_TIPS: 'TRAIN_CHANGE_WORK_TIPS',
    TRAIN_WORK_REFRESH: 'TRAIN_WORK_REFRESH',
    USE_ENERGY: "USE_ENERGY", //使用水电
    COLLECT_ITEM: "COLLECT_ITEM", //拾取道具
    UNLOCK_THEME: "UNLOCK_THEME", //解锁主题
    ADD_CLOUD: "ADD_CLOUD", //生成云
    REMOVE_CLOUD: "REMOV_CLOUD",
    COOK_DOSING_UPDATE: "COOK_DOSING_UPDATE", // 锅子中食材改变
    ENERGY_UNLOCK_AUTO: "ENERGY_UNLOCK_AUTO", // 解锁了自动加速功能
    ENERGY_STOP_AUTO: "ENERGY_STOP_AUTO", // 停止自动加速
    SET_FOCUS: "SET_FOCUS", // 设置当前聚焦信息


    //设施相关
    UNLOCK_BUILD: "UNLOCK_BUILD", //解锁设施
    CHANGE_BUILD: "CHANGE_BUILD", //设施更换
    LEVEL_UP_BUILD: "LEVEL_UP_BUILD", //设施升级
    CARRIAGE_EDIT: 'CARRIAGE_EDIT',
    UNLOCK_ALL_BUILD: "UNLOCK_ALL_BUILD",
    UNLOCK_HEAD_BUILD: "UNLOCK_HEAD_BUILD", //解锁车头设施
    PLAY_REWARD_BUILD: "PLAY_REWARD_BUILD", //演绎(通过奖励)获得设施动画
    SHOW_BUILDUP_TIPS: "SHOW_BUILDUP_TIPS", //显示建造增加资源生产
    SHOW_BUILDCOST_TIPS: "SHOW_BUILDCOST_TIPS", //显示建造消耗
    SHOW_TRAIN_BTNS: "SHOW_TRAIN_BTNS",
    THEME_UNLOCK_OVER: "THEME_UNLOCK_OVER",
    UNLOCK_TRAIN_GOODS: "UNLOCK_TRAIN_GOODS",
    HIDE_BUILDS: "HIDE_BUILDS", //隐藏建筑（修建别的建筑时）
    SHOW_BUILDS: "SHOW_BUILDS",  //展示建筑

    //教程相关
    START_PLOT: 'START_PLOT',
    CHANGE_PLOT: 'CHANGE_PLOT',
    CHANGE_C_PLOT: 'CHANGE_C_PLOT',
    END_PLOT: 'END_PLOT',
    END_PLOT_UI: 'END_PLOT_UI',
    END_C_PLOT: 'END_C_PLOT',
    START_DIALOG: 'START_DIALOG',
    END_DIALOG: 'END_DIALOG',
    RECOVER_C_PLOT: 'RECOVER_C_PLOT',
    TRY_NEXT_C_PLOT: 'TRY_NEXT_C_PLOT',
    BACK_C_OPTIONS: 'BACK_C_OPTIONS',
    FADEOUT_C_BLACK: 'FADEOUT_C_BLACK',
    GUIDE_START: 'GUIDE_START',
    GUIDE_CHANGE_PROGRESS: 'GUIDE_CHANGE_PROGRESS',
    GUIDE_STEP_END: 'GUIDE_STEP_END',
    GUIDE_END: 'GUIDE_END',
    GUIDE_CLEAN: 'GUIDE_CLEAN',
    GUIDE_SHOW_CLICK: 'GUIDE_SHOW_CLICK',
    GUIDE_SHOW_DRAG: 'GUIDE_SHOW_DRAG',
    GUIDE_SHOW_GUIDER: 'GUIDE_SHOW_GUIDER',
    GUIDE_GUIDER_CLICK: 'GUIDE_GUIDER_CLICK',
    GUIDE_SHOW_COMBO_CLICK: 'GUIDE_SHOW_COMBO_CLICK',
    GUIDE_UNLOCK_FUNTION: 'GUIDE_UNLOCK_FUNTION',
    GUIDE_FREE_TOUCH: 'GUIDE_FREE_TOUCH',
    GUIDE_RESET: 'GUIDE_RESET',
    GUIDE_HIDE_FINGER: 'GUIDE_HIDE_FINGER',
    GUIDE_SAVE_CAT_START: 'GUIDE_SAVE_CAT_START',
    GUIDE_SAVE_CAT_END: 'GUIDE_SAVE_CAT_END',
    GUIDE_UNLOCK_SAVE_CAT: 'GUIDE_UNLOCK_SAVE_CAT',
    GUIDE_JACKPOT_END1: 'GUIDE_JACKPOT_END1',
    GUIDE_JACKPOT_END2: 'GUIDE_JACKPOT_END2',
    GUIDE_JACKPOT_END3: 'GUIDE_JACKPOT_END3',
    GUIDE_JACKPOT_END4: 'GUIDE_JACKPOT_END4',
    GUIDE_DROP_STAR_2: 'GUIDE_DROP_STAR_2',
    GUIDE_REPAIR_TRAIN: 'GUIDE_REPAIR_TRAIN',
    GUIDE_REPAIR_TRAIN_END: 'GUIDE_REPAIR_TRAIN_END',
    GUIDE_REPAIR_TRAIN_START: 'GUIDE_REPAIR_TRAIN_START',
    GUIDE_ENTER_TRAIN: 'GUIDE_ENTER_TRAIN',
    GUIDE_IGNORE_SELECT_ROLENODE_2: 'GUIDE_IGNORE_SELECT_ROLENOD_2',
    GUIDE_SHOW_SELECT_ROLENODE_2: 'GUIDE_SHOW_SELECT_ROLENODE_2',
    GUIDE_SHOW_SELECT_END: 'GUIDE_SHOW_SELECT_END',
    GUIDE_ENTER_TRAIN_END: 'GUIDE_ENTER_TRAIN_END',
    GUIDE_GOTO_PLANET: 'GUIDE_GOTO_PLANET',
    GUIDE_GOTO_PLANET_END: 'GUIDE_GOTO_PLANET_END',
    GUIDE_CLOCK_DROP: 'GUIDE_CLOCK_DROP',
    GUIDE_REACH_TREE_HOUSE: 'GUIDE_REACH_TREE_HOUSE',
    GUIDE_LIBRARY_GUESS_START_FINISH: 'GUIDE_LIBRARY_GUESS_START_FINISH',
    GUIDE_UNLOCK_GARDEN_END: 'GUIDE_UNLOCK_GARDEN_END',
    GUIDE_ON_COLLIDE: 'GUIDE_ON_COLLIDE',
    GUIDE_SHOW_BATTLE_GUIDE: 'GUIDE_SHOW_BATTLE_GUIDE',
    GUIDE_HIDE_BATTLE_GUIDE: 'GUIDE_HIDE_BATTLE_GUIDE',
    GUIDE_SHOW_BATTLE_TALK: 'GUIDE_SHOW_BATTLE_TALK',
    GUIDE_BUILD_HEAD: 'GUIDE_BUILD_HEAD',
    GUIDE_BUILD_HEAD_END: 'GUIDE_BUILD_HEAD_END',
    GUIDE_BATTLE_COLLIDE: 'GUIDE_BATTLE_COLLIDE',
    GUIDE_BATTLE_DEATH: 'GUIDE_BATTLE_DEATH',
    GUIDE_BATTLE_SHOW_SKILL_TIPS: "GUIDE_BATTLE_SHOW_SKILL_TIPS",
    GUIDE_CAN_CLICK_TICKET: 'GUIDE_CAN_CLICK_TICKET',
    GUIDE_TASK_END: 'GUIDE_TASK_END',
    GUIDE_SHOW_TASK: 'GUIDE_SHOW_TASK',
    GUIDE_INTO_TEAM: 'GUIDE_INTO_TEAM',
    GUIDE_OPEN_ANIM_END: 'GUIDE_OPEN_ANIM_END',
    GUIDE_RELATER_BLACK_IN: 'GUIDE_RELATER_BLACK_IN',
    GUIDE_FIRST_ENTER_PLANET_1006: 'GUIDE_FIRST_ENTER_PLANET_1006',
    GUIDE_FIRST_ENTER_PLANET_1006_END: 'GUIDE_FIRST_ENTER_PLANET_1006_END',
    GUIDE_FIRST_ENTER_PLANET_1009_COME: 'GUIDE_FIRST_ENTER_PLANET_1009_COME',
    GUIDE_FIRST_ENTER_PLANET_1009_COME_END: 'GUIDE_FIRST_ENTER_PLANET_1009_COME_END',
    GUIDE_FIRST_ENTER_PLANET_1009_LEAVE: 'GUIDE_FIRST_ENTER_PLANET_1009_LEAVE',
    GUIDE_FIRST_ENTER_PLANET_1009_LEAVE_END: 'GUIDE_FIRST_ENTER_PLANET_1009_LEAVE_END',
    GUIDE_NEXT_QUALITY_EQUIP: 'GUIDE_NEXT_QUALITY_EQUIP',
    GUIDE_START_UNLOCK_BLACK_HOLE: 'GUIDE_START_UNLOCK_BLACK_HOLE',
    GUIDE_END_UNLOCK_BLACK_HOLE: 'GUIDE_END_UNLOCK_BLACK_HOLE',
    GUIDE_UNLOCK_TOWER: 'GUIDE_UNLOCK_TOWER',
    GUIDE_UNLOCK_TRANSPORT: 'GUIDE_UNLOCK_TRANSPORT',
    GUDIE_MOVE_TO_ORE: 'GUDIE_MOVE_TO_ORE',
    GUDIE_MOVE_TO_ORE_END: 'GUDIE_MOVE_TO_ORE_END',
    GUIDE_SHOW_TOUCH_MOVE: 'GUIDE_SHOW_TOUCH_MOVE',
    GUIDE_TOUCH_MOVE_END: 'GUIDE_TOUCH_MOVE_END',
    GUIDE_UNLOCK_SPACE_STONE: 'GUIDE_UNLOCK_SPACE_STONE',

    //弱引导
    SHOW_WEAK_GUIDE: "SHOW_WEAK_GUIDE",

    //抽卡相关
    REFRESH_DALIY_COUNT: "REFRESH_DALIY_COUNT", //刷新每日抽卡次数
    JACKPOT_SUCCESS: 'JACKPOT_SUCCESS', //抽卡成功
    JACKPOT_SHOWPRIZE: 'JACKPOT_SHOWPRIZE', //展示奖励

    // 设置相关
    SET_MUSIC: 'SET_MUSIC',//设置背景音乐
    SET_SOUND_EFFECT: 'SET_SOUND_EFFECT',//设置背景音乐


    // 登录注册相关
    CERTIFICATION_COMPLETE: 'CERTIFICATION_COMPLETE', //完成实名认证
    LOGIN_COMPLETE: 'LOGIN_COMPLETE', //登录完成

    //主界面UI
    GET_UI_FUNCTION_NODE: 'GET_UI_FUNCTION_NODE', //获取UI节点
    SHOW_UI: 'SHOW_UI', //打开UI
    HIDE_UI: 'HIDE_UI', //关闭UI
    HIDE_MENU: 'HIDE_MENU', //关闭UI之menu
    DO_WHEN_UI_SHOW: 'DO_WHEN_UI_SHOW',

    BUY_BUILD: 'BUY_BUILD',// 购买设施

    //乘客相关
    PASSENGER_ACTION_CHANGE: 'PASSENGER_ACTION_CHANGE', //乘客行为改变
    PASSENGER_STATE_CHANGE: 'PASSENGER_STATE_CHANGE', //乘客状态改变
    PASSENGER_FLIP: 'PASSENGER_FLIP',// 乘客朝向
    USE_BUILD_END: 'USE_BUILD_END',// 使用车厢设施结束
    USE_BUILD_ENTER: 'USE_BUILD_ENTER',// 开始使用车厢设施
    UPDATE_PASSENGER_POS: 'UPDATE_PASSENGER_POS',
    PASSENGER_ENTER_CONNECT: 'PASSENGER_ENTER_CONNECT', //乘客进入车厢连接处
    PASSENGER_EXIT_CONNECT: 'PASSENGER_EXIT_CONNECT', //乘客离开车厢连接处
    PASSENGER_ENTER_CARRIAGE: 'PASSENGER_ENTER_CARRIAGE', //乘客进入车厢
    PASSENGER_EXIT_CARRIAGE: 'PASSENGER_EXIT_CARRIAGE', //乘客离开车厢
    PASSENGER_ENTER_CARRIAGE_TOP: 'PASSENGER_ENTER_CARRIAGE_TOP', //乘客进入车厢顶
    PASSENGER_EXIT_CARRIAGE_TOP: 'PASSENGER_EXIT_CARRIAGE_TOP', //乘客离开车厢顶
    ENTER_DROM: 'ENTER_DROM',//进入寝室
    BACK_CORRIDOR: 'BACK_CORRIDOR',// 返回走廊
    UPDATE_INTIMACY: "UPDATE_INTIMACY",// 更新亲密度
    UNLOCK_PASSENGER: 'UNLOCK_PASSENGER',//获得新角色
    PASSENGER_LEVEL_UP: 'PASSENGER_LEVEL_UP',//角色升级
    PASSENGER_STARLV_UP: 'PASSENGER_STARLV_UP',//角色突破
    PASSENGER_SKILL_NO_NEW: 'PASSENGER_SKILL_NO_NEW',
    PASSENGER_HEART_CHANGE: 'PASSENGER_HEART_CHANGE', //乘客爱心更新
    REPLACE_CARRIAGE: 'REPLACE_CARRIAGE', //更换车厢
    PASSENGER_UPDATE: 'PASSENGER_UPDATE', //入住列车
    PASSENGER_CHECK_IN: 'PASSENGER_CHECK_IN', //入住列车
    PASSENGER_CHECK_IN_TIPS: 'PASSENGER_CHECK_IN_TIPS', //入住列车飘条
    PASSENGER_CHECK_OUT: 'PASSENGER_CHECK_OUT', //离开列车
    ROLE_SHOW_SPEECH_BUBBLE: 'ROLE_SHOW_SPEECH_BUBBLE',
    ROLE_HIDE_SPEECH_BUBBLE: 'ROLE_HIDE_SPEECH_BUBBLE',
    PASSENGER_EXPLORE_BOX_CHANGE: 'PASSENGER_EXPLORE_BOX_CHANGE',
    PASSENGER_PLOT_CHANGE: 'PASSENGER_PLOT_CHANGE',
    PASSENGER_LEVELUP_EQUIP: 'PASSENGER_LEVELUP_EQUIP',//升级装备
    PASSENGER_SKIN_CHANGE: 'PASSENGER_SKIN_CHANGE', // 切换皮肤
    PASSENGER_RESET: 'PASSENGER_RESET', //乘客重生
    PASSENGER_TALENT_LV_CHANGE: 'PASSENGER_TALENT_LV_CHANGE', //乘客天赋等级改变
    PASSENGER_EAT_FINASH: 'PASSENGER_EAT_FINASH',//乘客吃完饭了
    PASSENGER_DIALOG_START: 'PASSENGER_DIALOG_START',
    PASSENGER_DIALOG_END: 'PASSENGER_DIALOG_END',
    PASSENGER_TRANS: "PASSENGER_TRANS",
    PASSENGER_CHANGE_WORK: "PASSENGER_CHANGE_WORK",
    DEBUG: "DEBUG",
    PASSENGER_PROFILE_UNLOCK: 'PASSENGER_PROFILE_UNLOCK', //乘客资料解锁

    //乘客界面相关
    ROLE_PNL_CHANGE_TAB: 'ROLE_PNL_CHANGE_TAB',
    ROLE_PNL_ON_SELECT: "ROLE_PNL_ON_SELECT",
    SHOW_ROLE_TRANSFER: "SHOW_ROLE_TRANSFER",
    SHOW_ROLE_EQUIP: "SHOW_ROLE_EQUIP",
    HIDE_ROLE_FRAGMENT_GOTO: "HIDE_ROLE_FRAGMENT_GOTO",

    //任务相关
    TASK_UNLOCK: 'TASK_UNLOCK', //任务解锁时
    TASK_UPDATE: 'TASK_UPDATE', //任务发生变化时
    TASK_COMPLETE: 'TASK_COMPLETE', //任务完成时
    TASK_DETAIL_ENTER: 'TASK_DETAIL_ENTER', //进入某任务详情界面时
    GET_TASK_DETAIL_ID: 'GET_TASK_DETAIL_ID',
    HIDE_TASK_DETAIL: 'HIDE_TASK_DETAIL',
    TRAIN_BUILD_OVER: 'TRAIN_BUILD_OVER', //车厢开门动画播完
    BUILD_OVER_TRAIN_ITEM: 'BUILD_OVER_TRAIN_ITEM', //设施建造完成动画播完
    ACHIEVE_COMPLETE: 'ACHIEVE_COMPLETE',
    ACHIEVE_UPDATE: 'ACHIEVE_UPDATE',

    //自由采集
    COLLECT_TASK_TARGET_ERROR: 'COLLECT_TASK_TARGET_ERROR',

    //每日任务
    DAILY_TASK_FINISH: 'DAILY_TASK_FINISH',
    TRAIN_DAILY_TASK_START: 'TRAIN_DAILY_TASK_START',
    TRAIN_DAILY_TASK_DONE: 'TRAIN_DAILY_TASK_DONE',
    TRAIN_DAILY_TASK_END: 'TRAIN_DAILY_TASK_END',
    TRAIN_DAILY_TASK_FINISH: 'TRAIN_DAILY_TASK_FINISH',
    DAILY_TASK_DIALOG_SUBMIT: 'DAILY_TASK_DIALOG_SUBMIT',
    DAILY_TASK_BATTLE_SUBMIT: 'DAILY_TASK_BATTLE_SUBMIT',
    BURST_TASK_START: 'BURST_TASK_START',
    BURST_TASK_FINISH: 'BURST_TASK_FINISH',

    //工具相关
    // MAKE_TOOL_SUCCESS: 'MAKE_TOOL_SUCCESS', //工具打造成功
    TOOL_TABLE_UP: 'TOOL_TABLE_UP', //升级打造台
    TOOL_CHANGE_OK: 'TOOL_CHANGE_OK', //更换工具成功
    TOOL_CHANGE: 'TOOL_CHANGE', //更换工具
    TOOL_MAKE: 'TOOL_MAKE', //制作工具
    TOOL_SELECT: 'TOOL_SELECT', //选择工具

    //战斗相关
    BATTLE_ON_COLLIDE: 'BATTLE_ON_COLLIDE', //对撞时
    BATTLE_COLLIDE_END: 'BATTLE_COLLIDE_END', //对撞结束
    BATTLE_ON_END: 'BATTLE_ON_END', //战斗结束
    BATTLE_PAUSE_NEXT: 'BATTLE_PAUSE_NEXT', //暂停下一次行动
    BATTLE_RESUME_NEXT: 'BATTLE_RESUME_NEXT', //恢复下一次行动
    BATTLE_EQUIP_CHANGE: 'BATTLE_EQUIP_CHANGE',//装备更换
    BATTLE_SKIP: 'BATTLE_SKIP', //跳过战斗

    EVENT_GAME_SHOW: 'EVENT_GAME_SHOW', //游戏进入前台
    EVENT_GAME_HIDE: 'EVENT_GAME_HIDE', //游戏进入后台

    //委托界面
    ENTRUST_REFRESH_VIEW: 'ENTRUST_REFRESH_VIEW', //刷新界面
    ENTRUST_COMPLETE: 'ENTRUST_COMPLETE',
    ENTRUST_UNLOCK: 'ENTRUST_UNLOCK', //坑位解锁
    ENTRUST_START: 'ENTRUST_START',

    //悬赏界面
    WANTED_UPDATE: "WANTED_UPDATE", //悬赏更新

    //邮件
    MAIL_REFRESH_DETAIL: 'MAIL_REFRESH_DETAIL', //刷新邮件细节
    MAIL_REFRESH_VIEW: 'MAIL_REFRESH_VIEW', //刷新邮件视图

    //个人信息
    RENAME_SUCCESS: 'RENAME_SUCCESS',

    //背包
    EXCHANGE_TARGET_CHANGE: 'EXCHANGE_TARGET_CHANGE', //材料置换目标材料改变

    //黑洞
    BLACK_HOLE_MOVE: "BLACK_HOLE_MOVE",

    //爬塔
    TOWER_BATTLE_WIN: "TOWER_BATTLE_WIN",
    TOWER_CHANGE_ENERGY: "TOWER_CHANGE_ENERGY",

    //副本
    INSTANCE_BATTLE_WIN: "INSTANCE_BATTLE_WIN",
    INSTANCE_SWEEP_START: "INSTANCE_SWEEP_START",
    INSTANCE_SWEEP_OVER: "INSTANCE_SWEEP_OVER",
    INSTANCE_SWEEP_ERROR: "INSTANCE_SWEEP_ERROR",
    INSTANCE_DAILY_REWARD_GET: "INSTANCE_DAILY_REWARD_GET",
    INSTANCE_COMPLETE_PUZZLE: "INSTANCE_COMPLETE_PUZZLE",

    //运输
    TRANSPORT_STATE_CHANGE: 'TRANSPORT_STATE_CHANGE',
    TRANSPORT_MEET_MONSTER: 'TRANSPORT_MEET_MONSTER',
    TRANSPORT_DEFEAT_MONSTER: 'TRANSPORT_DEFEAT_MONSTER', // 运输击败海盗
    TRANSPORT_UN_DEFEAT_MONSTER: 'TRANSPORT_UN_DEFEAT_MONSTER', // 运输未击败海盗
    TRANSPORT_MONSTER_ICON_SHOW: 'TRANSPORT_MONSTER_ICON_SHOW', // 屏幕右侧海盗icon
    TRANSPORT_MONSTER_ICON_HIDE: 'TRANSPORT_MONSTER_ICON_HIDE', // 屏幕右侧海盗icon
    TRANSPORT_FOCUS_MONSTER: 'TRANSPORT_FOCUS_MONSTER', // 屏幕右侧海盗icon
    TRANSPORT_TAKE: 'TRANSPORT_TAKE',
    TRANSPORT_FINISH: 'TRANSPORT_FINISH',
    TRANSPORT_FAILED: 'TRANSPORT_FAILED',

    //黑市
    STORE_REFRESH: "STORE_REFRESH",

    //共鸣
    SET_RESONANCE: "SET_RESONANCE",

    //种植
    FIELD_TREE_MATURE: "FIELD_TREE_MATURE",
    FIELD_LEVEL_UP: "FIELD_LEVEL_UP",
    FIELD_HARVEST: "FIELD_HARVEST",

    //挖矿
    ORE_SHOW_EFFECT: "ORE_SHOW_EFFECT",
    ORE_LEVEL_CHANGE: "ORE_LEVEL_CHANGE",
    ORE_REFRESH_VIEW: "ORE_REFRESH_VIEW",
    ORE_MOVE: "ORE_MOVE",
    ORE_EQUIP_MAKE: "ORE_EQUIP_MAKE",

    //通缉
    ARREST_GET: "ARREST_GET",
    ARREST_REFRESH: "ARREST_REFRESH",

    //空间宝石
    SPACESTONE_MARK_CHANGE: "SPACESTONE_MARK_CHANGE",
    SPACESTONE_LV_UP: "SPACESTONE_LV_UP",

    //竞技场
    PVP_NORMAL_BATTLE_RESULT: "PVP_NORMAL_BATTLE_RESULT",
    PVP_NORMAL_MY_SCORE_CHANGED: "PVP_NORMAL_MY_SCORE_CHANGED",
    PVP_NORMAL_MY_RANK_CHANGED: "PVP_NORMAL_MY_RANK_CHANGED",
    PVP_NORMAL_UPDATE_RANK_LIST: "PVP_NORMAL_UPDATE_RANK_LIST",
    PVP_TICKET_CHANGED: "PVP_TICKET_CHANGED",

    //时间石相关
    GET_TIME_STONE_KEY: 'GET_TIME_STONE_KEY',
    GET_TIME_STONE_KEY_FRAG: 'GET_TIME_STONE_KEY_FRAG',

    //装备
    SELL_EQUIP: 'SELL_EQUIP', //出售装备

    // 列车科技
    TRAIN_TECH_UPGRADE: "TRAIN_TECH_UPGRADE", // 科技升级
    // 宣传局
    PUBLICITY_ROLE_NUM_CHANGE: "PUBLICITY_ROLE_NUM_CHANGE", // 宣传局人口数改变
    PUBLICITY_DURATION_TIME_CHANGE: "PUBLICITY_DURATION_TIME_CHANGE", // 宣传局生产时长改变
    PUBLICITY_UPGRADE: "PUBLICITY_UPGRADE", // 宣传局突破

}
