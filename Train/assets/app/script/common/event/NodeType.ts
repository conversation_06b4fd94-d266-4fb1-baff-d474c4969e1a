/**
 * 全局事件（全大写单词间用下划线隔开）
 */
export default {
       //教程节点
       GUIDE_BUTTON_BUILD: 'GUIDE_BUTTON_BUILD',
       GUIDE_BUTTON_BUILD_ENGINE: 'GUIDE_BUTTON_BUILD_ENGINE',
       GUIDE_BUTTON_FIX: 'GUIDE_BUTTON_FIX',
       GUIDE_STAR_TOP_LEFT: 'GUIDE_STAR_TOP_LEFT',
       GUIDE_STAR_DOWN_LEFT: 'GUIDE_STAR_DOWN_LEFT',
       GUIDE_BUTTON_EXPLORE: 'GUIDE_BUTTON_EXPLORE',
       GUIDE_BUTTON_EXPLORE_AUTO: 'GUIDE_BUTTON_EXPLORE_AUTO',
       GUIDE_PLANET_INDEX_1: 'GUIDE_PLANET_INDEX_1',
       GUIDE_BUTTON_LANDED: 'GUIDE_BUTTON_LANDED',
       G<PERSON><PERSON>_CUR_PLANET_NODE: 'GUIDE_CUR_PLANET_NODE',
       G<PERSON><PERSON>_TASK_INDEX_1: 'GUIDE_TASK_INDEX_1',
       GUIDE_MINE_INDEX_1: 'GUIDE_MINE_INDEX_1',
       GUIDE_BUTTON_BACK_1: 'GUIDE_BUTTON_BACK_1',
       GUIDE_BUTTON_BACK_2: 'GUIDE_BUTTON_BACK_2',
       GUIDE_BUTTON_BACK_3: 'GUIDE_BUTTON_BACK_3',
       GUIDE_BUTTON_BACK_4: 'GUIDE_BUTTON_BACK_4',
       GUIDE_BUTTON_BACK_6: 'GUIDE_BUTTON_BACK_6',
       GUIDE_BUTTON_BACK_7: 'GUIDE_BUTTON_BACK_7',
       GUIDE_BUTTON_BACK_8: 'GUIDE_BUTTON_BACK_8',
       GUIDE_STAR_TOP_MID: 'GUIDE_STAR_TOP_MID',
       GUIDE_SEPECIAL_CAT: 'GUIDE_SEPECIAL_CAT',
       GUIDE_BUTTON_LEVELUP: 'GUIDE_BUTTON_LEVELUP',
       GUIDE_BUTTON_RECEIVE: 'GUIDE_BUTTON_RECEIVE',
       GUIDE_BUTTON_CHARACTER: 'GUIDE_BUTTON_CHARACTER',
       GUIDE_BATTLE_GRATUITY: 'GUIDE_BATTLE_GRATUITY',
       GUIDE_BUTTON_GET_ON: 'GUIDE_BUTTON_GET_ON',
       GUIDE_BUTTON_GET_ON_2: 'GUIDE_BUTTON_GET_ON_2',
       GUIDE_BUTTON_CHECK_IN: 'GUIDE_BUTTON_CHECK_IN',
       GUIDE_STAR_BUTTOM: 'GUIDE_STAR_BUTTOM',
       GUIDE_BUTTON_LOTTERY: 'GUIDE_BUTTON_LOTTERY',
       GUIDE_BUTTON_LOTTERY_ONCE: 'GUIDE_BUTTON_LOTTERY_ONCE',
       GUIDE_BLANK_1: 'GUIDE_BLANK_1',
       GUIDE_CHARACTER_1005: 'GUIDE_CHARACTER_1005',
       GUIDE_CHARACTER_1006: 'GUIDE_CHARACTER_1006',
       GUIDE_CHARACTER_1007: 'GUIDE_CHARACTER_1007',
       GUIDE_BATTLE_ATTRIBUTE: 'GUIDE_BATTLE_ATTRIBUTE',
       GUIDE_BATTLE_LEVEL: 'GUIDE_BATTLE_LEVEL',
       GUIDE_BUTTON_CHALLENGE: 'GUIDE_BUTTON_CHALLENGE',
       GUIDE_CHARACTER_BATTLE_1005: 'GUIDE_CHARACTER_BATTLE_1005',
       GUIDE_CHARACTER_BATTLE_1006: 'GUIDE_CHARACTER_BATTLE_1006',
       GUIDE_CHARACTER_BATTLE_1007: 'GUIDE_CHARACTER_BATTLE_1007',
       GUIDE_CHARACTER_TRAINITEM_RIGHT: 'GUIDE_CHARACTER_TRAINITEM_RIGHT',
       GUIDE_TRAIN_ITEM_INDEX_1_UNLOCK: 'GUIDE_TRAIN_ITEM_INDEX_1_UNLOCK',
       GUIDE_TRAIN_ITEM_INDEX_1_LEVELUP: 'GUIDE_TRAIN_ITEM_INDEX_1_LEVELUP',
       GUIDE_BUTTON_INTO: 'GUIDE_BUTTON_INTO',
       GUIDE_BUTTON_START_BATTLE: 'GUIDE_BUTTON_START_BATTLE',
       GUIDE_BUTTON_START_ROUND: 'GUIDE_BUTTON_START_ROUND',
       GUIDE_BUTTON_GIFT: 'GUIDE_BUTTON_GIFT',
       GUIDE_TRAIN_INDEX_2: 'GUIDE_TRAIN_INDEX_2',
       GUIDE_TRAIN_CONSOLE: 'GUIDE_TRAIN_CONSOLE',
       GUIDE_PLANET_INDEX_2: 'GUIDE_PLANET_INDEX_2',
       GUIDE_BUTTON_GOTO: 'GUIDE_BUTTON_GOTO',
       GUIDE_BUTTON_SPEED_UP: 'GUIDE_BUTTON_SPEED_UP',
       GUIDE_BUTTON_TRAIN: 'GUIDE_BUTTON_TRAIN',
       GUIDE_BUTTON_TOOL: 'GUIDE_BUTTON_TOOL',
       GUIDE_BUTTON_ENTRUST: 'GUIDE_BUTTON_ENTRUST',
       GUIDE_BUTTON_ACHIEVEMENT: 'GUIDE_BUTTON_ACHIEVEMENT',
       GUIDE_BUTTON_PLAYENTER: 'GUIDE_BUTTON_PLAYENTER',
       GUIDE_BUTTON_BLACKHOLE: 'GUIDE_BUTTON_BLACKHOLE',
       GUIDE_BUTTON_TOWER: 'GUIDE_BUTTON_TOWER',
       GUIDE_BUTTON_TRADE: 'GUIDE_BUTTON_TRADE',
       GUIDE_BUTTON_INSTANCE: 'GUIDE_BUTTON_INSTANCE',
       GUIDE_BUTTON_ARENA: 'GUIDE_BUTTON_ARENA',
       GUIDE_BUTTON_ADD: 'GUIDE_BUTTON_ADD',
       GUIDE_TRAIN_INDEX_1: 'GUIDE_TRAIN_INDEX_1',
       GUIDE_BUTTON_BUY: 'GUIDE_BUTTON_BUY',
       GUIDE_BUTTON_INTERACT: 'GUIDE_BUTTON_INTERACT',
       GUIDE_TRAIN_HEAD: 'GUIDE_TRAIN_HEAD',
       GUIDE_CLOCKWORK: 'GUIDE_CLOCKWORK',
       GUIDE_BUTTON_ITEM: 'GUIDE_BUTTON_ITEM',
       GUIDE_PLANET_1_STAIRS: 'GUIDE_PLANET_1_STAIRS',
       GUIDE_BATTLE_GUIDER: 'GUIDE_BATTLE_GUIDER',
       GUIDE_TOOL_TAG_2: 'GUIDE_TOOL_TAG_2',
       GUIDE_TOOL_TAG_3: 'GUIDE_TOOL_TAG_3',
       GUIDE_TOOL_INDEX_2: 'GUIDE_TOOL_INDEX_2',
       GUIDE_SEPECIAL_TICKET: 'GUIDE_SEPECIAL_TICKET',
       GUIDE_SEPECIAL_BALLOON: 'GUIDE_SEPECIAL_BALLOON',
       GUIDE_SEPECIAL_ENGINE: 'GUIDE_SEPECIAL_ENGINE',
       GUIDE_SEPECIAL_CATDOOR_ENGINE: 'GUIDE_SEPECIAL_CATDOOR_ENGINE',
       GUIDE_BUTTON_BUILDTOOL_1: 'GUIDE_BUTTON_BUILDTOOL_1',
       GUIDE_BUTTON_TOOL_BUILD: 'GUIDE_BUTTON_TOOL_BUILD',
       GUIDE_BUTTON_TOOL_EQUIP: 'GUIDE_BUTTON_TOOL_EQUIP',
       GUIDE_BUTTON_TOOLLIST_3: 'GUIDE_BUTTON_TOOLLIST_3',
       GUIDE_UI_CURRENCY_STAR: 'GUIDE_UI_CURRENCY_STAR',
       GUIDE_TIME_STONE: 'GUIDE_TIME_STONE',
       GUIDE_RESONANCE_INTO: 'GUIDE_RESONANCE_INTO',
       GUIDE_BUTTON_RESONANCE: 'GUIDE_BUTTON_RESONANCE',
       GUIDE_GALAXY_NEXT: 'GUIDE_GALAXY_NEXT',
       GUIDE_BUTTON_CHECKIN: 'GUIDE_BUTTON_CHECKIN',
       GUIDE_CHECKIN_INDEX_2: 'GUIDE_CHECKIN_INDEX_2',
       GUIDE_CHECKIN_DORM_1_1006: 'GUIDE_CHECKIN_DORM_1_1006',
       GUIDE_BUTTON_CHECKIN_DORM_1: 'GUIDE_BUTTON_CHECKIN_DORM_1',
       GUIDE_BATTLE_EQUIP: 'GUIDE_BATTLE_EQUIP',
       GUIDE_STAR_MAP: 'GUIDE_STAR_MAP',
       GUIDE_TRANS_LIST_1: 'GUIDE_TRANS_LIST_1',
       GUIDE_TRANS: 'GUIDE_TRANS',
       GUIDE_NOTE_BOOK: 'GUIDE_NOTE_BOOK',
       GUIDE_CAN_RESONANCE_ROLE: 'GUIDE_CAN_RESONANCE_ROLE',
       GUIDE_RESONANCE_YES: 'GUIDE_RESONANCE_YES',
       GUIDE_PLANET_AREA: 'GUIDE_PLANET_AREA',
       GUIDE_PLANET_AREA_1: 'GUIDE_PLANET_AREA_1',
       GUIDE_BLACK_HOLE_KEY: 'GUIDE_BLACK_HOLE_KEY',
       GUIDE_WORK_1: "GUIDE_WORK_1",
       GUIDE_BUTTON_WORK_ENGINE_1: "GUIDE_BUTTON_WORK_ENGINE_1",
       GUIDE_BUTTON_DAILY_TASK: "GUIDE_BUTTON_DAILY_TASK",
       GUIDE_BUTTON_MAKE_EQUIP: "GUIDE_BUTTON_MAKE_EQUIP",
       GUIDE_BUTTON_WEAR_EQUIP: "GUIDE_BUTTON_WEAR_EQUIP",
       GUIDE_BUTTON_NEXT_QUALITY_EQUIP: "GUIDE_BUTTON_NEXT_QUALITY_EQUIP",
       GUIDE_TRANSPORT_WEIGHT: "GUIDE_TRANSPORT_WEIGHT",
       GUIDE_BUTTON_SPACE_STONE_MARK: "GUIDE_BUTTON_SPACE_STONE_MARK",
       GUIDE_INSTANCE_LEVEL: "GUIDE_INSTANCE_LEVEL",
       GUIDE_SAVE_CAT: "GUIDE_SAVE_CAT",
       GUIDE_DAILY_TASK_ROLE: "GUIDE_DAILY_TASK_ROLE",
       GUIDE_BLACK_HOLE_ENTRY: "GUIDE_BLACK_HOLE_ENTRY",

       PLANET_UI_CURRENCY_ICON: 'PLANET_UI_CURRENCY_ICON', //获取星球货币icon节点
       PLANET_ITEM_ICON: 'PLANET_ITEM_ICON', //星球道具节点
       PLANET_UI_TOPLAYER: 'PLANET_UI_TOPLAYER', //星球ui顶层节点
       UI_BAG: "UI_BAG", //背包节点
       SELECTBUILD_CURRENCY: "SELECTBUILD_CURRENCY", //车厢设施建造界面货币节点

       PLANET_UI_BLESS_ICON: 'PLANET_UI_BLESS_ICON',

}