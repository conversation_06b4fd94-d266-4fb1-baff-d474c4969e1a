import {
    AchievementCfg,
    AchievementTypeCfg,
    ArrestCfg,
    BattleSkillViewCfg,
    BuildCfg,
    BuildLevelCfg,
    CarriageThemeCfg,
    CarriageUsePosInfo,
    CharacterCfg,
    CharacterFragCfg,
    CharacterLevelCostCfg,
    CharacterPlotControl,
    CharacterPlotStep,
    CharacterProfileAttrCfg,
    CharacterProfileCfg,
    CharacterSkinCfg,
    CharacterStarLvCfg,
    ChestCfg,
    EquipCfg,
    EquipEffectCfg,
    EquipLevelCfg,
    EquipMakeCfg,
    EquipStoreCfg,
    FieldLevelCfg,
    FieldSeedCfg,
    GuideStep,
    InstanceLevelCfg,
    ItemCfg,
    JackpotCfg,
    OreItemCfg,
    OreLevel,
    PlanetMonsterCfg,
    PlanetProfileCfg,
    PlanetProfileReward,
    PlotStep,
    PublicityPlayCfg,
    QuestionCfg,
    SpineAnimCfg,
    ToolCfg,
    ToolLevelCfg,
    ToolTabelCfg,
    TowerMonsterCfg,
    TrainGoodsCfg,
    TrainGoodsLevelCfg,
    TrainGoodsLevelEffect,
    TrainWorkCfg,
    TransportLevelCfg,
    TransportLevelUpCfg,
    UnlockFuncCfg
} from "../constant/DataType";
import {
    ConditionType,
    EquipEffectTarget,
    EquipEffectType,
    IntType,
    ItemType,
    LangCfgName,
    LifeSkillEffectType,
    PassengerAttr,
    PassengerQuality,
    PlanetProfileType,
    RelaterType,
    ROLE_ATTR_ID,
    RoleAnimalType,
    RoleBattleType,
    SkillType,
    UIFunctionType,
    ValueType
} from '../constant/Enums';

class CfgHelper {

    private characterMap: { [key: string]: CharacterCfg } = {}
    private plotMap: { [key: string]: PlotStep[][] } = {}
    private CharacterPlotMap: { [key: string]: CharacterPlotStep[][] } = {}
    private guideMap: { [key: string]: GuideStep[] } = {}
    private toolTabelMap: { [id: number]: ToolTabelCfg } = {}
    private trainGoodsMap: { [trainId: number]: TrainGoodsCfg[] } = {}
    private achievementMap: { [typeId: number]: AchievementCfg[] } = {}
    private plotControlMap: { [characterId: number]: CharacterPlotControl[] } = {}
    private instanceLevelMap: { [instanceId: number]: InstanceLevelCfg } = {}
    private planetMoveTimeMap: { [fromId: number]: { toId: number, time: number }[] } = {}
    private trainGoodsLevelMap: { [id: string]: TrainGoodsLevelCfg } = {}
    private arrestMap: { [id: number]: ArrestCfg } = {}
    private characterProfileMap: { [id: number]: CharacterProfileCfg } = {}
    private characterProfileAttrMap: { [id: string]: CharacterProfileAttrCfg } = {}
    private planetProfileMap: { [id: number]: PlanetProfileCfg } = {}
    private planetProfileRewardMap: { [id: string]: PlanetProfileReward } = {}
    private equipSellMap: number[] = []
    private questionMap: { [id: number]: QuestionCfg[] } = {}

    private characterBaseAttrMap: any = {}

    public init() {
        let characterDatas = assetsMgr.getJson<CharacterCfg>("Character").datas
        let monsterDatas = assetsMgr.getJson<PlanetMonsterCfg>("PlanetMonster").datas
        let roleDatas = characterDatas.concat(monsterDatas as any)
        for (let data of roleDatas) {
            this.characterMap[data.id] = data;
        }

        let jackDatas = assetsMgr.getJson<JackpotCfg>("Jackpot").datas
        let jackWeightSum = jackDatas.reduce((a, b) => a + b.weight, 0)
        for (let jackData of jackDatas) {
            let datas = characterDatas.filter(c => c.quality == jackData.id)
            let qSum = datas.reduce((a, b) => a + b.weight, 0)
            let p1 = jackData.weight / jackWeightSum
            for (let data of datas) {
                let p2 = data.weight / qSum
                data.weight = Math.round(p1 * p2 * 10000)
            }
        }

        //皮肤配置 好像没啥用了
        let skinDatas = assetsMgr.getJson<CharacterSkinCfg>("CharacterSkin").datas;
        for (let data of skinDatas) {
            let cid = data['id'].split('-')[0];
            let characterInfo = this.characterMap[cid];
            if (!!characterInfo) {
                if (!characterInfo.skin) characterInfo.skin = [];
                characterInfo.skin.push(data);
            }
        }

        // 角色星级配置
        let starLvUpDatas = assetsMgr.getJson<CharacterStarLvCfg>('StarUp').datas;
        for (let starLvUpData of starLvUpDatas) {
            let lv = starLvUpData.id
            starLvUpData.lv = lv
        }

        let pushKeyOrder = function (theMap: {}, key: string, order: number, data: any) {
            let temp1 = theMap[key]
            if (!temp1) temp1 = theMap[key] = []
            let temp2 = temp1[order]
            if (!temp2) temp2 = temp1[order] = []
            temp2.push(data)
        }

        //剧情对话
        let plot = assetsMgr.getJson<PlotStep>('Plot')
        this.CharacterPlotMap = {}
        let characterPlotDatas = assetsMgr.getJson<CharacterPlotStep>('CharacterPlot').datas
        for (let data of characterPlotDatas) {
            let { key, order, type, id } = data
            order--
            pushKeyOrder(this.CharacterPlotMap, key, order, data)
            if (type == RelaterType.Dialog) {
                // 转成plot配置
                let value = ut.cloneObject<CharacterPlotStep>(data)
                value.key = id
                value.order = 1
                plot.set(id, value)
            }
        }

        this.plotMap = {}
        let plotDatas = plot.datas
        for (let data of plotDatas) {
            let { key, order } = data
            order--
            pushKeyOrder(this.plotMap, key, order, data)
        }

        let CharacterProfileDatas = assetsMgr.getJson<CharacterProfileCfg>('CharacterProfile').datas
        this.characterProfileMap = {}
        for (let data of CharacterProfileDatas) {
            let id = data.id
            // 这里兼容处理一下好友类型
            if (data.type == 9 && !this.getCharacter(data.characterId)?.profile.friends[0]) {
                continue
            }
            if (data.type == 11 && !this.getCharacter(data.characterId)?.profile.friends[1]) {
                continue
            }
            this.characterProfileMap[id] = data
        }

        let CharacterProfileAttrDatas = assetsMgr.getJson<CharacterProfileAttrCfg>('CharacterProfileAttr').datas
        this.characterProfileAttrMap = {}
        for (let data of CharacterProfileAttrDatas) {
            let id = data.id
            this.characterProfileAttrMap[id] = data
        }

        let PlanetProfileDatas = assetsMgr.getJson<PlanetProfileCfg>('PlanetProfile').datas
        this.planetProfileMap = {}
        for (let data of PlanetProfileDatas) {
            let id = data.id
            this.planetProfileMap[id] = data
        }

        let PlanetProfileRewardDatas = assetsMgr.getJson<PlanetProfileReward>('PlanetProfileReward').datas
        this.planetProfileRewardMap = {}
        for (let data of PlanetProfileRewardDatas) {
            let id = data.id
            const reward = data.reward
            data.grouped = reward.reduce((groups, item) => {
                if (!groups[item.group]) {
                    groups[item.group] = []
                }
                groups[item.group].push(item)
                groups[item.group].sort((a, b) => a.group - b.group)
                return groups
            }, {})
            this.planetProfileRewardMap[id] = data
        }

        let guideDatas = assetsMgr.getJson<GuideStep>('Guide').datas
        this.guideMap = {}
        for (let data of guideDatas) {
            let id = Math.floor(data.id / 100)
            if (!this.guideMap[id]) this.guideMap[id] = []
            this.guideMap[id].push(data)
        }

        //打造台
        let toolTableDatas = assetsMgr.getJson<ToolTabelCfg>('ToolTable').datas
        for (let data of toolTableDatas) {
            this.toolTabelMap[data.id] = data
        }

        let trainGoodsDatas = assetsMgr.getJson<TrainGoodsCfg>('TrainGoods').datas
        this.trainGoodsMap = {}
        for (const data of trainGoodsDatas) {
            let id = data.trainId
            if (!this.trainGoodsMap[id]) this.trainGoodsMap[id] = []
            this.trainGoodsMap[id].push(data)
        }

        let achievementDatas = assetsMgr.getJson<AchievementCfg>('Achievement').datas
        this.achievementMap = {}
        for (const data of achievementDatas) {
            let id = data.typeId
            if (!this.achievementMap[id]) this.achievementMap[id] = []
            this.achievementMap[id].push(data)
        }

        let plotControlDatas = assetsMgr.getJson<CharacterPlotControl>('CharacterPlotControl').datas
        for (const data of plotControlDatas) {
            let id = data.characterId
            if (!this.plotControlMap[id]) this.plotControlMap[id] = []
            this.plotControlMap[id].push(data)
        }

        // 副本关卡
        let instanceLevelDatas = assetsMgr.getJson<InstanceLevelCfg>("InstanceLevel").datas
        for (const data of instanceLevelDatas) {
            if (!this.instanceLevelMap[data.id]) this.instanceLevelMap[data.id] = data
        }

        let planetMoveTimeDataDatas = assetsMgr.getJson<any>("PlanetMoveTime").datas
        for (let data1 of planetMoveTimeDataDatas) {
            for (let data2 of planetMoveTimeDataDatas) {
                let id1 = data1.id, id2 = data2.id
                // console.log(`${id1}, ${id2}`)
                let time = (!!data1.time && data1.time[id2]) || (data2.time && data2.time[id1]) || 1000000
                if (!this.planetMoveTimeMap[id1]) this.planetMoveTimeMap[id1] = []
                this.planetMoveTimeMap[id1].push({ toId: id2, time })
                //if (!this.planetMoveTimeMap[id2]) this.planetMoveTimeMap[id2] = []
                //this.planetMoveTimeMap[id2].push({ toId: id1, time })
            }
        }

        let lvRatio = this.getLvAttrRatio()
        const LOOP_COUNT = Math.round(1 / lvRatio)

        let handle = (id: number, baseHp: number, baseAtk: number) => {
            if (!this.characterBaseAttrMap[baseHp]) this.characterBaseAttrMap[baseHp] = {}
            if (this.characterBaseAttrMap[baseHp][baseAtk]) return

            let hpAry = ut.numAvgSplit(baseHp, LOOP_COUNT)
            let atkAry = ut.numAvgSplit(baseAtk, LOOP_COUNT)

            let lvs = []
            let firstAry = hpAry, secondAry = atkAry
            if (baseHp < baseAtk) {
                firstAry = atkAry
                secondAry = hpAry
            }
            for (let i = 0; i < LOOP_COUNT; i++) {
                let val1 = firstAry.max()
                let val2 = secondAry.min()
                firstAry.remove(val1)
                secondAry.remove(val2)

                let hp = val1, attack = val2
                if (firstAry == atkAry) {
                    attack = val1
                    hp = val2
                }
                lvs[i] = { hp, attack }

                let t = firstAry
                firstAry = secondAry
                secondAry = t
            }
            this.characterBaseAttrMap[baseHp][baseAtk] = lvs
        }
        for (let role of roleDatas) {
            handle(role.id, role.hp, role.attack)
        }
        let attr = this.getMiscData("blackHole").baseAttr
        handle(ROLE_ATTR_ID.BLACK_HOLE, attr, attr)

        const trainGoodsData = assetsMgr.getJson<TrainGoodsLevelCfg>('TrainGoodsLevel').datas
        for (let data of trainGoodsData) {
            let tmp = Object.assign({}, data)
            if (tmp.lv > 1) {
                trainGoodsData.forEach(t => {
                    if (t.goodsId == tmp.goodsId && t.lv < tmp.lv) {
                        tmp.attrValue += t.attrValue
                    }
                })
            }
            this.trainGoodsLevelMap[tmp.id] = tmp
        }

        const arrestData = assetsMgr.getJson<ArrestCfg>('Arrest').datas
        for (let data of arrestData) {
            this.arrestMap[data.id] = data
        }

        this.initTransportLevelCfgExp()

        this.initEquipSellMap()

        const questionData = assetsMgr.getJson<QuestionCfg>("Question").datas
        for (let data of questionData) {
            if (!this.questionMap[data.characterId]) this.questionMap[data.characterId] = []
            this.questionMap[data.characterId].push(data)
        }
        const publicityPlayCfgAry = assetsMgr.getJson<PublicityPlayCfg>("PublicityPlay").datas
        let group = {}
        for (const pc of publicityPlayCfgAry) {
            group[pc.planetId] = group[pc.planetId] || []
            group[pc.planetId].push(pc)
        }
        for (const _ in group) {
            const ary = group[_]
            ary.sort((a, b) => a.lv - b.lv)
            for (let i = 1; i < ary.length; i++) {
                ary[i].req += ary[i - 1].req
                ary[i].rate += ary[i - 1].rate
            }
        }
    }
    public getToolTableByLv(lv: number) {
        return this.toolTabelMap[lv]
    }

    public getToolSkinCfg(lv: number) {
        let res: { [type: string]: string } = {}
        for (let i in this.toolTabelMap) {
            for (let j in this.toolTabelMap[i].part) {
                let data = this.toolTabelMap[i].part[j].split('_')
                res[`${data[1]}`] = this.toolTabelMap[i].part[j]
            }
            if (+i >= lv) return res
        }
    }

    public getMaxToolTableLv() {
        let max: number = 0
        for (let data in this.toolTabelMap) {
            max = Math.max(max, this.toolTabelMap[data].id)
        }
        return max
    }

    public getTool(id: number) {
        return assetsMgr.getJsonData<ToolCfg>('Tool', id)
    }

    public getToolLevel(id: string) {
        return assetsMgr.getJsonData<ToolLevelCfg>('ToolLevel', id)
    }

    public getTrainGoods(trainId: number) {
        return this.trainGoodsMap[trainId]
    }

    // 获取车厢配料类型道具
    public getTrainDosing() {
        const ary = assetsMgr.getJson<ItemCfg>('Item').datas.filter(m => m.type == ItemType.DOSING)
        const links = assetsMgr.getJson<FieldSeedCfg>('FieldSeed').datas
        // 补一次类型
        ary.forEach(a => {
            if (a["fType"]) return
            const field = links.find(l => l.reward.find(r => r.type == ConditionType.PROP && r.id == a.id))
            if (field) {
                a["fType"] = field.type
            }
        })
        return ary
    }

    // 默认解锁的
    public getTrainGoodDefaults() {
        return assetsMgr.getJson<TrainGoodsCfg>('TrainGoods').datas.filter(m => m.sortId == 0)
    }

    public getTrainGoodsLevel(id: string, lv: number) {
        return this.trainGoodsLevelMap[`${id}-${lv}`]
    }

    public getTrainGoodsAttr(id: string, lv: number) {
        return assetsMgr.getJson<TrainGoodsLevelCfg>("TrainGoodsLevel").datas.filter(d => d.goodsId == id && d.lv <= lv).reduce((sum, b) => sum + (b.attrValue || 0), 0)
    }

    public getBuildById(buildId: string) {
        return assetsMgr.getJsonData<BuildCfg>('TrainItem', buildId)
    }

    public getBuilds(carriageId: number, skin: number = 1) {
        return assetsMgr.getJson<BuildCfg>('TrainItem').datas.filter(d => d.carriageId == carriageId && d.skin == skin && d.show)
    }

    public getBuildsSameOrder(carriageId: number, order: number = 1) {
        return assetsMgr.getJson<BuildCfg>('TrainItem').datas.filter(d => d.carriageId == carriageId && d.order == order && d.show)
    }

    public getThemes(carriageId: number) {
        return assetsMgr.getJson<CarriageThemeCfg>("TrainTheme").datas.filter(d => d.carriageId == carriageId)
    }

    public getRoleSkins(characterId: number) {
        return assetsMgr.getJson<CharacterSkinCfg>("CharacterSkin").datas.filter(d => Number(d.characterId) == characterId)
    }

    public getBuildMax(carriageId: number) {
        return this.getBuilds(carriageId).length
    }

    public getAchievementCfg(id: string) {
        return assetsMgr.getJsonData<AchievementCfg>('Achievement', id)
    }

    public getAchievementTypeCfg(typeId: number) {
        return assetsMgr.getJsonData<AchievementTypeCfg>('AchievementType', typeId)
    }

    public getAllAchievement() {
        return this.achievementMap
    }

    public getCharacter(id: number) {
        return this.characterMap[id]
    }

    public getPlanetJsonName(planetId, jsonName) {
        return `planet/${planetId}/${planetId}_${jsonName}`
    }

    public getPlanetJson(planetId, jsonName) {
        jsonName = this.getPlanetJsonName(planetId, jsonName)
        return assetsMgr.getJson<any>(jsonName)
    }

    public getPlanetJsonData(planetId, jsonName, id) {
        jsonName = this.getPlanetJsonName(planetId, jsonName)
        return assetsMgr.getJsonData<any>(jsonName, id)
    }

    public getThemeMapJsonData(carriageId) {
        let jsonName = `carriage/${carriageId}_ThemeMap`
        let json = assetsMgr.getJson<any>(jsonName)
        return json?.datas[0]
    }

    public getEntrustNum(id: number) {
        return assetsMgr.getJson<any>('EntrustStarPlace').datas.find(data => data.id == id)?.num || 0
    }

    public getChestPoint(id: number) {
        let res = 0
        assetsMgr.getJson<any>('Chest').datas.forEach((data) => {
            if (data.itemId == id) {
                res = data.medal
                return
            }
        })
        return res
    }

    public getChestItemId(id: number) {
        return assetsMgr.getJson<any>('Chest').datas.find((data) => {
            return data.id == id
        })?.itemId || id
    }

    public getChestData(itemId: number) {
        return assetsMgr.getJson<ChestCfg>('Chest').datas.find(m => m.itemId == itemId)
    }

    public getMiscData(id: string) {
        return assetsMgr.getJson<{ [key: string]: any }>('Misc_C').datas[id]
    }

    public getUnlockFunDatas() {
        return assetsMgr.getJson<UnlockFuncCfg>('UnlockFunc').datas
    }

    public getPropData(id: number) {
        return assetsMgr.getJsonData<ItemCfg>('Item', id)
    }

    public getPlotData(key: string) {
        return this.plotMap[key]
    }

    public getPlotId(key: string, step: number = 1) {
        return `${LangCfgName.PLOT}.${key}_${step}`
    }

    public getCharacterPlotData(key: string) {
        return this.CharacterPlotMap[key]
    }
    public getCharacterPlotMaxOrder(key: string): number {
        let temp1 = this.getCharacterPlotData(key)
        if (!temp1) return 0
        for (let i = temp1.length - 1; i >= 0; i--) {
            let temp2 = temp1[i]
            for (const temp3 of temp2) {
                if (this.isKeyStepCharacterPlot(temp3)) {
                    return temp3.order
                }
            }
        }
    }
    public isKeyStepCharacterPlot(plot: CharacterPlotStep) {
        return plot.reward || plot.buyCost
    }
    public haveKeyStepCharacterPlot(plots: CharacterPlotStep[]) {
        for (const plot of plots) {
            if (this.isKeyStepCharacterPlot(plot)) {
                return true
            }
        }
    }
    public getCharacterPlotKeyByRewardId(rewardId: number) {
        for (const key in this.CharacterPlotMap) {
            let temp1 = this.CharacterPlotMap[key]
            for (const temp2 of temp1) {
                for (const temp3 of temp2) {
                    if (temp3.reward && temp3.reward.some(r => r.id == rewardId)) {
                        return key
                    }
                }
            }
        }
    }

    public getGuideCfg() {
        return this.guideMap
    }

    public getFunctionName(type: UIFunctionType) {
        let data = this.getUnlockFunDatas()
        let func = data.find(func => func.type == type)
        if (func) {
            return func.name
        }
    }

    public getFunctionCfg(type: UIFunctionType) {
        let data = this.getUnlockFunDatas()
        return data.find(func => func.type == type)
    }

    public getStarLvCfg(starLv: number): CharacterStarLvCfg {
        return assetsMgr.getJsonData<CharacterStarLvCfg>("StarUp", starLv)
    }

    public getStarLvDatas(): CharacterStarLvCfg[] {
        return assetsMgr.getJson<CharacterStarLvCfg>("StarUp").datas
    }

    public getInitStarLv(roleId?: number) {
        let cfg = this.getCharacter(roleId)
        let quality = cfg.quality
        return this.getStarLvByQuality(quality)
    }

    public getStarLvByQuality(quality) {
        let datas = assetsMgr.getJson<CharacterStarLvCfg>("StarUp").datas
        return datas.find(data => data.quality == quality)?.lv || 0
    }

    public getLvUpCost(id: number, level: number) {
        let levelUpCost = assetsMgr.getJsonData<CharacterLevelCostCfg>('CharacterLevelCost', level)
        if ((Number(id) == 1005 && Number(level) < 3)) {
            return levelUpCost.buyCost.filter(c => c.type != ConditionType.STAR_DUST)
        }
        return levelUpCost.buyCost
    }

    public getRoleAttrByStarLv(level: number) {
        let attrRatio = this.getStarLvAttrRatio()
        return level * attrRatio
    }

    public getRoleAttr(id: number, level: number, starLv: number, attr: PassengerAttr) {
        let cfg = cfgHelper.getCharacter(id)
        let baseHp, baseAtk
        if (cfg) {
            baseHp = cfg.hp
            baseAtk = cfg.attack
        }
        else {
            baseHp = baseAtk = this.getMiscData("blackHole").baseAttr
        }
        if (!this.characterBaseAttrMap[baseHp] || !this.characterBaseAttrMap[baseHp][baseAtk]) {
            cc.warn("getRoleAttr not found", id, baseHp, baseAtk)
            return 0
        }
        let starLvRatio = this.getStarLvAttrRatio()
        let starRate = 1 + starLvRatio * starLv

        let lvs = this.characterBaseAttrMap[baseHp][baseAtk]
        let len = lvs.length
        let count = Math.floor((level - 1) / len)
        let baseVal = baseHp
        if (attr == PassengerAttr.ATTACK) {
            baseVal = baseAtk
        }
        let val = baseVal * (count + 1)

        let remainLv = (level - 1) - count * len
        for (let i = 0; i < remainLv; i++) {
            val += lvs[i][attr]
        }
        val = Math.round(val * starRate)
        return val
    }

    public getRoleAttrByAdd(id: number, level: number, starLv: number, attr: PassengerAttr) {
        return this.getRoleAttr(id, level + 1, starLv, attr) - this.getRoleAttr(id, 1, starLv, attr)
    }

    // public getTalentAttr(id: number, attr: PassengerAttr, talentLv: number = 0) {
    //     let cfg = this.getCharacter(id)
    //     if (!cfg) return 0
    //     let datas = assetsMgr.getJson<any>("TalentAttrLevel").datas.filter(data => data.id <= talentLv)
    //     let rate = datas.reduce((data, item) => {
    //         return data + item.rate
    //     }, 0)
    //     let base = cfg[attr] || 0
    //     return Math.round(base * rate)
    // }

    public getMaxTalentLv() {
        return assetsMgr.getJson<any>("TalentAttrLevel").datas.last().id
    }

    public getMaxSkillLv(roleId: number, skillId: number): number {
        return this.getSkillLv(roleId, 1000, skillId)
    }

    //根据角色等级获取技能等级
    public getSkillLv(roleId: number, roleLevel: number, skillId: number) {
        let cfg = this.getCharacter(roleId)
        let skillIndex = this.getIndexBySkillId(skillId)
        let datas = this.getSkillTemplateDatas(cfg.skillTmpId)
        let index = datas.findIndex(d => roleLevel < d.level)
        if (index == 0) return 0
        let data = index < 0 ? datas.last() : datas[index - 1]
        return data.skill[skillIndex].lv
    }

    //技能的上一级是角色升到几级
    public getPreSkillRoleLv(roleId: number, nowLv: number, skillId: number) {
        let skillLv = this.getSkillLv(roleId, nowLv, skillId)
        let cfg = this.getCharacter(roleId)
        let skillIndex = this.getIndexBySkillId(skillId)
        let datas = this.getSkillTemplateDatas(cfg.skillTmpId)
        let curIndex = datas.findIndex(d => d.skill[skillIndex].lv == skillLv)
        return datas[curIndex - 1]?.level || 0
    }

    //上次技能的等级
    public getPreSkillLv(roleId: number, nowLv: number, skillId: number) {
        let roleLv = this.getPreSkillRoleLv(roleId, nowLv, skillId)
        if (roleLv == 0) return 0
        return this.getSkillLv(roleId, roleLv, skillId)
    }

    //技能的下一级是角色升到几级
    public getNextSkillRoleLv(roleId: number, nowLv: number, skillId: number) {
        let skillLv = this.getSkillLv(roleId, nowLv, skillId)
        let cfg = this.getCharacter(roleId)
        let skillIndex = this.getIndexBySkillId(skillId)
        let datas = this.getSkillTemplateDatas(cfg.skillTmpId)
        let nextLvData = datas.find(d => d.skill[skillIndex].lv > skillLv) || datas.last()
        return nextLvData.level
    }

    //下次技能的等级
    public getNextSkillLv(roleId: number, nowLv: number, skillId: number) {
        let roleLv = this.getNextSkillRoleLv(roleId, nowLv, skillId)
        return this.getSkillLv(roleId, roleLv, skillId)
    }

    //技能解锁的角色等级
    public getUnlockSkillLv(roleId: number, skillId: number) {
        let cfg = this.getCharacter(roleId)
        let skillIndex = this.getIndexBySkillId(skillId)
        let datas = this.getSkillTemplateDatas(cfg.skillTmpId)
        let data = datas.find(d => d.skill[skillIndex].lv > 0)
        if (data) {
            return data.level
        }
        return 0
    }

    //升级到level时是否有技能解锁/升级
    public checkUnlockSkillLv(roleId: number, level: number) {
        let cfg = this.getCharacter(roleId)
        let datas = this.getSkillTemplateDatas(cfg.skillTmpId)
        return !!datas.find(d => d.level === level)
    }

    public getChangeSkill(roleId: number, level: number) {
        let curSkills = cfgHelper.getSkillsByLv(roleId, level)
        let preSkills = cfgHelper.getSkillsByLv(roleId, level - 1)
        for (let curSkill of curSkills) {
            let skills = preSkills
            let skill = skills.find(s => s.id == curSkill.id)
            if (skill) {
                if (skill.lv < curSkill.lv) {
                    return curSkill
                }
            }
            else {
                curSkill.isNew = true
                return curSkill
            }
        }
    }

    //根据等级获取所有已解锁的技能
    public getSkillsByLv(roleId: number, level: number, skillTmpId?) {
        let ret = []
        let cfg = this.getCharacter(roleId)
        if (!cfg?.battleSkill) return ret
        let datas = this.getSkillTemplateDatas(skillTmpId || cfg.skillTmpId)
        let index = datas.findIndex(d => level < d.level)
        if (index == 0) return ret
        let data = index < 0 ? datas.last() : datas[index - 1]
        for (let i = 0; i < data.skill.length; i++) {
            let id = this.getIdBySkillIndex(cfg.battleSkill, i)
            if (!data.skill[i] || data.skill[i].calcLv <= 0) break
            let lv = data.skill[i].lv
            if (!assetsMgr.checkJsonData("BattleSkill", id)) {
                break
            }
            ret.push({ id, lv })
        }
        return ret
    }

    public getSkillTemplateDatas(tmpId) {
        return assetsMgr.getJson<any>("BattleSkillTemplate").datas.filter(d => d.tmpId == tmpId)
    }

    public getIndexBySkillId(id) {
        return Math.floor(id % 100000 / 10000)
    }

    public getIdBySkillIndex(mainId, index) {
        return index * 10000 + mainId
    }

    public getMainSkillId(id) {
        return id % 100000 % 10000
    }

    public getAnim(id: number | string, name: string, json: string) {
        let cfg = assetsMgr.getJsonData<SpineAnimCfg>(json, id)
        // if (!cfg) {
        //     twlog.error(`${json} no cfg ${id}`)
        // }
        let ary = cfg?.animations || []
        return ary.find(a => a.name == name)
    }

    public getRoleAnim(id: number | string, name: string) {
        return this.getAnim(id, name, '_RoleAnim')
    }

    public getBuildAnim(id: number | string, name: string) {
        return this.getAnim(id, name, '_BuildAnim')
    }

    public getBuildAnimEventTime(id: number | string, name: string, eventName = "effect") {
        let anim = this.getAnim(id, name, '_BuildAnim')
        let events = anim.events || []
        let event = events.find(e => e.name == eventName)
        if (!event) {
            twlog.error("getBuildAnimEventTime not found", id, name, eventName)
        }
        return event?.time || 0
    }

    public getRoleAnimTime(id: number | string, name: string) {
        let anim = this.getRoleAnim(id, name)
        if (!anim) {
            twlog.error("getRoleAnimTime not found", id, name)
            return 1
        }
        return anim.duration
    }

    public getBuildAnimTime(id: number | string, name: string) {
        let anim = this.getBuildAnim(id, name)
        if (!anim) {
            twlog.error("getBuildAnimTime not found", id, name)
            return 1
        }
        return anim.duration
    }

    public getRoleAnimsTime(id: number | string, names: string[]) {
        let time = 0
        for (let name of names) {
            time += this.getRoleAnimTime(id, name)
        }
        return time
    }

    public getBuildAnimsTime(id: number | string, names: string[]) {
        let time = 0
        for (let name of names) {
            time += this.getBuildAnimTime(id, name)
        }
        return time

    }

    public mapCarriageUsePosList(posList, basePos) {
        return posList.map((info, i) => {
            let pos = basePos.add(cc.v2(info.x, info.y))
            let res: CarriageUsePosInfo = { pos, index: i }
            ut.setValue("id|type|dir|force", info, res)
            if (info.width || info.height) {
                let { width, height } = info
                let rect = cc.rect(pos.x - width * 0.5, pos.y - height * 0.5, width, height)
                res.rect = rect
            }
            return res
        })
    }

    //这个配置没啥用了，准备删
    public getWorksByCarriageId(carriageId: number) {
        return assetsMgr.getJson<TrainWorkCfg>("TrainWork").datas.filter(data => data.train == carriageId)
    }

    public getWork(carriageId: number, index: number) {
        return this.getWorksByCarriageId(carriageId).find(data => data.index == index)
    }

    public getPlotControlData(id: number) {
        return this.plotControlMap[id] || []
    }

    public getBuildLvCfg(carriageId: number, order: number, lv: number) {
        return assetsMgr.getJsonData<BuildLevelCfg>("TrainItemLevel", `${carriageId}-${order}-${lv}`)
    }

    public checkBuildLvCfg(carriageId: number, order: number, lv: number) {
        return assetsMgr.checkJsonData<BuildLevelCfg>("TrainItemLevel", `${carriageId}-${order}-${lv}`)
    }

    public getThemeLvCfg(carriageId: number, order: number) {
        return assetsMgr.getJsonData<CarriageThemeCfg>("TrainTheme", `${carriageId}-${order}`)
    }

    public getCharacterIdByPlotKey(key: string) {
        for (const id in this.plotControlMap) {
            const ary = this.plotControlMap[id]
            for (const data of ary) {
                if (data.plotKey == key) {
                    return id
                }
            }
        }
    }
    public getCharacterIdByRewardId(rewardId: number) {
        let key = this.getCharacterPlotKeyByRewardId(rewardId)
        if (!key) {
            cc.error("getCharacterIdByRewardId 1error", rewardId)
            return
        }
        let roleId = this.getCharacterIdByPlotKey(key)
        if (!roleId) {
            cc.error("getCharacterIdByRewardId 2error", key)
            return
        }
        return Number(roleId)
    }

    public getTowerCheckPointsByLayer(layer: number) {
        return assetsMgr.getJson<TowerMonsterCfg>("TowerMonster").datas.filter(d => Number(d.id.split("-")[0]) == layer)
    }

    public getInstanceLevels() {
        return assetsMgr.getJson<InstanceLevelCfg>("InstanceLevel").datas
    }

    public getInstanceLevel(id: number) {
        return this.instanceLevelMap[id]
    }

    public getAnimalTypeName(type: RoleAnimalType) {
        return `character_animal_type_${type}`
    }

    public getBattleTypeName(type: RoleBattleType) {
        return `character_battle_type_${type}`
    }

    public getPlanetMoveTime(from: number, to: number) {
        let vis = new Map()
        let dis: { [id: number]: number } = {}
        let prePlanet: { [id: number]: number } = {} // 节点id的上一节点id
        dis[from] = 0
        prePlanet[from] = from
        while (1) {
            let min = 1000000
            let key
            for (let id in dis) {
                if (!vis.get(id) && dis[id] < min) {
                    key = id
                    min = dis[id]
                }
            }
            if (!key) break
            vis.set(key, 1)
            for (let line of this.planetMoveTimeMap[key]) {
                let tmp = line.time + min
                if (!vis.get(line.toId) && (!dis[line.toId] || dis[line.toId] > tmp)) {
                    dis[line.toId] = tmp
                    prePlanet[line.toId] = key
                }
            }
        }
        let road: number[] = []
        let cur = to
        while (cur != from) {
            road.push(cur)
            cur = prePlanet[cur]
        }
        road.push(from)
        return { time: dis[to] * ut.Time.Second, road }
    }

    public getLvAttrRatio() {
        return this.getMiscData("lvAttrRatio")
    }

    public getStarLvAttrRatio() {
        return this.getMiscData("starLvAttrRatio")
    }

    public getQualityByStarLv(starLv) {
        return assetsMgr.getJsonData<CharacterStarLvCfg>("StarUp", starLv)?.quality || PassengerQuality.N
    }

    public getRealStarLv(starLv) {
        let lv = 0
        let star = starLv
        let datas = assetsMgr.getJson<CharacterStarLvCfg>("StarUp").datas
        for (let cfg of datas) {
            if (cfg.lv <= star) {
                if (cfg.quality == this.getQualityByStarLv(star)) {
                    lv++
                }
            }
            else break
        }
        return lv - 1
    }

    public getMaxStarLvByStarLv(starLv) {
        let lv = 0
        let star = starLv
        let datas = assetsMgr.getJson<CharacterStarLvCfg>("StarUp").datas
        for (let cfg of datas) {
            if (cfg.quality == this.getQualityByStarLv(star)) {
                lv++
            }
        }
        return lv - 1
    }

    public getMaxLvByStarLv(starLv: number) {
        return this.getStarLvCfg(starLv).levelMax
    }

    public getFragByCharacterIdAndQuality(characterId: string, quality: number) {
        let datas = assetsMgr.getJson<CharacterFragCfg>('CharacterFrag').datas
        return datas.find(e => e.characterId == characterId && e.quality == quality)
    }

    public getFragById(id: number) {
        return assetsMgr.getJsonData<CharacterFragCfg>('CharacterFrag', id)
    }

    public getMaxLv() {
        return assetsMgr.getJson<CharacterStarLvCfg>("StarUp").datas.last().levelMax
    }

    public getSeedInfoById(id: number) {
        return assetsMgr.getJsonData<FieldSeedCfg>("FieldSeed", id)
    }

    public getTransportLevelUpCfg(level: number) {
        const doc = assetsMgr.getJsonData<TransportLevelUpCfg>('TransportLevelUp', level)
        return doc
    }

    public getFieldLevelCfg(level: number) {
        return assetsMgr.getJsonData<FieldLevelCfg>('FieldLevel', level)
    }

    public getFieldMaxLevel() {
        let level = 1
        while (1) {
            let cfg = this.getFieldLevelCfg(level)
            if (!cfg) {
                level--
                break
            }
            if (!cfg.target) break
            level++
        }
        return level
    }

    public getOreItemById(id: number) {
        return assetsMgr.getJsonData<OreItemCfg>('OreItem', id)
    }

    public getEquipMakeById(level: number, pro: number) {
        let pLv = cfgHelper.getProficiencyLv(level, pro)
        return assetsMgr.getJsonData<EquipMakeCfg>('EquipMake', `${level}-${pLv}`)
    }

    public getMaxEquipQuality() {
        let data = assetsMgr.getJson<EquipMakeCfg>('EquipMake').datas
        let max = 1
        data.forEach(e => {
            max = Math.max(max, e.level)
        })
        return max
    }

    public getEquipByRoleAndIndex(roleId: number, index: number) {
        return assetsMgr.getJson<EquipCfg>('Equip').datas.find(m => m.roleId == roleId && m.index == index)
    }

    public getEquipStoreById(storeId: number) {
        return assetsMgr.getJsonData<EquipStoreCfg>('EquipStore', storeId)
    }

    public getOreLevels() {
        return assetsMgr.getJson<OreLevel>("OreLevel").datas
    }

    public getSkillViewCfg(roleId: number, skillId: number, type: SkillType) {
        let cfg
        if (type == SkillType.BATTLE) {
            cfg = assetsMgr.getJsonData<BattleSkillViewCfg>("BattleSkillControl", `${roleId}-${skillId}`)
        }
        else if (type == SkillType.BLACKHOLE_EQUIP) {
            cfg = assetsMgr.getJsonData<BattleSkillViewCfg>("BlackHoleEquip", skillId)
        }
        else if (type == SkillType.INSTANCE) {
            cfg = assetsMgr.getJsonData<BattleSkillViewCfg>("InstanceSkill", skillId)
        }
        return cfg
    }

    public getEquipEffetcs(id: number, level: number, proficiency: number = -1) {
        let cfgDetail = assetsMgr.getJsonData<EquipLevelCfg>("EquipLevel", level)
        let equip = assetsMgr.getJsonData<EquipCfg>("Equip", id)
        let roleId = equip.roleId
        let index = equip.index
        let effects = []

        let rangeIndex = this.getProficiencyLv(level, proficiency) - 1
        for (let effect of cfgDetail.effect) {
            let cfg = assetsMgr.getJsonData<EquipEffectCfg>("EquipEffect", effect.id)
            if (cfg.equipIndex != index) {
                continue
            }
            let rangeInfo = { min: effect.min, max: effect.max }
            if (proficiency >= 0) {
                rangeInfo = this.getEquipSection(effect)[rangeIndex]
            }
            let min = rangeInfo?.min, max = rangeInfo?.max
            let store = effect.store || 0
            let type = cfg.type
            let target = cfg.target
            if (type == EquipEffectType.ATTR) {
                let attrType = PassengerAttr.HP
                if (target == EquipEffectTarget.ATTACK) {
                    attrType = PassengerAttr.ATTACK
                }
                store = cfgHelper.getRoleAttr(roleId, store + 1, 0, attrType)
                min = cfgHelper.getRoleAttr(roleId, min + 1, 0, attrType)
                max = cfgHelper.getRoleAttr(roleId, max + 1, 0, attrType)
                let base = cfgHelper.getRoleAttr(roleId, 1, 0, attrType)
                min -= base
                max -= base
                store -= base
            }
            effects.push({ id: cfg.id, type, target, min, max, store })
        }
        return effects
    }

    public getEquipEffectAttrByLv(equipId: number, effect: EquipEffectCfg, lv: number) {
        let data = assetsMgr.getJsonData<EquipCfg>("Equip", equipId)
        let roleId = data.roleId
        let attr = lv
        if (effect.type == EquipEffectType.ATTR) {
            let attrType = PassengerAttr.ATTACK
            if (effect.target == EquipEffectTarget.HP) {
                attrType = PassengerAttr.HP
            }
            attr = this.getRoleAttrByAdd(roleId, lv, 0, attrType)
        }
        return attr
    }

    private getProficiencyLv(level: number, pro: number) {
        let datas = assetsMgr.getJson<EquipMakeCfg>("EquipMake").datas.filter(d => d.level == level)
        let sum = 0
        for (let i = 1; i < datas.length; i++) {
            let times = datas[i].times
            sum += times
            if (pro < sum) {
                return i
            }
        }
        return datas.length
    }

    public getEquipEffetcsLv(id: number, level: number, proficiency: number = -1) {
        let cfgDetail = assetsMgr.getJsonData<EquipLevelCfg>("EquipLevel", level)
        let equip = assetsMgr.getJsonData<EquipCfg>("Equip", id)
        let index = equip.index
        let effects = []

        let rangeIndex = this.getProficiencyLv(level, proficiency) - 1
        for (let effect of cfgDetail.effect) {
            let cfg = assetsMgr.getJsonData<EquipEffectCfg>("EquipEffect", effect.id)
            if (cfg.equipIndex != index) {
                continue
            }
            let rangeInfo = { min: effect.min, max: effect.max }
            if (proficiency >= 0) {
                rangeInfo = this.getEquipSection(effect)[rangeIndex]
            }
            let min = rangeInfo?.min, max = rangeInfo?.max
            let store = effect.store || 0
            let type = cfg.type
            let target = cfg.target
            effects.push({ id: cfg.id, type, target, min, max, store })
        }
        return effects
    }

    private getEquipSection(effect) {
        return assetsMgr.getJson<any>("EquipSection").datas.find(({ min, max }) => {
            return effect.min == min && effect.max == max
        })?.sec || []
    }

    public getArrestContent(id: number) {
        return this.arrestMap[id].content
    }

    public getCharacterProfileByCharacterIdAndType(characterId: number, type: number) {
        let profileId = characterId * 100 + type
        return this.characterProfileMap[profileId]
    }

    public getCharacterProfileById(profileId: number) {
        return this.characterProfileMap[profileId]
    }

    public getCharacterProfileAttrById(characterId: number, id: string | number) {
        const data = this.characterProfileAttrMap[id]
        const assign = Object.assign({}, data)
        if (assign.unlockCount == 9) {
            const character = this.getCharacter(characterId)
            if (character.profile.friends.length > 1) {
                assign.unlockCount += 1
            }
        }
        return assign
    }


    public getCharacterProfileLvByUnlockCount(characterId: number, num: number) {
        let res = '0'
        for (let lv in this.characterProfileAttrMap) {
            let need = this.characterProfileAttrMap[lv].unlockCount
            if (need == 9) {
                // 羁绊是2个 数量+1
                const character = this.getCharacter(characterId)
                if (character.profile.friends.length > 1) {
                    need += 1
                }
            }
            if (need <= num) {
                res = lv
            }
        }
        return Number(res)
    }

    public getCharacterProfileAttrEffectById(id: string | number) {
        const effect: any[] = []
        for (let lv in this.characterProfileAttrMap) {
            if (+lv > +id) break
            const cfg = this.characterProfileAttrMap[lv]
            for (const eff of cfg.effect) {
                const exists = effect.find(e => e.type == eff.type && e.target == eff.target && e.isPercent == eff.isPercent)
                if (exists) {
                    exists.val += eff.val
                    continue
                }
                effect.push({
                    type: eff.type,
                    target: eff.target,
                    val: eff.val,
                    isPercent: eff.isPercent,
                })
            }
        }
        return effect
    }

    public getCharacterDialog(id: number) {
        return assetsMgr.getJsonData<any>("CharacterDialog", id)
    }

    /**
     * 获取熟练度对应的阶段 和当前阶段计算后的熟练度值
     * @param tableLv 打造台等级
     * @param proficiency 熟练度
     * step : 当前熟练度所处阶段
     * maxStep : 当前台子最高阶段
     * value : 当前熟练度计算阶段扣除后，在当前阶段的熟练度
     * maxVal : 当前阶段的最大熟练度
     */
    public getProficiencyStepNum(tableLv: number, proficiency: number = 0): { step: number, maxStep: number, value: number, maxVal: number } {
        let makeDatas = assetsMgr.getJson<EquipMakeCfg>("EquipMake").datas.filter(d => d.level == tableLv)
        let maxStep = makeDatas.length
        let step = maxStep
        let value = proficiency
        let maxVal = makeDatas.last().times
        let sum = 0
        for (let i = 1; i < makeDatas.length; i++) {
            let times = makeDatas[i].times
            if (proficiency < sum + times) {
                step = i
                maxVal = times
                break
            }
            sum += times
        }
        value = proficiency - sum
        value = Math.min(value, maxVal)
        return {
            step,
            maxStep,
            value,
            maxVal,
        }
    }

    // 初始化运送任务的经验值数据
    private initTransportLevelCfgExp() {
        const docAry = assetsMgr.getJson<TransportLevelCfg>('TransportLevel')?.datas
        if (docAry) {
            for (const doc of docAry) {
                if (doc.id == 1) {
                    doc.exp = 0
                    continue
                }
                let ary = assetsMgr.getJson<any>('TransportMonster')?.datas
                if (ary) {
                    ary = ary.filter(l => l.lv == doc.id - 1)
                    const cnt = ary.length
                    doc.exp = cnt * cfgHelper.getMiscData("transport").rareExp
                }
            }
        }
    }

    /**
     * 运送任务配置
     * @param level 
     * @returns 
     */
    public getTransportLevelCfg(level: number) {
        return assetsMgr.getJsonData<TransportLevelCfg>('TransportLevel', level)
    }

    /**
     * 计算运送等级
     * @param exp 
     */
    public calculateTransportLevel(exp: number): number {
        if (!exp) return 1
        const ary = assetsMgr.getJson<TransportLevelCfg>('TransportLevel').datas
        let lv = 1
        for (const cfg of ary) {
            if (exp < cfg.exp) break
            exp -= cfg.exp
            lv = cfg.id
        }
        return lv
    }
    public getEquipIndexKey(index: number) {
        return `equip_tablePos_${index}`
    }

    private initEquipSellMap() {
        let datas = assetsMgr.getJson<EquipMakeCfg>('EquipMake').datas
        let curLv = 0
        for (let data of datas) {
            let cost = data.cost.find(c => c.type == ConditionType.STAR_DUST)
            let lvCfg = assetsMgr.getJsonData<EquipLevelCfg>('EquipLevel', data.level)
            let min = 0, max = 0
            let ratio = this.getMiscData("equip").sell
            let equipIndex = 1
            for (let effect of lvCfg.effect) {
                let sections = this.getEquipSection(effect)
                let section = sections[data.proficiency - 1]
                if (!section) continue
                let effectDetail = assetsMgr.getJsonData<EquipEffectCfg>("EquipEffect", effect.id)
                if (effectDetail.equipIndex != equipIndex) continue
                if (effectDetail.type == EquipEffectType.SKILL) {
                    min += section.min * 5
                    max += section.max * 5
                }
                else {
                    min += section.min
                    max += section.max
                }
            }
            let mid = Math.round((min + max) / 2)
            for (let i = curLv; i <= mid; i++) {
                this.equipSellMap[i] = Math.round(cost.num * ratio)
            }
            curLv = mid + 1
        }
    }

    public getEquipSellPrice(lv: number) {
        return this.equipSellMap[lv] || this.equipSellMap.last()
    }

    /**
     * 使用单位等级获取对应星级
     * @param level 等级
     * @returns 
     */
    public getMonsterStarLvByLevel(level: number) {
        const starUpData = assetsMgr.getJson<any>('MonsterStarUp').datas
        for (const data of starUpData) {
            if (level <= data.levelMax) {
                return data.id
            }
        }
        return 0
    }

    public getPlanetProfileById(id: number) {
        return this.planetProfileMap[id]
    }

    public getPlanetProfileByPlanetId(planetId: number, type?: PlanetProfileType) {
        return Object.values(this.planetProfileMap).filter(p => p.planetId == planetId && (type == undefined || p.type == type))
    }

    public getPlanetProfileByCond(planetId: number, type?: PlanetProfileType, sort?: number, area?: number) {
        return Object.values(this.planetProfileMap).filter(p => p.planetId == planetId && (type == undefined || p.type == type) && (sort == undefined || p.sort == sort) && (area == undefined || p.area == area))
    }
    // 获取星球某区域贴纸奖励数据
    public getPlanetProfileReward(planetId: number, area: number) { return this.planetProfileRewardMap[planetId + "-" + area] }

    public getQuestions(characterId?: number) {
        return this.questionMap[characterId] || []
    }
}

export const cfgHelper = new CfgHelper()

window["cfgHelper"] = cfgHelper
