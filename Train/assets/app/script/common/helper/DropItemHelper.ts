import { util } from "../../../core/utils/Utils"
import { Msg } from "../../../proto/msg-define"
import ConditionObj from "../../model/common/ConditionObj"
import CarriageModel from "../../model/train/common/CarriageModel"
import DropItemObj from "../../model/train/common/DropItemObj"
import DropMoneyObj from "../../model/train/common/DropMoneyObj"
import ClickTouchCmpt from "../../view/cmpt/common/ClickTouchCmpt"
import { MAX_VALUE, MAX_ZINDEX } from "../constant/Constant"
import { ConditionType, ItemID, WeakGuideType } from "../constant/Enums"
import EventType from "../event/EventType"
import NodeType from "../event/NodeType"
import { animHelper } from "./AnimHelper"
import { gameHelper } from "./GameHelper"
import { resHelper } from "./ResHelper"
import { viewHelper } from "./ViewHelper"

/**
 * 扔钱相关
 */
class DropItemHelper {

    private items: cc.Node[] = [] //地上面所有的物品

    private tempVec: cc.Vec2 = cc.v2()
    private tempVec1: cc.Vec2 = cc.v2()
    private tempVec2: cc.Vec2 = cc.v2()

    private lightAreaMap: { [key: string]: cc.Node } = {}

    private collectId: number = 0
    private collectMap: { [id: number]: { count: number, client: boolean, sent?: boolean, item: ConditionObj } } = {}

    // 播放掉落钱
    public async playDropMoney(role: { map: CarriageModel, position: cc.Vec2 }, drop: DropItemObj, parent: cc.Node, isMerge: boolean) {
        const it = await resHelper.getNode(drop.getPrefabUrl(), '_drop_item_')
        if (!role || !role.map.active) {
            return resHelper.putNode(it)
        }

        it.zIndex = cc.macro.MAX_ZINDEX
        let dropNode = this.addDropNode(it, parent, drop, isMerge ? null : role.map)

        if (!role.position.equals(drop.position)) {
            dropNode.setPosition(role.position.x, role.position.y + 60)
            await cc.tween(it).then(cc.jumpTo(0.5, drop.position, 100, 1)).promise()
        }
        else {
            dropNode.setPosition(role.position.x, role.position.y)
        }

        if (isMerge) {
            resHelper.putNode(it)
            let node = this.items.find(item => item.Data && item.Data.drop == drop)
            if (node) {
                this.updateDropNode(node, drop)
            }
        }
        else {
            let drops = role.map.getAllDrops()
            if (!drops.some(d => d == drop)) {
                resHelper.putNode(it)
            }
            else {
                this.setDropItemEvent(it, drop.zIndex)
            }
        }
        eventCenter.emit(EventType.DROP_ITEM_ANIM_END)
    }

    public async initLightAreas(map: CarriageModel, parent: cc.Node) {
        let areas = map.moneyAreas
        for (let area of areas) {
            if (area.tips || area.up) {
                const it = await resHelper.getNode("drop/drop_light_area", '_drop_item_')
                if (!it || !map.active || !parent.isValid) {
                    return resHelper.putNode(it)
                }
                this.lightAreaMap[area.tag || area.id] = it
                it.parent = parent
                let pos = cc.v2(area.pos.x, area.pos.y + 100)
                it.setPosition(pos)
                let zIndex = map.getDropZIndex(pos, area.up)
                it.zIndex = zIndex
                it.Component("DropLightAreaCmpt").setCarriage(map)
            }
        }
    }

    // 显示所有掉落钱
    public async initDropMoney(map: CarriageModel, parent: cc.Node) {
        this.items.delete(item => item.Data.map == map)

        const items = map.getAllDrops()
        for (let i = 0, l = items.length; i < l; i++) {
            const m = items[i]
            const it = await resHelper.getNode(m.getPrefabUrl(), '_drop_item_')
            if (!it || !map.active || !parent.isValid) {
                return resHelper.putNode(it)
            }
            this.addDropNode(it, parent, m, map)?.setPosition(m.position)
            this.setDropItemEvent(it, m.zIndex, true)
        }
    }

    private addDropNode(it: cc.Node, parent: cc.Node, drop: DropItemObj, map: CarriageModel) {
        if (!drop) {
            return null
        }
        it.parent = parent
        it.stopAllActions()
        this.updateDropNode(it, drop)

        if (map) {
            it.Data = { uid: drop.uid, areaId: drop.areaId, map: map, drop: drop, init: false }
            this.items.push(it)
        } else {
            it.Data = null
        }
        return it
    }

    private updateDropNode(it: cc.Node, drop: DropItemObj) {
        it.opacity = 255
        it.scale = 1
        if (drop instanceof DropMoneyObj) {
            const icon = it.Child('body')
            icon.scale = 1
            icon.opacity = 255
            animHelper.stopTrailingParticle(icon)
        }
    }

    private setDropItemEvent(it: cc.Node, zIndex: number, init?: boolean) {
        if (!it || !it.isValid || !it.Data || !it.Data.map.active) {
            return
        }
        this.addLight(it)

        it.zIndex = zIndex
        it.Data.init = true
        const touch = it.Child("touch").Component(ClickTouchCmpt)
        let body = it.Child('body')
        if (init) {
            const anim = body.Component(cc.Animation)
            anim.play()
            let dur = anim.currentClip.duration
            anim.setCurrentTime(ut.randomRange(0, dur * 2))
        } else {
            body.Component(cc.Animation).play()
        }
        touch.on(() => {
            this.onClickMoney(it)
        })
    }

    private addLight(it: cc.Node) {
        let data = it.Data
        let drop = data.drop
        let item = drop.item
        let lightAreaNode = this.lightAreaMap[drop.getTag()]
        if (!lightAreaNode) {
            twlog.error("addLight not found", drop)
            return
        }
        let addNode = (pos) => {
            let lightNode = lightAreaNode.children.find(node => !node.active)
            if (!lightNode) {
                lightNode = cc.instantiate2(lightAreaNode.children[0], lightAreaNode)
            }
            let color = "#FF9400"
            if (item.type == ConditionType.PROP) {
                if (item.id == ItemID.ELECTRIC || item.id == ItemID.WATER) {
                    color = "#69baf2"
                }
                else if (item.id == ItemID.VITALITY) {
                    color = "#FB953E"
                }
            }
            lightNode.SetColor(color)
            lightNode.opacity = 99
            lightNode.Data = it
            lightNode.active = true
            let p = ut.convertToNodeAR(it, lightAreaNode, pos)
            lightNode.setPosition(p)
        }

        if (drop.tips) {
            addNode(cc.v2(31, 41))
            addNode(cc.v2(-31, 41))
        }
        else {
            addNode(cc.v2(0, 36))
        }
    }

    // 点击钱
    @util.addLock
    private async onClickMoney(node: cc.Node) {
        let map: CarriageModel = node.Data.map
        let drop = node.Data.drop
        let drops = dropItemHelper.getAllDropsByCarriage(map)
        let tag = drop.getTag()
        if (!map.isCollectAll()) {
            drops = drops.filter((_drop) => {
                if (_drop.getTag() != tag) {
                    return
                }
                else if (!_drop.item.isSame(drop.item)) {
                    return
                }
                return true
            })
        }

        let weakGuide = gameHelper.weakGuide.curGuide
        if (weakGuide?.id == WeakGuideType.COLLECT_STAR) {
            gameHelper.weakGuide.cancel()
        }
        node.Data._click = true

        return this.collect(drops)
    }

    public async collect(drops: DropMoneyObj[], showFlutter: boolean = true, flutterWorldPos?) {
        let items = []
        for (let drop of drops) {
            let item = items.find(it => it.isSame(drop.item))
            if (!item) {
                item = new ConditionObj().init(drop.item.type, drop.item.id)
                items.push(item)
            }
            item.num += drop.getNum()

            if (drop.carriageId) {
                let carriage = gameHelper.train.getCarriageById(drop.carriageId)
                carriage.getAllDrops().remove("uid", drop.uid)
            }
        }

        let itemIds = items.map((item) => {
            let id = this.genCollectId(item.num, item)
            ut.wait(10).then(() => this.onCollectByClient(id)) //防止节点被销毁逻辑走不到，这里做个保险
            return { id, item }
        })

        this.collectDropMoney(drops).then((items: ConditionObj[]) => {
            if (!items) return
            for (let { id, item } of itemIds) {
                let it = items.find(it => it.isSame(item))
                let num = it?.num || 0
                this.onCollectBySever(id, num)
            }
        })

        if (showFlutter) {
            let worldPosition = flutterWorldPos
            if (!worldPosition) {
                let node = this.items.find(n => n.Data._click)
                if (node) {
                    worldPosition = node.convertToWorldSpaceAR(cc.v2(0, 0))
                }
            }
            if (worldPosition) {
                let offsetY = 100
                let startY = 0
                items.forEach((item, i)=>{
                    eventCenter.emit(EventType.SHOW_FLUTTER_MONEY, item, worldPosition, startY + offsetY * i)
                })
                showFlutter = false
            }
        }

        for (let { id, item } of itemIds) {
            let ds = drops.filter(d => d.item.isSame(item))
            if (item.type == ConditionType.HEART) {
                (async()=>{
                    await util.promiseMap(ds, async (drop) => {
                        let source = drop.moneySources[0]
                        let passenger = gameHelper.passenger.getPassenger(Number(source.id))
                        passenger.setHeart(0)
                        if (cc.isValid(passenger.view)) {
                            await passenger.view.Component("PassengerView").playClaimHeartAnim(drop.getNum(), showFlutter)
                        }
                    })
                    this.onCollectByClient(id)
                })()
            }
            else {
                let nodes = ds.map(drop => this.items.find(it => {
                    if (!it?.Data?.drop) {
                        cc.warn("collectDropMoney fail", it)
                        return false
                    }
                    return it.Data.drop.uid == drop.uid
                })).filter(it => cc.isValid(it))
                if (nodes.length > 0) {
                    this.playCollectAnim(nodes, id, showFlutter)
                }
            }
        }
    }

    public getAllDropsByCarriage(carriage: CarriageModel) {
        let drops = []
        drops.pushArr(carriage.getAllDrops())
        for (let passenger of carriage.getPassengers()) {
            if (passenger.hasHeart()) {
                drops.push(passenger.getDropByHeart())
            }
        }
        return drops
    }

    // 拾取掉落的钱
    public async collectDropMoney(moneyObjs: DropMoneyObj[]) {
        let data: proto.IC2S_CollectItemMessage = { carriages: [], passengerStar: 0, heart: 0 }

        for (let { moneySources, item } of moneyObjs) {
            for (let source of moneySources) {
                if (source.type == ConditionType.CARRIAGE) {
                    let carriage = data.carriages.find(c => c.id == source.id)
                    if (!carriage) {
                        carriage = { id: Number(source.id), items: [] }
                        data.carriages.push(carriage)
                    }
                    let it = carriage.items.find((it: any) => item.isSame(it))
                    if (!it) {
                        it = { type: item.type, id: Number(item.id), num: 0 }
                        carriage.items.push(it)
                    }
                    it.num += source.num
                }
                else if (source.type == ConditionType.PASSENGER) {
                    if (item.type == ConditionType.STAR_DUST) {
                        data.passengerStar += source.num
                    }
                    else {
                        data.heart += source.num
                    }
                }
            }
        }

        let msg = new proto.C2S_CollectItemMessage(data)
        const res = await gameHelper.net.request(Msg.C2S_CollectItemMessage, msg)
        const { code, carriages, passengerStar, heart } = proto.S2C_CollectItemRespMessage.decode(res)
        if (code == 0) { //todo 如果返回的值是0要不要处理
            gameHelper.world.clearOutputTime()
            let Items = []
            let addItem = (cond: ConditionObj)=>{
                let it = Items.find(it => it.isSame(cond))
                if (!it) {
                    it = new ConditionObj().init(cond.type, cond.id)
                    Items.push(it)
                }
                it.num += cond.num
            }
            for (let { id, items } of carriages) {
                let carriage = gameHelper.train.getCarriageById(id)
                for (let item of items) {
                    let cond = gameHelper.toCondition(item)
                    let outputObj = carriage.getOutputObjByCond(cond)
                    outputObj.accTotal += item.num
                    gameHelper.grantReward(cond, false)

                    addItem(cond)
                }
            }
            if (passengerStar) {
                let cond = new ConditionObj().init(ConditionType.STAR_DUST, -1, passengerStar)
                gameHelper.grantReward(cond, false)
                addItem(cond)
            }

            if (heart) {
                let cond = new ConditionObj().init(ConditionType.HEART, -1, heart)
                gameHelper.grantReward(cond, false)
                addItem(cond)
            }

            return Items
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    private async playCollectAnim(nodes: cc.Node[], collectId: number, showFlutter?) {
        let node = nodes[0]
        let item = node.Data.drop.item
        let min = MAX_VALUE, max = 0

        let target: cc.Node
        let flyStart
        let flyEnd = () => {
            this.onCollectByClient(collectId)
        }
        if (item.type == ConditionType.STAR_DUST) {
            target = eventCenter.get(EventType.GET_UI_MONEY_ICON, ConditionType.STAR_DUST);
        }
        else if (item.type == ConditionType.PROP) {
            target = eventCenter.get(NodeType.UI_BAG)
            flyStart = () => { eventCenter.emit(EventType.PLANET_GEN_BAG_ASSETS1) }
        }
        const targetPos = target.getPosition(this.tempVec1)
        const arr: { node: cc.Node, startPos: cc.Vec2, endPos: cc.Vec2, dis: number, zIndex: number }[] = []

        let cameraCtrl = cc.Camera.findCamera(node)
        for (let it of nodes) {
            this.items.remove(it)

            let tag = it.Data.drop.getTag()
            let lightArea = this.lightAreaMap[tag]
            if (lightArea) {
                let lightNodes = lightArea.children.filter(n => n.Data == it)
                for (let node of lightNodes) {
                    node.active = false
                }
            }

            // 计算位置
            it.stopAllActions()
            it.Child("touch")?.Component(ClickTouchCmpt)?.off()
            it.removeComponent("SpeedUpTimeScaleCmpt")
            it.removeComponent("TimeScaleCmpt")
            const hh = it.height * 0.5
            const wc = it.convertToWorldSpaceAR(cc.Vec2.ZERO, this.tempVec)
            const pos: cc.Vec2 = cameraCtrl.getWorldToScreenPoint(wc, this.tempVec) as any
            if (pos.x < 0 || pos.x > cc.winSize.width) {  //屏幕外的直接消失
                resHelper.putNode(it)
                continue
            }
            const startPos = target.parent.convertToNodeSpaceAR(pos)
            startPos.y += hh * (cameraCtrl.zoomRatio - 1)
            const endPos = cc.v2(targetPos.x, targetPos.y - hh)
            const dis = startPos.sub(endPos, this.tempVec2).mag();
            (dis < min) && (min = dis);
            (dis > max) && (max = dis);
            arr.push({ node: it, startPos: startPos, endPos: endPos, dis: dis, zIndex: 1 })
        }

        if (showFlutter) {
            let fluttersMap = {}
            for (let it of nodes) {
                let tag = it.Data.drop.carriageId
                if (!fluttersMap[tag]) fluttersMap[tag] = { node: it, num: 0 }
                if (it.Data._click) {
                    fluttersMap[tag].node = it
                }
                fluttersMap[tag].num += it.Data.drop.getNum()
            }
    
            for (let key in fluttersMap) {
                let { node, num } = fluttersMap[key]
                let worldPosition = node.convertToWorldSpaceAR(cc.v2(0, node.height * 0.5))
                let cond = new ConditionObj().init(item.type, item.id, num)
                eventCenter.emit(EventType.SHOW_FLUTTER_MONEY, cond, worldPosition)
            }
        }

        // 排序zindex
        arr.sort((a, b) => a.node.zIndex - b.node.zIndex).forEach((m, i) => m.zIndex = i + 1)
        // 根据距离排序
        arr.sort((a, b) => a.dis - b.dis)
        const maxDis = max - min
        // 播放飞到UI
        // audioMgr.playSFX('common/sound11', { volume: 0.5 })
        flyStart && flyStart()
        await ut.promiseMap(arr, (async (data, i) => {
            const it = data.node
            it.parent = target.parent
            it.zIndex = data.zIndex
            it.setPosition(data.startPos)

            const body = it.Child('body')
            body.scale *= cameraCtrl.zoomRatio

            animHelper.stopTrailingParticle(body)

            await ut.wait(maxDis <= 0 ? 0 : (data.dis - min) / maxDis * 0.06)
            body.Component(cc.Animation).stop()
            body.y = 0

            animHelper.playTrailingParticle(body)
            // 起飞
            return animHelper.flyOneMoney(it, data.endPos, body, i)
        }))
        flyEnd && flyEnd()
    }

    public collectAllByCarraige(carriage: CarriageModel) {
        let item = this.items.find(item => item.Data.map == carriage)
        if (item) {
            this.onClickMoney(item)
        }
    }

    public getItemsByCarriage(carriage: CarriageModel) {
        return this.items.filter(item => item.Data.map == carriage)
    }

    public update() {
        // this.items.forEach(m => {
        //     if (m.isValid && m.Data && m.Data.drop) {
        //         const zindex = m.Data.drop.zIndex
        //         if (m.Data.init && m.zIndex !== zindex) {
        //             m.zIndex = zindex
        //         }
        //     }
        // })
    }

    public clean() {
        while (this.items.length > 0) {
            this.cleanNode(this.items.pop())
        }
    }

    private cleanNode(node: cc.Node) {
        if (node && node.isValid) {
            node.off("click")
            resHelper.putNode(node)
        } else {
            node?.destroy()
        }
    }

    public hideAll(val: boolean) {
        this.items.forEach(m => m.active = !val)
    }

    public getClientCurreny(type) {
        let sum = 0
        for (let key in this.collectMap) {
            let info = this.collectMap[key]
            if (info.item.type == type && info.client) {
                sum += info.count
            }
        }
        return sum
    }

    public genCollectId(count, item: ConditionObj) {
        let id = this.collectId++
        this.collectMap[id] = { count, client: true, item }
        return id
    }

    public onCollectBySever(collectId: number, num: number) {
        let info = this.collectMap[collectId]
        if (!info) return
        if (info.sent) { //如果先走客户端，这里做一下重新更新一下ui视图
            delete this.collectMap[collectId]
            let item = info.item
            if (item.type == ConditionType.STAR_DUST) {
                eventCenter.emit(EventType.UPDATE_STARDUST, 0)
            }
            else if (item.type == ConditionType.HEART) {
                eventCenter.emit(EventType.UPDATE_HEART, 0)
            }
        }
        else { //先走服务器，把值变成正确的
            info.count = num
            info.client = false
        }
    }

    public onCollectByClient(collectId: number) {
        let info = this.collectMap[collectId]
        if (!info) return
        if (!info.client) { //先走服务器
            delete this.collectMap[collectId]
        }
        else {
            info.sent = true
        }
        let item = info.item
        if (item.type == ConditionType.STAR_DUST) {
            eventCenter.emit(EventType.UPDATE_STARDUST, info.count, true)
        }
        else if (item.type == ConditionType.HEART) {
            eventCenter.emit(EventType.UPDATE_HEART, info.count, true)
        }
        else if (item.type == ConditionType.PROP) {
            eventCenter.emit(EventType.PLANET_GEN_BAG_ASSETS2, [new ConditionObj().init(item.type, item.id, info.count)], animHelper.planetGenStart())
        }
    }

    public getCondsByDrops(drops: DropMoneyObj[]) {
        let items = []
        for (let drop of drops) {
            let item = items.find(it => it.isSame(drop.item))
            if (!item) {
                item = new ConditionObj().init(drop.item.type, drop.item.id)
                items.push(item)
            }
            item.num += drop.getNum()
        }
        return items
    }
}

export const dropItemHelper = new DropItemHelper()

if (CC_DEV) {
    window["dropItemHelper"] = dropItemHelper
}