import { DIR_POINTS_4, DIR_POINTS_8 } from "../constant/Constant"
import { RoleDir } from "../constant/Enums"

// 一个节点
class Node {
    public uid: string = '0_0'
    public point: cc.Vec2 = cc.v2(0, 0)
    public parent: Node = null
    public F: number = 0
    public G: number = 0
    public H: number = 0
    public ignoreCheck: boolean = false //是否忽略检测
    public direction: RoleDir = RoleDir.RIGHT // agent到达此节点时的朝向
    public get x() { return this.point.x }
    public get y() { return this.point.y }
    public fromDir: cc.Vec2 = null
    public init(x: number, y: number, direction?: RoleDir) {
        this.uid = x + '_' + y + '_' + (direction !== undefined ? direction : RoleDir.RIGHT)
        this.point.set2(x, y)
        this.parent = null
        this.F = 0
        this.G = 0
        this.H = 0
        this.ignoreCheck = false
        this.fromDir = null
        this.direction = direction !== undefined ? direction : RoleDir.RIGHT
        return this
    }
    public has(x: number, y: number, direction: RoleDir) {
        return this.point.x === x && this.point.y === y && this.direction === direction
    }
    public updateParent(node: Node, tag: number) {
        this.parent = node
        this.G = node.G + tag
        this.F = this.H + this.G
    }
}

class AstarNodePool {
    private nodePool: Node[] = []// 节点对象池
    public newNode(): Node {
        if (this.nodePool.length > 0) {
            return this.nodePool.pop()
        }
        return new Node()
    }
    public removeNode(node: Node) {
        this.nodePool.push(node)
    }
}
const commonNodePool = new AstarNodePool()

/**
 * A星
 */
export default class AStar {
    private tempPathPoints: cc.Vec2[] = []
    private tempDisV2: cc.Vec2 = cc.v2()
    private nodePool: Node[] = []// 节点对象池
    private nodePoolIdx: number = 0
    private opened: Node[] = []
    private closed: any = {}
    private dirPoints: any[] = []
    private dirCount: number = 0
    public checkCanPass: Function = null// 检测当前点是否可以通过
    public area: any = null
    public mapHeight: number = 0
    public mapWidth: number = 0
    public checkEndPoint: Function = null// 检测当前点是否是终点
    public turnCost: number = 0.1; // 转身损耗

    constructor(checkCanPass?: Function, mapWidth: number = 0, mapHeight: number = 0, checkEndPoint?: Function, turnCost: number = 1) {
        this.checkCanPass = checkCanPass;
        this.mapHeight = mapHeight;
        this.mapWidth = mapWidth;
        this.checkEndPoint = checkEndPoint;
        this.setDir(8);
        this.turnCost = turnCost;
    }
    public setDir(cnt: number) {
        this.dirPoints = cnt === 4 ? DIR_POINTS_4 : DIR_POINTS_8
        this.dirCount = this.dirPoints.length
    }
    public clean() {
        this.checkCanPass = null
        this.area = null
        this.opened = null
        this.closed = null
        this.dirPoints = null
        this.tempPathPoints = null
        this.nodePool.forEach(node => {
            commonNodePool.removeNode(node)
        });
        this.nodePool.length = 0
        this.nodePool = null
    }
    private reset() {
        this.nodePoolIdx = 0
        this.opened.length = 0
        this.closed = {}
        this.tempPathPoints.length = 0
        let count = Math.floor(this.nodePool.length * 0.5)
        for (let i = 0; i < count; i++) {
            let node = this.nodePool.pop()
            commonNodePool.removeNode(node)
        }
    }
    // 新建一个节点
    private newNode(x: number, y: number, direction?: RoleDir) {
        let node = this.nodePool[this.nodePoolIdx++]
        if (!node) {
            node = commonNodePool.newNode()
            this.nodePool.push(node)
        }
        return node.init(x, y, direction)
    }
    // 是否在范围内
    private inMapRange(point: cc.Vec2) {
        return point.x >= 0 && point.x < this.mapWidth && point.y >= 0 && point.y < this.mapHeight
    }
    // 检查拐角是否有障碍 只用于8个方向行走
    private checkCornerHasTile(x1: number, y1: number, x2: number, y2: number, direction: RoleDir): boolean {
        return this.checkCanPass(x1, y1, direction) && this.checkCanPass(x2, y2, direction)
    }
    private *searh(start: cc.Vec2, end?: cc.Vec2, initialDirection: RoleDir = RoleDir.RIGHT) {
        if (end && start.equals(end)) {
            return [start];
        }
        if (end && (!this.inMapRange(end))) {
            return []
        }
        this.reset()
        // 把第一个点装进开起列表
        this.opened.push(this.newNode(start.x, start.y, initialDirection))
        let cnt = 0
        while (this.opened.length > 0) {
            const node = this.opened.shift()
            if (this.checkEndPoint) {
                if (this.checkEndPoint(node.point.x, node.point.y)) {
                    return this.genPoints(node)
                }
            } else {
                if (node.point.equals(end)) {
                    return this.genPoints(node)
                }
            }
            this.closed[node.uid] = true
            let has = false
            for (let i = 0; i < this.dirCount; i++) {
                const d = this.dirPoints[i]
                const x = node.point.x + d.point.x;
                const y = node.point.y + d.point.y;
                // 根据移动方向计算agent朝向
                let moveDirection = node.direction; // 默认继承当前节点方向
                if (d.point.x > 0) {
                    moveDirection = RoleDir.RIGHT;
                } else if (d.point.x < 0) {
                    moveDirection = RoleDir.LEFT;
                }
                // x方向无变化时保持当前方向
                const nextUid = x + '_' + y + '_' + moveDirection;
                if (node.ignoreCheck) {
                } else if ((end && !end.equals2(x, y)) && (!this.checkCanPass(x, y, moveDirection) || this.closed[nextUid])) {
                    continue
                } else if (this.dirCount === 8 && d.tag !== 1 && !this.checkCornerHasTile(x, node.y, node.x, y, moveDirection)) {// 优化拐歪
                    continue
                }
                has = true
                // 方向切换时加上转身损耗
                let turnStepCost = (node.direction !== moveDirection) ? this.turnCost : 0;
                let dTag = d.tag + turnStepCost;
                // 如果开启列表中已经有了 那么看现在这个节点到那的距离是否短一点
                const it = this.opened.find(m => m.point.equals2(x, y) && m.direction === moveDirection)
                if (it) {
                    if (node.G + dTag < it.G) {
                        it.updateParent(node, dTag)
                        it.fromDir = d
                        it.direction = moveDirection // 更新节点的方向
                    }
                } else {
                    const temp = this.newNode(x, y, moveDirection)
                    temp.fromDir = d
                    // 启发函数H加上终点方向与当前方向的转身损耗
                    if (end) {
                        const targetDir = RoleDir.RIGHT; // 可根据实际需求调整
                        const turnCost = (temp.direction !== targetDir) ? this.turnCost : 0;
                        temp.H = end.sub(temp.point, this.tempDisV2).mag() + turnCost;
                    } else {
                        temp.H = 1;
                    }
                    temp.updateParent(node, dTag)
                    this.opened.push(temp)
                }
            }
            // 如果当前生在障碍中 那么下次就忽略障碍
            if (!has && !this.checkCanPass(node.x, node.y, node.direction)) {
                this.opened.unshift(node)
                node.ignoreCheck = true
            } else {
                node.ignoreCheck = false
            }
            this.opened.sort((a, b) => a.F - b.F)
            if (++cnt > 50) {
                cnt = 0
                yield
            }
            if (!this.opened) {
                return []
            }
        }
        return []
    }
    public async searchPath(start: cc.Vec2, end?: cc.Vec2, initialDirection: RoleDir = RoleDir.RIGHT): Promise<cc.Vec2[]> {
        let search = this.searh(start, end, initialDirection)
        while (true) {
            let { value, done } = search.next()
            if (done) {
                return value
            }
            await ut.waitNextFrame()
        }
    }
    public searchPathSync(start: cc.Vec2, end?: cc.Vec2, initialDirection: RoleDir = RoleDir.RIGHT): cc.Vec2[] {
        let search = this.searh(start, end, initialDirection)
        while (true) {
            let { value, done } = search.next()
            if (done) {
                return value
            }
        }
    }
    private genPoints(node: Node) {
        this.tempPathPoints.length = 0
        while (node.parent !== null) {
            this.tempPathPoints.push(node.point)
            node = node.parent
        }
        return this.tempPathPoints.reverse()
    }
    //有点问题，先暂存
    private pathOptimize(paths: any[]) {
        let checkPassDirectly = (p1, p2) => {
            let minX = Math.min(p1.x, p2.x)
            let maxX = Math.max(p1.x, p2.x)
            let minY = Math.min(p1.y, p2.y)
            let maxY = Math.max(p1.y, p2.y)
            for (let x = minX; x <= maxX; x++) {
                for (let y = minY; y <= maxY; y++) {
                    if (this.checkCanPass(x, y)) continue
                    let rect = cc.rect(x, y, 1, 1)
                    if (cc.Intersection.lineRect(p1, p2, rect)) {
                        return false
                    }
                }
            }
            return true
        }

        let newPaths = [paths[0]]
        for (let i = 0; i < paths.length - 1;) {
            let nextIndex = i + 1
            for (let j = nextIndex + 1; j < paths.length; nextIndex++, j++) {
                if (!checkPassDirectly(paths[i], paths[j])) {
                    break
                }
            }
            newPaths.push(paths[nextIndex])
            i = nextIndex
        }
        return newPaths
    }
}