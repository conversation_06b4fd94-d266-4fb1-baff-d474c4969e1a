import { Condition, PlanetAreaCfg, PlanetCfg, TaskCfg, UnlockFuncTarget } from "../constant/DataType"
import { ConditionType, PlanetNodeType, UIFunctionType } from "../constant/Enums"
import { cfgHelper } from "./CfgHelper"
import { gameHelper } from "./GameHelper"
import { viewHelper } from "./ViewHelper"
import EventType from "../event/EventType"
import UIDisplayCmpt from "../../view/cmpt/ui/UIDisplayCmpt"
import PlanetNodeModel from "../../model/planet/PlanetNodeModel"

//功能解锁小助手
class UnlockHelper {
    private dicTaskFuncition: { [key: string]: string }//缓存功能解锁key所对应的任务
    private dicGuideFuncition: { [key: string]: { id: number, index: number } }//缓存功能解锁key所对应的新手
    private bolUseReal: boolean
    public needShow = []
    private guideUnlockFunc: { [key: string]: boolean } = {}

    public init() {
        eventCenter.on(EventType.PLANET_NODE_COMPLETE, (planetId, node?: PlanetNodeModel) => {
            let uid = node?.uid
            if (!uid) return
            if (node.getMap().getBranch()) return
            this.checkUnlockFunc(m => m.type == ConditionType.PLANET_NODE && m.id == uid)
            if (node.nodeType == PlanetNodeType.CHECK_POINT) {
                this.checkUnlockFunc(m => m.type == ConditionType.PLANET_BATTLE_NODE && m.id == node.getId())
            }
        })
        eventCenter.on(EventType.PLANET_COMPLETE, (id: number) => {
            this.checkUnlockFunc(m => m.type == ConditionType.PLANET_COMPLETE && m.id == id)
        })
        eventCenter.on(EventType.UNLOCK_PASSENGER, () => {
            let num = gameHelper.passenger.getPassengers().length
            this.checkUnlockFunc(m => m.type == ConditionType.PASSENGER && m.num == num)
        })

        eventCenter.on(EventType.GUIDE_UNLOCK_FUNTION, (type: UIFunctionType) => {
            this.guideUnlockFunc[type] = true
        })
    }

    public initFunctionNode(getFun: (type: UIFunctionType) => cc.Node, lockFunc?: (show: boolean, node: cc.Node, type: UIFunctionType) => void) {
        for (let _type in UIFunctionType) {
            let type = _type as UIFunctionType
            let node = getFun(type)
            if (!node) continue
            this.setNodeUnlock(node, type, lockFunc)
        }
    }
    public setNodeUnlock(node: cc.Node, type: UIFunctionType, lockFunc?: (show: boolean, node: cc.Node, type: UIFunctionType) => void) {
        if (!node) return
        node.Component(cc.Widget)?.updateAlignment()
        let show = this.isUnlockFunction(type)
        if (lockFunc) {
            lockFunc(show, node, type)
        }
        else {
            let cmpt = node.Component(UIDisplayCmpt)
            if (cmpt) {
                cmpt.display(show)
            }
            else {
                node.active = show
            }
        }
    }

    //需要同时满足，各个表的功能判断
    public isUnlockFunction(type: UIFunctionType) {
        return this.isGuideOverByUnlockFunc(type) && this.isUnlockFuncByMisc(type)
    }

    //根据guide表判断功能解锁；用于视图上
    public isGuideOverByUnlockFunc(key: UIFunctionType) {
        if (!this.dicGuideFuncition) {
            this.setDicFuncGuide()
        }
        if (this.guideUnlockFunc[key]) return true
        let data = this.dicGuideFuncition[key]
        if (data) {
            return gameHelper.guide.isStepComplete(data.id, data.index)
        }
        return true
    }

    //根据unlockFunc表判断功能解锁；用于逻辑上
    public isUnlockFuncByMisc(type: UIFunctionType) {
        let info = cfgHelper.getUnlockFunDatas().find(info => info.type == type)
        if (!info?.isShow) {
            if (!gameHelper.openGuide) return true
        }
        switch (type) {
            case UIFunctionType.PLAY_ORE:
                return gameHelper.ore.isUnlock
            case UIFunctionType.PLAY_INSTANCE:
                return gameHelper.instance.isUnlock
            case UIFunctionType.PLAY_BLACKHOLE:
                return gameHelper.blackHole.isUnlock
        }
        if (!info) return true
        let ary = info.target
        if (!ary) return true
        for (const one of ary) {
            if (!this.completeOneTarget(one)) {
                return false
            }
        }
        return true
    }

    private completeOneTarget(target: UnlockFuncTarget) {
        let type = target.type
        switch (type) {
            case ConditionType.PLANET_NODE:
                return gameHelper.planet.isPassNode(target.id)
            case ConditionType.PLANET_BATTLE_NODE:
                return gameHelper.planet.isPassBattle(target.id)
            case ConditionType.PLANET_COMPLETE:
                let pMod = gameHelper.planet.getPlanet(target.id)
                return pMod && pMod.isDone()
            case ConditionType.BUILD_ID:
                return gameHelper.train.isUnlockBuild(target.id)
            case ConditionType.PASSENGER:
                return gameHelper.passenger.getPassengers().length >= target.num
            case ConditionType.INSTANCE_LEVEL:
                return gameHelper.instance.currentLevel >= target.num
            default:
                cc.error('completeOneTarget unknown type', type)
                return false
        }
    }

    // 只适用只有一个目标的
    public checkUnlockFunc(checkFun: (one: UnlockFuncTarget) => boolean) {
        let unlockFuncs = cfgHelper.getUnlockFunDatas().filter(info => info.target?.length == 1 && checkFun(info.target[0]))
        for (let fn of unlockFuncs) {
            eventCenter.emit(EventType.UNLOCK_FUNTION, fn.type)

            if (fn.isShow) {
                this.addShow(fn.type)
            }
        }
    }

    public getTaskIdByFuncKey(key: UIFunctionType) {
        if (!this.dicTaskFuncition) {
            this.setDicFuncTask()
        }
        return this.dicTaskFuncition[key]
    }
    public useRealTime(): Boolean {
        if (this.bolUseReal == undefined) {
            this.bolUseReal = this.checkEndTime()
            if (!this.bolUseReal) {
                eventCenter.on(EventType.GUIDE_STEP_END, this.updateRealTime, this)
            }
        }
        return this.bolUseReal
    }
    private updateRealTime() {
        this.bolUseReal = this.checkEndTime()
        if (this.bolUseReal) {
            eventCenter.off(EventType.GUIDE_STEP_END, this.updateRealTime, this)
            gameHelper.guide.setGuideTime(this.getEndingTimeId())
        }
    }
    private checkEndTime() {
        let id = this.getEndingTimeId()
        return gameHelper.guide.isStepComplete2(id)
    }
    // 假时间结束步骤id
    private getEndingTimeId() {
        return cfgHelper.getMiscData("guide").endingTimeId
    }
    private getKeyIfFunctionReward(reward?: Condition[]) {
        if (reward) {
            for (const cond of reward) {
                if (cond.type == ConditionType.FUNCTION) {//功能(解锁)
                    return cond.id
                }
            }
        }
    }
    private setDicFuncTask() {
        let dic = {}
        let all = assetsMgr.getJson<TaskCfg>('Task').datas
        all.forEach((cfg) => {
            let key = this.getKeyIfFunctionReward(cfg.reward)
            if (key) {
                if (dic[key]) {
                    twlog.error(`Task.json中功能解锁${key}存在重复`)
                }
                dic[key] = cfg.id
            }
        })
        this.dicTaskFuncition = dic
    }
    private setDicFuncGuide() {
        let dic = {}
        let all = cfgHelper.getGuideCfg()
        for (const id in all) {
            all[id].forEach((cfg, index) => {
                let key = cfg.unlockFunc
                if (key) {
                    if (dic[key]) {
                        twlog.error(`Guide.json中功能解锁${key}存在重复`)
                    }
                    dic[key] = { id: Number(id), index }
                }
            })
        }
        this.dicGuideFuncition = dic
    }

    public unlockFunction(getFun: (type: UIFunctionType) => cc.Node, type) {
        let node = getFun(type)
        if (!node) return
        let cmpt = node.Component(UIDisplayCmpt)
        if (cmpt) {
            cmpt.display(true)
        }
        else {
            node.active = true
        }
    }

    private _getUnlockDescription(type: UIFunctionType): { key: string, params?: any[] } {
        let info = cfgHelper.getUnlockFunDatas().find(info => info.type == type)
        if (!info) return
        if (info.lockTip)
            return { key: info.lockTip }
        let ary = info.target
        if (!ary) return
        let target = ary[0]
        switch (target.type) {
            case ConditionType.PLANET_NODE:
                return this.getKPPlanetNode(target.id)
            case ConditionType.PLANET_COMPLETE:
                let cfg1 = assetsMgr.getJsonData<PlanetCfg>("Planet", target.id)
                let str1 = cfg1 ? assetsMgr.lang(cfg1.name) : ''
                return { key: 'unlockFunc_tips_4', params: [str1] }
            case ConditionType.BUILD_ID:
                let cfg2 = cfgHelper.getBuildById(target.id)
                let str2 = cfg2 ? assetsMgr.lang(cfg2.name) : ''
                return { key: 'unlockFunc_tips_1', params: [str2] }
            case ConditionType.PASSENGER:
                return { key: 'unlockFunc_tips_2', params: [target.num] }
            default:
                cc.error('_getUnlockDescription unknown type', type)
                return
        }
    }
    private getKPPlanetNode(tagId: string): { key: string, params?: any[] } {
        let [_planetId, _mapId, _index] = tagId.split('-')
        let planetId = Number(_planetId)
        let mapId = Number(_mapId)
        let index = Number(_index)

        let cfg1 = assetsMgr.getJson<PlanetAreaCfg>("PlanetArea").datas.find(a => a.planetId == planetId && a.maps.includes(mapId))
        let str1 = cfg1 ? assetsMgr.lang(cfg1.name) : ''
        let progress = 0
        for (let id of cfg1.maps) {
            let json = cfgHelper.getPlanetJsonData(planetId, "PlanetMap", id)
            let nodes = json.node
            if (id == mapId) {
                nodes = nodes.slice(0, index)
            }
            progress += nodes.filter(n => n.type == PlanetNodeType.CHECK_POINT).length
            if (id == mapId) {
                break
            }
        }
        let str2 = assetsMgr.lang('name_chapter_planet', progress)
        return { key: 'unlockFunc_tips_5', params: [str1, str2] }
    }

    /**解锁说明*/
    public getUnlockDescription(type: UIFunctionType) {
        return this._getUnlockDescription(type) || { key: 'unlockFunc_tips_6' }
    }

    public checkLockAndAlert(type: UIFunctionType) {
        if (!this.isUnlockFunction(type)) {
            let { key, params } = this.getUnlockDescription(type)
            viewHelper.showAlert(key, { params })
            return true
        }
        return false
    }

    public canOutput() {
        return unlockHelper.isGuideOverByUnlockFunc(UIFunctionType.STAR)
    }


    public addShow(type: UIFunctionType) {
        if (this.needShow.includes(type)) return
        this.needShow.push(type)
    }

    public async checkAndShow() {
        for (let i = this.needShow.length - 1; i >= 0; i--) {
            let type = this.needShow[i]
            console.log("checkAndShow", type)
            viewHelper.showPnl("planet/PlanetPlayUnlockPnl", type)
            await eventCenter.wait(EventType.GUIDE_UNLOCK_FUNTION, (t) => {
                return t == type
            })
            this.needShow.splice(i, 1)
        }
    }
}
export const unlockHelper = new UnlockHelper()

window["unlockHelper"] = unlockHelper