import { res<PERSON><PERSON><PERSON> } from "./ResHelper"
import { ConditionType, ItemID } from "../constant/Enums"
import CurveLerp from "../curve/CurveLerp"
import AutoReleaseCmt from "../../../core/component/AutoReleaseCmt"
import { gameHelper } from "./GameHelper"
import ConditionObj from "../../model/common/ConditionObj"
import { viewHelper } from "./ViewHelper"
import NodeType from "../event/NodeType"
import { FingerGuide } from "../constant/DataType"
import { mapHelper } from "./MapHelper"
import { uiHelper } from "./UIHelper"

/**
 * 动画帮助
 */
class AnimHelper {

    private tempVec: cc.Vec2 = cc.v2()

    private curveLerps = new Map<string, CurveLerp>()

    public async flyOneMoney(it: cc.Node, endPos: cc.Vec2, anim?: cc.Node, i: number = 0) {
        return new Promise<void>(resolve => {
            const startPos = it.getPosition(this.tempVec)
            let w = Math.floor((endPos.x - startPos.x) / 3), h = Math.floor((endPos.y - startPos.y) / 3)
            const a = cc.v2(startPos.x + w + ut.randomRange(-80, 80), startPos.y + h + ut.randomRange(-80, 80))
            const b = cc.v2(startPos.x + (w * 2) + ut.randomRange(-80, 80), startPos.y + (h * 2) + ut.randomRange(-80, 80))
            anim = anim || it
            cc.tween(it)
                .bezierTo(0.6, a, b, endPos)  //星尘飞行时间
                .call(() => {
                    resolve()
                    anim.stopAllActions()
                    resHelper.putNode(it)
                    // cc.tween(anim).to(0.3, { opacity: 0, scale: 1.1 }).call(() => resHelper.putNode(it)).start()
                }).start()
            cc.tween(anim).to(0.5, { scale: 1 }).start()
        })
    }

    public async flyPassengerHeart(it: cc.Node, endPos: cc.Vec2) {
        return new Promise<void>(resolve => {
            const startPos = it.getPosition()
            let w = Math.floor((endPos.x - startPos.x) / 3), h = Math.floor((endPos.y - startPos.y) / 3)
            const a = startPos
            const b = cc.v2(startPos.x + (w * 2) + ut.randomRange(-80, 80), startPos.y + (h * 2) + ut.randomRange(-80, 80))
            cc.tween(it)
                .bezierTo(0.6, a, b, endPos)  //爱心飞行时间
                .call(() => {
                    resolve()
                    it.destroy()
                }).start()
        })
    }

    public async flyBezierToBag(it: cc.Node, target: cc.Node) {
        let t = 0.8
        let startPos = it.getPosition()
        let targetPos = target.getPosition()
        let vec = targetPos.sub(startPos)
        let targetPos1 = cc.v2(startPos.x + vec.x * 0.2, startPos.y + vec.y * 1.5)
        let targetPos2 = cc.v2(startPos.x + vec.x * 0.6, startPos.y + vec.y * 0.5)
        cc.tween(it).to(t, { scale: 0.3 }).start()
        await cc.tween(it).bezierTo(t, targetPos1, targetPos2, targetPos).promise()
    }

    public createSpriteOnBag(centerPos: cc.Vec2, target: cc.Node) {
        let it = new cc.Node()
        it.parent = cc.find("Canvas")
        it.setPosition(centerPos)
        it.addComponent(cc.Sprite)
        ut.convertParent(it, target.parent)
        return it
    }

    private async waitLoadSpf(item: ConditionObj, it: cc.Node, tag: string = mc.currWindName) {
        if (item.isPropTimeMail()) {
            await resHelper.setSpf("timeMail/bb_xinjian", it, tag)
        } else {
            await resHelper.loadIconByCondInfo(item, it, tag)
        }
    }

    public async flyToBag(item: ConditionObj, centerPos: cc.Vec2, tag: string) {
        let target: cc.Node = eventCenter.get(NodeType.UI_BAG)
        let it = this.createSpriteOnBag(centerPos, target)
        await this.waitLoadSpf(item, it, tag)
        if (!cc.isValid(target)) return
        await this.flyPassengerHeart(it, target.getPosition())
    }

    public async flyMailToBag(item: ConditionObj, centerPos: cc.Vec2) {
        let target: cc.Node = eventCenter.get(NodeType.UI_BAG)
        let it = this.createSpriteOnBag(centerPos, target)
        await this.waitLoadSpf(item, it)
        await cc.tween(it).set({ scale: 0 }).to(0.2, { scale: 1 }).promise()
        await ut.wait(0.5)
        await this.flyBezierToBag(it, target)
        it.destroy()
        await this.scaleBag(target)
    }

    public async scaleBag(target: cc.Node) {
        cc.Tween.stopAllByTarget(target)
        await cc.tween(target).to(0.15, { scale: 1.3 }).delay(0.04).to(0.1, { scale: 0.85 }).to(0.05, { scale: 1 }).promise()
    }

    // 飘点击时候的钱
    public async playFlutterMoney(cond: ConditionObj, root: cc.Node, position: cc.Vec2, key: string) {
        const it: cc.Node = await resHelper.getNode('flutter/FLUTTER_TEXT_2', key)
        if (!root.isValid) {
            resHelper.putNode(it)
            return twlog.info('playFlutterMoney !root.isValid')
        }
        if (!cc.isValid(it)) {
            return
        }
        let index = 0
        if (cond.type == ConditionType.HEART) {
            index = 1
        }
        else if (cond.type == ConditionType.DIAMOND) {
            index = 5
        }
        else if (cond.type == ConditionType.PROP) {
            if (cond.id == ItemID.WATER) {
                index = 2
            }
            else if (cond.id == ItemID.ELECTRIC) {
                index = 3
            }
            else if (cond.id == ItemID.VITALITY) {
                index = 4
            }
        }
        let icon = it.Child('icon')
        icon.Component(cc.MultiFrame).setFrame(index)
        icon.scale = viewHelper.getAdaptScale(icon.getContentSize(), cc.size(100, 80))
        it.parent = root
        it.Child('count', cc.Label).string = `+${uiHelper.getShowNum(cond)}`
        it.setPosition(position)
        let startY = position.y
        it.opacity = 0
        cc.tween(it)
            .delay(0.1)
            .to(0.3, { y: startY + 120, opacity: 255 }, { easing: cc.easing.sineOut })
            .delay(0.9)
            .to(0.1, { y: startY + 150, opacity: 0 }, { easing: cc.easing.sineIn })
            .call(() => resHelper.putNode(it)).start()
    }

    public getCurveLerpValue(name: string, ratio: number = 0) {
        let curveLerp = this.curveLerps.get(name)
        if (!curveLerp) {
            let anim = assetsMgr.getAnim(name)
            curveLerp = new CurveLerp().init(anim)
            this.curveLerps.set(name, curveLerp)
        }
        return curveLerp.getValue(ratio)
    }

    public playContinueBlink(node: cc.Node) {
        let base = 0
        node.opacity = base
        cc.Tween.stopAllByTarget(node)
        cc.tween(node).to(0.4, { opacity: 255 }).delay(1.2).to(0.6, { opacity: base }).delay(0.2).union().repeatForever().start()
    }

    public async playFlutterNum(name: string, val: number, root: cc.Node, position: cc.Vec2, key: string, height: number = 70) {
        const it = await resHelper.getNode(`flutter/${name}`, key)
        if (!root.isValid) {
            resHelper.putNode(it)
            return twlog.info(`${name} !root.isValid`)
        }
        if (!it || !it.isValid) {
            return
        }
        it.parent = root
        let sign = ""
        if (val > 0) {
            sign = "-"
        }
        it.Child('val', cc.Label).string = sign + val
        it.setPosition(position)
        it.opacity = 255
        await this.playFlutter(it, height)
        resHelper.putNode(it)
    }

    public async playFlutter(it: cc.Node, height: number = 70) {
        it.opacity = 255
        return cc.tween(it)
            .by(0.3, { y: height * 0.4 }, { easing: cc.easing.sineOut })
            .by(1, { y: height * 0.6, opacity: -it.opacity }, { easing: cc.easing.sineIn })
            .promise()
    }

    public async showWeakGuideFinger(root: cc.Node, guide: FingerGuide, pos?: cc.Vec2) {
        let weakGuide = gameHelper.weakGuide
        weakGuide.show = true
        let uid = ++weakGuide.uid
        let weakGuideFinger = weakGuide.finger
        if (weakGuideFinger && cc.isValid(weakGuideFinger)) {
            weakGuideFinger.parent = root
        } else {
            let url = 'guide/weak_guide_finger'
            let tag = 'weakGuide'
            let pre = await assetsMgr.loadTempRes(url, cc.Prefab, tag)
            if (uid != weakGuide.uid || !weakGuide.show) return
            if (!weakGuide.finger && cc.isValid(root)) {
                weakGuideFinger = cc.instantiate2(pre, root)
                weakGuideFinger.addComponent(AutoReleaseCmt).init(url, tag)
            }
        }
        weakGuide.finger = weakGuideFinger
        weakGuideFinger.Component("FingerWdtCtrl").init(guide, pos)
    }

    // 移除弱引导箭头
    public removeWeakGuideFinger() {
        let weakGuide = gameHelper.weakGuide
        weakGuide.show = false
        let weakGuideFinger = weakGuide.finger
        if (weakGuideFinger) {
            if (weakGuideFinger.isValid) {
                weakGuideFinger.destroy()
            }
            weakGuide.finger = null
        }
    }

    public async playClickShake(node) {
        cc.Tween.stopAllByTarget(node)
        let times = [
            { "time": 0.0505, "x": -10 },
            { "time": 0.1172, "x": 10 },
            { "time": 0.1839, "x": -5 },
            { "time": 0.2505, "x": 5 },
        ]
        for (let i = 0; i < times.length; i++) {
            let { time, x } = times[i]
            let preTime = times[i - 1]?.time || 0
            let t = (time - preTime) * 0.8
            await cc.tween(node).by(t, { x }).promise()
        }
    }

    // 播放拖尾粒子效果(支持子节点下有多个同名拖尾粒子)
    public playTrailingParticle(body: cc.Node, keyParticle: string = 'lizi', keyMotionSteak: string = 'motion') {
        body.children.forEach(node => {
            if (node.name == keyParticle) {
                node.active = true
                let ps = node.Component(cc.ParticleSystem)
                ps.positionType = cc.ParticleSystem.PositionType.FREE//对于刚创建的粒子，这里赋值会早于_initWithDictionary里面的赋值，故对于这样的情况在编辑器里使用custom修改此type
                ps.resetSystem()
            } else if (node.name == keyMotionSteak) {
                node.active = true
            }
        })
    }
    public stopTrailingParticle(body: cc.Node, keyParticle: string = 'lizi', keyMotionSteak: string = 'motion') {
        body.children.forEach(node => {
            if (node.name == keyParticle) {
                node.active = false
                node.Component(cc.ParticleSystem).stopSystem()
            } else if (node.name == keyMotionSteak) {
                node.active = false
            }
        })
    }
    // 简易动画:把小气泡一下子ber出来
    public showBubbleAction(node: cc.Node, scale: number = 1) {
        node.scale = 0
        cc.Tween.stopAllByTarget(node)
        cc.tween(node).to(0.2, { scale: scale }).start()
    }
    // 只有从隐藏到显现才会有动画
    public showBubbleByActive(node: cc.Node, scale?: number) {
        if (node.active)
            return
        node.active = true
        this.showBubbleAction(node, scale)
    }

    private aniSid: number = 0
    private generateAniSid() {
        let sid = this.aniSid++
        if (sid > 10000) {
            sid = this.aniSid = 0
        }
        return sid
    }
    private planetGenDic: object = {}
    public planetGenStart(): number {
        let key = this.generateAniSid()
        this.planetGenDic[key] = true
        return key
    }
    public planetGenEnd(key: number) {
        delete this.planetGenDic[key]
    }
    public isPlanetGenStop(key: number): boolean {
        return this.planetGenDic[key] == null
    }
    public planetGenStopAll() {
        this.planetGenDic = {}
    }

    public moveYIn(it: cc.Node, time: number, showY: number, hideY: number) {
        it.active = true
        it.opacity = 255
        if (time <= 0) {
            it.y = showY
            return
        }
        it.y = hideY
        return cc.tween(it).to(time, { y: showY }, { easing: cc.easing.sineOut }).promise()
    }
    public async moveYOut(it: cc.Node, time: number, showY: number, hideY: number) {
        if (time <= 0) {
            it.y = hideY
            it.active = false
            it.opacity = 0
            return
        }
        it.y = showY
        await cc.tween(it).to(time, { y: hideY, opacity: 0 }, { easing: cc.easing.sineIn }).promise()
        it.active = false
    }

    public playShowPnlAction(node: cc.Node) {
        let orgScale = node.scale
        node.scale = 0.4
        return cc.tween(node).to(0.25, { scale: orgScale }, { easing: cc.easing.backOut }).promise()
    }

    public showEquipTips(root: cc.Node, startY: number) {
        root.active = true
        root.opacity = 0
        cc.Tween.stopAllByTarget(root)
        //飘出
        cc.tween(root)
            .to(0.3, { y: startY + 30, opacity: 255 }, { easing: cc.easing.sineOut })
            .delay(1.5)
            .to(0.1, { y: startY + 50, opacity: 0 }, { easing: cc.easing.sineIn })
            .call(() => {
                root.active = false
                root.y = startY
            }).start()
    }

    public async playPlanetRewardJump(node: cc.Node, center: cc.Vec2, recordPosAry: cc.Vec2[], opt: any = {}) {
        const maxa = opt.maxa || 60, maxb = opt.maxb || 50  //椭圆长宽
        let pos = mapHelper.sparsePosRandom(() => {
            let a = maxa
            let b = maxb
            // 以椭圆中心为原点，在椭圆内生成一个随机点
            const angle = Math.random() * Math.PI * 2;
            const radius = Math.sqrt(Math.random()); // 使用sqrt来确保点在椭圆区域内均匀分布
            let x = a * radius * Math.cos(angle);
            let y = b * radius * Math.sin(angle);

            y += 10//整体需要向上偏移一点，抵消透视
            return cc.v2(x, y)
        }, recordPosAry, 30)

        const jumpHeight = opt.jumpHeight || 180  //第一次跳跃高度 => 后面每次 = 前一次 / 2
        node.setPosition(center)
        node.scale = 0
        let targetPos = cc.v2(center.x + pos.x, center.y + pos.y)                   //第一次跳跃落点
        let pos_y = Math.abs(pos.y) //为了让每一次跳跃都高一点
        let targetPos1 = cc.v2(targetPos.x + pos.x * 0.8, targetPos.y + pos_y * 0.1)//第二次跳跃落点
        let targetPos2 = cc.v2(targetPos1.x + pos.x * 0.3, targetPos1.y + pos_y * 0.1)//第三次跳跃落点
        
        let moveX = ut.randomRange(20, 40);
        if (pos.x < 0) {
            moveX = -moveX;
        }
        let targetPos3 = cc.v2(targetPos2.x + moveX, targetPos2.y - pos_y * 0.2)    //滑动终止点
        let time = ut.getRandomNum(0.25, 0.45, true, 3)     //跳跃时间（最小时间，最大时间）
        let heightIndex = ut.getRandomNum(0.95, 1.05, true, 3) //跳跃高度指数 => 跳跃

        let p = cc.tween(node).then(cc.jumpTo(time, targetPos, jumpHeight * heightIndex, 1))
        .then(cc.jumpTo(time * 0.7, targetPos1, jumpHeight * 0.5 * heightIndex, 1))
        .then(cc.jumpTo(time * 0.3, targetPos2, jumpHeight * 0.25 * heightIndex, 1))
        .to(time, { x: targetPos3.x, y: targetPos3.y }, { easing: cc.easing.sineOut })
        .promise()
        cc.tween(node).to(time * 0.2, { scale: 1 }, { easing: cc.easing.cubicOut }).start()
        return p
    }
}

export const animHelper = new AnimHelper()
if (cc.sys.isBrowser) {
    window['animHelper'] = animHelper
}
