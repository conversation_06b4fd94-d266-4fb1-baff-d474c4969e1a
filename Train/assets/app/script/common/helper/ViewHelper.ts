import ConditionObj from "../../model/common/ConditionObj"
import { BattleLevelType, CommonBtnType, ConditionType, HexColor, InstanceWeather, LangCfgName, TipsNotType } from "../constant/Enums"
import NetEvent from "../event/NetEvent"
import NotEvent from "../event/NotEvent"
import { gameHelper } from "./GameHelper"
import { cfgHelper } from "./CfgHelper"
import { Condition, InstanceLevelCfg, MessageBoxOpts, TipsOpts } from "../constant/DataType"
import PlanetCheckPointModel from "../../model/planet/PlanetCheckPointModel"
import { resHelper } from "./ResHelper"
import LocaleLabel from '../../../core/component/LocaleLabel'
import { COMMON_BTN, MAX_VALUE } from "../constant/Constant"
import EventType from '../event/EventType'
import CoreEventType from "../../../core/event/CoreEventType"
import BaseTipsCmpt from "../../view/cmpt/bubble/BaseBubbleCmpt"
import Monster from "../../model/battle/Monster"
import BaseBubbleCmpt from "../../view/cmpt/bubble/BaseBubbleCmpt"
import BattleRole from "../../model/battle/BattleRole"
import PassengerModel from "../../model/passenger/PassengerModel"
import { BattleTeam } from "../../model/battle/BattleMgr"
import PlanetModel from "../../model/planet/PlanetModel"
import CarriageModel from "../../model/train/common/CarriageModel"

/**
 * 视图帮助方法
 */
class ViewHelper {

    // 跳转场景
    public gotoWind(val: string) {
        eventCenter.emit(mc.Event.GOTO_WIND, val)
    }

    // 需要延迟进入
    public async delayEnterWind(val: string, delay: number) {
        const now = Date.now()
        eventCenter.emit(mc.Event.LOAD_BEGIN_WIND)
        await this.preloadWind(val)
        const time = delay - (Date.now() - now) * 0.001
        if (time > 0) {
            await ut.wait(time, mc.currWind)
        }
        eventCenter.emit(mc.Event.LOAD_END_WIND)
        eventCenter.emit(mc.Event.GOTO_WIND, val)
    }

    // 预加载场景
    public async preloadWind(key: string, progress?: (done: number, total: number) => void) {
        return new Promise<void>(resolve => eventCenter.emit(mc.Event.PRELOAD_WIND, key, resolve, progress))
    }

    // 预加载UI
    public async preloadPnl(key: string, params?: any[], progress?: (done: number, total: number) => void) {
        return new Promise<mc.BasePnlCtrl>(resolve => eventCenter.emit(mc.Event.PRELOAD_PNL, key, params, resolve, progress))
    }

    // 显示UI
    public showPnl(key: string | mc.BasePnlCtrl, ...params: any) {
        eventCenter.emit(mc.Event.OPEN_PNL, key, ...params)
    }

    public setTop(info: ConditionType[], zIndex = 100) {
        eventCenter.emit(EventType.SHOW_TOP, info, zIndex)
    }

    // 隐藏UI
    public hidePnl(key: string | mc.BasePnlCtrl) {
        eventCenter.emit(mc.Event.HIDE_PNL, key)
    }

    // 关闭UI
    public closePnl(key: string | mc.BasePnlCtrl) {
        eventCenter.emit(mc.Event.CLOSE_PNL, key)
    }

    // 显示提示框
    public showAlert(msg: string, data?: { type?: ConditionType | ConditionObj, lang?: LangCfgName, params?: any[], delayTime?: number, hideMask?: boolean, cb?: Function, cd?: number }) {
        eventCenter.emit(CoreEventType.LOAD_NOTICE, "Alert", NotEvent.OPEN_ALERT, msg, data)
        // eventCenter.emit(NotEvent.OPEN_ALERT, msg, data)
    }

    public showNetError(code: any) {
        this.showAlert("login_tips_8", { lang: LangCfgName.LOAD, params: [code] })
    }

    //加载界面调用 提示框
    public showLoadAlert(msg, data?: { type?: ConditionType | ConditionObj, lang?: LangCfgName, params?: any[], delayTime?: number, hideMask?: boolean, cb?: Function }) {
        if (!data) data = {}
        data.lang = LangCfgName.LOAD
        this.showAlert(msg, data)
    }

    // 显示对话框
    public showMessageBox(msg: string, ok?: Function, cancel?: Function, opts?: MessageBoxOpts) {
        eventCenter.emit(CoreEventType.LOAD_NOTICE, "MessageBox", NotEvent.OPEN_MESSAGE_BOX, msg, ok, cancel, opts)
    }

    // 显示加载界面对话框
    public showLoadMessageBox(msg: string, ok?: Function, cancel?: Function, opts?: MessageBoxOpts) {
        if (!opts) opts = {}
        opts.lang = LangCfgName.LOAD
        this.showMessageBox(msg, ok, cancel, opts)
    }

    public showReconnect() {
        eventCenter.emit(CoreEventType.LOAD_NOTICE, "Reconnect", NotEvent.SHOW_RECONNECT)
    }

    public showLoadingWait(show) {
        if (show) {
            eventCenter.emit(mc.Event.LOAD_BEGIN_PNL)
        }
        else {
            eventCenter.emit(mc.Event.LOAD_END_PNL)
        }
    }

    public showBubble(name: string, pos: cc.Node | cc.Vec2, data?: any, opt?) {
        eventCenter.emit(NotEvent.SHOW_BUBBLE, name, pos, data, opt)
    }

    public hideBubble() {
        eventCenter.emit(NotEvent.HIDE_BUBBLE)
    }

    // 显示tips
    public showTips(key: string | Function, opts?: TipsOpts) {
        eventCenter.emit(NotEvent.SHOW_TIPS, key, opts)
    }

    public hideTips(showAnim: boolean = true) {
        eventCenter.emit(NotEvent.HIDE_TIPS, showAnim)
    }

    // 显示tips(星球场景专用 层级不一样)
    public showPlanetTips(key: string, type: TipsNotType = TipsNotType.NORMAL, opts?: TipsOpts) {
        eventCenter.emit(EventType.PLANET_SHOW_TIPS, key, type, opts)
    }

    public hidePlanetTips(showAnim: boolean = true) {
        eventCenter.emit(EventType.PLANET_HIDE_TIPS, showAnim)
    }

    public showTask(key: string) {
        eventCenter.emit(NotEvent.SHOW_TASK, key)
    }

    // 网络请求开始
    public showNet() {
        eventCenter.emit(CoreEventType.LOAD_NOTICE, "NetWait", NetEvent.NET_REQ_BEGIN)
    }

    // 网络请求结束
    public hideNet() {
        eventCenter.emit(CoreEventType.LOAD_NOTICE, "NetWait", NetEvent.NET_REQ_END)
    }

    // 属性展开关闭时候的列表
    public updateUnfoldList(list: cc.Node[], count: number, val: boolean) {
        count = val ? 0 : count
        for (let i = count, l = list.length; i < l; i++) {
            list[i].active = val
        }
    }

    // 适应大小，只缩小不放大；如果targetSize为0，则保持不变
    public adaptNodeSize(node: cc.Node, selfSize: cc.Size, targetSize: cc.Size) {
        selfSize = selfSize || node.getContentSize()
        let scale = this.getAdaptScale(selfSize, targetSize)
        if (scale == 0 || isNaN(scale)) return
        node.scale = Math.min(scale, 1)
    }

    public getAdaptScale(baseSize: cc.Size, targetSize: cc.Size) {
        // 先算出宽度比例
        let scale = targetSize.width / baseSize.width
        // 如果高度大了 就用高的比例
        if (baseSize.height * scale > targetSize.height) {
            scale = targetSize.height / baseSize.height
        }
        return scale
    }

    public setCharacterName(node: cc.Node, cid: number) {
        let cfg = cfgHelper.getCharacter(cid);
        node.Component(LocaleLabel).setKey(cfg.name);
        node.Component(cc.LabelOutline).SetColor(HexColor[`ROLE_Q${cfg.quality}`])
    }

    // 显示通用奖励
    public async showGeneralReward(list: ConditionObj[], needMerge: boolean = true, options?: {} & any) {
        list = list.filter(c => c.type != ConditionType.WORLD_TIME) //加模拟时间奖励不显示出来
        if (!list) return
        let listNum: ConditionObj[] = []
        let listPassenger: ConditionObj[] = [] //宝箱解锁乘客
        let listProfile: ConditionObj[] = [] // 贴纸资料
        list.forEach(cond => {
            switch (cond.type) {
                case ConditionType.BUILD_ID:
                    if (cond.id != "1015-1-1") {
                        listNum.push(cond)
                    }
                    break;
                case ConditionType.PASSENGER:
                    listPassenger.push(cond)
                    break;
                case ConditionType.PROP:
                    listNum.push(cond)
                    break;
                case ConditionType.PLANET_PROFILE:
                    listProfile.push(cond)
                    break;
                default:
                    listNum.push(cond)
                    break;
            }
        })
        if (listPassenger.length > 0) {
            let pnlKey = "jackpot/JackPotInvitedPnl"
            viewHelper.showPnl(pnlKey, listPassenger)
            await this.waitCloseUI(pnlKey)
        }
        if (listProfile.length > 0) {
            listNum.length = 0
            let pnlKey = "planetEntry/DeepRewardPnl"
            viewHelper.showPnl(pnlKey, list)
            await this.waitCloseUI(pnlKey)
        }
        if (listNum.length > 0) {
            let pnlKey = 'common/RewardPnl'
            this.showPnl(pnlKey, listNum, needMerge, options)
            await this.waitCloseUI(pnlKey)
        }
    }

    public async showBlackHoleReward(data: { rewards: ConditionObj[], done: boolean, resolve: Function }) {
        let pnlKey = 'blackHole/BlackHoleRewardPnl'
        this.showPnl(pnlKey, data)
        await this.waitCloseUI(pnlKey)
    }

    public async waitCloseGuide(ignores?: string) {
        let arr = ignores ? ignores.split('|') : []
        let check = () => { return !mc.getViewMgr().hasOpenPnl(arr) }
        if (check()) return
        twlog.info("waitCloseGuide")
        eventCenter.emit(EventType.GUIDE_FREE_TOUCH)
        await eventCenter.wait(CoreEventType.PNL_LEAVE, check)
        eventCenter.emit(EventType.GUIDE_RESET)
    }

    public async waitClosePnl(ignores?: string) {
        let arr = ignores ? ignores.split('|') : []
        let check = () => { return !mc.getViewMgr().hasOpenPnl(arr) }
        if (check()) return
        await eventCenter.wait(CoreEventType.PNL_LEAVE, check)
    }

    public async waitCloseUI(pnlKey: string) {
        let check = () => { return this.checkPnlClose(pnlKey) }
        if (check()) return
        await eventCenter.wait(CoreEventType.PNL_LEAVE, check)
    }

    public async waitEnterUI(pnlKey: string) {
        let check = () => { return this.checkPnlEnter(pnlKey) }
        if (check()) return
        await eventCenter.wait(CoreEventType.PNL_ENTER, check)
    }

    public async waitAnyClickEnd() {
        return new Promise<cc.Event.EventTouch>((resolve) => {
            let touchNode = cc.find('Canvas')
            touchNode.off(cc.Node.EventType.TOUCH_END)
            touchNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
                touchNode.off(cc.Node.EventType.TOUCH_END)
                resolve(event)
            }, this, true)
        })
    }

    public checkPnlEnter(pnlKey: string) {
        let pnl = mc.getPnl(pnlKey)
        return pnl && pnl.getActive()
    }

    public checkHavePnl(pnlKey: string) {
        let pnl = mc.getPnl(pnlKey)
        if (pnl && pnl.getActive()) return true
        return mc.isPnlInQueue(pnlKey)
    }

    public checkPnlClose(pnlKey: string) {
        let pnl = mc.getPnl(pnlKey)
        if (pnl && pnl.getActive()) return false
        return !mc.isPnlInQueue(pnlKey)
    }

    public async showBattle(data: {
        monsters?: (Monster | BattleRole)[],
        battleRoles?: (Monster | BattleRole)[],
        battleBg?: string,
        checkPoint?: PlanetCheckPointModel,
        battlePos?: cc.Vec2,
        isPreview?: boolean,
        passengers?: (PassengerModel | BattleRole)[],
        team?: BattleTeam,
        isBlackHole?: boolean,
        onStartBattle?: Function,
        onWin?: Function,
        rewards?: (ConditionObj | Condition)[],
        title?: { name: string, progress: string },
        skip?: boolean,
        instanceData?: InstanceLevelCfg,
        buff?: BattleRole
        desc?: string,
        isWin?: boolean
        noAgain?: boolean
        isTransport?: boolean
        levelType: BattleLevelType,
        levelId: string,
        pvpData?: { score: number, rank: number }
    }, showResult: boolean = true): Promise<any> {
        let callOnce = async () => {
            const orgData = Object.assign({}, data)

            let battleRoles = data.battleRoles
            if (!battleRoles) {
                battleRoles = await new Promise(callback => {
                    this.showPnl('battle/BattleReady', Object.assign({ callback }, orgData))
                })
                data.battleRoles = battleRoles
            }
            if (battleRoles) {
                let battleResult: any = await new Promise(callback => {
                    this.showPnl('battle/Battle', Object.assign({ callback, battleRoles }, orgData))
                })
                let again = battleResult?.again
                if (again) {
                    delete data.battleRoles
                    return await callOnce()
                }
                let isWin = battleResult?.isWin
                if (battleResult !== null && showResult) { //如果不是点的离开
                    let again = await new Promise(callback => {
                        this.showPnl('battle/BattleResult', Object.assign(orgData, { callback, isWin }))
                    })
                    if (!isWin) {
                        if (again) {
                            delete data.battleRoles
                            return await callOnce()
                        }
                    }
                }

                return battleResult
            }
        }
        let res = await callOnce()
        return res
    }

    public async shake(node: cc.Node, duration, factor = 1) {
        let orgPos = node["__orgPos"] || node.getPosition()
        node["__orgPos"] = orgPos
        node.stopAllActions();
        let time = duration / 10
        factor *= 0.8
        cc.tween(node).sequence(
            cc.moveTo(time, cc.v2(5, 7).multiplyScalar(factor)),
            cc.moveTo(time, cc.v2(-6, 7).multiplyScalar(factor)),
            cc.moveTo(time, cc.v2(-10, 3).multiplyScalar(factor)),
            cc.moveTo(time, cc.v2(3, -6).multiplyScalar(factor)),
            cc.moveTo(time, cc.v2(-5, 5).multiplyScalar(factor)),
            cc.moveTo(time, cc.v2(2, -8).multiplyScalar(factor)),
            cc.moveTo(time, cc.v2(-8, -10).multiplyScalar(factor)),
            cc.moveTo(time, cc.v2(3, 10).multiplyScalar(factor)),
            cc.moveTo(time, cc.v2(0, 0).multiplyScalar(factor))
        ).repeatForever().start()

        await cc.tween(node).delay(duration).promise()
        node.stopAllActions();
        node.setPosition(orgPos);
    }

    // public followNode(node: cc.Node, follower, syncFunc?: Function) {
    //     syncFunc = syncFunc || function(){
    //         follower.x = node.x
    //         follower.y = node.y
    //     }
    //     node.on('position-changed', syncFunc)
    // }

    public setMaterial(node, material: cc.Material) {
    }

    public async playFlutterCost(val: number, root: cc.Node, position: cc.Vec2, key: string) {
        const it = await resHelper.getNode('FLUTTER_TEXT_3', key)
        if (!root.isValid) {
            resHelper.putNode(it)
            return twlog.info('playFlutterCost !root.isValid')
        }
        if (!it || !it.isValid) {
            return
        }
        it.parent = root
        it.Child('val', cc.Label).string = ut.numberToString(val)
        it.setPosition(position.x - 20, position.y)
        it.opacity = 255
        cc.tween(it)
            .by(0.3, { y: 30 }, { easing: cc.easing.sineOut })
            .by(1, { y: 40, opacity: 0 }, { easing: cc.easing.sineIn })
            .call(() => resHelper.putNode(it)).start()
    }

    //传的是角色的id 和lv
    public async showSkillTips(root: cc.Node, baseNode: cc.Node | cc.Vec2, data: { id: number, lv?: number, skills?, attr?}, key: string) {
        let childName = "BattleSkillBubble"
        let tips = root.getChildByName(childName)
        if (!tips) {
            let url = `bubble/${childName}`
            let prefab = await assetsMgr.loadTempRes(url, cc.Prefab, key)
            if (!cc.isValid(root)) {
                assetsMgr.releaseTempRes(url, key)
                return
            }
            tips = root.getChildByName(childName)
            if (!tips) {
                tips = cc.instantiate2(prefab, root)
            }
        }

        let pos: cc.Vec2
        if (baseNode instanceof cc.Node) { // 如果传入的是节点，从节点的(0.5, 1)出来
            if (baseNode.Component(cc.Button)) { //如果是button，还可能在缩放的过程中，直接坐标转换会有问题，这里按当前节点无缩放处理；如果需要考虑，请外部处理好直接传入坐标
                let rect = baseNode.getRect(baseNode.getPosition())
                let offset = cc.v2(rect.center.x, rect.yMax + 10)
                pos = ut.convertToNodeAR(baseNode.parent, tips.parent, offset)
            }
            else {
                let rect = baseNode.getRect()
                let offset = cc.v2(rect.center.x, rect.yMax + 10)
                pos = ut.convertToNodeAR(baseNode, tips.parent, offset)
            }

        }
        else if (baseNode instanceof cc.Vec2) { //直接传入本地坐标
            pos = baseNode
        }

        let width = tips.width
        let minDis = 5 //离边界的最小距离
        let safeArea = cc.sys.getSafeAreaRect();
        let minX = safeArea.x - cc.winSize.width / 2, maxX = safeArea.x + safeArea.width - cc.winSize.width / 2;
        pos.x = cc.misc.clampf(pos.x, minX + width * 0.5 + minDis, maxX - width * 0.5 - minDis)
        tips.setPosition(pos)
        tips.Component(BaseTipsCmpt).init(data)
    }

    public setCommonBtn(type: CommonBtnType, node: cc.Node) {
        let diNode = node.Child('di')
        let { di, outline } = COMMON_BTN[type]
        diNode.SetColor(di)
        node.walk((child) => {
            let outlineCmpt = child.getComponent(cc.LabelOutline)
            if (outlineCmpt) {
                outlineCmpt.color = new cc.Color().fromHEX(outline)
            }
        }, () => { })
    }

    //星球回主界面
    public gotoMainFromPlanet() {
        let showPnl
        eventCenter.emit(mc.Event.GOTO_WIND, "main", { showPnl })
    }

    //去星球入口
    public gotoPlanetEntry() {
        gameHelper.planet.isEntry = true
        eventCenter.emit(mc.Event.GOTO_WIND, "planetEntry")
    }

    //通过功能跳转到星球
    public gotoPlanet() {
        let planet = gameHelper.planet.getCurPlanet()
        if ((planet.isUnlockEntry() && gameHelper.planet.isEntry)) {
            viewHelper.gotoPlanetEntry()
        }
        else if (planet.branchId) {
            let planet = gameHelper.planet.getCurPlanet()
            let branch = planet.getCurBranch()
            viewHelper.gotoWind(branch.windName)
        }
        else {
            viewHelper.gotoPlanetMain()
        }
    }

    //去星球主线
    public gotoPlanetMain() {
        let planet = gameHelper.planet.getCurPlanet()
        planet.setBranch()
        gameHelper.planet.isEntry = false
        eventCenter.emit(mc.Event.GOTO_WIND, planet.getCurMap().getArea().windName)
    }

    //去星球支线
    public gotoPlanetBranch(planet: PlanetModel, index = 1) {
        let branch = planet.branches.find(b => b.index == index)
        gameHelper.planet.isEntry = false
        planet.setBranch(branch.id)
        viewHelper.gotoWind(branch.windName)
    }

    public adapterAttrSize(node: cc.Node) {
        let lb = node.Component(cc.Label)
        let str = lb.string
        if (str.length <= 2) {
            lb.spacingX = 0
            lb.fontSize = 68
        }
        else if (str.length <= 3) {
            lb.spacingX = -4
            lb.fontSize = 54
        }
        else {
            lb.spacingX = -5
            lb.fontSize = 44
        }
    }

    //保持节点不反转
    public fixNodeFilp(node: cc.Node) {
        let worldScale = node.getWorldScale(cc.v2())
        let scaleX = Math.abs(node.scaleX)
        node.scaleX = scaleX * ut.normalizeNumber(worldScale.x)
    }

    public showUI(show) {
        let event = show ? EventType.SHOW_UI : EventType.HIDE_UI
        eventCenter.emit(event)
    }

    public getMainCamera() {
        return cc.find("Canvas/Main Camera").Component(cc.Camera)
    }

    public getPlanetCamera() {
        return cc.find("Canvas/PlanetCamera").Component(cc.Camera)
    }

    public getUICamera() {
        return cc.find("Canvas/UICamera").Component(cc.Camera)
    }

    public getSpriteFrameByCapture(camera: cc.Camera) {
        let { width, height } = cc.winSize
        let renderTexture = new cc.RenderTexture();
        renderTexture.initWithSize(width, height, cc.RenderTexture.DepthStencilFormat.RB_FMT_S8);
        camera.targetTexture = renderTexture
        camera.render()
        camera.targetTexture = null
        let spf = new cc.SpriteFrame()
        spf.setFlipY(true);
        spf.setTexture(renderTexture)
        return spf
    }

    public getPlanetWindScale(force: boolean = false) {
        if (!force && !gameHelper.guide.logic.isPlanetWind()) return 1 //目前只有星球场景用到
        return Math.max(1, cc.winSize.height / 1642)
    }

    public async playAnimation(sk, animName, elapsed = 0, loop = false) {
        let dur = sk.getAnimationDuration(animName)
        if (dur <= 0) {
            sk.playAnimation(animName, loop)
            return elapsed
        }
        if (loop) {
            elapsed %= dur
        }
        if (dur > elapsed) {
            await sk.playAnimation(animName, loop, cc.misc.clampf(elapsed, 0, dur))
        }
        elapsed -= dur
        return elapsed
    }

    public activeMainWind(active) {
        viewHelper.showUI(active)
        eventCenter.emit(CoreEventType.ACTIVE_WIND, active)
    }

    //对话气泡挂在在乘客身上
    public async showSpeechBubbleByRole(roleId: number, key: string, time?: number) {
        eventCenter.emit(EventType.ROLE_SHOW_SPEECH_BUBBLE, roleId, key, time)
    }

    public hideSpeechBubbleByRole(roleId: number) {
        eventCenter.emit(EventType.ROLE_HIDE_SPEECH_BUBBLE, roleId)
    }

    //对话气泡挂在在场景中，需要场景中有个节点挂载BubbleRootCmpt
    public async showSpeechBubble(worldPos: cc.Vec2, key: string, time?: number) {
        eventCenter.emit(EventType.SHOW_SPEECH_BUBBLE, worldPos, key, time)
    }

    public hideSpeechBubble() {
        eventCenter.emit(EventType.HIDE_SPEECH_BUBBLE)
    }

    public adaptLabel(label: cc.Label | cc.RichText, maxWidth: number = 500) {
        if (label instanceof cc.RichText) {
            label.maxWidth = 0
        }
        else {
            label.overflow = cc.Label.Overflow.NONE
        }
        if (label instanceof cc.Label) {
            label._forceUpdateRenderData()
        }
        let width = label.node.width
        if (width > maxWidth) { //要换行
            if (label instanceof cc.RichText) {
                label.maxWidth = maxWidth
            }
            else {
                label.overflow = cc.Label.Overflow.RESIZE_HEIGHT
                label.node.width = maxWidth
                label._forceUpdateRenderData()
            }
        }
    }

    public showWatchAd(type: proto.AdType, callback: (bol: boolean) => void) {
        viewHelper.showPnl("main/WatchAdPnl", { type, callback })
    }

    public async backCameraFromEdit(carriage: CarriageModel, id: number, time: number = 1) {
        eventCenter.emit(EventType.SET_FOCUS, { carriage, id })
        await ut.wait(time)
        eventCenter.emit(EventType.BACK_CAMERA_FROM_EDIT, carriage, id)
    }

    public getHpStr(hp: number) {
        if (hp >= MAX_VALUE) return "∞"
        return String(hp)
    }
}

export const viewHelper = new ViewHelper()

if (CC_DEV) {
    window["viewHelper"] = viewHelper
}
