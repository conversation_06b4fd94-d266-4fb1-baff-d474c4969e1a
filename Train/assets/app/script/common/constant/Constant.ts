/////////////// 所有常量（全大写单词间用下划线隔开）///////////////

import { util } from "../../../core/utils/Utils"
import { BuildAttr, CfgName, CommonBtnType, ConditionType, PassengerAttr, PassengerQuality, PlanetMineType, RoleAnimalType, RoleBattleType, SpeedUpType } from "./Enums"

type TempRangePoint = {
    point: cc.Vec2
    tag: number
}

// 4个方向
const DIR_POINTS_4: TempRangePoint[] = []
for (let i = -1; i <= 1; i++) {
    for (let j = -1; j <= 1; j++) {
        const tag = Math.abs(i) + Math.abs(j)
        if (tag !== 1) {
            continue
        }
        DIR_POINTS_4.push({ point: cc.v2(i, j), tag: 1 })
    }
}

// 8个方向
const DIR_POINTS_8: TempRangePoint[] = []
for (let i = -1; i <= 1; i++) {
    for (let j = -1; j <= 1; j++) {
        if (i === 0 && j === 0) {
            continue
        }
        const tag = Math.abs(i) + Math.abs(j)
        DIR_POINTS_8.push({ point: cc.v2(i, j), tag: tag === 1 ? tag : 1.414 })
    }
}

// 点击间隔
const CLICK_SPACE = 5

// 一格的大小
const TILE_SIZE = 45
//
const TILE_SIZE_HALF = cc.v2(TILE_SIZE * 0.5, TILE_SIZE * 0.5)
// 斜度
const SKEW_ANGLE = 30
// 斜着的外宽高
const SKEW_SIZE = cc.size(118, 70)
// 斜着的内宽高 59 35
const SKEW_SIZE_HALF = cc.size(SKEW_SIZE.width * 0.5, SKEW_SIZE.height * 0.5)
// 斜边
const SKEW_LEN = Math.sqrt(SKEW_SIZE_HALF.width * SKEW_SIZE_HALF.width + SKEW_SIZE_HALF.height * SKEW_SIZE_HALF.height)

// 层级最大值
const MAX_ZINDEX = 10000

//弹窗可关闭时间
const UNLOCK_TIME_LONG = 0.8
const UNLOCK_TIME_SHORT = 0.4
//弹窗淡入时间
const UNLOCK_FADEIN_TIME = 0.3

// 长按时间
const LONG_PRESS_TIME = 0.4

const CARRIAGE_PASSENGER_LIMIT = 4
const CARRIAGE_WORK_LIMIT = 2

const MAX_VALUE = Number.MAX_SAFE_INTEGER

const CARRIAGE_LEN = 2456 //一节车厢的长度
const CARRIAGE_HEIGHT = 1206 //车厢高度
const CARRIAGE_SPACING = cc.v2(90, 67) //车厢间隔
const CARRIAGE_UP_TWOROW = 40 //双层时上移
const CARRIAGE_CONTENT_SIZE = cc.size(2150, 810) //车厢内容大小

// pnl层级
const PNL_ZINDEX = {
    _Max: 3276, //不能超过这个
    AntiAddiction: 3000,
    CharacterPlot: 1004,
    TimeFilter: 1003,
    Plot: 1002,
    Guide: 1001,
    Reward: 500,
    Top: 1,
    UI: -1,
    Bottom: -100,
}

// notice层级
const NOTICE_ZINDEX = {
    TOP: 1000, //这个应该是最高
    RECONNECT: 999,
    LOADING: 100,
}

const NONE_MESSAGE_DURATION = 3

// 排队间距
const QUEUE_SPACE = 80

// common/image下的图
const CONDITION_ICON = {
    [ConditionType.STAR_DUST]: 'cailiao_icon_xingxing',
    [ConditionType.HEART]: 'cailiao_icon_aixin',
    [ConditionType.DIAMOND]: 'cailiao_icon_zuanshi',
    [ConditionType.BLACK_HOLE_CURRENCY]: 'cailiao_icon_xinghaibi',
    [ConditionType.ARREST_CURRENCY]: 'cailiao_icon_tongjibi',
}

// 某个指定路径下的图
const CONDITION_OTHER_ICON = {
}

// 根据目录，配置表获取图
const CONDITION_ITEM_ICON_DIR = {
    [ConditionType.PROP]: { dir: "prop", jsonName: CfgName.PROP },
    [ConditionType.BUILD_ID]: { dir: "train/itemIcon", jsonName: CfgName.BUILD },
    [ConditionType.EQUIP]: { dir: "equip", jsonName: "Equip" },
    [ConditionType.ORE_ITEM]: { dir: "ore/icon", jsonName: "OreItem" },
    [ConditionType.SEED]: { dir: "prop", jsonName: "FieldSeed" },
    // [ConditionType.CHARACTER_PROFILE]: { dir: "archives/character", jsonName: "CharacterProfile" },
    [ConditionType.PLANET_PROFILE]: { dir: "profile/icon", jsonName: "PlanetProfile" },
}

const TOOL_SLOT_NAME = {
    [PlanetMineType.TREE]: 'zhiwu', //植物
    [PlanetMineType.ORE]: 'kuangwu', //矿物
    [PlanetMineType.PART]: 'lingjian', //零件类
}

const MAX_BATTLE_COUNT = 5;
const MAX_PLANET_ROBOT_COUNT = 4;

const TIME_LANG = {
    'd': 'common_timeUnit_day',
    'h': 'common_timeUnit_hour',
    'm': 'common_timeUnit_minute',
    's': 'common_timeUnit_second',
}

const DATE_LANG = {
    'y': 'common_SLTimeUnit_year',
    'm': 'common_SLTimeUnit_month',
    'w': 'common_SLTimeUnit_week',
    'd': 'common_SLTimeUnit_day',
}

const TIME_SHORT_LANG = {
    'd': 'common_timeUnitShort_day',
    'h': 'common_timeUnitShort_hour',
    'm': 'common_timeUnitShort_minute',
    's': 'common_timeUnitShort_second',
}


const COMMON_BTN = {
    [CommonBtnType.YELLOW]: { di: '#face3a', outline: '#c5752e' }, //确定，返回，领取
    [CommonBtnType.GREEN]: { di: '#a8da6f', outline: '#5c7e26' }, //消耗相关
    [CommonBtnType.BLUE]: { di: '#73cbf2', outline: '#217da6' }, //个别单独场景
    [CommonBtnType.GRAY]: { di: '#b9b9b9', outline: '#6f6f6f' }, //取消，不可点
    [CommonBtnType.RED]: { di: '#db606a', outline: '#883a40' }, //取消，警示
}

const MAX_DANCE_COUNT = 4

const PASSENGER_QUALITY_COLOR = {
    [PassengerQuality.N]: '#52BAEA',
    [PassengerQuality.R]: '#898ce2',
    [PassengerQuality.SR]: '#e4a52c',
}

const PASSENGER_QUALITY_OUTLINE_COLOR = {
    [PassengerQuality.N]: '#63a0f4',
    [PassengerQuality.R]: '#d965fa',
    [PassengerQuality.SR]: '#ffa200',
}

const PASSENGER_QUALITY_NAME = {
    [PassengerQuality.N]: 'common_character_quality_1',
    [PassengerQuality.R]: 'common_character_quality_2',
    [PassengerQuality.SR]: 'common_character_quality_3',
    [PassengerQuality.SSR]: 'common_character_quality_4',
}

//需要在领取奖励弹窗后展示飞行演绎的类型
const CONDITION_NEEDPLAY = [
    ConditionType.DIAMOND,
    ConditionType.HEART,
    ConditionType.STAR_DUST,
    ConditionType.ELECTRIC,
    ConditionType.WATER,
    ConditionType.PROP,
    ConditionType.CHEST,
    ConditionType.BLACK_HOLE_CURRENCY,
    ConditionType.ARREST_CURRENCY,
]

const MORN_TIME = 7 * util.Time.Hour //白天开始时间
const NIGHT_TIME = 20 * util.Time.Hour //夜晚开始时间
const DAWN_TRANS_TIME = 30 * util.Time.Minute //夜晚转白天过渡时间
const DUSK_TRANS_TIME = 30 * util.Time.Minute //白天转夜晚过渡时间

const LIFT_HEIGHT = 2451 //机械城电梯高度

const DORM_RIGHT_BED_ID = "1013-1-4"
const GUIDE_PARTERRE_ID = 2010 //永恒花园花坛id
const GUIDE_FLOWERTREE_ID = 1012 //永恒银花火树id

const SPEED_UP_RANDOM = {
    [SpeedUpType.S2]: [1, 2],
    [SpeedUpType.S4]: [1, 2],
}

const BuildDelay = 0.05//延迟x秒后播放设施建造动画
const RoleJump = [1003, 1004]//跳着走路的角色们
const ROLE_DETAIL_CFG = [
    { type: PassengerAttr.ATTACK, index: 0 },
    { type: PassengerAttr.HP, index: 1 },
]

const CURRENY_CFG = {
    [ConditionType.STAR_DUST]: { name: "name_item_star", content: "content_item_star", level: 1 },
    [ConditionType.HEART]: { name: "name_item_heart", content: "content_item_heart", level: 1 },
    [ConditionType.DIAMOND]: { name: "name_item_gem", content: "content_item_gem", level: 2 },
    [ConditionType.BLACK_HOLE_CURRENCY]: { name: "name_item_bhc", content: "content_item_bhc", level: 2 },
}

const AUTO_EXPLORE_MAX_SHOW_TIME = 600 //自动探索最大显示时间

const CACHE_UPDATE_TIME = 10

const BUILD_ATTRS = [BuildAttr.STAR, BuildAttr.HEART, BuildAttr.WATER, BuildAttr.ELECTRICITY, BuildAttr.VITALITY, BuildAttr.LOAD]

const TOOL_ATTR_CFG = [
    { lb: "tool_guiText_15", attr: "attack" },
    { lb: "tool_guiText_17", attr: "hit", effect: "tool_guiText_20", type: PlanetMineType.TREE },
    { lb: "tool_guiText_16", attr: "break", effect: "tool_guiText_19", type: PlanetMineType.ORE },
    { lb: "tool_guiText_18", attr: "amp", effect: "tool_guiText_21", type: PlanetMineType.PART },
]

const MAX_OPEN_CHEST = 10 //每次开宝箱最大数量

const BATTLE_PREVIEW_BG = {
    BLACK_HOLE: "xhmg_tc_bak5",
    INSTANCE: "fb_zhandou",
    TOWER: "sgzj_tc_pic2",
    INSTANCE_RAINBOW: "fb_tc_bg_caihong",
    INSTANCE_WIND: "fb_tc_bg_feng",
    INSTANCE_SUN: "fb_tc_bg_lieri",
    INSTANCE_SUNNY: "fb_tc_bg_qingkong",
    INSTANCE_SNOW: "fb_tc_bg_xue",
    INSTANCE_RAIN: "fb_tc_bg_yu",
    MINE: "wk_xz_sw1",
}

const BATTLE_TYPE_ICON = {
    [RoleBattleType.ASSIST]: "jnlx_fuzhu",
    [RoleBattleType.DAMAGE]: "jnlx_shanghai",
    [RoleBattleType.IMPROVE]: "jnlx_ziqiang",
    [RoleBattleType.SUMMON]: "jnlx_zhaohuan",
    [RoleBattleType.CONTROL]: "jnlx_kongzhi",
}

const ANIMAL_TYPE_ICON = {
    [RoleAnimalType.CAT]: "jslx_cat",
    [RoleAnimalType.DOG]: "jslx_dog",
    [RoleAnimalType.OTHER]: "jslx_dna",

}

const DEFAULT_IGNORES = [
    "guide/Guide", "guide/RelaterPnl", "common/Plot", "common/Top", "common/UI", "common/PlanetUI", "planet/PlanetTipsPnl"
]

const NET_ERROR_CODE = {
    DIS_CONNECT: -300,
    SERVER_ERROR: -500,
}

const EQUIP_QUALITY_CORLOR = [
    "#64c813",
    "#24bfbe",
    "#6d8ee4",
    "#9984f1",
    "#f75f83",
    "#EF74c9",
    "#75a3c8",
    "#dcdcfb"
]

// 月份天数（2月固定28天）
const MONTH_DAYS: number[] = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

const CURRENCY_UI_TYPE = {
    [ConditionType.STAR_DUST]: "StarDustUI",
    [ConditionType.HEART]: "HeartUI",
    [ConditionType.DIAMOND]: "DiamondUI",
    [ConditionType.BLACK_HOLE_CURRENCY]: "BlackHoleCurrencyUI",
}

const THROW_FUEL_SPEED = 1000 //车厢丢东西的飞行速度

export {
    DIR_POINTS_4,
    DIR_POINTS_8,
    CLICK_SPACE,
    TILE_SIZE,
    TILE_SIZE_HALF,
    SKEW_ANGLE,
    SKEW_SIZE,
    SKEW_SIZE_HALF,
    SKEW_LEN,
    LONG_PRESS_TIME,
    UNLOCK_TIME_LONG,
    UNLOCK_TIME_SHORT,
    UNLOCK_FADEIN_TIME,
    MAX_ZINDEX,
    CARRIAGE_PASSENGER_LIMIT,
    CARRIAGE_WORK_LIMIT,
    MAX_VALUE,
    PNL_ZINDEX,
    NONE_MESSAGE_DURATION,
    QUEUE_SPACE,
    CONDITION_ICON,
    CONDITION_OTHER_ICON,
    CONDITION_ITEM_ICON_DIR,
    TOOL_SLOT_NAME,
    MAX_BATTLE_COUNT,
    MAX_PLANET_ROBOT_COUNT,
    TIME_LANG,
    COMMON_BTN,
    TIME_SHORT_LANG,
    MAX_DANCE_COUNT,
    PASSENGER_QUALITY_COLOR,
    PASSENGER_QUALITY_OUTLINE_COLOR,
    PASSENGER_QUALITY_NAME,
    CONDITION_NEEDPLAY,
    CARRIAGE_LEN,
    CARRIAGE_HEIGHT,
    MORN_TIME,
    NIGHT_TIME,
    DAWN_TRANS_TIME,
    DUSK_TRANS_TIME,
    DATE_LANG,
    CARRIAGE_SPACING,
    CARRIAGE_UP_TWOROW,
    CARRIAGE_CONTENT_SIZE,
    LIFT_HEIGHT,
    DORM_RIGHT_BED_ID,
    GUIDE_PARTERRE_ID,
    GUIDE_FLOWERTREE_ID,
    NOTICE_ZINDEX,
    SPEED_UP_RANDOM,
    BuildDelay,
    RoleJump,
    ROLE_DETAIL_CFG,
    CURRENY_CFG,
    AUTO_EXPLORE_MAX_SHOW_TIME,
    CACHE_UPDATE_TIME,
    BUILD_ATTRS,
    TOOL_ATTR_CFG,
    MAX_OPEN_CHEST,
    BATTLE_PREVIEW_BG,
    BATTLE_TYPE_ICON,
    ANIMAL_TYPE_ICON,
    DEFAULT_IGNORES,
    NET_ERROR_CODE,
    EQUIP_QUALITY_CORLOR,
    MONTH_DAYS,
    CURRENCY_UI_TYPE,
    THROW_FUEL_SPEED,
}
