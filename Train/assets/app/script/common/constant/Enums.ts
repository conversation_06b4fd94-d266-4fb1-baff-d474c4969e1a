/////////////// 所有枚举（全大写单词间用下划线隔开）///////////////


import BattlePreviewPnlCtrl from "../../view/battle/BattlePreviewPnlCtrl";

enum ValueType {
    INT = 'INT',
    PER = 'PER'
}

/**抽卡进入奖池方式 */
enum JackpotEnterType {
    DEFAULT,// 默认
    LUCK,// 加入指定幸运物
    PLANET// 解锁指定星球
}

/**奖池奖品类型 */
enum PrizeType {
    CHARACTER,
    LUCKCARM
}

enum PassengerLocation {
    TOP,
    CARRIAGE,
    CONNECT,// 车厢连击处
    OUT, //外出
}

enum PassengerAction {
    IDLE = 0, //什么也不做
    WAIT, //等待，一般用于等待一些异步状态
    MOVE,

    WAIT_SINK,
    USE_SINK,

    WAIT_POT,
    USE_POT,

    WAIT_FRIDGE,
    USE_FRIDGE,

    USE_DINING_TABLE,

    CARRIAGE_FOOD,
    WAIT_FOR_FOOD,
    WAIT_FOR_WAITER,
    EAT_BY_TABLE,

    SLEEP,
    EAT,
    CHEER, //
    TREATMENT,
    GIVEMONEY,
    BATHE,
    WASH,
    CLEAN,
    FIRE,
    CUTTING,
    SIT,
    DRAINAGER
}

enum RoleDir {
    NONE,
    LEFT,
    RIGHT,
}

enum ShellAni {
    BuildFront = 'aniBuild1',// 车厢建造前半部分动画
    BuildAfter = 'aniBuild2',// 车厢建造后半部分动画
    Building = 'aniBuild3',// 车厢建造中
    BuildIdle = 'aniIdle',// 车厢建造闲置
    BuildingIdle = 'aniIdle3',// 车厢建造中待机
    BuildOverIdle = 'aniIdle2',// 车厢建造完等待点击待机
    MOVE = 'aniIdle4',// 航行待机
    BuildStatic = 'jingzhi',// 车厢建造门打开静止
    BuildDecline = 'jiangxia'// 车厢建造门关闭静止

}

enum HeroAction {
    IDLE = 0, //什么也不做
    WAIT_TO_TARGET,
    MOVE,
    COLLECT,
    COLLECT_END,
    WAIT_CLAIM_REWARD,
    WAIT_TO_COLLECT,
    WAIT_TO_BATTLE,
    BATTLE,
    WAIT,
    SAVE_CAT,
    JUMP_GAME,
    CHASE_1,
    CHASE_2,
    CHASE_3,
    QUESTION,
    WAIT_TO_QUESTION,
    WAIT_TO_RAGE_MODE,
}

enum CarriageType {
    DORM, // 宿舍
    WORK, //工作
    VITALITY, //娱乐
}

enum CarriageID {
    DORM = 1013,//宿舍1
    DORM2,//宿舍2
    HEAD,//车头
    ENGINE,//动力室
    DINING,//餐厅
    DORM3,//宿舍3
    WATER, //造水室
    BATHROOM,//浴室
    DORM4,//宿舍4
    DANCEHALL,//舞厅
}

enum ForgeState {
    IDLE = 0,
    WORKING = 1,
    END = 2,
}

enum TrainState {
    Idle,
    Fight,
    Damage,
}

enum ItemID {
    STAR_HAMMER = 1,
    NOTE = 2,
    TIMESTONE = 4,//时间宝石
    WATER = 5,//水
    ELECTRIC = 6,//电
    UPGRADE_STONE = 7,//进阶石
    ENERGY = 8, //电池
    TICKET = 9, //车票
    InviteCard1 = 10, //蓝色角色邀请卡
    InviteCard2 = 11, //紫色角色邀请卡
    InviteCard3 = 12, //橙色角色邀请卡

    NormalBreak = 14,//普通镐子
    SpecialBreak = 15,//高级镐子
    Boom = 16, //炸弹
    Drill = 17,//钻头
    VITALITY = 18,//元气值
    SPACE_STONE = 19,//空间宝石
    TICKET_FRAG = 21,//车票碎片
    SPACE_STONE_FRAG = 22,//车票碎片
    PLANET_PROFILE_CONTAINER = 23,//星球贴纸容器
    PASSENGER_PROFILE_CONTAINER = 24,//乘客贴纸容器
    TECH = 25,//科比
    PUBLICITY = 26,//宣传单

    TIME_STONE_KEY_2 = 402,//时间之钥2
}

enum ItemType {
    DEFAULT = 0,
    MATERIAL = 1,//包括木材，矿物，零件
    CHEST = 2, //宝箱
    COLLECTION = 3,//采集物(自由采集获得)
    TIME_STONE_KEY = 4, //时间宝石钥匙
    TIME_STONE_KEY_FRAG = 5, //时间宝石钥匙碎片
    TIME_BOX = 6, //时间宝箱
    ROLE_TICKET = 10,//乘客指定邀请券
    TIME_MAIL = 16,//时空信件
    DOSING = 56, //烹饪材料
    FERTILIZER = 60,//肥料
}

enum MapType {
    RECT, //正常
    SKEW, //斜
    SKEW_L, //斜
    SKEW_R, //斜
}


enum FacilityState {
    LOCK,   //未解锁
    UNLOCK, //已解锁
    BUYED, //已购买
    USING,  //使用中
}

enum MapSceneType {
    MAIN = 1,// 主楼
}

enum BuildAttr {
    STAR = 'star',
    HEART = 'heart',
    ELECTRICITY = "electricity", //电力
    WATER = "water",//水
    VITALITY = "vitality", //元气值
    SKIN = "skin",//皮肤
    LOAD = "load",//载重
}

enum PassengerAttr {
    HP = 'hp',
    ATTACK = 'attack',
    SKILL = 'skill',
}

enum PassengerQuality {
    N = 1,
    R,
    SR,
    SSR,
}

enum BedState {
    LIE = 'lie',
    UNLIE = 'unlie'
}

enum ConditionType { //通用条件类型
    GM = -1, //一些特殊操作
    NONE = 0,
    DIAMOND = 1,//钻石
    STAR_DUST = 2,//星尘
    HEART = 3,//爱心
    WORLD_TIME = 4,// 模拟时间
    WATER = 5,//水
    ELECTRIC = 6,//电
    VITALITY = 7,//元气值
    TECH = 9,//科比

    PASSENGER = 10,//乘客
    PROP = 11,//道具
    BUILD_ID = 12,//设施
    FUNCTION = 13,//功能(解锁)
    CARRIAGE = 14,//车厢
    ENTRUST_SEAT = 15,//委托坑位
    CHEST = 17, //宝箱
    SEED = 18, //种子
    PLANET_NODE = 21, //星球节点
    PLANET_BATTLE_NODE = 211, //战斗节点
    PLANET_COMPLETE = 22, //星球通关
    EQUIP = 23, //武器
    PASSENGER_SKIN = 24, //角色皮肤
    CHARACTER_FRAG = 25, //乘客碎片
    BLACK_HOLE_CURRENCY = 26,//星海币
    ORE_ITEM = 27, //矿石
    ARRESTPAPER = 28,//通缉令
    ARREST_CURRENCY = 29,//通缉币
    CHARACTER_PROFILE = 30,//乘客贴纸
    PLANET_PROFILE = 31,//星球贴纸
    PROFILE_BRANCH_ENERGY = 32, //记忆阁能量
    PUBLICITY = 33, //宣传单

    PASSENGER_EXPLORE_SP = 101, //乘客探索奖励材料特殊处理
    INSTANCE_LEVEL = 201, //副本等级
    AD = 301, //广告
    JACKPOT_COUNT = 401, // 累计邀请乘客次数
    TASK_DIALOG = 501, // 传话任务
}

enum PassengerAnimationType {
    LIFE = 'life',
    BATTLE = 'battle',
    COLLECT = 'collect',
    SP = 'sp'
}


let setBaseType = function (enums, baseType) {
    for (let key in enums) {
        let val = enums[key]
        enums[key] = baseType + "/" + val
    }
}

enum PassengerLifeAnimation {
    IDLE = "ani_idle",
    WALK = "ani_walk",
    SLEEP = "ani_sleep",
    JUMP = 'ani_jump',
    DOWN = 'ani_down',
    SIT = "sit_idle",
    CLEAN = "ani_clean",
    COOK = "ani_cook2",
    CUT_FOOD = "ani_cook",
    GET_UP = "ani_getUp", //起床(躺->坐起)
    GET_DOWN = "ani_getDown", //躺下(坐着->躺)
    SIT_SLEEP = "sit_sleep",
    SIT_DAP = 'sit_dap', //坐着弹一弹
    TO_LEFT_BED = "goToBed_1", //走台阶上床
    TO_RIGHT_BED = 'goToBed_2', //走书柜上床
    STAND_TO_SIT = 'change_standToSit', //跳起来坐下中间衔接
    SIT_TO_STAND = 'change_sitToStand', //落到地面中间衔接
    CHECK_IN_IDLE = 'checkIn_idle',
    CHECK_IN_WALK = 'checkIn_walk',
    CHECK_IN_TIDY = 'checkIn_tidy',
    SIT_ACT = 'sitAct', //坐着表演
    STAND_ACT_START = 'standAct_start', //站着表演-开始
    STAND_ACT = 'standAct', //站着表演-循环/一次性
    STAND_ACT_END = 'standAct_end', //站着表演-结束
    TRAIN_HEAD_ACT = 'act_chetou', //车头表演
    STAND_SLEEP = 'stand_sleep', //站着睡觉
    WALK_PLATE = "walk_plate", //端着盘子走
    FISH_START = "catchStar_fish1", //钓鱼-抛竿
    FISH = "catchStar_fish2", //钓鱼-等待
    FISH_END = "catchStar_fish3", //钓鱼-收杆
    CATCH_STAR = "catchStar", //捕星

    REPAIR = "ani_fix", //修理
    SEARCH = "ani_search", //搜查
    SEARCH_WALK = "ani_search_walk", //搜查走
    WORK = "ani_work", //工作
    WASH = "ani_wash", //洗衣服
    WALK_JOB = "ani_walk_job", //用于手上没东西的走
    IDLE_JOB = "ani_idle_job", //用于手上没东西的idle

    //吃
    STAND_EAT = "dining/eat",    //站着吃
    STAND_MUNCH = "dining/munch",    //站着咀嚼
    STAND_EAT_BY_PLATE = "dining/eat_by_plate", //站着端着盘子吃
    STAND_MUNCH_BY_PLATE = "dining/munch_by_plate", //站着端着盘子咀嚼
    SIT_EAT = "dining/sit_eat",  //坐着吃
    SIT_MUNCH = "dining/sit_munch",  //坐着咀嚼
    SIT_EAT_BY_PLATE = "dining/sit_eat_by_plate",   //坐着端着盘子吃
    SIT_MUNCH_BY_PLATE = "dining/sit_munch_by_plate",   //坐着端着盘子咀嚼
    SIT_EAT_BY_TABLE = "dining/sit_eat_by_table",   //坐着把盘子放桌子上吃
    SIT_MUNCH_BY_TABLE = "dining/sit_munch_by_table",   //坐着把盘子放桌子上咀嚼
    WALK_WITH_HAND = "dining/walk_with_hand", //拿着食物的走
    WALK_WITH_PLATE = "dining/walk_with_plate",   //端着盘子走

    //喝
    STAND_DRINK = "dining/drink",    //站着喝
    STAND_TASTE = "dining/taste",    //站着品味
    SIT_DRINK = "dining/sit_drink",  //坐着喝
    SIT_TASTE = "dining/sit_taste",  //坐着品味
    WALK_WITH_DRINK = "dining/walk_with_drink", //拿着饮料走

    //动力室专属
    ENGINE_NOTES = "engine/notes", //工程狮记笔记，循环
    ENGINE_NOTES_THINK = "engine/notes2", //工程狮记笔记, 思考
    ENGINE_SIT_SLEEP = "engine/sitSleep", //工程师摸鱼
    ENGINE_WORK = "engine/sit_work", //坐拉力器
    ENGINE_COMPUTER = "engine/sit_work2", //操作电脑
    ENGINE_THROW = "engine/throw", //扔燃料

    // 餐厅
    DINING_WORK = "dining/ani_work",

    //浴室
    ANI_BATH = "ani_bath", //泡澡

    // 舞厅
    ANI_DANCE = "ani_dance", //跳舞

    //造水间专属
    WATER_ADD = "water/ani_add", //加煤
    WATER_AIR = "water/ani_step", //打气
    WATER_CRY = "water/ani_cry", //哭哭
    WATER_PULL = "water/ani_pull", //实验台加入材料
    WATER_PLAY = "water/ani_play", //玩游戏

    APPEAR = "ani_flash2", //出现
    DISAPPEAR = "ani_flash1", //消失
}
setBaseType(PassengerLifeAnimation, PassengerAnimationType.LIFE)

enum PassengerBattleAnimation {
    ATTACK_IDLE = 'aniAttackIdle',

    ATTACK_READY = 'aniAttackReady', //对撞前的准备动作
    ATTACK_READY_JUMP = 'aniAttackReadyJump', //准备状态下的起跳
    ATTACK_JUMP = 'aniAttackJump', //对撞起跳
    ATTACK = 'aniAttack',
    ATTACK_HIT = 'aniHit', //在对撞时受击
    ATTACK_FALL = 'aniAttackFall', //普通下落
    ATTACK_FALL2 = 'aniAttackFall2', //击败对方时下落

    ATTACK_DIAUP = 'aniDiaup', //对撞时死亡
    ATTACK_DIAUP2 = 'aniDiaup2', //对撞时同归于尽
    DEAD = 'aniDiaup3', //普通受击死亡

    HIT = 'aniAttackStand', //一般受击

    SKILL = "skill", //普通使用技能
    DEATH_SKILL = "deathSkill1", //对撞时死亡，需要放技能的动画
    DEATH_SKILL2 = "deathSkill2", //普通受击死亡，需要放技能的动画
    DEATH_SKILL_LOOP = "deathSkill_loop", //普通受击死亡，需要循环放技能的动画

    GAIN_BUFF = 'gainBuff', //获得增益
}
setBaseType(PassengerBattleAnimation, PassengerAnimationType.BATTLE)

enum PassengerAnimation {
}

enum PassengerSpAnimation {
    IDLE = "ani_idle",
    WALK = "ani_walk",
    CLEAN = 'ani_clean1', //正面打扫
    CLEAN_DOWN = 'ani_clean2', //下方打扫
    CLEAN_UP = 'ani_clean3', //上方打扫
    HAPPY = 'ani_happy', //叉腰开心
    TALK = 'ani_talk', //一般交谈
    TALK_TIRED = 'ani_talk2', //疲惫交谈
    TALK_SHOUT = 'ani_talk3', //喊话
    TALK_THINK = 'ani_talk4', //说话(带着思考的样子)
    TIDY = 'ani_tidy',
    TIDY2 = 'ani_tidy2',
    TIDY3 = 'ani_tidy3',
    THINK = 'ani_think',
    GLASSES = 'ani_glasses',
}
setBaseType(PassengerSpAnimation, PassengerAnimationType.SP)

enum HeroAnimation {
    PICK_TREE = "aniPickTree",
    PICK_ORE = "aniPickOre",
    PICK_PART = "aniPickPart",
    PICK_SEED_DOWN = "aniPickSeed_down",
    PICK_SEED_UP = "aniPickSeed_up",
    PICK_SEED_IDLE_UP = "aniPickSeed_idle_up",
    PICK_SEED_IDLE_DOWN = "aniPickSeed_idle_down",

    //QTE
    QTE_TREE_MISS = "aniQTETreeMiss",
    QTE_ORE_MISS = "aniQTEOreMiss",
    QTE_PART_MISS = "aniQTEPartMiss",

    //跳一跳
    JUMP = 'aniJump',
    JUMP2 = 'aniJump2',
    JUMP3 = 'aniJump3',
    JUMP4 = 'aniJump4',

    //跑
    RUN = 'aniRun',

    //狂暴模式
    RAGE_MODE_1 = 'aniPickRun1',
    RAGE_MODE_2 = 'aniPickRun2',
    RAGE_MODE_3 = 'aniPickRun3',
}
setBaseType(HeroAnimation, PassengerAnimationType.COLLECT)

enum PassengerLifeEvent {
    SLEEP = "SLEEP",
    WAKEUP = "WAKEUP",
    LEAVE = "LEAVEHOME",
    GOHOME = "GOHOME",
    DINNER = "DINNER",
}

enum BuildAnimation {
    IDLE = "aniIdle",
    JINGZHI = "jingzhi",
    BUILD = "aniBuild",
    OLD = 'aniIdle_old',
    WORK = "aniWork",
    USE = "aniUse",
}


//一号寝室设施类型
enum DormBuildType {
    LEFT_CHAIR = 1,
    LEFT_BED,
    TABLE,
    RIGHT_BED,
    RIGHT_CHAIR,
    BOOKCASE,
}

//二号寝室设施类型
enum Dorm2BuildType {
    BED = 1,
    TABLE,
    CHAIR_1 = 3,
    HEARTH,
    TV = 6,
    CHAIR_2 = 10,
    CHAIR_3 = 7,
}

enum Dorm3BuildType {
    SHED = 4,
    BED_1 = 1,
    BED_6 = 6,
    TOILET = 5,
    CHAIR_3 = 3,
    CHAIR_7 = 7,
    WASHSTAND = 8,
}

enum Dorm4BuildType {
    BED_1 = 1,
    BED_4 = 4,
    CHAIR_3 = 3,
    CHAIR_7 = 7,
}

enum EngineBuildType {
    POWER = 1,
    CHAIR = 3,
    WATER_BOX,
    PULLER,
    COMPUTER,
    Energy = 9,
    TREADMILL = 10,
}

enum DiningBuildType {
    ORDER_FOOD = 1,
    ORDER_DRINK = 4,
    BAKING_MACHINE = 7,
    TABLE_3 = 3,
    TABLE_10 = 10,
    CHAIR_2 = 2,
    CHAIR_5 = 5,
    CHAIR_6 = 6,
    CHAIR_9 = 9,
    CHAIR_12 = 12,
}

enum WaterBuildType {
    TABLE = 1,
    SAUNA = 3,
    SAUNA_SHELL = 4,
    LIGHT = 5,
    GAME = 6,
    AIR = 8,
    RUN = 10,
}

enum BathRoomType {
    FROG = 3,//青蛙
    POOL_LEFT = 1,//浴池-左
    POOL_RIGHT = 4,//浴池-右
    BOARD_LEFT = 8,//踏板-左
    BOARD_RIGHT = 9,//踏板-右
}

enum BuildUnlockType {
    DEFAULT,// 默认解锁
    NEED_BUY// 需要购买
}

enum BuildFromType {
    NONE,
    BUY,
    CHANGE,
}

enum SoundState {
    CLOSE,
    OPEN,
}


enum AccelerateState {
    NORMAL,// 正常
    ACCELERATE // 加速中
}

// 消息通知类型
enum MessageType {
    NONE, //自动消失 白色
    PROP, //获得道具 白色
}

enum CfgName {
    PLANET = "Planet",
    PROP = "Item",
    BUILD = "TrainItem"

}

//教程步骤类型
enum GuideStepType {
    CALL_FUNC = 0, //调用某个函数
    SEND_EVENT, //发送事件
    SESSION,//开启某个可操作区域
    STORY, //对话/剧情，本质是SESSION的语法糖
    CLICK_NEXT_STEP, // 点击后再发送事件
    WAIT_NEXT_STEP, // 等下一步
}

//关键教程标记
enum GuideStepMark {
    REPAIR_TRAIN = "REPAIR_TRAIN",
    ENTER_TRAIN = 'ENTER_TRAIN',
    BUILD_BED_START = "BUILD_BED_START",
    BUILD_BED_END = "BUILD_BED_END",
    MOVE_PLANET_2_START = "MOVE_PLANET_2_START",
    MOVE_PLANET_2_END = "MOVE_PLANET_2_END",
    OPEN_ANIM = "OPEN_ANIM",
    DEEP_EXPLORE_START = "DEEP_EXPLORE_START",
    DEEP_EXPLORE_END = "DEEP_EXPLORE_END",
    FIRST_ENTER_PLANET_1009 = "FIRST_ENTER_PLANET_1009",
    START_ORE_PUZZLE = "START_ORE_PUZZLE",
    START_SPACE_STONE = "START_SPACE_STONE",
    MAKE_NEXT_EQUIP = "MAKE_NEXT_EQUIP",
}

enum StoryType {
    TEXT = 0, //纯文本
    OPTION, //选项，确定/取消
}

enum GuideModule {
    START = 1,
    PLANET_COLLECT,
    USE_BUILD,
    PLANET_BATTLE,
    USE_BUILD2,
    SAVE_CAT,
    CHARACTER,
    PLANET_COMPLETE,
    TRIAN_HEAD = 13,
}

enum PlanetEvent {
    //学院星
    CLOCKWORK = 'clockwork',
    SAVE_CAT = 'save_cat',
    TREE_HOUSE = 'tree_house',
    FOOD = 'food',
    LIBRARY_GUESS = 'library_guess',

    //永恒花园
    JUMP_1 = 'jump1',
    JUMP_2 = 'jump2',
    CURVE_CHASE = 'curve_chase',//弯道追逐
    BATTLE_TWO = 'battle_two',//第二场战斗
    FLOWER_TREE = 'FLOWER_TREE',//银花火树
    ENTER_GARDEN_MAP_2 = 'enter_map_2',
    AWAKE_GARDEN_BOSS = 'awake_boss',
    TIME_STONE_GARDEN = 'time_stone',
    NOTE_BOOK = "note_book", //博士喵笔记
    BLACK_HOLE_ENTRY = "black_hole_entry", //星海迷宫入口

    //机械城
    ENTER_LIFT = "enter_lift", //进入电梯
    BLACK_HOLE_KEY = "black_hole_key", //星海迷宫钥匙

    //宝石星
    HOLE_AFTER = "hole_after", //洞

    //魔眼
    STAR_FRAG = "star_frag", //星光碎片

    JUMP = 'jump',

    // 雪星
    SIK_R1 = "sik_r1",
    SIK_R2 = "sik_r2",

    //通用
    RANDOM_BOX = "random_box", //随机宝箱
    TIME_LIMIT_BOX = "time_limit_box", //限时宝箱
    MONSTER_BOX = "monster_box", //怪物宝箱
    TOOL_BLESS = "tool_bless", //工具祝福
    RAGE_MODE = "rage_mode", //狂暴模式
    PUZZLE_BOX = "puzzle_box", //解谜
}

// hex颜色
enum HexColor {
    NEXT_PREVIEW = '#7AC23F',// 技能等级下一级预览颜色变化
    NOR_PREVIEW = '#6F512C'
}

enum PlanetMoveState {
    ENTER = 1,
    MOVE,
    EXIT,
    REACH,
}

enum PlanetNodeType {
    MINE = 0, //采集物
    CHECK_POINT, //战斗关卡
    PORTAL, //传送门
    PUZZLE, //解谜
    CHEST, //宝箱
    NONE, //空节点
    QUESTION, //问答
}

enum PlanetMineType {
    TREE = 1, //植物
    ORE, //矿物
    PART, //零件类
    SEED, //种子
}

enum PlanetGuideType {
    CLICK,
    SLIDER,
}

enum PlanetMineGameType {
    CLICK = "CLICK",
    QTE = "QTE",
    HIGH = "HIGH",
}

enum LongPress {
    LPSTART = 'LPSTART',// 长按开始
    LPEND = 'LPEND',// 长按结束
    CLICK = "CLICK"
}

enum PassengerType {
    BATTLE = 1,
    NORMAL,
}

enum SkillType {
    BATTLE = "battleSkill",
    LIFE = "growSkill",
    EQUIP = "equipSkill",
    BLACKHOLE_EQUIP = "blackHoleEquip",
    INSTANCE = "INSTANCE",
    TRAIN = "TRAIN",
}

enum LifeSkillTarget {
    TRAIN = "TRAIN",//车厢
    TRAIN_SELF = "TRAIN_SELF", //乘客自己居住的车厢
    CHARACTER = "CHARACTER",// 角色
    CHARACTER_ROOMMATE = "CHARACTER_ROOMMATE", //角色（舍友，排除自己）
    ROOMMATE_NOBATTLE = "ROOMMATE_NOBATTLE", //没有战斗力的舍友
    CHARACTER_MEMBER = "CHARACTER_MEMBER",	//角色（参与委托的人，排除主人）
    ENTRUST_LIFE = "ENTRUST_LIFE", //生活委托，用来对生活委托做增益
    UNLOCK_ENTRUST = "UNLOCK_ENTRUST", //解锁生活委托（解锁前，不可刷出）
    DISPATCH_ENTRUST = "DISPATCH_ENTRUST", //可接取生活委托
}

enum LifeSkillEffectType {
    STAR = "STAR",	//星尘（设施产出）
    ELECTRICITY = "ELECTRICITY", //电力
    WATER = "WATER",//水
    TIP = "TIP", //小费（角色产出）
    HEART = "HEART", //爱心（角色产出）
    ATTACK = "ATTACK",
    HP = "HP",
    ENTRUST_REWARD = "ENTRUST_REWARD",//委托奖励
    ENTRUST_TIME = "ENTRUST_TIME",//委托时间
    EXPLORE_TIME = "EXPLORE_TIME", //探索时间
    COLLECT_STAR = "COLLECT_STAR", //自动捡星尘
    COLLECT_HEART = "COLLECT_HEART", //自动捡爱心
}

enum SkillEffectiveType {
    GO_OUT_WITH = "GO_OUT_WITH",
    GET_ON_TRAIN = "GET_ON_TRAIN",
    ROOMMATE = "ROOMMATE",
    BUILD_TRAIN = "BUILD_TRAIN",
    SAMERACE = "SAMERACE",
    WORKING = "WORKING",
}

enum ExtractType {
    Common,//普通抽
    LUCK // 幸运抽
}

//通用按钮的配色
enum CommonBtnType {
    YELLOW,
    GREEN,
    BLUE,
    GRAY,
    RED,
}

enum PassengerUpTips {
    UP_ATTR = 'UpArrtTips',
    UP_SKILL = 'UpSkillTips',
    UNLOCK_TALENT = 'UnlockTalentTips',
    COMMON = 'UpLeaveTips',
    ADD_SHIP = 'FlotShipTips'
}

enum Bundle {
    START = "startSubpackage",//新手玩家必须要使用的资源
    RES = "RESOURCE",
}

//存档状态
enum RecordState {
    SAME = 0, //和线上一样
    DOWNLOAD = 1, //需要下载
    UPLOAD = 2, //需要上传
    CD = 3, //上传时间冷却中
    RECHECK = 4, //上传整个存档校验
    LOG_OFF = 5, //注销
}

//语言配置表名
enum LangCfgName {
    DEFAULT = "LanguageTemplate",
    LOAD = "LanguageLoading",
    PLOT = "Plot",
    CharacterPlot = "CharacterPlot",
}

enum UIFunctionType {
    BUILD = "BUILD",
    EXPLORE = "EXPLORE",
    CHARACTER = "CHARACTER",
    LOTTERY = "LOTTERY",
    SPEED_UP = "SPEED_UP",
    TRAIN = "TRAIN",
    TASK = 'TASK',
    STAR = 'STAR',
    TOOL = 'TOOL',
    BAG = 'BAG',
    SET = 'SET',
    STAR_MAP = 'STAR_MAP',
    CURRENCY_1 = 'CURRENCY_1',
    CURRENCY_2 = 'CURRENCY_2',
    CURRENCY_3 = 'CURRENCY_3',
    PLANET_BAG = 'PLANET_BAG',
    PLANET_BACK = 'PLANET_BACK',
    VIEWING_MODE = 'VIEWING_MODE',
    CHARACTER_DEVELOP = 'CHARACTER_DEVELOP',
    BATTLE_AUTO = 'BATTLE_AUTO',
    BATTLE_SPEED = 'BATTLE_SPEED',
    BATTLE_BACK = 'BATTLE_BACK',
    EXPLORE_AUTO = 'EXPLORE_AUTO',
    EXPLORE_SPEED_UP = 'EXPLORE_SPEED_UP',
    ACHIEVEMENT = 'ACHIEVEMENT',
    SHOP = 'SHOP',
    TRANS = "TRANS",
    RESONANCE = "RESONANCE",
    WORK_WATER = 'WORK_WATER',
    WORK_ENGINE = 'WORK_ENGINE',
    ITEMTRANS = "ITEMTRANS",
    PLAY_ENTER = "PLAY_ENTER",
    PLAY_BLACKHOLE = "PLAY_BLACKHOLE",
    PLAY_TOWER = "PLAY_TOWER",
    PLAY_TRADE = "PLAY_TRADE",
    PLAY_ARENA = "PLAY_ARENA",
    PLAY_ENTRUST = 'PLAY_ENTRUST',
    PLAY_INSTANCE = "PLAY_INSTANCE",
    PLAY_INSTANCE_BOSS = "PLAY_INSTANCE_BOSS",
    MENU_BATHHOUSE = "MENU_BATHHOUSE",
    MENU_BALLROOM = "MENU_BALLROOM",
    MENU_DINING = "MENU_DINING",
    MENU_FOOD = "MENU_FOOD",
    MENU_DRINK = "MENU_DRINK",
    CHECKIN_DORM_1 = "CHECKIN_DORM_1",
    CHECKIN_DORM_2 = "CHECKIN_DORM_2",
    CHECKIN_DORM_3 = "CHECKIN_DORM_3",
    CHECKIN_DORM_4 = "CHECKIN_DORM_4",
    EQUIP_BUY = "EQUIP_BUY",
    EQUIP_MAKE = "EQUIP_MAKE",
    PLAY_DAILY_TASK = "PLAY_DAILY_TASK",
    PLAY_COLLECT = "PLAY_COLLECT",
    PLAY_ORE = "PLAY_ORE",
    PLAY_TRANSPORT = "PLAY_TRANSPORT",
    PLAY_SPACE_STONE = "PLAY_SPACE_STONE",
    PLAY_FIELD = "PLAY_FIELD",
    PLAY_SEED = "PLAY_SEED",
    WIKI = "WIKI",
    PLAY_ARCHIVES = "PLAY_ARCHIVES",
    BLACK_HOLE_SHOP = "BLACK_HOLE_SHOP",
    EQUIP_MAKE_2 = "EQUIP_MAKE_2",
    PLAY_PVP_1 = "PLAY_PVP_1",
    PLAY_PVP_2 = "PLAY_PVP_2",
    PLAY_PVP_3 = "PLAY_PVP_3",
    PLAY_WANTED = "PLAY_WANTED",
    DEEP_EXPLORE = "DEEP_EXPLORE",
    PROFILE = "PROFILE",
    OUTPUT = "OUTPUT",
    PLAY_TRAIN_DAILY_TASK = "PLAY_TRAIN_DAILY_TASK",
    TRAIN_ACTIVITY = "TRAIN_ACTIVITY",
    TRAIN_TECH = "TRAIN_TECH",
    PLANET_PUBLICITY = "PLANET_PUBLICITY",
}

enum GuideFingerType {
    HAND = "HAND",//手+光圈
    HAND_B = "HAND_B",//手
    ARROWS = "ARROWS",//箭头
    TOUCH_MOVE = "TOUCH_MOVE",//引导拖动屏幕
}

enum GuiderType {
    GUIDER = 1005,
    GUIDER_1015 = 1015,
}

enum BUILD_MOUNT_POINT {
    SLEEP = "sleep",
    BODY2 = "body2",
    SIT = "sit",
    WALK = "walk",
    USE = 'use',
}

enum TipsNotType {
    NORMAL = 1,
    OPT,
    NORMAL_2
}

enum TaskType {
    GOTO_PLANET = 'GOTO_PLANET',//前往指定id星球
    EXPLORE_PLANET = 'EXPLORE_PLANET',//指定id星球探索进度达到value
    EXPLORE_PLANET_COMPLETE = 'EXPLORE_PLANET_COMPLETE',//通关星球
    BUILD_TRAINITEM = 'BUILD_TRAINITEM',//建设指定id[]设施
    BUILD_TRAINITEM_LEVEL = 'BUILD_TRAINITEM_LEVEL',//指定车厢完成指定等级设施的建设(主题等级)
    TRAINITEM_LEVELUP = 'TRAINITEM_LEVELUP',//将指定设施升到level级
    TRAINITEM_LEVELUP2 = 'TRAINITEM_LEVELUP2',//将指定id[]车厢的所有设施升到level级
    UNLOCK_THEME = 'UNLOCK_THEME',//解锁指定id[]车厢的主题
    BUILD_TRAIN_INDEX = 'BUILD_TRAIN_INDEX',//建设第value节车厢
    BUILD_TRAIN = 'BUILD_TRAIN',//建设指定id[]车厢
    GET_ITEM = 'GET_ITEM',//收集指定item[].id材料 数量达到item[].num
    CHARACTER_LEVEL = 'CHARACTER_LEVEL',//所有角色等级总计达到value
    CHARACTER_RANK = 'CHARACTER_RANK',//所有角色星级总计达到value
    CHARACTER_LEVELUP = 'CHARACTER_LEVELUP',//value个角色升到level级
    CHARACTER_RANKUP = 'CHARACTER_RANKUP',//value个角色培养到rank星
    CHARACTER_GETON2 = 'CHARACTER_GETON2',//指定id[]角色入住指定id车厢(不指定id车厢时表示任意车厢)
    CHARACTER_GETON = 'CHARACTER_GETON',//value个角色入住列车
    GET_CHARACTER_INDEX = 'GET_CHARACTER_INDEX',//邀请第value名乘客
    TOOL_BUILD = 'TOOL_BUILD',//打造num个工具
    TOOL_CHANGE = 'TOOL_CHANGE',//更换指定类型的工具
    TOOL_LEVELUP2 = 'TOOL_LEVELUP2',//指定id[]类型的工具（仅限一种），升级value次（不指定id时，不限工具类型，升级value次）
    TOOL_LEVELUP = 'TOOL_LEVELUP',//指定id[]类型的工具（仅限一种），升到value级（不指定id时，所有工具升到value级）
    TOOL_RANKUP = 'TOOL_RANKUP',//指定类型的工具，升到value品阶
    TOOL_TABLEUP = 'TOOL_TABLEUP',//打造台升到level级
    ENTRUST_START = 'ENTRUST_START',//开始进行指定数量的委托任务
    ENTRUST_COMPLETE = 'ENTRUST_COMPLETE',//累计完成value次委托
    GOTO_WORK = 'GOTO_WORK',//安排value个角色去指定id车厢工作
    GOODS_UNLOCK = 'GOODS_UNLOCK',//解锁车厢内所有商品
    PLANET_BATTLE_ID = 'PLANET_BATTLE_ID',//通关星球战斗节点id
    TO_DEEP_EXPLORE = 'TO_DEEP_EXPLORE',//前往深度探索
    DEEP_EXPLORE = 'DEEP_EXPLORE',//完成深度探索
    TOWER = 'TOWER',//前往时光之境
    PROFILE = 'PROFILE',//前往乘客档案
    GOTO_PLANET_ENTRY_1009 = 'GOTO_PLANET_ENTRY_1009',//回宝石星小镇
    GOTO_PLANET_ENTRY_1007 = 'GOTO_PLANET_ENTRY_1007',//回雪星小镇调查
    ORE_PUZZLE = 'ORE_PUZZLE',//前往挖矿解谜
    EXPLORE_PLANET_AREA = 'EXPLORE_PLANET_AREA',//前往星球探索
    TO_TRANSPORT = 'TO_TRANSPORT',//前往运送
    TRANSPORT = 'TRANSPORT',//前往运送
    INSTANCE_PUZZLE = 'INSTANCE_PUZZLE',//魔眼谜题
}

enum TaskTriggerType {
    REACH_PLANET = "REACH_PLANET",//抵达某星球后触发
    COMPLATE_PLANET = "COMPLATE_PLANET",//通关某星球后触发
    COMPLATE_PLANET_ID = "COMPLATE_PLANET_ID",//完成星球节点后触发
    BUILT_CARRIAGE = "BUILT_CARRIAGE",//已建造某车厢后触发
    UNLOCKFUNC = "UNLOCKFUNC",//伴随某个功能一同解锁
    GUIDE_UNLOCKFUNC = "GUIDE_UNLOCKFUNC",//已解锁某新手功能后触发
    GUIDE_COMPLATE_ID = "GUIDE_COMPLATE_ID",//已完成某新手步骤后触发
    COMPLATE_BATTLE = "COMPLATE_BATTLE",//完成某场战斗时解锁
    COMPLATE_THEME = "COMPLATE_THEME",//完成某主题时解锁
}

enum ToolProperty {
    ATK = 'ATK',//工具攻击力
    SPD = 'SPD',//工具攻击速度
    ADD = 'ADD',//采集物掉落
}

// 车厢建造界面位置类型
enum ConsolePosType {
    Up = 'Up',
    Down = 'Down',
    TwoUp = 'TwoUp',
    TwoDown = 'TwoDown',
}

enum GuideFingerDir {
    TOP = "TOP",
    LEFT = "LEFT",
    RIGHT = "RIGHT",
    BOTTOM = "BOTTOM",
}

enum WeakGuideType {
    BUY_RIGHT_BED_1 = 1, //主界面引导买床
    COLLECT_STAR = 2, //引导捡星尘
    BUY_RIGHT_BED_2,
    BUY_RIGHT_BED_3,
    GOTO_BUY_BUILD_1 = 5,//去建造
    GOTO_BUY_BUILD_2,
    GOTO_BUY_TRAIN_1 = 7,//去建设车厢
    GOTO_BUY_TRAIN_2,
    GOTO_BUY_TRAIN_3,
    ASSIGN_WORK_1 = 10,//安排工作
    GO_PLANET_1 = 11,//前往星球
    GO_PLANET_2,
    CHARACTER_GETON_1 = 13,//角色入住
    CHARACTER_GETON_2,
    CHARACTER_1 = 15,//角色
    JACKPOT_1 = 16,//邀请
    EXPLORE_1 = 17,//探索
    PLANET_CONTROL_1 = 18,
    TOOL_MAKE_1 = 19,
    GOTO_LEVELUP_BUILD_1 = 20,//去升级(设施)
    GOTO_LEVELUP_BUILD_2 = 21,
    BACK_TO_PLANET_1 = 22,//返回星球
}

enum GuideTaskTargetType {
    TREE_HOUSE_STAIR,
    TREE_HOUSE_DOOR,
    LIBRARY_GUESS,
}

enum RoleGuideActionType {
    TRIAN_HEAD_DRINK = 1,
}

// new标签类型
enum MarkNewType {
    PROP = 1,//新获得的关键道具
    THEME = 2,//新解锁的主题
    ROLE_NEW = 3,//新获得的角色
    ROLE_UNREAD = 4,//没有阅读过角色档案
    BUILD_CAN_LVUP = 5,//设施可以继续升级
    BUILD_UNLOCK_SKIN = 6,//设施解锁新外观
    PROP_USE = 7,//获得的关键道具是否使用过
    NPC_DIALOG = 8,//NPC对话
    PLANET = 9,//星球
    SEED = 10,//种子
}
// new标签类型所需数据个数(默认1)
let MarkNewLength = {
    [MarkNewType.THEME]: 2,
}

enum EnergyRecoverType {
    FREE = 1,
    DIAMOND = 2,
    ENERGY = 3,
    AD = 4,
}

enum SpeedUpType {
    S1 = "s1", //模拟时间流逝
    S2 = "s2", //产出表现（常规）
    S3 = "s3", //数值效果
    S4 = "s4", //产出表现（引导期间）
    S5 = "s5", //模拟演绎
    S6 = 's6', //星球航行加速
    S7 = 's7', //自动采集加速
    S8 = 's8', //车厢建造加速
}

enum EntrustLifeType {
    SKILL_CLEAN = "SKILL_CLEAN",
    SKILL_SHOW = "SKILL_SHOW",
    ATTRIBUTE_ACCOMPANY = "ATTRIBUTE_ACCOMPANY",
    ATTRIBUTE_BUSINESS = "ATTRIBUTE_BUSINESS",
    ATTRIBUTE_EXPLORE = "ATTRIBUTE_EXPLORE",
    ATTRIBUTE_PATROL = "ATTRIBUTE_PATROL",
    ATTRIBUTE_GUARD = "ATTRIBUTE_GUARD",
}

enum EntrustLifeGroup {
    CLEAN = 1,
    SHOW,
    ACCOMPANY,
    BUSINESS,
    EXPLORE,
    ATTACK,
    HP,
}

enum EntrustLifeAttributeType {
    HP = 'hp',
    ATTACK = 'attack',
    TIP = 'tip',
    HEART = 'heart',
    EXPLORE = 'explore',
    SKILL = 'skill'
}

enum CarriageUsePosType {
    NORMAL,
    CLEAN, //鸡毛掸子打扫
    CLEAN_FLOOR, //吸尘器打扫
    START, //出生点
    CHECK_IN, //入住时出生的点位
    CHESS, //下棋
    TALK, //聊天
    EXPLORE_BOX, //拿着宝箱的站位
    DANCING,//跳舞
    REPAIR, //修理
    STAND, //站岗
}

//进入车厢方式
enum CarriageIntoType {
    NORMAL, //一般切换车厢
    CHECK_IN, //入住
    CHANGE_WORK, //变更工作
}

enum GoodsType {
    FOOD = 1,
    DRINK,
    TEA,
}

enum UserType {
    APP_WX = "app_wx",
    APPLE = "app_apple",
    GUEST = "app_guest",
    WX = "wx",
    QQ = "qq",
    FB = "app_fb",
    TWITTER = "app_twitter",
    GOOGLE = "app_google",
    FB_INSTANT = 'fb_instant',
}

enum Platform {
    UNKNOWN = "unknown",
    WX = "wx",
    QQ = "qq",
    ANDROID = "android",
    IOS = "ios",
    WEB = "web",
}

enum Channel {
    NONE = "",
    TAPTAP = "taptap",
}

enum PayPlatformType {
    NONE = 'none',
    WX = 'wx',
    QQ = 'qq',
    APPLE = 'apple',
    GOOGLE = 'google',
    APP_WX = 'app_wx'
}

enum PassengerPlotState {
    NONE,
    START,
    COMPLETE
}

enum BlackHoleNodeType {
    START = 0,
    BATTLE,
    ATTR,
    REBIRTH,
    AID,
    END,
    BOX,
}

enum BlackHoleBuffType {
    SELECT = 1,
    RANDOM,
    ALL,
}

enum RelaterType {
    Plot = 1,
    Options,
    Black,
    Dialog,
}

enum RolePnlTabType {
    DETAIL,
    RESONANCE,
    TRANSFER,
}

enum RoleAnimalType {
    CAT = 1,
    DOG,
    OTHER,
}

enum RoleBattleType {
    ASSIST = 1, //辅助
    DAMAGE, //伤害
    IMPROVE, //自强
    SUMMON, //召唤
    CONTROL, //控制
}

enum GoodsObjectType {
    CHARACTER = "CHARACTER",
    BATTLE_TYPE = "BATTLE_TYPE",
    ANIMAL_TYPE = "ANIMAL_TYPE",
}

enum WantedState {
    DEFAULT = 0,
    START,
    END,
    COMPLETE
}

enum WantedConditionType {
    STAR = 0,
    QUALITY,
    ANIMAL_TYPE,
    BATTLE_TYPE,
}

enum StoreId {
    BLACK = 1,
    BLACK_HOLE = 2,
    ARREST = 3,
    RESOURCE = 4,
}

enum TeamId {
    COMMON = 0,
    BLACK_HOLE = 10,//有自己独立的team
    TOWER = 11,
}

enum ChangeWorkType {
    HIRE = 1,
    CHANGE,
    FIRE,
}

enum RuleType {
    NONE,
    EQUIP_LV,//装备升级
    BLACKHOLE,//星海迷宫
    TOWER,//时光之境
    BATTLE,//战斗（主线）
    ENTRUST,//悬赏
    STORE,//贸易星
    LOTTERY,//邀请乘客
    RESONANCE,//共鸣
    INSTANCE,//副本
    TRANSPORT,//运送
    FIELD,//种地
    ORE,//挖矿
    ARREST,//通缉
    ROLE_TRANSFER, // 乘客转移
    PVP_NORMAL, // 普通竞技场
    PVP_HIGH, // 高级竞技场
    PVP_PEAK, // 巅峰竞技场
    DEEP_EXPLORE, // 深度探索
    COLLECT, // 收集
}

enum FieldType {
    VEGETABLE = 1,//菜地
    FRUIT,//果园
}

enum FieldState {
    LOCK,//未解锁
    FREE,//空闲
    SEED,//种子（需浇水）
    TREE,//生长期
    FRUIT,//成熟
}

enum FieldOperationType {
    UNLOCK = 1,
    PLANET,
    WATER,
    FERTILIZER,
    HARVEST,
}

enum OreLandType {
    NONE = 0,
    GRAY,
    BLUE,
    PURPLE,
    BLACK,
    NEXT,
    SPECIAL,//传送门
    BREAK,
    BACK,
    BOSS,
    START,
    BOOM,
    DRILL,
    RUNAWAY_PURPLE,
}

enum OreOperateType {
    ONE = 0,
    TWICE,
    BOOM,
    DRILL,
    BATTLE,
    NEXT,
    SPECIAL,

}

enum InstanceWeather {
    WIND, // 大风
    SUN,// 烈日
    SUNNY,// 晴空
    SNOW,// 雪
    RAIN,// 雨
    RAINBOW,// 彩虹
}

enum BattlePreviewPnlType {
    NORMAL = 0,
    INSTANCE,

}

enum BlackHoleEquipTarget {
    BATTLE_TYPE = 1,
    ANIMAL_TYPE,
    ALL,
}

enum ROLE_ATTR_ID {
    BLACK_HOLE = 0,
    INSTANCE,
    EQUIP,
    TRAIN,
}

enum TalentAttrType {
    BASE_HP = 0,
    BASE_ATTACK = 1,
    SKILL,
}

enum OreMakeType {
    NORMAL,
    GREAT,
    PERFECT,
}

enum EquipEffectType {
    ATTR = 1,
    SKILL,
}

enum EquipEffectTarget {
    ATTACK = 1,
    HP,
}

enum IntType {
    CEIL = 1,
    FLOOR,
}

enum DailyTaskType {
    COLLECT = 1,
    EQUIP,
    DIALOG,
    BATTLE,
}

enum EquipAttrCompareState {
    FLAT = "flat",
    UP = "up",
    DOWN = "down",
}

enum OreMakePnlType {
    Weapon = 1, // 武器
    Armor, // 防具
    RING, // 饰品
    MAS, //
}

enum DailyTaskState {
    TAKE,
    FINISH, //已完成，已领奖
}

enum BattleLevelType {
    NONE = "NONE",
    MAIN = "MAIN", // 主线
    BLACK_HOLE = "BLACK_HOLE", // 迷宫
    TOWER = "TOWER", //爬塔
    ORE_ENTRY = "ORE_ENTRY", //挖矿入口
    ORE = "ORE", //挖矿战斗
    TRANSPORT = "TRANSPORT", //运送
    SPACE_STONE = "SPACE_STONE", //空间宝石支线
    INSTANCE = "INSTANCE", //副本
    ARREST = "ARREST", //悬赏
    PVP_NORMAL = "PVP_NORMAL", //普通竞技场
    DAILY_TASK = "DAILY_TASK", // 日常任务
}

enum TransferType {
    NORMAL, // 转移
    RESONANCE, // 共鸣
}

enum PlanetProfileType {
    TieZi = 1,
    Mxp,
    YinZhang,
    QuWen,
}
enum NPC_ID {
    MASTER_SHU = 4001,
    BLACK_HOLE_SHOP = 4002,
    BUY_EQUIP_SHOP = 4003,
    MAKE_EQUIP_SHOP = 4004,
    TRANSFER_MGR = 4006,
}

enum PLANET_ITEM_ID {
    CLOCKWORK = "CLOCKWORK",
    BLACK_HOLE_KEY = "BLACK_HOLE_KEY",
    ORE_KEY_1 = "ORE_KEY_1",
    ORE_KEY_2 = "ORE_KEY_2",
    ORE_KEY_3 = "ORE_KEY_3",
    ORE_KEY_4 = "ORE_KEY_4",
    INSTANCE_KEY_1 = "INSTANCE_KEY_1",
    INSTANCE_KEY_2 = "INSTANCE_KEY_2",
}

enum AchievementType {
    PLANET = 201,
}

enum ArchivesItemType {
    AGE = 1,
    SIGN = 2,
    TITLE = 3,
    CHAT = 4,
    THING = 5,
    STORY_1 = 6,
    STORY_2 = 7,
    STORY_3 = 8,
    PARTNER_1 = 9,
    MAIL = 10,
    PARTNER_2 = 11
}

enum TrainDailyTaskType {
    PATROL = 1, //巡逻
    REPAIR = 2, //修理
    OVERTIME = 3, //加班
    CLEAN = 4, //打扫
    STAND = 5, //站岗
    LOST_SEARCH = 6, //失物搜寻
    INSPECT = 7, //查寝
    CLOTHES_CLEAN = 8, //清洗衣物
    SAFETY_PROMOTION = 9, //安全宣传
    LIFE_GOODS = 10, //发放生活用品
}

enum TrainBurstTaskType {
    PIRATE = 1, //海盗入侵
    CLEAN = 2, //清理
    METEOR = 3, //陨石
    POWER = 4, //电力故障
    FIRE = 5, //失火
    WAVE = 6, //波动异常
}

enum TrainTechType {
    SHIP = 1, //飞船数量+x
    CARRIAGE_ROLE_CNT = 2, //寝室床位+x
    TRAIN_SPEED = 3, //列车行驶速度x%
    TRAIN_DAILY_TASK = 4, //列车任务数量+x
    DEEP_EXPLORE = 6, //深度探索效率提升x%
    TIME_MACHINE = 7, //时光机加速效果提升x%
    TRAIN_LOAD = 8, //货仓容量+x
    HERT = 101, //爱心产出+x/h
    HERT_PERCENT = 102, //爱心产出+x%
    STAR_DUST = 103, //星尘产出+x/h
    STAR_DUST_PERCENT = 104, //星尘产出+x%
    ELECTRIC = 105, //电力产出+x/h
    ELECTRIC_PERCENT = 106, //电力产出+x%
    VITALITY = 107, //元气值产出+x/h
    VITALITY_PERCENT = 108, //元气值产出+x%
    WATER = 109, //水产出+x%
    WATER_PERCENT = 110, //水产出+x/h
}

export {
    CarriageType,
    CarriageID,
    ForgeState,
    TrainState,
    MapType,
    ItemID,
    ItemType,
    JackpotEnterType,
    PrizeType,
    FacilityState,
    PassengerAction,
    MapSceneType,
    PassengerAttr,
    BedState,
    ValueType,
    PassengerLocation,
    ConditionType,
    PassengerAnimation,
    PassengerLifeAnimation,
    PassengerBattleAnimation,
    PassengerAnimationType,
    MessageType,
    CfgName,
    HeroAnimation,
    HeroAction,
    StoryType,
    GuideStepType,
    GuideStepMark,
    PlanetEvent,
    PlanetMoveState,
    HexColor,
    PlanetNodeType,
    PlanetMineType,
    PlanetGuideType,
    BuildAnimation,
    ShellAni,
    LongPress,
    LifeSkillTarget,
    PassengerType,
    BuildUnlockType,
    BuildFromType,
    SoundState,
    ExtractType,
    CommonBtnType,
    PassengerUpTips,
    Bundle,
    AccelerateState,
    RecordState,
    LangCfgName,
    UIFunctionType,
    GuideFingerType,
    GuiderType,
    PlanetMineGameType,
    PassengerQuality,
    GuideModule,
    BUILD_MOUNT_POINT,
    PassengerLifeEvent,
    PassengerSpAnimation,
    TipsNotType,
    TaskType,
    TaskTriggerType,
    ToolProperty,
    DormBuildType,
    Dorm2BuildType,
    Dorm3BuildType,
    Dorm4BuildType,
    EngineBuildType,
    DiningBuildType,
    ConsolePosType,
    GuideFingerDir,
    WeakGuideType,
    GuideTaskTargetType,
    RoleGuideActionType,
    MarkNewType,
    MarkNewLength,
    EnergyRecoverType,
    SpeedUpType,
    EntrustLifeType,
    EntrustLifeGroup,
    EntrustLifeAttributeType,
    RoleDir,
    CarriageUsePosType,
    LifeSkillEffectType,
    SkillType,
    SkillEffectiveType,
    CarriageIntoType,
    GoodsType,
    UserType,
    PassengerPlotState,
    BuildAttr,
    WaterBuildType,
    BathRoomType,
    BlackHoleNodeType,
    BlackHoleBuffType,
    RelaterType,
    RolePnlTabType,
    RoleAnimalType,
    RoleBattleType,
    GoodsObjectType,
    WantedState,
    WantedConditionType,
    StoreId,
    TeamId,
    ChangeWorkType,
    Platform,
    PayPlatformType,
    RuleType,
    FieldType,
    FieldState,
    FieldOperationType,
    OreLandType,
    OreOperateType,
    InstanceWeather,
    BattlePreviewPnlType,
    BlackHoleEquipTarget,
    ROLE_ATTR_ID,
    TalentAttrType,
    OreMakeType,
    EquipEffectType,
    EquipEffectTarget,
    IntType,
    DailyTaskType,
    EquipAttrCompareState,
    OreMakePnlType,
    DailyTaskState,
    BattleLevelType,
    TransferType,
    NPC_ID,
    PLANET_ITEM_ID,
    Channel,
    PlanetProfileType,
    AchievementType,
    ArchivesItemType,
    TrainDailyTaskType,
    TrainBurstTaskType,
    TrainTechType,
}
