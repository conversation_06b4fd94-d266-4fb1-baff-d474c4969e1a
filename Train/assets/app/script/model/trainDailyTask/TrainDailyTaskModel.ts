import { Msg } from "../../../proto/msg-define"
import { TrainDailyTaskItemCfg, TrainDailyTaskLevelCfg } from "../../common/constant/DataType"
import { UIFunctionType, WantedConditionType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import ConditionObj from "../common/ConditionObj"
import PassengerModel from "../passenger/PassengerModel"
import { WantedCondition } from "../wanted/WantedModel"


@mc.addmodel('trainDailyTask', 101)
export default class TrainDailyTaskModel<T extends TrainDailyTaskItem> extends mc.BaseModel {
    public data: proto.ITrainDailyTask = null
    protected _list: T[] = null
    protected _ticker: number = 0

    public init() {
        this._list = []
        if (this.data) {
            this.data.list.forEach((item) => this.createTaskItem(item))
        }
        this.initListener()
    }

    private initListener() {
        eventCenter.on(EventType.UNLOCK_FUNTION, (type) => {
            if (type == UIFunctionType.PLAY_TRAIN_DAILY_TASK) {
                this.syncAll()
            }
        })
    }

    public createTaskItem(item: proto.ITrainDailyTaskItem) {
        const taskItem = new TrainDailyTaskItem().init(item) as T
        taskItem.index = this._list.length
        this._list.push(taskItem)
    }

    public getList() { return this._list }
    public getListByID(id: number) { return this._list.filter(item => item.id == id) }

    update(dt: number) {
        if (!this._list?.length) return
        this._ticker += dt * 1000
        if (this._ticker < 1000) return
        this._ticker = 0
        for (const item of this._list) {
            if (item.state == proto.CommonState.InProcess && item.getSurplusTime() <= 0) {
                this.syncAll(item.index)
            }
        }
    }

    @ut.queue
    public async syncAll(index: number = -1) {
        if (index >= this._list.length) return
        const { code, data } = await gameHelper.net.requestWithDataWait(Msg.C2S_SyncAllTrainDailyTaskMessage, { index })
        if (code != 0) return void viewHelper.showNetError(code)
        if (index == -1) {
            data.forEach((item, i) => {
                if (i >= this._list.length) {
                    this.createTaskItem(item)
                    return
                }
                const taskItem = this._list[i]
                taskItem.init(item)
            })
            if (data.length < this._list.length) {
                this._list.splice(data.length, this._list.length - data.length)
            }
            return
        }
        if (!data || data.length == 0) return
        const item = this._list[index]
        item.init(data[0])
    }

    public oneKeyPlan(item: TrainDailyTaskItem): number[] {
        // 拥有的所有乘客 
        const roles = gameHelper.passenger.getPassengers()
            // 排除已经安排在其他未完成的工作中的乘客
            .filter(r => !this.getList().find(w => w.state == proto.CommonState.InProcess && w.roles.find(id => id == r.id)))
        // 获取任务的招工要求和最大安排乘客数量
        const conditions = item.conditions
        const maxPeople = item.people
        // 如果没有乘客可用，直接返回null
        if (roles.length === 0) {
            return null
        }
        // 如果没有招工要求，直接返回空数组
        if (conditions.length === 0) {
            return []
        }
        // 将条件分为品质条件和其他条件
        const qualityConditions = conditions.filter(c => c.type === WantedConditionType.QUALITY)
        const otherConditions = conditions.filter(c => c.type !== WantedConditionType.QUALITY)
        // 预处理：计算每个乘客满足哪些条件
        const satisfyMap = new Map<number, Set<string>>()
        // 记录每个乘客的品质
        const roleQualityMap = new Map<number, number>()
        for (const role of roles) {
            const satisfySet = new Set<string>()
            for (const condition of otherConditions) {
                if (condition.isSatisfy([role])) {
                    const key = `${condition.type}_${condition.value}`
                    satisfySet.add(key)
                }
            }
            satisfyMap.set(role.id, satisfySet)
            // 记录乘客品质
            roleQualityMap.set(role.id, role.quality)
        }
        // 将非品质条件转换为唯一标识的集合
        const requiredConditions = new Map<string, number>()
        for (const condition of otherConditions) {
            const key = `${condition.type}_${condition.value}`
            requiredConditions.set(key, condition.num)
        }
        // 处理品质条件
        const qualityMatches = new Map<number, number[]>() // 品质要求 -> 满足的乘客ID列表
        const usedRoles = new Set<number>() // 已经用于品质匹配的乘客
        // 对每个品质条件
        for (const qCondition of qualityConditions) {
            const requiredQuality = Number(qCondition.value)
            const matchedRoles: number[] = []
            // 首先尝试找到完全匹配的品质
            const exactMatches = roles.filter(r =>
                !usedRoles.has(r.id) &&
                r.quality === requiredQuality
            )
            // 然后尝试找到更高品质的
            const higherMatches = roles.filter(r =>
                !usedRoles.has(r.id) &&
                r.quality > requiredQuality
            ).sort((a, b) => a.quality - b.quality) // 优先使用较低的高品质
            // 先使用完全匹配的
            for (const role of exactMatches) {
                if (matchedRoles.length < qCondition.num) {
                    matchedRoles.push(role.id)
                    usedRoles.add(role.id)
                }
            }
            // 如果还需要更多，使用高品质的
            for (const role of higherMatches) {
                if (matchedRoles.length < qCondition.num) {
                    matchedRoles.push(role.id)
                    usedRoles.add(role.id)
                }
            }
            // 如果这个品质条件没满足，直接返回null
            if (matchedRoles.length < qCondition.num) {
                return null
            }
            qualityMatches.set(requiredQuality, matchedRoles)
        }
        // 对剩余的非品质条件使用贪心算法
        const availableRoles = roles.filter(r => !usedRoles.has(r.id))
        const sortedRoles = [...availableRoles].sort((a, b) => {
            const aSatisfy = satisfyMap.get(a.id)
            const bSatisfy = satisfyMap.get(b.id)
            return bSatisfy.size - aSatisfy.size
        })
        const greedySolution: number[] = []
        const remainingConditions = new Map(requiredConditions)
        for (const role of sortedRoles) {
            if (remainingConditions.size === 0 ||
                (greedySolution.length + Array.from(usedRoles).length) >= maxPeople) {
                break
            }
            const satisfySet = satisfyMap.get(role.id)
            let useful = false
            for (const [key, count] of remainingConditions.entries()) {
                if (satisfySet.has(key)) {
                    useful = true
                    const newCount = count - 1
                    if (newCount <= 0) {
                        remainingConditions.delete(key)
                    } else {
                        remainingConditions.set(key, newCount)
                    }
                }
            }
            if (useful) {
                greedySolution.push(role.id)
            }
        }
        // 如果还有未满足的条件，返回null
        if (remainingConditions.size > 0) {
            return null
        }
        // 合并品质匹配和贪心算法的结果
        const finalSolution = [
            ...Array.from(usedRoles),
            ...greedySolution
        ]
        // 如果总人数不够，返回null
        if (finalSolution.length < maxPeople) {
            return null
        }
        return finalSolution
    }

}


export class TrainDailyTaskItem {
    protected _id: number = 0
    protected _state: proto.CommonState = proto.CommonState.NotStart
    protected _endTime: number = 0
    protected _conditions: WantedCondition[] = null
    protected _rewards: ConditionObj[] = null
    protected _roles: number[] = null
    protected _people: number = 0
    protected _cfg: TrainDailyTaskItemCfg = null
    protected _lvCfg: TrainDailyTaskLevelCfg = null
    protected _trainId: number = -1
    protected _level: number = 0
    public index: number = -1

    get id() { return this._id }
    get state() { return this._state }
    get surplusTime() { return Math.max(0, this._endTime - gameHelper.now()) }
    get conditions() { return this._conditions }
    get rewards() { return this._rewards }
    get roles() { return this._roles }
    get people() { return this._people }
    get cfg() { return this._cfg }
    get costTime() { return this._lvCfg.costTime }
    get trainId() { return this._trainId }
    get level() { return this._level }

    public init(data: proto.ITrainDailyTaskItem) {
        this._id = data.id
        let preState = this._state
        this._state = data.state
        this._endTime = -1
        if (this.inProcess()) {
            this._endTime = data.surplusTime + gameHelper.now()
        }
        this._conditions = data.conditions.map(c => new WantedCondition(c))
        this._rewards = gameHelper.toConditions(data.rewards).sort((a, b) => a.type - b.type)
        this._roles = data.roles
        this._people = data.people
        this._trainId = data.trainId
        this._level = data.level
        this._cfg = assetsMgr.getJsonData<TrainDailyTaskItemCfg>("TrainDailyTaskItem", this._id)
        this._lvCfg = assetsMgr.getJsonData<TrainDailyTaskLevelCfg>("TrainDailyTaskLevel", `${this._level}`)
        // 排个序
        this.conditions.sort((a, b) => {
            if (a.type != b.type) {
                return a.type - b.type
            }
            if (a.value != b.value) {
                if (a.type == WantedConditionType.QUALITY) {
                    return b.value - a.value
                }
                else {
                    return a.value - b.value
                }
            }
        })
        // 合并一下完全相同的类型
        const ary: WantedCondition[] = []
        for (const sig of this.conditions) {
            const exists = ary.find(c => c.type == sig.type && c.value == sig.value)
            if (!exists) {
                ary.push(sig)
                continue
            }
            exists.num += sig.num
        }
        this._conditions = ary

        if (preState == proto.CommonState.InProcess && this._state == proto.CommonState.DoneWithoutReward) {
            eventCenter.emit(EventType.TRAIN_DAILY_TASK_DONE, this)
        }
        return this
    }

    public canStart() { return this._state == proto.CommonState.NotStart }
    public inProcess() { return this._state == proto.CommonState.InProcess }

    public getConditionMatchNum(role: PassengerModel) {
        return this.conditions.filter(cond => {
            switch (cond.type) {
                case WantedConditionType.QUALITY:
                    return role.quality >= cond.value
                case WantedConditionType.ANIMAL_TYPE:
                    return role.animalType == cond.value
                case WantedConditionType.BATTLE_TYPE:
                    return role.battleType == cond.value
            }
        }).length
    }

    public calculateConditions(roles: number[]) {
        const roleAry = roles.map(id => gameHelper.passenger.getPassenger(id))
        let result = {}
        // 品质要求 必须是唯一的 一个乘客只能匹配一次 优先匹配最高
        const qualityUsed = []
        const qualityCond = this.conditions.filter(con => con.type == WantedConditionType.QUALITY)
            .sort((a, b) => b.value - a.value)
        if (qualityCond.length) {
            result[WantedConditionType.QUALITY] = {}
        }
        for (const condition of qualityCond) {
            if (!result[WantedConditionType.QUALITY][condition.value]) {
                result[WantedConditionType.QUALITY][condition.value] = 0
            }
            for (const role of roleAry) {
                if (!role) continue
                if (qualityUsed.includes(role.id)) continue
                if (condition.isSatisfy([role])) {
                    const cur = result[WantedConditionType.QUALITY][condition.value]
                    if (cur >= condition.num) {
                        break
                    }
                    result[WantedConditionType.QUALITY][condition.value]++
                    qualityUsed.push(role.id)
                }
            }
        }

        for (const condition of this.conditions) {
            if (condition.type == WantedConditionType.QUALITY) continue
            let line = result[condition.type]
            if (line == undefined) {
                line = {}
            }
            let v = line[condition.value]
            if (v == undefined) {
                v = 0
            }
            v += roleAry.filter(role => role && condition.isSatisfy([role])).length
            line[condition.value] = Math.min(v, condition.num)
            result[condition.type] = line
        }
        return result
    }

    public getSurplusTime() { return Math.max(0, this._endTime - gameHelper.now()) }

    @ut.addLock
    public async requestStart(roles: number[]) {
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_StartTrainDailyTaskMessage, { index: this.index, roles })
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        this._state = proto.CommonState.InProcess
        this._roles = roles

        this._endTime = this.costTime * ut.Time.Minute + gameHelper.now()
        eventCenter.emit(EventType.TRAIN_DAILY_TASK_START, this)
        return true
    }

    @ut.addLock
    public async requestFinish() {
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_ClaimTrainDailyTaskRewardMessage, { index: this.index })
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        this._state = proto.CommonState.FinishWithReward
        this._roles.length = 0
        await gameHelper.grantRewardAndShowUI(this.rewards)
        eventCenter.emit(EventType.TRAIN_DAILY_TASK_FINISH, this)
        return true
    }
}