import CoreEventType from "../../../core/event/CoreEventType"
import { DORM_RIGHT_BED_ID, GUIDE_FLOWERTREE_ID, GUIDE_PARTERRE_ID } from "../../common/constant/Constant"
import { FocusCfg, GuideStep, MoneyAreaInfo, TrainCfg } from "../../common/constant/DataType"
import { CarriageID, CarriageType, ConditionType, GuideStepMark, HeroAction, PassengerLifeAnimation, PlanetEvent, PlanetMineType, RoleDir, RoleGuideActionType, RolePnlTabType, UIFunctionType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import NodeType from "../../common/event/NodeType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { dropItemHelper } from "../../common/helper/DropItemHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { unlockHelper } from "../../common/helper/UnlockHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import BattleResultPnlCtrl from "../../view/battle/BattleResultPnlCtrl"
import EternalGardenWindCtrl from "../../view/eternalGarden/EternalGardenWindCtrl"
import PlanetWindCtrl from "../../view/planet/PlanetWindCtrl"
import PlanetEntryWindCtrl from "../../view/planetEntry/PlanetEntryWindCtrl"
import RolePnlCtrl from "../../view/role/RolePnlCtrl"
import ConditionObj from "../common/ConditionObj"
import ActionTree from "../passenger/ActionTree"
import { StateType } from "../passenger/StateEnum"
import PlanetCheckPointModel from "../planet/PlanetCheckPointModel"
import PlanetEmptyNode from "../planet/PlanetEmptyNode"
import PlanetMineModel from "../planet/PlanetMineModel"
import CarriageModel from "../train/common/CarriageModel"
import DropMoneyObj from "../train/common/DropMoneyObj"
import DormModel from "../train/dorm/DormModel"
import EngineModel from "../train/engine/EngineModel"

export default class GuideLogic {

    private events: any[] = []

    public async showPlot(stepInfo: GuideStep) {
        await this.showPlotKey(stepInfo.plotKey)
    }

    public async showPlotKey(key: string) {
        let p = this.onEvent(EventType.END_PLOT, (plotKey) => {
            return plotKey == key
        })
        gameHelper.plot.start(key)
        await p
    }

    public async checkBattleFailPnl_Garden1Key(key: string) {
        let p = this.onEvent(EventType.END_PLOT, (plotKey) => {
            return plotKey == key
        })
        gameHelper.plot.start(key)
        await p
    }

    // 弱引导点击剧情选项一
    public async showPlotClickOne(stepInfo: GuideStep) {
        let p = this.onEvent(EventType.END_PLOT, (plotKey) => {
            return plotKey == key
        })
        let key = stepInfo.plotKey
        gameHelper.plot.start(key, 0, { guideOpOne: stepInfo })
        await p
    }

    public async showPlotThenClosePnl(stepInfo: GuideStep, params: Array<string>) {
        await this.showPlot(stepInfo)
        let path = params[0]
        viewHelper.closePnl(path)
    }

    public async showPlotWaitEvent(stepInfo: GuideStep, params: Array<string>) {
        let event = params[0]
        let p = this.onEvent(event)
        eventCenter.emit(EventType.GUIDE_FREE_TOUCH)
        await p
        await this.showPlot(stepInfo)
    }

    public async showPlotAndStopCollect(stepInfo: GuideStep) {
        eventCenter.emit(EventType.PLANET_HIDE_CUR_MINE_UI, gameHelper.hero.getTargetModel())
        this.stopCollect()
        await this.showPlot(stepInfo)
        this.startCollect()
    }

    // 笔记飞入背包、说话
    public async showPlotAndFlyItem2(stepInfo: GuideStep) {
        await ut.waitNextFrame()//切换场景过来 给一帧时间 否则会崩
        eventCenter.emit(EventType.PLANET_HIDE_CUR_MINE_UI, gameHelper.hero.getTargetModel())
        this.stopCollect()
        // await animHelper.flyToBag(new ConditionObj().init(ConditionType.PROP, ItemID.NOTE), cc.v2(-274, -264))
        await this.showPlot(stepInfo)
        this.startCollect()
    }

    public async unlockFunction(stepInfo: GuideStep, params?: UIFunctionType[]) {
        let p = this.onEvent(EventType.GUIDE_UNLOCK_FUNTION, (_type) => {
            return type == _type
        })
        let type = stepInfo.unlockFunc || params[0]
        viewHelper.showPnl("guide/FunctionUnlockPnl", type)

        await p
    }

    public unlockFunctionTask() {
        eventCenter.emit(EventType.GUIDE_UNLOCK_FUNTION, UIFunctionType.TASK)
    }

    public async unlockFunctionInMain(stepInfo: GuideStep) {
        if (!this.isMainScene()) await this.waitGotoMain()
        await this.unlockFunction(stepInfo)
    }

    public async unlockPlay(stepInfo: GuideStep, params: Array<string>) {
        let p = this.onEvent(EventType.GUIDE_UNLOCK_FUNTION, (_type) => {
            return type == _type
        })
        let type = stepInfo.unlockFunc || params[0]
        viewHelper.showPnl("planet/PlanetPlayUnlockPnl", stepInfo.unlockFunc)
        await p
    }

    public async showGuidePnl(stepInfo) {
        let call = stepInfo?.logic?.call
        if (!call) return
        if (!this[call]) return
        viewHelper.showPnl('guide/Guide')
        await this[call](stepInfo)
    }

    public async showClick(stepInfo) {
        eventCenter.emit(EventType.GUIDE_SHOW_CLICK, stepInfo, true)
    }

    public async showDrag(stepInfo) {
        eventCenter.emit(EventType.GUIDE_SHOW_DRAG, stepInfo)
    }

    public showTouchMove(stepInfo) {
        eventCenter.emit(EventType.GUIDE_SHOW_TOUCH_MOVE, stepInfo)
    }

    public async showClickSelectRole(stepInfo, params: Array<number>) {
        let id = params[0]
        let pnl = mc.getPnl("role/Role") as RolePnlCtrl
        pnl.trySelectRole(id)
        await this.showClick(stepInfo)
    }

    public showPnl(stepInfo, params: Array<string>) {
        let path = params[0]
        let p = this.waitPnl(stepInfo, [path])
        viewHelper.showPnl(path)
        return p
    }

    public resetAutoSpeed() {
        gameHelper.battle.isAuto = false
        gameHelper.battle.setSpeed(1)
    }
    public resetTeam406() {
        let uids: string[] = []
        let team = gameHelper.battle.getTeam()
        if (gameHelper.guide.isStepComplete2(406)) {
            uids.push('1005')
        }
        team.setRolesBySever(uids)
        team.setRoles(uids)
    }
    public resetTeam1006() {
        let uids: string[] = ['1005']
        let team = gameHelper.battle.getTeam()
        if (gameHelper.guide.isStepComplete2(1006)) {
            uids.push('1006')
        }
        team.setRolesBySever(uids)
        team.setRoles(uids)
    }

    public resetDorm1Camera() {
        this.focusCarriage(CarriageID.DORM, 0, 20)
    }

    public async showGuider(stepInfo) {
        let p = this.onEvent(EventType.GUIDE_GUIDER_CLICK)
        eventCenter.emit(EventType.GUIDE_SHOW_GUIDER, stepInfo)
        await p
    }

    public async showPureGuider(stepInfo, params: Array<string>) {
        let event = params[0]
        eventCenter.emit(EventType.GUIDE_SHOW_GUIDER, stepInfo)
        await this.onEvent(event)
    }

    public async showAfterGuider(stepInfo, params: Array<string>) {
        await this.showGuider(stepInfo)
        eventCenter.emit(params[0])
    }

    public async freeTouch(stepInfo) {
        eventCenter.emit(EventType.GUIDE_FREE_TOUCH, stepInfo)
    }

    public async waitPnl(stepInfo, args) {
        let [pnlKey] = args
        let check = () => { return viewHelper.checkPnlEnter(pnlKey) }
        if (check()) return
        await this.onEvent(CoreEventType.PNL_ENTER, check)
    }

    public async waitPnlClose(stepInfo, args) {
        let [pnlKey] = args
        let check = () => { return viewHelper.checkPnlClose(pnlKey) }
        if (check()) return
        await this.onEvent(CoreEventType.PNL_LEAVE, check)
    }

    public async waitEvent(stepInfo, args) {
        let [event] = args
        await this.onEvent(event)
    }

    public async waitEventFreeTouch(stepInfo, args) {
        this.freeTouch(stepInfo)
        await this.waitEvent(stepInfo, args)
    }

    public async waitPlanetNodeEnd(stepInfo, args) {
        let [id] = args
        let check = () => { return gameHelper.planet.isPassNode(id) }
        if (check()) return
        await this.onEvent(EventType.PLANET_NODE_COMPLETE, check)
    }

    public async waitToTarget() {
        let check = () => { return gameHelper.hero.getAction() == HeroAction.WAIT_TO_TARGET }
        if (check()) return
        await this.onEvent(EventType.HERO_CHANGE_STATE, check)
    }

    public async showFocusCarriage(stepInfo, args) {
        let [id] = args
        await this.focusCarriage(Number(id))
    }

    public async focusCarriage(id: CarriageID, dur: number = 0.3, offX?: number) {
        let carriage = gameHelper.train.getCarriageById(id)
        let focusCfg: FocusCfg = {
            needBackZoomRatio: false,
            needBackX: false,
            needFocusZoomRatio: true,
            backZoomRatio: null,
            time: dur,
            offX,
        }
        eventCenter.emit(EventType.FOCUS_CARRIAGE, carriage, focusCfg)
        await this.onEvent(EventType.FOCUS_CARRIAGE_END)
    }

    // 需要抛出四堆星尘(不抛直接出现)
    private dropStardust1() {
        dropItemHelper.clean()
        let carriage: CarriageModel = this.getDorm()
        let dropMoneys = carriage.getAllDrops()
        dropMoneys.length = 0

        let build = carriage.getBuilds()[0]
        let aryVal = cfgHelper.getMiscData("guide").starGetFirst
        let startPos = build.position
        let areaTops = carriage.moneyAreas.filter(a => a.up)
        let areaBottoms = carriage.moneyAreas.filter(a => !a.up && !a.tips)
        this.dropManyStarDust(carriage, startPos, 4, aryVal[0], areaTops[0])//顶部左侧-4颗
        this.dropManyStarDust(carriage, startPos, 2, aryVal[1], areaTops.last())//顶部右侧-2颗
        this.dropManyStarDust(carriage, startPos, 3, aryVal[2], areaBottoms[0])//底部左侧-3颗
        this.dropManyStarDust(carriage, startPos, 3, aryVal[3], areaBottoms[1])//底部中间-3颗
        eventCenter.emit(EventType.UPDATE_ALL_DROP, carriage)
    }
    private dropManyStarDust(carriage: CarriageModel, startPos: cc.Vec2, count: number, val: number, area: MoneyAreaInfo) {
        let one = Math.floor(val / count)
        let duo = val % count
        for (let i = 1; i <= count; i++) {
            let num = i == count ? one + duo : one
            this.dropOneStarDust(carriage, startPos, false, num, area)
        }
    }
    private dropOneStarDust(carriage: CarriageModel, startPos: cc.Vec2, needNotic: boolean, val: number, area: MoneyAreaInfo) {
        // let drop = carriage.addDropMoney(area, startPos, new ConditionObj().init(ConditionType.STAR_DUST, null, val), false)
        // drop.isGuide = true
    }

    public async planetCollect(stepInfo) {
        let check1 = () => {
            let t = gameHelper.hero.getTargetModel()
            return t && !t.dead
        }
        if (!check1()) {
            await this.onEvent(EventType.TARGET_PLANET_NODE, () => {
                return check1()
            })
        }

        let target = gameHelper.hero.getTargetModel()
        eventCenter.emit(EventType.GUIDE_SHOW_COMBO_CLICK, stepInfo, true)
        await this.onEvent(EventType.PLANET_NODE_COMPLETE, () => {
            return target.dead
        })
    }

    public showComboClick(stepInfo) {
        eventCenter.emit(EventType.GUIDE_SHOW_COMBO_CLICK, stepInfo)
    }

    //乘客丢小费
    public async dropStardust3() {
        let role = gameHelper.passenger.getPassenger(1008)
        let pos = role.getPosition()
        let carriage: CarriageModel = this.getDorm()
        let area = carriage.moneyAreas.find(a => !a.up)
        let val = cfgHelper.getMiscData("guide").starGetThird
        let node: cc.Node = eventCenter.get(NodeType.GUIDE_STAR_BUTTOM)
        let targetPos = node.getPosition()
        let drop = new DropMoneyObj().init(area.id, targetPos, val)
        let dropMoneys = carriage.getAllDrops()
        drop.isGuide = true
        carriage.setDropZIndex(drop)
        dropMoneys.add(drop)
        eventCenter.emit(EventType.ROLE_DROP_MONEY, { map: carriage, position: pos }, drop, false)

        await this.onEvent(EventType.DROP_ITEM_ANIM_END)
    }

    public async waitPlanetComplete() {
        let check = () => {
            let pnl = mc.getPnl("planet/PlanetComplete")
            return pnl && pnl.getActive()
        }

        if (check()) {
            return
        }

        eventCenter.emit(EventType.GUIDE_FREE_TOUCH)
        await this.onEvent(CoreEventType.PNL_ENTER, (pnl) => {
            return check()
        })
    }

    public async speedUp(stepInfo) {
        eventCenter.emit(EventType.GUIDE_SHOW_CLICK, stepInfo)

        await this.onEvent(EventType.TIME_ACCELERATE_END)
    }

    //赏金猎犬睡觉
    public async roleSleep() {
        this.focusCarriage(CarriageID.DORM)
        this.roleAction(async (action) => {
            let model = action.params
            let carriage = model.carriage
            let build = carriage.getLeftBed()
            await action.run(model.toBedSit, { build, time: 60 })
            action.ok()
        }, 1005)

        await this.onEvent(EventType.PASSENGER_STATE_CHANGE, (model, state) => {
            if (state.type == StateType.SIT) {
                return true
            }
        })
    }

    //赏金猎犬吃东西
    public async roleEat() {
        this.focusCarriage(CarriageID.DORM)
        this.roleAction(async (action) => {
            let model = action.params
            let carriage = model.carriage
            let build = carriage.getChairs()[0]
            if (build) {
                await action.run(async (action2) => {
                    action2.params = { build }
                    await model.toChairAct(action2, async (action3) => {
                        await action3.run(model.sitEat, { build, eatCount: 2, munchCount: 1, delayTime: 0.1 })
                        action3.ok()
                    })
                })
                await action.wait(60)
            }
            action.ok()
        }, 1005)

        await this.onEvent(EventType.PASSENGER_STATE_CHANGE, (model, state) => {
            if (state == StateType.EAT) {
                return true
            }
        })
    }

    private async dogStandUp() {
        await ut.wait(0.5)
        let id = 1005
        let role = gameHelper.passenger.getPassenger(id)
        if (!role.isCheckIn()) {
            return
        }
        let pos = this.dormLeftChairPos()
        role.setPosition(pos)
        role.actionAgent.clean()
    }
    public dogFreedom() {
        this.waitLeaveMainScene().then(() => {
            gameHelper.passenger.restartRole(1005)
        })
    }
    private dormLeftChairPos(dicBuild?: any) {
        let carriage = this.getDorm() as DormModel
        let build = carriage.getLeftChair()
        if (dicBuild) dicBuild.build = build
        return build.getUsePos(0)
    }
    // 赏金猎犬坐在左侧凳子，idle
    public async dogSitIdle() {
        let id = 1005
        let role = gameHelper.passenger.getPassenger(id)
        if (!role.isCheckIn()) {
            await role.checkInBySever(this.getDorm(), 1)
        }
        let actionAgent = role.actionAgent
        let state = actionAgent.getState(StateType.SIT)
        if (state?.data.dogSitIdle) return
        if (role.carriageId != CarriageID.DORM) {
            role.carriage?.roleExit(role)
            this.getDorm().roleEnter(role)
        }
        gameHelper.passenger.terminateRole(id)
        gameHelper.passenger.stopRole(id)
        let dic = { "dogSitIdle": true }
        this.dormLeftChairPos(dic)
        role.setPointAndPosition(null, cc.v2(0, 0))
        actionAgent.pushState(StateType.SIT, dic)
    }

    public dogCatIdle() {
        this.dogSitIdle()
        this.catStandIdle()
    }

    // 胡桃夹子站在原地idle
    private async catStandIdle() {
        let id = 1006
        let role = gameHelper.passenger.getPassenger(id)
        if (!role.isCheckIn()) {
            await role.checkInBySever(this.getDorm(), 2)
        }
        if (role.carriageId != CarriageID.DORM) {
            role.carriage?.roleExit(role)
            this.getDorm().roleEnter(role)
        }
        gameHelper.passenger.terminateRole(id)
        gameHelper.passenger.stopRole(id)
        role.setPointAndPosition(null, cc.v2(1700, 252))
        role.actionAgent.pushState(StateType.IDLE)
    }

    //胡桃夹子收拾行李
    public async roleCheckIn() {
        let id = 1006
        let role = gameHelper.passenger.getPassenger(id)
        this.focusCarriage(CarriageID.DORM)
        role.setPosition(cc.v2(2150, 252))
        await this.roleAction(async (action) => {
            let model = action.params
            let actionAgent = model.actionAgent
            actionAgent.pushState(StateType.CHECK_IN)
            action.onTerminate = () => {
                actionAgent.popState(StateType.CHECK_IN)
            }
            await action.run(model.move, { pos: cc.v2(1700, 252), moveAnim: PassengerLifeAnimation.CHECK_IN_WALK })
            await action.run(model.checkInTidy)
            actionAgent.popState(StateType.CHECK_IN)
            action.ok()
        }, id, false)
    }

    //胡桃夹子睡觉
    public async roleSleep2() {
        this.focusCarriage(CarriageID.DORM)
        let id = 1006
        let role = gameHelper.passenger.getPassenger(id)
        let dorm = this.getDorm()
        if (!role.isCheckIn()) {
            await role.checkInBySever(dorm, 2)
            await this.roleCheckIn()
        } else {
            role.actionAgent.clean()
        }
        this.roleAction(async (action) => {
            let model = action.params
            let carriage = model.carriage
            let rightBed = carriage.getRightBed()
            await action.run(model.toRightBedSleep, { time: 60 })
            action.ok()
        }, id)

        await this.onEvent(EventType.PASSENGER_STATE_CHANGE, (model, state) => {
            if (state.type == StateType.SLEEP) {
                return true
            }
        })

        this.dogStandUp()
    }

    public async roleAction(actName, id = 1005, resumeAction = true) {
        let role = gameHelper.passenger.getPassenger(id)
        let actionAgent = role.actionAgent
        let curAction = actionAgent["action"]
        let actionTree: ActionTree = curAction["actionTree"]
        actionAgent.resume()
        actionTree.terminate()
        if (Array.isArray(actName)) {
            for (let name of actName) {
                await actionTree.start(curAction[name])
            }
        }
        else if (typeof actName == 'function') {
            await actionTree.start(actName, curAction, curAction)
        }
        else {
            await actionTree.start(curAction[actName])
        }
        if (resumeAction) {
            gameHelper.passenger.restartRole(id)
        }
    }

    public ignoreSelectRoleNode2() {
        eventCenter.emit(EventType.GUIDE_IGNORE_SELECT_ROLENODE_2)
    }

    public async showSelectRoleNode2() {
        eventCenter.emit(EventType.GUIDE_SHOW_SELECT_ROLENODE_2)
        await this.onEvent(EventType.GUIDE_SHOW_SELECT_END)
    }

    public async repairTrain(stepInfo) {
        eventCenter.emit(EventType.GUIDE_REPAIR_TRAIN)
        this.showClick(stepInfo)
    }

    public async enterTrain(stepInfo) {
        eventCenter.emit(EventType.GUIDE_ENTER_TRAIN)
    }

    public async gotoPlanet() {
        gameHelper.planet.getCurPlanet().showReachAnim = true
        eventCenter.emit(EventType.GUIDE_GOTO_PLANET)
    }

    public showPlanetBack() {
        eventCenter.emit(EventType.GUIDE_UNLOCK_FUNTION, UIFunctionType.PLANET_BACK)
    }

    public async showPlanetBag() {
        eventCenter.emit(EventType.GUIDE_UNLOCK_FUNTION, UIFunctionType.BAG)
    }

    public showOutput() {
        eventCenter.emit(EventType.GUIDE_UNLOCK_FUNTION, UIFunctionType.OUTPUT)
    }

    public async stopCollect() {
        gameHelper.hero.reset()
    }

    public startCollect() {
        gameHelper.hero.startAction()
    }

    public async resumeCollect() {
        gameHelper.hero.onNextAction()
    }

    public async roleStopThink() {
        this.stopCollect()
        gameHelper.hero.setAction(HeroAction.CHASE_3)//停下思考
    }

    public async initRolePos() {
        let id = 1005
        gameHelper.passenger.terminateRole(id)
        gameHelper.passenger.stopRole(id)
        let role = gameHelper.passenger.getPassenger(id)
        role.setPosition(cc.v2(531, 185))
        // -543.337 + 2150 / 2
        // -264.568 + 900 / 2
        await this.focusCarriage(CarriageID.DORM)
    }

    public async initRolePos2() {
        let id = 1005
        gameHelper.passenger.terminateRole(id)
        gameHelper.passenger.stopRole(id)
        let role = gameHelper.passenger.getPassenger(id)
        role.setPosition(cc.v2(607, 139))
        //-311.1 + 900 / 2
        role.setDir(RoleDir.RIGHT)
        await this.focusCarriage(CarriageID.DORM)
    }

    public async unlockSaveCat() {
        eventCenter.emit(EventType.GUIDE_UNLOCK_SAVE_CAT)
        this.stopCollect()
    }

    public async showBattleGuider(stepInfo) {
        eventCenter.emit(EventType.BATTLE_PAUSE_NEXT)
        await Promise.race([this.showGuider(stepInfo), ut.wait(3)])
        eventCenter.emit(EventType.BATTLE_RESUME_NEXT)
    }

    public sendEvent(stepInfo: GuideStep, params: Array<number>) {
        let name = params[0]
        eventCenter.emit(name)
    }

    public async focusDailyTaskRole() {
        let task = gameHelper.dailyTask.getTasks()[0]
        if (!task) return
        let id = task.getSender()
        let role = gameHelper.passenger.getPassenger(id)
        await this.focusCarriage(role.carriageId)
    }

    public showAbnormal1() {
        return new Promise((resolve) => {
            viewHelper.showPnl("spaceStone/SpaceStoneAbnormalPnl", 1, resolve)
        })
    }

    public showAbnormal2() {
        return new Promise((resolve) => {
            viewHelper.showPnl("spaceStone/SpaceStoneAbnormalPnl", 2, resolve)
        })
    }

    //--------------check Func -----------------
    public checkMine() {
        return this.isPlanetWind() && gameHelper.hero.getTargetModel() instanceof PlanetMineModel
    }

    public checkFirstMineEnd() {
        let planet = gameHelper.planet.getSchoolPlanet()
        return this.isPlanetWind() && planet.getProgress() >= 1
    }

    public checkGardenMine() {
        if (gameHelper.planet.curPlanetId == 1005) {
            return this.checkMine()
        }
    }

    // 对零件采集物采集无效5次后
    public checkMinePART(stepInfo: GuideStep, fromEvent: boolean) {
        if (!this.isPlanetWind()) return
        let target = gameHelper.hero.getTargetModel()
        if (!(target instanceof PlanetMineModel)) return
        if (target.type != PlanetMineType.PART) return
        if (fromEvent) {
            target.guideParam += 1
        }
        return target.guideParam >= 5
    }

    // 采集高血量银花火树时，造成20点伤害后触发引导
    public checkMine1012() {
        if (!this.checkMine()) return
        let target: PlanetMineModel = gameHelper.hero.getTargetModel()
        if (target.mineId != GUIDE_FLOWERTREE_ID) return
        return target.maxHp - target.hp >= 20
    }

    public checkMine1012End() {
        if (!(mc.currWind instanceof EternalGardenWindCtrl)) return
        let carriages = gameHelper.train.getCarriages()
        if (carriages.length > 1) return
        let target = gameHelper.planet.getCurPlanet().getPreNode() as PlanetMineModel
        if (target) {
            return target.mineId == GUIDE_FLOWERTREE_ID
        }
    }

    // 砍完花坛雕塑
    public checkMine2010End() {
        if (!(mc.currWind instanceof EternalGardenWindCtrl)) return
        let carriages = gameHelper.train.getCarriages()
        if (carriages.length > 1) return
        let target = gameHelper.planet.getCurPlanet().getPreNode() as PlanetMineModel
        if (target) {
            return target.mineId == GUIDE_PARTERRE_ID
        }
    }

    public checkMonster() {
        return this.isPlanetWind() && gameHelper.hero.getTargetModel() instanceof PlanetCheckPointModel
    }

    public checkNodeMonster() {
        return this.isPlanetWind() && gameHelper.planet.getCurPlanet().getCurNode() instanceof PlanetCheckPointModel
    }

    public checkClockworkComplete() {
        return this.checkPlanetEventNodeEnd(PlanetEvent.CLOCKWORK, false)
    }

    public checkFood() {
        return this.checkPlanetEventNode(PlanetEvent.FOOD)
    }

    public checkFoodEnd() {
        return this.checkPlanetEventNodeEnd(PlanetEvent.FOOD)
    }

    public checkSaveCat() {
        return this.checkPlanetEventNode(PlanetEvent.SAVE_CAT)
    }

    public checkSaveCatEnd() {
        return this.checkPlanetEventNodeEnd(PlanetEvent.SAVE_CAT)
    }

    public checkTreeHouse() {
        if (!this.checkPlanetEventNode(PlanetEvent.TREE_HOUSE)) return false
        let target = gameHelper.hero.getTargetModel() as PlanetEmptyNode
        return target.customFun && target.customFun()
    }

    public checkCurveChaseEnd() {
        return this.checkPlanetEventNodeEnd(PlanetEvent.CURVE_CHASE)
    }

    public checkGardenBattle3End() {
        if (!this.isPlanetWind()) return
        let id = 1005
        let curPlanet = gameHelper.planet.getCurPlanet()
        if (curPlanet.getId() != id) return
        let nodes = curPlanet.getNodes()
        let count = 0
        for (let node of nodes) {
            if (node instanceof PlanetCheckPointModel) {
                count++
                if (count >= 3) {
                    return node.isPass()
                }
            }
        }
        return false
    }

    public checkMachineBattle1End() {
        if (!this.isPlanetWind()) return
        let id = 1006
        let curPlanet = gameHelper.planet.getCurPlanet()
        if (curPlanet.getId() != id) return
        let nodes = curPlanet.getNodes()
        let count = 0
        for (let node of nodes) {
            if (node instanceof PlanetCheckPointModel) {
                count++
                if (count >= 1) {
                    return node.isPass()
                }
            }
        }
        return false
    }

    public checkPlanetEventNode(eventName) {
        let target = gameHelper.hero.getTargetModel()
        if (!target) return false
        return target.eventName == eventName
    }

    public checkPlanetEventNodeEnd(eventName, checkEnd = true) {
        let node = gameHelper.planet.getNodeByEvent(eventName)
        if (!node) return false
        if (checkEnd && !node.isEnd) return false
        return node.isPass()
    }

    public checkBattleReady() {
        return this.isPnlEnter("battle/BattleReady")
    }

    public checkBattleStart() {
        return this.isPnlEnter("battle/Battle")
    }

    public checkPlanetComplete() {
        return this.isPnlEnter("planet/PlanetComplete")
    }

    public checkBattleFailPnl() {
        let pnl = mc.getPnl("battle/BattleResult") as BattleResultPnlCtrl
        return pnl && pnl.isFail()
    }

    //永恒花园第一个战斗关卡失败
    public checkBattleFailPnl_Garden1() {
        if (this.checkBattleFailPnl()) {
            if (mc.currWind instanceof EternalGardenWindCtrl) {
                let map = gameHelper.planet.getCurPlanet().getMap(1)
                let nodes = map.getNodes()
                let firstBattle = nodes.find(n => n instanceof PlanetCheckPointModel)
                return gameHelper.hero.target == firstBattle
            }
        }
    }

    public checkTaskDetailHeadBuild() {
        let detailId = eventCenter.get(EventType.GET_TASK_DETAIL_ID)
        if (detailId == null) return false
        if (!this.checkCompleteSchool()) return false
        let id = cfgHelper.getMiscData("guide").taskId[0]
        return detailId == id && gameHelper.task.getDataById(id)?.canOver
    }

    public checkCompleteSchool() {
        let model = gameHelper.planet.getSchoolPlanet()
        return model && model.isDone()
    }

    public checkCompleteGarden() {
        let model = gameHelper.planet.getGardenPlanet()
        return model && model.isDone()
    }

    // 在主界面触发 且 学院星完成
    public checkInMainAndCompleteSchool() {
        return this.isMainScene() && this.checkCompleteSchool()
    }

    // 在主界面触发 且 没有完成学院星的第二场战斗
    public checkInMainAndNoPassSchoolMonster2() {
        return this.isMainScene() && !gameHelper.planet.isPassBattle("1001-1-2")
    }

    // 在主界面触发 且 永恒花园完成
    public checkInMainAndCompleteGarden() {
        return this.isMainScene() && this.checkCompleteGarden()
    }

    public checkInMainCompleteSchoolNoPnl() {
        return this.isMainScene() && this.checkCompleteSchool()
    }

    public checkInMainCompleteGardenNoPnl() {
        return this.isMainScene() && this.checkCompleteGarden()
    }

    public checkInMainCompleteSchool() {
        return this.isMainScene() && this.checkCompleteSchool()
    }

    public checkInMainAndCanGoNextGalaxy() {
        return this.isMainScene() && viewHelper.checkHavePnl('planet/PlanetChoose') && gameHelper.planet.canGoNextGalaxy(gameHelper.planet.getCurGalaxyId())
    }

    public checkInMainAndUnlockResonance() {
        return this.isMainScene() && unlockHelper.isUnlockFuncByMisc(UIFunctionType.RESONANCE)
    }

    public checkInMainAndUnlockAchievement() {
        return this.isMainScene() && unlockHelper.isUnlockFuncByMisc(UIFunctionType.ACHIEVEMENT)
    }

    // 通过了指定星球节点 且 材料足够
    public checkCanGuideTrainInPlanet() {
        return this.isPlanetWind() && this.checkCanBuyEngine()
    }

    public checkCanGuideTrainInMain() {
        return this.isMainScene() && this.checkCanBuyEngine()
    }

    private checkCanBuyEngine() {
        let cfg = assetsMgr.getJsonData<TrainCfg>('Train', CarriageID.ENGINE)
        return cfg && gameHelper.checkConditions(gameHelper.toConditions(cfg.buyCost))
    }

    public checkUnlockRightBed() {
        return !!this.getRightBed()
    }

    public checkCanUnlockRightBed() {
        let id = DORM_RIGHT_BED_ID
        let data = cfgHelper.getBuildById(id)
        if (!data) return
        let cfg = cfgHelper.getBuildLvCfg(data.carriageId, data.order, 1)
        if (!cfg) return
        return gameHelper.checkConditions(gameHelper.toConditions(cfg.buyCost))
    }

    private getRightBed() {
        let dorm = gameHelper.train.getCarriageById(CarriageID.DORM) as DormModel
        if (!dorm) return
        return dorm.getRightBed()
    }

    public checkUnlockEnginePowerInMain() {
        if (!this.isMainScene()) return false
        let carriage = gameHelper.train.getCarriageById(CarriageID.ENGINE) as EngineModel
        if (!carriage) return
        return carriage.getPower() != null
    }

    public checkEvent(stepInfo: GuideStep, fromEvent: boolean) {
        return fromEvent
    }

    public checkReachGarden() {
        if (!this.isMainScene()) return
        if (gameHelper.planet.curPlanetId != 1005) return
        let model = gameHelper.planet.getCurPlanet()
        if (!model) return
        return !model.isLanded()
    }

    public checkWaitToTarget() {
        return gameHelper.hero.getAction() == HeroAction.WAIT_TO_TARGET
    }

    public checkBattle2001Death(step, fromEvent, eventParams) {
        if (!fromEvent) return
        let id = eventParams[0]
        return id == 2001
    }

    public check1006Death(step, fromEvent, eventParams) {
        if (!fromEvent) return
        let id = eventParams[0]
        return id == 1006
    }

    public checkNoteBook() {
        return this.isPlanetWind() && this.checkPlanetEventNode(PlanetEvent.NOTE_BOOK)
    }

    public checkEngineBuildEnd() {
        return this.isMainScene() && gameHelper.train.getCarriageById(CarriageID.ENGINE).isBuilt
    }

    public checkOpenTransPnl(step, fromEvent, eventParams) {
        let pnl = mc.getPnl("role/RolePnl")
        if (pnl && pnl.getActive()) {
            if (eventParams && eventParams == RolePnlTabType.TRANSFER) {
                return true
            }
            //@ts-ignore
            let tabType = pnl.getTabType()
            return tabType == RolePnlTabType.TRANSFER
        }
    }

    public firstEnterMachinePlanet() {
        return this.checkFirstEnter(1006)
    }

    private checkFirstEnter(id) {
        let planet = gameHelper.planet.getPlanet(id)
        if (!planet) return false
        return this.isPlanetEntry(id) && !planet.isLanded() && !planet.needLandAnim()
    }

    private enterMachinePlanet() {
        let planetId = 1006
        return this.isPlanetEntry(planetId)
    }

    public secondEnterMachinePlanet() {
        return this.enterMachinePlanet() && unlockHelper.isUnlockFunction(UIFunctionType.PLAY_BLACKHOLE)
    }

    public checkBlackHoleKey() {
        return this.isPlanetWind() && this.checkPlanetEventNode(PlanetEvent.BLACK_HOLE_KEY)
    }

    public checkCanUnlockBlackHole() {
        return this.isPlanetEntry(1005) && this.checkPlanetEventNodeEnd(PlanetEvent.BLACK_HOLE_KEY)
    }

    public checkUnlockTower() {
        return unlockHelper.isUnlockFuncByMisc(UIFunctionType.PLAY_TOWER)
    }

    public firstEnterPlanet_1009() {
        return this.checkFirstEnter(1009)
    }

    public checkCanUnlockDailyTask() {
        return unlockHelper.isUnlockFuncByMisc(UIFunctionType.PLAY_DAILY_TASK) && this.isMainScene()
    }

    public checkEnterMakeEquipPnl() {
        return this.isPnlEnter("ore/OreMakePnl")
    }

    public checkCanMakeNextEquip() {
        return this.checkEnterMakeEquipPnl() && gameHelper.ore.getMaxCanMakeQuality() >= 2
    }

    public firstEnterPlanet_1007() {
        return this.checkFirstEnter(1007)
    }

    public checkEnterTransportPnl() {
        return this.isPnlEnter("transport/TransportPnl")
    }

    public checkCanUnlockSpaceStoneBranch() {
        return gameHelper.planet.getPlanet(1007) && this.isPlanetWind() && unlockHelper.isUnlockFuncByMisc(UIFunctionType.PLAY_SPACE_STONE)
    }
    
    public checkEnterSpaceStonePnl() {
        return this.isPnlEnter("spaceStone/SpaceStonePnl")
    }

    public checkCompleteMole() {
        return gameHelper.planet.getPlanet(1009) && this.isPlanetWind() && gameHelper.planet.isPassBattle("1009-2-9")
    }

    public firstEnterPlanet_1008() {
        return this.checkFirstEnter(1008)
    }

    public checkStarFragEnd() {
        return this.isPlanetWind() && this.checkPlanetEventNodeEnd(PlanetEvent.STAR_FRAG, false)
    }

    public checkEnterInstancePnl() {
        return this.isPnlEnter("instance/InstanceLevelPnl")
    }

    public checkCanClaimInstanceReward() {
        return gameHelper.instance.outputNum > 0 && this.isPnlEnter("instance/InstanceLevelPnl")
    }

    public checkUnlockDeepExplore() {
        return unlockHelper.isUnlockFuncByMisc(UIFunctionType.DEEP_EXPLORE) && this.isMainScene()
    }

    public checkCanDeepExplore() {
        let planet = gameHelper.planet.getCurPlanet()
        if (!planet.isDone()) return
        return this.isPlanetEntry() && !gameHelper.planet.getCurPlanet().needLandAnim()
    }

    public checkDeepExploreStart() {
        if (!this.isPnlEnter("planetEntry/DeepExplorePnl")) return false
        return gameHelper.deepExplore.getGuideExplore()
    }

    public checkDeepExploreEnd() {
        if (!this.isPlanetEntry()) return false
        if (!this.isNoPnl()) return false
        let explore = gameHelper.deepExplore.getGuideExplore()
        if (!explore) return false
        return !gameHelper.deepExplore.getExploreInfo(explore.planetId)
    }


    public checkUnlockCollect() {
        return this.isPlanetEntry(1001) && gameHelper.dailyTask.getTasks()[0] && !gameHelper.planet.getCurPlanet().needLandAnim()
    }

    public checkEnterBlackHole() {
        return this.isPnlEnter("blackHole/BlackHolePnl")
    }

    public checkEnterDeepExploreReady() {
        return this.isPnlEnter("planetEntry/DeepReadyPnl")
    }

    public checkCanUnlockOre() {
        return this.isPlanetEntry(1009) && this.checkPlanetEventNodeEnd(PlanetEvent.HOLE_AFTER)
    }

    //-----------------------------------------

    //------------ start func ----------------

    public async startCollectStardust() {
        this.dropStardust1()
        gameHelper.passenger.stopRole(1005)
        await this.focusCarriage(CarriageID.DORM)
    }

    public async collectFirstStardust(stepInfo) {
        let carriage: CarriageModel = this.getDorm()
        let areaTops = carriage.moneyAreas.filter(a => a.up)
        let areaTopLeftId = areaTops[0].id
        eventCenter.emit(EventType.GUIDE_SHOW_CLICK, stepInfo)
        await this.onEvent(EventType.UPDATE_STARDUST, () => {
            return !carriage.getAllDrops().find(m => m.areaId == areaTopLeftId)
        })
        carriage.saveGuideDrop()
    }

    public async collectElseStardust() {
        eventCenter.emit(EventType.GUIDE_FREE_TOUCH)
        let carriage: CarriageModel = this.getDorm()
        eventCenter.wait(EventType.UPDATE_STARDUST, () => {
            let bol = carriage.getAllDrops().length == 0
            if (bol) {
                gameHelper.passenger.startRole(1005)
            }
            return bol
        })
    }

    public async showBuild1015_1_1() {
        if (!this.isMainScene()) await this.waitGotoMain()
        let trainHead = gameHelper.train.getCarriageById(CarriageID.HEAD)
        let focusCfg: FocusCfg = {
            needBackZoomRatio: true,
            needBackX: true,
            needFocusZoomRatio: true,
            backZoomRatio: 0.7,
            time: 0.3,
        }
        eventCenter.emit(EventType.FOCUS_CARRIAGE, trainHead, focusCfg)

        //胡桃夹子走过去喝茶
        let id = 1006
        let role = gameHelper.passenger.getPassenger(id)
        let dorm = this.getDorm()
        if (!role.isCheckIn() || role.carriageId != dorm.getID()) {
            role.putToCarriage(dorm)
        }

        viewHelper.showUI(false)
        role.setPosition(cc.v2(1900, 100))
        role.setDir(RoleDir.RIGHT)
        await this.roleAction(async (action) => {
            let model = action.params
            model.actionAgent.guideType = RoleGuideActionType.TRIAN_HEAD_DRINK
            await action.run(model.moveToCarriage, trainHead)
            action.ok()
        }, id, false)
        await this.onEvent(EventType.PASSENGER_STATE_CHANGE, (model, state) => {
            if (model != role) return
            if (state?.type == StateType.SIT) {
                return true
            }
        })
        ut.wait(3).then(()=>{
            role.actionAgent.guideType = null
        })

        this.show2Build1015_1_1(id)
        this.show3Build1015_1_1(id)
        viewHelper.showUI(true)
    }

    private async show2Build1015_1_1(id: number) {
        viewHelper.showSpeechBubbleByRole(id, cfgHelper.getPlotId("guide_bubble_1006_1"))
    }

    private async show3Build1015_1_1(id: number) {
        await ut.wait(0.85)
        let role = gameHelper.passenger.getPassenger(id)
        let trainHead = gameHelper.train.getCarriageById(CarriageID.HEAD)
        let num = cfgHelper.getMiscData("guide").headstockStarNum
        trainHead.addDropMoneyByRole(new ConditionObj().init(ConditionType.STAR_DUST, -1, num), role)
    }

    public async waitGotoMain() {
        let p = this.onEvent(CoreEventType.WIND_ENTER)
        viewHelper.gotoWind('main')
        await p
        await ut.wait(0.3)
    }

    public showGuideTask() {
        eventCenter.emit(EventType.GUIDE_SHOW_TASK)
    }

    public async showFirstEnterMachinePlanet() {
        let p = this.onEvent(EventType.GUIDE_FIRST_ENTER_PLANET_1006_END)
        eventCenter.emit(EventType.GUIDE_FIRST_ENTER_PLANET_1006)
        await p
    }

    //----------------------------------------

    private isSchoolPlanetWind() {
        return mc.currWindName == "schoolPlanet"
    }

    private isMachinePlanetWind() {
        return mc.currWindName == "machinePlanet"
    }

    public isPlanetWind(id?) {
        if (id && gameHelper.planet.getCurPlanet().getId() != id) {
            return
        }
        return mc.currWind instanceof PlanetWindCtrl
    }

    public isPlanetEntry(id?) {
        if (id && gameHelper.planet.getCurPlanet().getId() != id) {
            return
        }
        return mc.currWind instanceof PlanetEntryWindCtrl
    }

    private isPnlEnter(key) {
        let pnl = mc.getPnl(key)
        return pnl && pnl.getActive()
    }

    public isMainScene() {
        return mc.currWindName == "main"
    }

    public isMainAndNoPnl() {
        return this.isMainScene() && this.isNoPnl()
    }

    public isNoPnl() {
        return !mc.getViewMgr().hasOpenPnl()
    }

    private async waitLeaveMainScene() {
        return new Promise(resolve => {
            let id = 0
            let check = () => {
                if (!this.isMainScene()) {
                    gameHelper.clearInterval(id)
                    resolve(true)
                }
            }
            id = gameHelper.setInterval(check, 100)
        })
    }

    private getDorm() {
        return gameHelper.train.getCarriageById(CarriageID.DORM)
    }

    private onEvent(type, checkEnd?: (...params) => boolean) {
        return new Promise(r => {
            let event
            let onEvent = (...params) => {
                if (!checkEnd || checkEnd(...params)) {
                    eventCenter.off(type, onEvent)
                    this.events.remove(event)
                    r(null)
                }
            }
            event = { type, func: onEvent }
            this.events.push(event)
            eventCenter.on(type, onEvent)
        })
    }

    public offAllEvent() {
        for (let { type, func } of this.events) {
            eventCenter.off(type, func)
        }
        this.events.length = 0
    }
}