import { Msg } from "../../../proto/msg-define"
import { Condition, InstanceLevelCfg } from "../../common/constant/DataType"
import { ConditionType, ROLE_ATTR_ID, UIFunctionType } from "../../common/constant/Enums"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import EventType from "../../common/event/EventType"
import BattleRole from "../battle/BattleRole"
import InstanceSkill from "./InstanceSkill"

@mc.addmodel('instance')
export default class InstanceModel extends mc.BaseModel {
    public data: proto.IInstance

    public currentLevel: number
    public isUnlock: boolean = false

    public init() {
        this.updateInfo(this.data)
    }

    public updateInfo(data: proto.IInstance) {
        this.currentLevel = data.level
        this.isUnlock = data.isUnlock
    }

    private passLevel(id: number) {
        this.currentLevel = id
    }

    public async battleWin(cfg: InstanceLevelCfg) {
        let level = cfg.id
        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_InstanceFightMessage, { level })
        if (code == 0) {
            gameHelper.grantRewards(gameHelper.toConditions(cfg.reward))
            this.passLevel(level)
            eventCenter.emit(EventType.INSTANCE_BATTLE_WIN)
            return true
        } else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public isPass(cfg: InstanceLevelCfg): boolean {
        return cfg.id <= this.currentLevel
    }

    public isLock(cfg: InstanceLevelCfg): boolean {
        return cfg.id > this.currentLevel + 1
    }

    public getBuff(data) {
        let skillId = data.skill
        let skills = [new InstanceSkill().init(skillId, 0)]
        let r = new BattleRole().initData({ id: Number(ROLE_ATTR_ID.INSTANCE), skills })
        for (let skill of skills) {
            skill.setRole(r)
        }
        return r
    }

    public async unlock() {
        let msg = new proto.C2S_UnlockInstanceMessage()
        let res = await gameHelper.net.request(Msg.C2S_UnlockInstanceMessage, msg, true)
        const { code } = proto.S2C_UnlockInstanceMessage.decode(res)
        if (code == 0) {
            this.isUnlock = true
            eventCenter.emit(EventType.UNLOCK_FUNTION, UIFunctionType.PLAY_INSTANCE)
            return true
        } else {
            viewHelper.showNetError(code)
            return false
        }
    }
}
