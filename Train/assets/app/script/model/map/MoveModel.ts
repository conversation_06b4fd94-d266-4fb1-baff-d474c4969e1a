import IMapObj from "./IMapObj"
import { mapHelper } from "../../common/helper/MapHelper"
import CarriageMap from "../train/common/CarriageMap"
import AStar from "../../common/helper/AStart"
import { MAX_ZINDEX } from "../../common/constant/Constant"
import BaseMap from "./BaseMap"
import { RoleDir } from "../../common/constant/Enums"

// 新增Shape类
export class Shape {
    constructor(
        public size: cc.Size = cc.size(1, 1),
        public anchor: cc.Vec2 = cc.v2(0, 0)
    ) {}

    public filpX() {
        let anchor = this.anchor
        anchor.x = this.size.width - 1 - anchor.x
    }

    getPoints(x: number, y: number, dir: RoleDir): cc.Vec2[] {
        let shape = this
        const width = shape?.size?.width ?? 1;
        const height = shape?.size?.height ?? 1;
        let anchorX = shape?.anchor?.x ?? 0;
        const anchorY = shape?.anchor?.y ?? 0;
    
        // 镜像锚点
        if (dir === RoleDir.LEFT) {
            anchorX = width - 1 - anchorX;
        }
    
        const leftBottomX = x - anchorX;
        const leftBottomY = y - anchorY;
    
        const points: cc.Vec2[] = [];
        for (let i = 0; i < width; i++) {
            for (let j = 0; j < height; j++) {
                points.push(cc.v2(leftBottomX + i, leftBottomY + j));
            }
        }
        return points;
    }
}

// 移动
export default class MoveModel extends IMapObj {

    public active: boolean = false //是否激活
    private _position: cc.Vec2 = cc.v2() //实际坐标位置
    protected _tempPosition: cc.Vec2 = cc.v2()
    private positionOffset: cc.Vec2 = cc.v2() //偏移
    public currVelocity: number = 90 //速度值
    public speedMul: number = 1 //速度倍数

    public map: BaseMap = null //所在地图信息
    public area: any = null
    // 
    protected paths: cc.Vec2[] = []// 当前移动路径
    public speed: cc.Vec2 = cc.v2()// 速度矢量
    public tempSpeed: cc.Vec2 = cc.v2()// 临时速度
    public targetPosition: cc.Vec2 = null// 当前目标位置
    private tempPosition: cc.Vec2 = cc.v2()
    public moving: boolean = false //是否移动中
    public tempMoveTarget: any = {}
    //
    public moveTargetPosition: cc.Vec2 = null //强行移动目标
    private reachCallback: Function = null

    private tempVec1: cc.Vec2 = cc.v2()
    private tempVec2: cc.Vec2 = cc.v2()
    private tempVec3: cc.Vec2 = cc.v2()

    //
    private astar: AStar = new AStar()
    //
    public pause: boolean = false

    public shape: Shape = null

    public dir: RoleDir = RoleDir.RIGHT

    // 激活
    public setActive(val: boolean) {
        this.active = val
        this.speedMul = val ? 1 : 1
        if (val && this.map) { //刷新一下zindex
            this.map.setRoleZindex(this)
        }
    }

    public setSpeed(speed) {
        this.currVelocity = speed
    }

    public getSpeed() {
        return this.currVelocity
    }

    public setDir(dir: RoleDir) {
        this.dir = dir
    }

    // 初始化地图信息
    public initMapInfo(map: BaseMap) {
        this.map = map
        this.paths.length = 0
        this.targetPosition = null
        this.moving = false
        this.astar.setDir(8)
        this.astar.checkCanPass = (x: number, y: number, dir) => {
            return map.checkCanPass(x, y, this.shape, dir);
        }
        this.setArea(map)
        this.setActive(true)
        return this
    }

    public get pathCount() {
        return this.paths.length
    }

    public get position() {
        return this._position.add(this.positionOffset, this._tempPosition)
    }

    public get actPosition() {
        return this._position
    }

    public setArea(area) {
        this.area = area
        this.astar.area = area
        this.astar.mapHeight = area.size.height
        this.astar.mapWidth = area.size.width
    }

    public setReachCallBack(cb) {
        this.reachCallback = cb
    }

    public setPointAndPosition(point: cc.Vec2, position?: cc.Vec2) {
        point = point || this.area.getActPointByPixel(position)
        position = position || this.area.getActPixelByPoint(point)
        this.point.set(point)
        this._position.set(position)
        this.map?.setRoleZindex(this)
    }

    public getPosition() {
        return this._position
    }

    public getPoint() {
        return this.point
    }

    public getPaths() {
        return this.paths;
    }

    public isMoving() {
        return this.moving
    }

    // 设置移动路径
    public setPath(paths: cc.Vec2[]) {
        if (mapHelper.clonePath(this.paths, paths)) {
            // if (!this.moving) {
            this.moving = true
            this.setTargetPosition()
            // }
        } else {
            this.targetPosition = null
            this.moving = false
        }
    }

    // 设置强行移动目标
    public setMoveTargetPosition(position: cc.Vec2) {
        if (this.paths.length > 0) {
            return
        }
        this.moving = true
        this.moveTargetPosition = position.clone()
        this.paths.push(this.moveTargetPosition)
        this.setTargetPosition()
    }

    public updateMovePosition(dt: number) {
        this.updateMove(dt)
        if (!this.moving) {
            this.moveTargetPosition = null
        }
    }

    // 设置目标点
    public setTargetPosition() {
        this.targetPosition = null
        if (this.paths.length === 0) {
            this.moving = false
            if (this.reachCallback) {
                this.reachCallback()
                this.reachCallback = null
            }
        } else {
            const point = this.paths.shift()
            if (this.paths.length === 0) { //最后一个默认是像素位置
                this.targetPosition = this.tempPosition.set(point)
                this.point.set(this.area.getActPointByPixel(this.targetPosition))
            } else {
                this.point.set(point)
                const pos = this.area.getMovePositionByPoint(point)
                this.targetPosition = pos ? this.tempPosition.set(pos) : this.area.getActPixelByPoint(this.point, this.tempPosition)
            }
            this._setSpeedVec()
        }
    }

    private _setSpeedVec() {
        // 设置速度
        let angle = ut.getAngle(this._position, this.targetPosition)
        this.angleToPoint(angle, this.currVelocity, this.speed)
    }

    public angleToPoint(angle: number, dis: number, out?: cc.Vec2) {
        out = out || cc.v2()
        dis = cc.misc.lerp(1, 1 / 1.9, Math.abs(ut.sin(angle))) * dis
        out.x = ut.cos(angle) * dis
        out.y = ut.sin(angle) * dis
        return out
    }

    // 刷新移动
    public updateMove(dt: number) {
        if (this.pause || !this.targetPosition) {
            return
        }
        // 刷新位置
        let preX = this._position.x
        let preY = this._position.y
        this._position = mapHelper.amendMoveSpeed(this.speed.mul(this.speedMul, this.tempVec3), this._position, this.targetPosition, dt)

        this.dir = this._position.x > preX ? RoleDir.RIGHT : RoleDir.LEFT
    
        // 刷新zindex
        this.zIndex = MAX_ZINDEX - this._position.y
        // 是否到达目的
        if (this._position.equals(this.targetPosition)) {
            this.setTargetPosition()
        }
        else {
            if (preX === this._position.x && preY === this._position.y) {
                this._setSpeedVec()
            }
        }
    }

    public async move(point: cc.Vec2 | Function, position?: cc.Vec2): Promise<boolean> {
        return new Promise(async (onReach) => {
            this.reachCallback = () => {
                onReach(true)
            }
            let succ = await this.searchPath(point, position)
            if (!succ) {
                onReach(false)
            }
        })
    }

    public moveForce(position: cc.Vec2) {
        return new Promise((onReach) => {
            this.reachCallback = () => {
                onReach(true)
            }
            this.setMoveTargetPosition(position)
        })
    }

    // 寻路
    public async searchPath(point: cc.Vec2 | Function, position?: cc.Vec2) {
        let paths
        if (typeof point == 'function') {
            this.astar.checkEndPoint = point
            paths = await this.astar.searchPath(this.point)
            this.astar.checkEndPoint = null
            let len = paths.length
            if (len > 0) { //设置最后一个为像素位置
                paths[len - 1] = this.area.getActPixelByPoint(paths[len - 1])
            }
        }
        else {
            this.tempVec1.set(point || this.area.getActPointByPixel(position))
            this.tempVec2.set(position || this.area.getActPixelByPoint(point))
            paths = await this.astar.searchPath(this.point, this.tempVec1)

            if (paths.length > 0) { //设置最后一个为像素位置
                paths.pop()
                paths.push(this.tempVec2.clone())
            } else if (this.tempVec1.equals(this.point) && !this.tempVec2.equals(this._position)) {
                paths.push(this.tempVec2.clone())
            }
        }

        this.setPath(paths)
        return paths.length > 0
    }

    public searchPathSync(point: cc.Vec2 | Function, position?: cc.Vec2) {
        let paths
        if (typeof point == 'function') {
            this.astar.checkEndPoint = point
            paths = this.astar.searchPathSync(this.point)
            this.astar.checkEndPoint = null
            let len = paths.length
            if (len > 0) { //设置最后一个为像素位置
                paths[len - 1] = this.area.getActPixelByPoint(paths[len - 1])
            }
        }
        else {
            this.tempVec1.set(point || this.area.getActPointByPixel(position))
            this.tempVec2.set(position || this.area.getActPixelByPoint(point))
            paths = this.astar.searchPathSync(this.point, this.tempVec1)

            if (paths.length > 0) { //设置最后一个为像素位置
                paths.pop()
                paths.push(this.tempVec2.clone())
            } else if (this.tempVec1.equals(this.point) && !this.tempVec2.equals(this._position)) {
                paths.push(this.tempVec2.clone())
            }
        }

        this.setPath(paths)
        return paths.length > 0
    }

    // 重置零时的移动目标
    public resetTempMoveTarget(mapUid: string, point?: cc.Vec2): { mapUid: string, point: cc.Vec2, position: cc.Vec2, pathState: number } {
        this.tempMoveTarget.mapUid = mapUid
        this.tempMoveTarget.point = point
        this.tempMoveTarget.position = null
        this.tempMoveTarget.pathState = 0
        return this.tempMoveTarget
    }

    public stop() {
        this.paths.length = 0
        this.targetPosition = null
        this.moving = false
    }

    //释放自己的对象引用，避免循环引用可能导致的泄漏和性能问题
    public clean() {
        super.clean()
        if (this.map) {
            this.map = null
        }
        if (this.astar) {
            this.astar.clean()
            this.astar = null
        }
        this.area = null
        this.paths = null
        if (this.tempMoveTarget.position) {
            this.tempMoveTarget.position = null
        }
        if (this.tempMoveTarget.point) {
            this.tempMoveTarget.point = null
        }
    }

    // 设置体积和锚点
    public setShape({ size, anchor }: { size?: cc.Size, anchor?: cc.Vec2 }) {
        if (!this.shape) {
            this.shape = new Shape(size, anchor)
        } else {
            if (size) {
                this.shape.size = size;
            }
            if (anchor) {
                this.shape.anchor = anchor;
            }
        }
    }

    public clearShape() {
        this.shape = null
    }

    public getShape() {
        return this.shape
    }

    public checkCanPass(x: number, y: number) {
        return this.astar.checkCanPass(x, y, this.dir)
    }
}