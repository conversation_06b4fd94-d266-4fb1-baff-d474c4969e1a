import { RoleDir } from "../../common/constant/Enums";
import { mapHelper } from "../../common/helper/MapHelper";
import { Agent } from "./MoveModel";

/**
 * 地图基类
 */
export default class BaseMap {

    protected matrix: { [key: string]: { [key: string]: number } } = {}; //每个格子的信息
    protected basePoint: cc.Vec2 = null;
    protected gridSize: number = 0; //单个格子的大小n*n
    protected size: cc.Size = cc.size(0, 0) //地图横向纵向格子的数量
    public active: boolean = false
    public uid: string = null

    protected tempVec: cc.Vec2 = cc.v2(0, 0)

    public checkCanPass(x: number, y: number, agent?: Agent, dir?: RoleDir) {
        // 体积和锚点
        const width = agent?.size?.width ?? 1;
        const height = agent?.size?.height ?? 1;
        let anchorX = agent?.anchor?.x ?? 0;
        const anchorY = agent?.anchor?.y ?? 0;

        // 根据方向做锚点镜像
        if (dir === RoleDir.LEFT) {
            anchorX = width - 1 - anchorX;
        }

        const leftBottomX = x - anchorX;
        const leftBottomY = y - anchorY;

        // 边界检查
        if (leftBottomX < 0 || leftBottomX + width > this.size.width) return false;
        if (leftBottomY < 0 || leftBottomY + height > this.size.height) return false;

        // 检查体积四周边界格子
        for (let i = 0; i < width; i++) {
            if (this.matrix[leftBottomX + i] && this.matrix[leftBottomX + i][leftBottomY] > 0) return false; // 下
            if (this.matrix[leftBottomX + i] && this.matrix[leftBottomX + i][leftBottomY + height - 1] > 0) return false; // 上
        }
        for (let j = 0; j < height; j++) {
            if (this.matrix[leftBottomX] && this.matrix[leftBottomX][leftBottomY + j] > 0) return false; // 左
            if (this.matrix[leftBottomX + width - 1] && this.matrix[leftBottomX + width - 1][leftBottomY + j] > 0) return false; // 右
        }
        return true;
    }

    public setBasePoint(point: cc.Vec2) {
        this.basePoint = point;
    }

    public getMatrix() {
        return this.matrix;
    }

    public getBasePoint() {
        return this.basePoint;
    }

    public getGridSize() {
        return this.gridSize;
    }

    public setGridSize(size: number) {
        this.gridSize = size
    }

    public getSize() {
        return this.size
    }

    public setSize(size) {
        this.size = size
    }

    public setRoleZindex(p?) {

    }

    public updatePoints(points: cc.Vec2[], add: boolean) {
        let val = add ? 1 : -1
        for (let point of points) {
            let { x, y } = point
            if (!this.matrix[x]) this.matrix[x] = {}
            if (!this.matrix[x][y]) this.matrix[x][y] = 0
            this.matrix[x][y] += val
        }
    }

    // 根据网格点获取像素点
    public getActPixelByPoint(point: cc.Vec2, out?: cc.Vec2) {
        out = out || cc.v2()
        out.x = (point.x + 0.5) * this.gridSize
        out.y = (point.y + 0.5) * this.gridSize
        out.addSelf(this.basePoint)
        return out
    }

    // 根据像素点获取网格点
    public getActPointByPixel(pos: cc.Vec2, out?: cc.Vec2) {
        out = out || cc.v2()
        pos = pos.sub(this.basePoint)
        out.x = Math.floor(pos.x / this.gridSize)
        out.y = Math.floor(pos.y / this.gridSize)
        return out
    }

    public getMovePositionByPoint() {
        return false
    }

    public getPointsByPolygon(polygonVertexs: cc.Vec2[]) {
        let { width, height } = this.getSize()
        let points = []
        for (let i = 0; i < width; i++) {
            for (let j = 0; j < height; j++) {
                let pos = this.getActPixelByPoint(cc.v2(i, j))
                if (cc.Intersection.pointInPolygon(pos, polygonVertexs)) {
                    points.push(cc.v2(i, j))
                }
            }
        }
        return points
    }
}