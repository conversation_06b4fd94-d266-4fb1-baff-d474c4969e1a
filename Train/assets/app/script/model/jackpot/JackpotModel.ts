import { ExtractHistory } from '../../common/constant/DataType';
import { ConditionType } from '../../common/constant/Enums';
import { cfgHelper } from '../../common/helper/CfgHelper';
import { gameHelper } from '../../common/helper/GameHelper';
import { Msg } from "../../../proto/msg-define";
import { viewHelper } from "../../common/helper/ViewHelper";
import EventType from '../../common/event/EventType';

export type ExtractData = {
    /**角色id*/
    id: number
    /**转换成碎片数量*/
    convert?: number
}

/**抽卡 */
@mc.addmodel('jackpot')
export default class JackpotModel extends mc.BaseModel {

    public data: proto.IJackpot

    private history: Array<ExtractHistory> = [];

    private historyCount: number;
    private jackpotDailyMaxNum: number;
    private dailyCount: number = 0;
    private points: number = 0
    private jackpotTotalCount: number = 0

    public init() {
        this.points = this.data.jackpotPoints
        this.jackpotTotalCount = this.data.jackpotTotalCount
        this.historyCount = cfgHelper.getMiscData('jackpotHistoryMaxNum') || 30;
        this.jackpotDailyMaxNum = cfgHelper.getMiscData('jackpotDailyMaxNum') || 50;
    }

    public getHistory() {
        return this.history;
    }

    public getJackpotTotalCount() { return this.jackpotTotalCount }
    public addJackpotCount(v: number) { this.jackpotTotalCount += v }


    public getChangeNum() {
        return 10
    }

    public updateInfo(dailyCount) {
        this.dailyCount = dailyCount
    }

    public toExtracts(list: proto.ICondition[]) {
        return list.map(cond => {
            let prize: ExtractData = { id: cond.id }
            if (cond.type == ConditionType.PASSENGER) {
            } else if (cond.type = ConditionType.CHARACTER_FRAG) {
                let cfg = cfgHelper.getFragById(cond.id)
                prize.id = +cfg.characterId
                prize.convert = cond.num
            }
            /*
            else if (cond.type == ConditionType.PROP) {
                prize.convert = cond.num
            }
                */
            else {
                twlog.error("toExtracts unknow", cond)
            }
            return prize
        })
    }

    public async commonExtract(count: number = 1) {
        let type = count == 1 ? 1 : 2 //1：单抽 2：十连
        let msgId = ut.uid()
        let msg = new proto.C2S_JackpotReqMessage({ type, msgId })
        const res = await gameHelper.net.request(Msg.C2S_JackpotReqMessage, msg, true)
        const { code, list } = proto.S2C_JackpotRspMessage.decode(res)
        if (code == 0) {
            eventCenter.emit(EventType.JACKPOT_SUCCESS)
            gameHelper.grantRewards(gameHelper.toConditions(list))
            this.updatePoints(type)
            this.addJackpotCount(count)
            return this.toExtracts(list)
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    public async JackpotPointsGet() {
        let msg = new proto.C2S_JackpotPointsGetMessage()
        const res = await gameHelper.net.request(Msg.C2S_JackpotPointsGetMessage, msg, true)
        const { code, list } = proto.S2C_JackpotPointsGetMessage.decode(res)
        if (code == 0) {
            gameHelper.grantRewards(gameHelper.toConditions(list))
            this.updatePoints(3)
            return this.toExtracts(list)
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    public getDailyCount() {
        return this.dailyCount
    }

    public getDailyMaxCount() {
        return this.jackpotDailyMaxNum
    }

    private updatePoints(type: number) {
        let point = cfgHelper.getMiscData('jackpotPointsConvert')
        let pointMax = cfgHelper.getMiscData('jackpotPointsGet')
        this.points += point * (type == 1 ? 1 : (type == 2 ? 10 : -Math.round(pointMax / point))) //1：单抽 2：十连 3:领取奖励
    }

    public getPointPercentage() {
        let pointMax = cfgHelper.getMiscData('jackpotPointsGet')
        return Math.round(this.points * (100 / pointMax))
    }

    public isFirst() {
        return false
    }

    public getDiamondNeed(drawNum) {
        let one = cfgHelper.getMiscData('jackPotPrice')
        let dis = drawNum == 1 ? 1 : cfgHelper.getMiscData('jackPotDiscount')
        return Math.round(drawNum * one * dis)
    }

    public insertHistory(history: ExtractHistory, prize: any) {
        for (let i = 0; i < prize.length; i++) {
            // '_c' 表示此角色已经被转换
            history.prize.push(prize[i].convert ? prize[i].id + '_c' : prize[i].id + '');
        }
        if (this.history.length > this.historyCount) this.history.pop();
        this.history.unshift(history);
    }


}
