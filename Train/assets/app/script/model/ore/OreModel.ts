import { util } from "../../../core/utils/Utils";
import { Msg } from "../../../proto/msg-define";
import { EquipLevelCfg, EquipMakeCfg, OreItemCfg } from "../../common/constant/DataType";
import { ConditionType, ItemID, MarkNewType, NPC_ID, OreLandType, OreOperateType, UIFunctionType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../common/ConditionObj";
import { OreHole } from "./OreHole";
import { OreLandNode } from "./OreLandNode";

const { ccclass, property } = cc._decorator;


const ROWVIEW = 6

export class OreItemObj {
    public id: number
    public count: number
    private cfg: OreItemCfg

    public get name() { return this.cfg?.name }
    public get content() { return this.cfg?.content }
    public get icon() { return this.cfg?.icon }
    public get xp() { return this.cfg?.xp }


    public init(id: number, count: number = 1) {
        this.id = id
        this.count = count
        this.initJson()
        return this
    }

    private initJson() {
        this.cfg = cfgHelper.getOreItemById(this.id)
    }

}

@mc.addmodel('ore')
export default class OreModel extends mc.BaseModel {
    public data: proto.IOre
    private oreHoles: OreHole[] = []
    private recoverTime: number = 0 //镐子刷新时间
    private recoverEndTime: number = 0
    private oreItems: OreItemObj[] = []
    // 当前难度
    private _currentLevel: number = 0
    public isUnlock: boolean = false
    // 记录解密成功的位置
    private puzzleKeySet = new Set()

    public getCurrentLevel() { return this._currentLevel }
    public setCurrentLevel(value: number) { this._currentLevel = value }

    public makePnlRandomPosMap = {} //打造界面用

    public init() {
        this.oreHoles = this.data.data.map((hole) => {
            return new OreHole().init(hole)
        })
        this._currentLevel = this.oreHoles.length
        this.recoverTime = this.data.recoverTime
        this.recoverEndTime = gameHelper.now() + this.recoverTime
        for (let id in this.data.oreItems) {
            let num = this.data.oreItems[id]
            if (num > 0) {
                this.oreItems.push(new OreItemObj().init(+id, this.data.oreItems[id]))
            }
        }
        this.isUnlock = this.data.isUnlock
    }

    public updateInfo(data: proto.IOre) {
        this.updateRecoverTime(data.recoverTime)
    }

    private updateRecoverTime(time: number) {
        this.recoverTime = time
        this.recoverEndTime = gameHelper.now() + time
    }

    public getOreHole(level: number) { return this.oreHoles[level] }
    public getOreMap(level: number) { return this.oreHoles[level].getOreMap() }
    public getDeep(level: number) { return this.oreHoles[level].getDeep() }
    public getRealDeep(level: number) { return this.oreHoles[level].getRealDeep() }

    public getOreNodeByPos(x: number, y: number, level: number) {
        return this.oreHoles[level]?.getOreNodeByPos(x, y)
    }

    public getOreItemList() { return this.oreItems }

    public changeOreItem(id: number, count: number): number {
        let item = this.getOreItemById(id)
        if (!item) {
            item = new OreItemObj().init(id, count)
            this.oreItems.push(item)
        } else {
            item.count += count
        }
        if (item.count <= 0) {
            this.delOreItem(id)
        }
        return item.count;
    }

    public delOreItem(id: number) {
        this.oreItems.remove("id", id)
    }

    public getOreCountById(id: number) {
        let ore = this.getOreItemById(id)
        if (!ore) return 0
        return ore.count
    }

    public getOreItemById(id: number) {
        return this.oreItems.find(p => p.id == id)
    }

    public getNormalBreakNum() {
        return gameHelper.bag.getPropCountById(ItemID.NormalBreak)
    }

    public getNormalBreakMaxNum() {
        return cfgHelper.getMiscData('ore').breakMaxNum
    }

    update() {
        if (!this.isUnlock) return
        if (this.getNormalBreakNum() < this.getNormalBreakMaxNum()) {
            if (this.getRecoverTime() <= 0) {
                this.syncBreak()
            }
        }
    }

    public getRecoverTime() {
        return Math.max(0, this.recoverEndTime - gameHelper.now())
    }

    private updateRecoverSurpluTime(time = 0) {
        this.recoverTime = time
        this.recoverEndTime = gameHelper.now() + time
    }

    //同步获得镐子
    @util.addLock
    public async syncBreak() {
        let msg = new proto.C2S_OreSyncBreakItemTimeMessage()
        let res = await gameHelper.net.request(Msg.C2S_OreSyncBreakItemTimeMessage, msg)
        const { item, time } = proto.S2C_OreSyncBreakItemTimeMessage.decode(res)
        gameHelper.bag.changeProp(item.id, item.num - gameHelper.bag.getPropCountById(item.id), true)
        this.updateRecoverSurpluTime(time)
    }

    public async operateLands(node: OreLandNode, opType: OreOperateType, level: number, extra?: number) {
        let deep = this.oreHoles[level].getDeep()
        let actNode = this.oreHoles[level].getBossNode(node)
        let msg = new proto.C2S_OreActionMessage({ x: actNode.getRow(), y: actNode.getLine() - deep, type: Number(opType), level: level + 1, extra })
        let res = await gameHelper.net.request(Msg.C2S_OreActionMessage, msg, true)
        const { code, data, rewards } = proto.S2C_OreActionMessage.decode(res)
        if (code == 0) {
            let e = this.getOreNodeByPos(actNode.getRow(), actNode.getLine(), level)
            !!e && e.operate(opType)
            this.updateLands(data, level)
            if (node.getType() != OreLandType.NONE) {
                this.costProp(opType)
            }
            gameHelper.grantRewards(gameHelper.toConditions(rewards))
            if (e.getType() == OreLandType.NONE && e.getRow() < ROWVIEW) {
                this.oreHoles[level].refreshRealDeep()
            }
            if (e.getType() == OreLandType.SPECIAL) {
                this.oreHoles[level].moveRow(false)
            }
            if (e.getType() == OreLandType.BACK) {
                this.oreHoles[level].moveRow(true)
            }
            if (!!rewards && rewards.length > 0) {
                return gameHelper.toConditions(rewards)
            }
            return true
        }
        if (code == 1) { viewHelper.showAlert('操作位置不正确') }
        else if (code == 2) { viewHelper.showAlert('选中格子不允许这个操作') }
        else if (code == 3) { viewHelper.showAlert('道具不足') }
        else viewHelper.showNetError(code)
        return false
    }


    private updateLands(data: proto.IOreRowData[], level: number) {
        return this.oreHoles[level]?.updateLands(data)
    }

    private costProp(opType: OreOperateType) {
        if (opType == OreOperateType.ONE) {
            gameHelper.bag.changeProp(ItemID.NormalBreak, -1)
        }
        else if (opType == OreOperateType.TWICE) {
            gameHelper.bag.changeProp(ItemID.SpecialBreak, -1)
        }
        else if (opType == OreOperateType.BOOM) {
            gameHelper.bag.changeProp(ItemID.Boom, -1)

        }
        else if (opType == OreOperateType.DRILL) {
            gameHelper.bag.changeProp(ItemID.Drill, -1)
        }
    }

    public async oreBattle(level: number) {
        let msg = new proto.C2S_OreLevelFightMessage({ level: level + 1 })
        let res = await gameHelper.net.request(Msg.C2S_OreLevelFightMessage, msg, true)
        const { code } = proto.S2C_OreLevelFightMessage.decode(res)
        if (code == 0) {
            await this.getOreLevelData(level)
            let cfg = cfgHelper.getOreLevels().find(d => d.id == level + 1)
            gameHelper.grantRewards(gameHelper.toConditions(cfg.reward))
            this._currentLevel = this.oreHoles.length
            eventCenter.emit(EventType.ORE_LEVEL_CHANGE)
        }
        else {
            if (code == 1) viewHelper.showAlert('难度已解锁')
            else if (code == 2) viewHelper.showAlert('不存在的难度配置')
            else { viewHelper.showNetError(code) }
        }
    }

    public async getOreLevelData(level) {
        let msg = new proto.C2S_GetOreLevelDataMessage({ level: level + 1 })
        let res = await gameHelper.net.request(Msg.C2S_GetOreLevelDataMessage, msg, true)
        const { data } = proto.S2C_GetOreLevelDataMessage.decode(res)
        const idx = this.oreHoles.findIndex(o => o.getLevel() == data.level)
        if (idx == -1) this.oreHoles.push(new OreHole().init(data))
        else {
            // 把原来的移除，插入新的
            this.oreHoles[idx] = new OreHole().init(data)
        }
    }

    public canReach(x: number, y: number, level: number) {
        return this.oreHoles[level]?.canReach(x, y)
    }

    public async getARoad(level: number) {
        let hole = this.oreHoles[level]
        let x = hole.startNode.x, y = hole.startNode.y
        let vis: number[][] = []
        let que: { x: number, y: number }[] = []//还没用过的
        let pre = new Map()
        let road: OreLandNode[] = []
        let endPos
        let ok: boolean = false
        pre.set(hole.getOreNodeByPos(x, y), null)
        que.push({ x, y })
        if (!vis[x]) vis[x] = []
        vis[x][y] = 1
        let index = 0
        while (index < que.length && !ok) {
            let nowX = que[index].x, nowY = que[index].y
            index++
            for (let i = -1; i <= 1; i++) {
                for (let j = -1; j <= 1; j++) {
                    if (i * j == 0) {
                        let posX = nowX + i, posY = nowY + j
                        let node = hole.getOreNodeByPos(posX, posY)
                        if (!!node && (!vis[posX] || !vis[posX][posY]) && node.getType() != OreLandType.BLACK) {
                            que.push({ x: posX, y: posY })
                            if (!vis[posX]) vis[posX] = []
                            vis[posX][posY] = 1
                            pre.set(hole.getOreNodeByPos(posX, posY), hole.getOreNodeByPos(nowX, nowY))
                            if (node.getType() == OreLandType.NEXT) {
                                endPos = { x: posX, y: posY }
                                ok = true
                            }
                        }
                    }
                }
            }
        }
        if (!ok) console.log('此路不通')
        else {
            let nowNode = hole.getOreNodeByPos(endPos.x, endPos.y)
            while (!!pre.get(nowNode)) {
                road.push(pre.get(nowNode))
                nowNode = pre.get(nowNode)
            }
            for (let i = road.length - 1; i >= 0; i--) {
                let node = road[i]
                if (node.getType() == OreLandType.NONE) {

                }
                else if (node.getType() == OreLandType.GRAY) {
                    await this.operateLands(node, OreOperateType.ONE, level)
                }
                else if (node.getType() == OreLandType.BLUE) {
                    await this.operateLands(node, OreOperateType.ONE, level)
                    await this.operateLands(node, OreOperateType.ONE, level)
                }
                else if (node.getType() == OreLandType.BREAK) {
                    await this.operateLands(node, OreOperateType.ONE, level)
                }
                else if (node.getType() == OreLandType.PURPLE || node.getType() == OreLandType.RUNAWAY_PURPLE) {
                    await this.operateLands(node, OreOperateType.BATTLE, level)
                }
                else if (node.getType() == OreLandType.BOOM) {
                    await this.operateLands(node, OreOperateType.BOOM, level)
                }
                else if (node.getType() == OreLandType.DRILL) {
                    await this.operateLands(node, OreOperateType.DRILL, level)
                }
            }
            eventCenter.emit(EventType.ORE_REFRESH_VIEW, true)
        }
    }

    public async unlock() {
        let msg = new proto.C2S_UnlockOreMessage()
        let res = await gameHelper.net.request(Msg.C2S_UnlockOreMessage, msg, true)
        const { code } = proto.S2C_UnlockOreMessage.decode(res)
        if (code == 0) {
            this.isUnlock = true
            this.syncBreak()
            gameHelper.new.pushNew(MarkNewType.NPC_DIALOG, [NPC_ID.MAKE_EQUIP_SHOP])
            eventCenter.emit(EventType.UNLOCK_FUNTION, UIFunctionType.PLAY_ORE)
            return true
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    public getPuzzleKey(index) {
        return this.puzzleKeySet.has(index)
    }

    public addPuzzleKey(index) {
        this.puzzleKeySet.add(index)
    }

    public removePuzzleKey(index) {
        this.puzzleKeySet.delete(index)
    }

    public canUnlcok() {
        return this.puzzleKeySet.size == 4
    }

    // 获取当前可以打造的最高品质
    public getMaxCanMakeQuality() {
        let datas = assetsMgr.getJson<EquipLevelCfg>("EquipLevel").datas
        let max = gameHelper.equip.showMaxQuality
        datas = datas.filter(d => d.quality > max) //效率优化
        for (let i = 0; i < datas.length; i++) {
            let data = datas[i]
            let lv = data.id
            let makeData = assetsMgr.getJson<EquipMakeCfg>('EquipMake').datas.find(d => d.level == lv)
            let ore = makeData.cost.filter(c => c.type == ConditionType.ORE_ITEM).last()
            if (gameHelper.getNumByCondition(ore) > 0) {
                max = Math.max(max, lv)
            }
        }
        return max
    }
}
