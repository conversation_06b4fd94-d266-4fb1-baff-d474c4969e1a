import { DATE_LANG, DAWN_TRANS_TIME, DUSK_TRANS_TIME, MORN_TIME, NIGHT_TIME, MONTH_DAYS } from "../../common/constant/Constant";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { unlockHelper } from "../../common/helper/UnlockHelper";
import EnergyModel from "./EnergyModel";
import { gameHelper } from "../../common/helper/GameHelper";
import { Msg } from "../../../proto/msg-define";
import { ConditionType, SpeedUpType } from "../../common/constant/Enums";
import { viewHelper } from "../../common/helper/ViewHelper";
import { util } from "../../../core/utils/Utils";
import EventCenter from "../../../core/utils/EventCenter";
import ConditionObj from "../common/ConditionObj";
import { uiHelper } from "../../common/helper/UIHelper";

@mc.addmodel('world', 10000)
export default class WorldModel extends mc.BaseModel {
    public time: number = 0
    private timeTransition: number = 1

    public energy: EnergyModel = null
    private preTime: number = 0

    private _data: proto.IPlayer = null

    private speedUpCfg = null

    private nextDayTime = 0 //每日刷新时间

    private nextWeekTime = 0//每周刷新时间

    private isDay: boolean = false//当前是否为白天
    private _tagNewData: boolean = false

    // 奖励产出时长
    private outputRewardTime: number = 0

    public init() {
        this.timeTransition = cfgHelper.getMiscData("timeTransition")
        this.speedUpCfg = cfgHelper.getMiscData("speedUp")

        let data = this.data || {}
        this.energy = new EnergyModel().init(data.energy)
        this.time = this.toRealSecond(data.time)
        this.updateNextDayTime(this.data.nextDaySurpluTime)
        this.updateNextWeekTime(this.data.nextWeekSurplusTime)
        this.preTime = this.time
        this.outputRewardTime = data.offlineRewardTime
    }

    get data() { return this._data }
    set data(v: proto.IPlayer) { this._data = v; this._tagNewData = true }

    update(dt) {
        if (this.getNextDaySurpluTime() <= 0) {
            this.syncDailyInfo()
        }

        this.energy && this.energy.update(dt)

        if (unlockHelper.useRealTime()) {
            let isNightPre = this.isNight()
            const preDay = this.getDay(this.getTime())
            this.time += this.transDT(dt, SpeedUpType.S3)
            let isNight = this.isNight()
            if (isNightPre != isNight) {
                eventCenter.emit(EventType.DAY_NIGHT_EXCHANGE)
            }
            const day = this.getDay(this.getTime())
            if (day != preDay) {
                eventCenter.emit(EventType.GAME_DAY_CHANGE)
            }
        }

        if (this.isSpeedUp() && this.energy.getEnergy() <= 0) {
            this.syncSpeedUp()
        }
        if (!this.isSpeedUp()) {
            this.addOfflineRewardTime(dt * ut.Time.Second)
        } else {
            this.addOfflineRewardTime((gameHelper.world.transDT(dt, SpeedUpType.S3) - dt) * ut.Time.Second)
        }
    }

    lateUpdate() {
        if (!mc.isPressed()) {
            this.stopSpeedUp()
        }
    }

    public transDT(dt: number, type: SpeedUpType) {
        if (this.isSpeedUp()) {
            return dt * this.speedUpCfg[type]
        }
        else {
            return dt
        }
    }

    public getDT(_?) {
        let dt = this.time - this.preTime
        return dt
    }

    public getTime() {
        return this.changeTime(this.time)
    }

    //游戏时间毫秒
    public setTime(time: number) {
        this.time = this.toRealSecond(time)
    }

    // 仅配合假时间用 不保存
    public setGuideTime(time: number) {
        this.time = time
        this.preTime = time
    }

    public changeTime(second: number) {
        return Math.floor(second * ut.Time.Second * this.timeTransition)
    }

    public getDayTime() {
        return this.getTime() % ut.Time.Day
    }

    public addTime(time: number) {
        this.time += this.toRealSecond(time)
    }

    public getHour() {
        return Math.floor(this.getDayTime() / ut.Time.Hour)
    }

    public getPassDay() {
        return Math.floor((this.getTime()) / ut.Time.Day)
    }

    public getNextHourSurplusWorldTime() {
        return ut.Time.Hour - (gameHelper.world.getTime() % ut.Time.Hour)
    }

    public isNight() {
        let time = this.getDayTime()
        return time >= NIGHT_TIME || time < MORN_TIME
    }

    public getMornTime() {
        return MORN_TIME
    }

    public getNightTime() {
        return NIGHT_TIME
    }

    public toRealSecond(time) {
        return time / ut.Time.Second / this.timeTransition
    }

    public toRealTime(time: number) {
        return time / this.timeTransition
    }

    public toWorldTime(time: number) {
        return time * this.timeTransition
    }

    public getMonth(time?: number): number {
        time = time || this.getTime()
        let remainingDays = Math.floor(time / ut.Time.Day)
        let month = 1

        for (let i = 0; i < MONTH_DAYS.length; i++) {
            if (remainingDays < MONTH_DAYS[i]) {
                break
            }
            remainingDays -= MONTH_DAYS[i]
            month++
        }

        return month
    }

    public getDay(time?: number): number {
        time = time || this.getTime()
        let remainingDays = Math.floor(time / ut.Time.Day)

        for (let i = 0; i < MONTH_DAYS.length; i++) {
            if (remainingDays < MONTH_DAYS[i]) {
                return remainingDays + 1
            }
            remainingDays -= MONTH_DAYS[i]
        }

        return 1
    }

    public getDayRatio() {
        let time = this.getDayTime()
        let mornTime = this.getMornTime()
        let nightTime = this.getNightTime()
        let ratio = 1
        if (this.isNight()) {
            if (nightTime <= time && time <= nightTime + DUSK_TRANS_TIME) { //白天转黑夜
                ratio = 1 - cc.misc.clamp01((time - nightTime) / DUSK_TRANS_TIME)
            }
            else if (mornTime - DAWN_TRANS_TIME <= time && time <= mornTime) {
                ratio = 1 - cc.misc.clamp01((mornTime - time) / DAWN_TRANS_TIME)
            }
            else {
                ratio = 0
            }
        }
        return ratio
    }

    public async speedUp() {
        this.energy.reqSpeedUp++
        let msg = new proto.C2S_SpeedUpMessage()
        const res = await gameHelper.net.request(Msg.C2S_SpeedUpMessage, msg, true)
        const { code, energy, time } = proto.S2C_SpeedUpMessage.decode(res)
        if (code == 0) {
            this.energy.isSpeedUp = true
            this.energy.setEnergy(energy)
            this.setTime(Number(time))
            eventCenter.emit(EventType.TIME_ACCELERATE_START)
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    public async stopSpeedUp() {
        if (this.energy.reqSpeedUp <= 0) return
        this.energy.reqSpeedUp--
        let msg = new proto.C2S_StopSpeedUpMessage()
        const res = await gameHelper.net.request(Msg.C2S_StopSpeedUpMessage, msg, true)
        const { code, energy, time } = proto.S2C_StopSpeedUpMessage.decode(res)
        if (code == 0) {
            this.energy.isSpeedUp = false
            this.energy.setEnergy(energy)
            this.setTime(Number(time))
            eventCenter.emit(EventType.TIME_ACCELERATE_END)
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    @ut.addLock
    public async syncSpeedUp() {
        let msg = new proto.C2S_SyncSpeedUpMessage()
        let res = await gameHelper.net.request(Msg.C2S_SyncSpeedUpMessage, msg)
        const { code, energy, time } = proto.S2C_SyncSpeedUpMessage.decode(res)
        if (code == 0) {
            this.energy.setEnergy(energy)
            this.setTime(Number(time))
            if (energy <= 0) {
                this.energy.isSpeedUp = false
                eventCenter.emit(EventType.TIME_ACCELERATE_END)
            }
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    public isSpeedUp() {
        return this.energy.isSpeedUp
    }

    formatWorldTime(time?) {
        let world = gameHelper.world
        time = time || world.getTime()
        let dayTime = time % ut.Time.Day
        let str = ""
        str += `${this.getMonth(time)}${assetsMgr.lang(DATE_LANG.m)}${this.getDay(time)}${assetsMgr.lang(DATE_LANG.d)}`
        str += ut.millisecondFormat(dayTime, "hh:mm")
        return `[${str}]`
    }

    public getNextDaySurpluTime() {
        return Math.max(0, this.nextDayTime - gameHelper.now())
    }

    public getNextWeekSurplusTime() {
        return Math.max(0, this.nextWeekTime - gameHelper.now())
    }

    public updateInfo(info) {
        this.setTime(info.time)
        this.energy.init(info.energy)
        this.updateNextDayTime(info.nextDaySurpluTime)
        this.updateNextWeekTime(this.data.nextWeekSurplusTime)
    }

    private updateNextDayTime(nextDaySurpluTime) {
        const beforeTime = this.nextDayTime

        this.nextDayTime = nextDaySurpluTime + gameHelper.now()
        if (this._tagNewData && beforeTime > 0) {
            if (beforeTime - gameHelper.now() <= 0) {
                this._tagNewData = false
                eventCenter.emit(EventType.DAILY_REFRESH)
            }
        }
    }

    private updateNextWeekTime(nextWeedSurplusTime) {
        this.nextWeekTime = nextWeedSurplusTime + gameHelper.now()
    }

    @util.addLock
    public async syncDailyInfo() {
        let msg = new proto.C2S_SyncDailyInfoMessage()
        let res = await gameHelper.net.request(Msg.C2S_SyncDailyInfoMessage, msg, true)
        let r = proto.S2C_SyncDailyInfoRespMessage.decode(res)
        const { code, jackpotDailyNum, energy, blackHole, instance, nextDaySurpluTime, store, wanted, arrestData, dailyTask,
            transport, spaceStone
        } = r
        if (code == 0) {
            gameHelper.jackpot.updateInfo(jackpotDailyNum)
            gameHelper.world.energy.updateInfo(energy)
            gameHelper.blackHole.updateInfo(blackHole)
            gameHelper.store.updateInfo(store)
            // gameHelper.wanted.updateInfo(wanted)
            // gameHelper.arrest.init(arrestData)
            gameHelper.dailyTask.updateInfo(dailyTask)
            gameHelper.transport.updateInfo(transport)
            gameHelper.spaceStone.updateInfo(spaceStone)
            this.updateNextDayTime(nextDaySurpluTime)
            this.outputRewardTime = r.offlineRewardTime
            eventCenter.emit(EventType.DAILY_REFRESH)
        }
        else if (code == 1) {
            this.updateNextDayTime(nextDaySurpluTime)
        }
        else {
            viewHelper.showNetError("syncDailyInfo " + code)
            await ut.wait(100)
        }
    }

    public getMaxOfflineConfig(next: boolean = false) {
        let cfg = null
        const ary = assetsMgr.getJson<any>("OutputDuration").datas
        for (const obj of ary) {
            switch (obj.type) {
                case "NONE":
                    cfg = obj
                    continue
                case "PASS_PLANET_NODE":
                    if (obj.param && obj.param.length) {
                        const nodeId = obj.param[0]
                        if (gameHelper.planet.isPassNode(nodeId)) {
                            cfg = obj
                        }
                    }
                    continue
            }
        }
        if (cfg && next) {
            cfg = ary.find(obj => obj.id == cfg.id + 1)
        }
        return cfg
    }

    // 获取最大产出时长
    public getMaxOfflineTime() {
        let duration = 0
        const setDur = (dur: number) => {
            duration = Math.max(duration, dur)
        }
        const cfg = this.getMaxOfflineConfig()
        if (cfg) {
            setDur(cfg.duration)
        }
        return duration * ut.Time.Minute
    }

    // 获取车厢已产出时长
    public getOutputTime(): number { return this.outputRewardTime }
    // 清空车厢已产出时长
    public clearOutputTime() { this.outputRewardTime = 0 }
    // 车厢已产出时长是否已满
    get isOutputTimeFull() { return this.outputRewardTime >= this.getMaxOfflineTime() }
    // 增加车厢已产出时长
    public addOfflineRewardTime(addVal: number): number {
        if (!unlockHelper.canOutput()) return
        if (!this.isOutputTimeFull) {
            this.outputRewardTime += addVal
        }
        return this.outputRewardTime
    }

}
