import { Msg } from "../../../proto/msg-define";
import { Condition, WantedCfg } from "../../common/constant/DataType";
import { ConditionType, UIFunctionType, WantedConditionType, WantedState } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../common/ConditionObj";
import PassengerModel from "../passenger/PassengerModel";

const { ccclass, property } = cc._decorator;

const bg = {
    0: {
        bg: [
            "lcrw_chexiang1_1",
            "lcrw_chexiang1_2",
            "lcrw_chexiang1_3",
            "lcrw_chexiang2_1",
            "lcrw_chexiang2_2",
            "lcrw_chexiang2_3",
            "lcrw_chexiang3_1",
            "lcrw_chexiang3_2",
            "lcrw_chexiang3_3",
            "lcrw_chexiang4_1",
            "lcrw_chexiang5_1",
            "lcrw_chexiang6_1",
            "lcrw_chexiang7_1",
            "lcrw_chexiang8_1",
            "lcrw_chexiang9_1",
            "lcrw_chexiang10_1",
        ],
        arg: [
            "lcrw_dasao",
            "lcrw_miehuo",
            "lcrw_yunshi",
        ]
    },
    1: {
        bg: ["lcrw_zhandou_bg_1001", "lcrw_zhandou_bg_1005", "lcrw_zhandou_bg_1006", "lcrw_zhandou_bg_1007"],
        arg: ["lcrw_zhandou"]
    }
}



export class WantedCondition {
    type: WantedConditionType = null
    value: number = 0
    num: number = 0

    constructor(data: proto.IWantedCondition) {
        this.type = data.type
        this.value = data.value
        this.num = 1
    }

    public isSatisfy(roles: PassengerModel[]) {
        return roles.some((role) => {
            switch (this.type) {
                case WantedConditionType.STAR: return role.getStarLv() >= this.value
                case WantedConditionType.QUALITY: return role.quality >= this.value
                case WantedConditionType.ANIMAL_TYPE: return role.animalType == this.value
                case WantedConditionType.BATTLE_TYPE: return role.battleType == this.value
            }
        })
    }
}

export class Wanted {
    private name: string = null
    private lv: number = 1
    private people: number = 0
    private endTime: number = 0
    private conditions: WantedCondition[] = []
    private _roles: number[] = []
    private rewards: ConditionObj[] = []
    private state: WantedState = WantedState.DEFAULT
    private cfgLevel: WantedCfg = null
    private _nameIndex: number = -1
    public index: number = -1
    public publisher: number = -1
    public type: number = 0
    private _bg: number = 0
    private _bgArg: number = 0

    get nameIndex() { return this._nameIndex }

    public init(data: proto.IWantedInfo) {
        this.name = `dailyTask_N_name_${data.name}`
        this._nameIndex = data.name - 1
        this.lv = data.level
        this.people = data.people
        this.updateSurplusTime(data.surplusTime)
        this.conditions = data.conditions.map(c => new WantedCondition(c))
        this.state = data.state
        this._roles = data.roles
        this._roles.length = this.people
        this.publisher = data.publisher || 1005
        this.rewards = gameHelper.toConditions(data.rewards)
        this.type = data.type
        this._bg = data.bg
        this._bgArg = data.bgArg

        this.initCfg(this.lv)
        this.initConditions()
        return this
    }

    get bg() { return bg[this.type].bg[this._bg] }
    get bgArg() { return bg[this.type].arg[this._bgArg] }

    private initCfg(lv: number) {
        this.cfgLevel = assetsMgr.getJsonData<WantedCfg>("WantedLevel", lv)
    }

    private initConditions() {
        this.conditions.sort((a, b) => {
            if (a.type != b.type) {
                return a.type - b.type
            }
            if (a.value != b.value) {
                if (a.type == WantedConditionType.QUALITY) {
                    return b.value - a.value
                }
                else {
                    return a.value - b.value
                }
            }
        })
        // 同类型要求合并
        const ary: WantedCondition[] = []
        for (const sig of this.conditions) {
            const exists = ary.find(c => c.type == sig.type && c.value == sig.value)
            if (!exists) {
                ary.push(sig)
                continue
            }
            exists.num += sig.num
        }
        this.conditions = ary
        // if (this.star) {
        //     this.conditions.unshift(new WantedCondition({ type: WantedConditionType.STAR, value: this.star }))
        // }
    }

    public start(roles: number[]) {
        this.setState(WantedState.START)
        this.setRoles(roles)
        this.updateSurplusTime(this.costTime)
    }

    public complete() {
        this.setState(WantedState.COMPLETE)
        this.setRoles([])
    }

    public needSync() {
        return this.state == WantedState.START && this.getSurplusTime() <= 0
    }

    public getSurplusTime() {
        return Math.max(0, this.endTime - gameHelper.now())
    }

    private updateSurplusTime(surplusTime: number) {
        this.endTime = surplusTime + gameHelper.now()
    }

    public calculateConditions(roles: number[]) {
        const roleAry = roles.map(id => gameHelper.passenger.getPassenger(id))
        let result = {}
        // 品质要求 必须是唯一的 一个乘客只能匹配一次 优先匹配最高
        const qualityUsed = []
        const qualityCond = this.conditions.filter(con => con.type == WantedConditionType.QUALITY)
            .sort((a, b) => b.value - a.value)
        if (qualityCond.length) {
            result[WantedConditionType.QUALITY] = {}
        }
        for (const condition of qualityCond) {
            if (!result[WantedConditionType.QUALITY][condition.value]) {
                result[WantedConditionType.QUALITY][condition.value] = 0
            }
            for (const role of roleAry) {
                if (!role) continue
                if (qualityUsed.includes(role.id)) continue
                if (condition.isSatisfy([role])) {
                    const cur = result[WantedConditionType.QUALITY][condition.value]
                    if (cur >= condition.num) {
                        break
                    }
                    result[WantedConditionType.QUALITY][condition.value]++
                    qualityUsed.push(role.id)
                }
            }
        }

        for (const condition of this.conditions) {
            if (condition.type == WantedConditionType.QUALITY) continue
            let line = result[condition.type]
            if (line == undefined) {
                line = {}
            }
            let v = line[condition.value]
            if (v == undefined) {
                v = 0
            }
            v += roleAry.filter(role => role && condition.isSatisfy([role])).length
            line[condition.value] = Math.min(v, condition.num)
            result[condition.type] = line
        }
        return result
    }

    public getName() { return this.name }
    public getLv() { return this.lv }
    public getPeople() { return this.people }
    public getEndTime() { return this.endTime }
    public getConditions() { return this.conditions }
    public getRoles() { return this._roles }
    public setRoles(roles: number[]) { this._roles = roles }
    public getRewards() { return this.rewards }
    public getState() { return this.state }
    public setState(state: WantedState) { this.state = state }
    public canReward() { return this.state == WantedState.END }
    get cfg() { return this.cfgLevel }
    get roles() { return this._roles }

    public get costSecond() { return this.cfgLevel.costTime * ut.Time.Minute / ut.Time.Second }
    public get costTime() { return this.cfgLevel.costTime * ut.Time.Minute }
    // public get star() { return this.cfgLevel.starCondition }
    public get star() { return null }

    public getConditionMatchNum(role: PassengerModel) {
        return this.conditions.filter(cond => {
            switch (cond.type) {
                case WantedConditionType.QUALITY:
                    return role.quality >= cond.value
                case WantedConditionType.ANIMAL_TYPE:
                    return role.animalType == cond.value
                case WantedConditionType.BATTLE_TYPE:
                    return role.battleType == cond.value
            }
        }).length
    }
}

@mc.addmodel('wanted')
export default class WantedModel extends mc.BaseModel {

    private wantedList: Wanted[] = []

    public data: proto.IWanted = null

    public refreshCost: ConditionObj = null

    public pnlOpenRed: boolean = false // 本次登录有没有打开过任务列表

    public init() {
        if (!this.data) return
        this.updateInfo(this.data)
        this.refreshCost = new ConditionObj().init(ConditionType.DIAMOND, -1, cfgHelper.getMiscData("wanted").refreshCost)
        this.initListener()
    }

    private initListener() {
        eventCenter.on(EventType.UNLOCK_FUNTION, (type) => {
            if (type == UIFunctionType.PLAY_ENTRUST) {
                this.sync()
            }
        })
    }

    public updateInfo(data: proto.IWanted) {
        data.list.forEach((it, i) => {
            this.setWanted(i, it)
        })
    }

    update() {
        for (let i = 0; i < this.wantedList.length; i++) {
            let wanted = this.wantedList[i]
            if (wanted.needSync()) {
                this.syncWanted(i)
            }
        }
    }

    @ut.queue
    public async syncWanted(index: number) {
        //发消息给后端
        let msg = new proto.C2S_SyncWantedMessage({ index })
        const _res = await gameHelper.net.request(Msg.C2S_SyncWantedMessage, msg)
        const res = proto.S2C_SyncWantedMessage.decode(_res)
        const { code, data } = res
        //前端乘客更改
        if (code == 0) {
            this.setWanted(index, data)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public async refrehWanted(index: number) {
        //发消息给后端
        let msg = new proto.C2S_RefrehWantedMessage({ index })
        const _res = await gameHelper.net.request(Msg.C2S_RefrehWantedMessage, msg, true)
        const res = proto.S2C_RefrehWantedMessage.decode(_res)
        const { code, data } = res
        //前端乘客更改
        if (code == 0) {
            this.setWanted(index, data)
            gameHelper.deductCondition(this.refreshCost)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public async startWanted(index: number, roles: number[]) {
        //发消息给后端
        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_StartWantedMessage, { index, roles })
        //前端乘客更改
        if (code == 0) {
            let wanted = this.getWanted(index)
            wanted.start(roles)
            eventCenter.emit(EventType.WANTED_UPDATE, wanted)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public async claimWantedReward(index: number) {
        //发消息给后端
        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_ClaimWantedRewardMessage, { index })
        //前端乘客更改
        if (code == 0) {
            let wanted = this.getWanted(index)
            wanted.complete()
            await gameHelper.grantRewardAndShowUI(wanted.getRewards())
            eventCenter.emit(EventType.WANTED_UPDATE, wanted)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public getWanted(index: number) {
        return this.wantedList[index]
    }

    public getWanteds() {
        return this.wantedList
    }

    private setWanted(index: number, data: proto.IWantedInfo) {
        let wanted = new Wanted().init(data)
        wanted.index = index
        this.wantedList[index] = wanted
        eventCenter.emit(EventType.WANTED_UPDATE, wanted)
    }

    public isInit() {
        return this.wantedList.length > 0
    }

    public checkSync() {
        if (!this.isInit()) {
            return this.sync()
        }
        return true
    }

    public async sync() {
        let msg = new proto.C2S_SyncAllWantedMessage()
        const res = await gameHelper.net.request(Msg.C2S_SyncAllWantedMessage, msg)
        const { code, data } = proto.S2C_SyncAllWantedMessage.decode(res)
        if (code == 0) {
            this.updateInfo(data)
            return true
        }
        return false
    }

    public oneKeyPlan(wanted: Wanted): number[] {
        // 拥有的所有乘客 
        const roles = gameHelper.passenger.getPassengers()
            // 排除已经安排在其他未完成的工作中的乘客
            .filter(r => !this.getWanteds().find(w => w.getState() == WantedState.START && w.getRoles().find(id => id == r.id)))

        // 获取任务的招工要求和最大安排乘客数量
        const conditions = wanted.getConditions()
        const maxPeople = wanted.getPeople()

        // 如果没有乘客可用，直接返回null
        if (roles.length === 0) {
            return null
        }
        // 如果没有招工要求，直接返回空数组
        if (conditions.length === 0) {
            return []
        }

        // 将条件分为品质条件和其他条件
        const qualityConditions = conditions.filter(c => c.type === WantedConditionType.QUALITY)
        const otherConditions = conditions.filter(c => c.type !== WantedConditionType.QUALITY)

        // 预处理：计算每个乘客满足哪些条件
        const satisfyMap = new Map<number, Set<string>>()
        // 记录每个乘客的品质
        const roleQualityMap = new Map<number, number>()

        for (const role of roles) {
            const satisfySet = new Set<string>()
            for (const condition of otherConditions) {
                if (condition.isSatisfy([role])) {
                    const key = `${condition.type}_${condition.value}`
                    satisfySet.add(key)
                }
            }
            satisfyMap.set(role.id, satisfySet)
            // 记录乘客品质
            roleQualityMap.set(role.id, role.quality)
        }

        // 将非品质条件转换为唯一标识的集合
        const requiredConditions = new Map<string, number>()
        for (const condition of otherConditions) {
            const key = `${condition.type}_${condition.value}`
            requiredConditions.set(key, condition.num)
        }

        // 处理品质条件
        const qualityMatches = new Map<number, number[]>() // 品质要求 -> 满足的乘客ID列表
        const usedRoles = new Set<number>() // 已经用于品质匹配的乘客

        // 对每个品质条件
        for (const qCondition of qualityConditions) {
            const requiredQuality = Number(qCondition.value)
            const matchedRoles: number[] = []

            // 首先尝试找到完全匹配的品质
            const exactMatches = roles.filter(r =>
                !usedRoles.has(r.id) &&
                r.quality === requiredQuality
            )

            // 然后尝试找到更高品质的
            const higherMatches = roles.filter(r =>
                !usedRoles.has(r.id) &&
                r.quality > requiredQuality
            ).sort((a, b) => a.quality - b.quality) // 优先使用较低的高品质

            // 先使用完全匹配的
            for (const role of exactMatches) {
                if (matchedRoles.length < qCondition.num) {
                    matchedRoles.push(role.id)
                    usedRoles.add(role.id)
                }
            }

            // 如果还需要更多，使用高品质的
            for (const role of higherMatches) {
                if (matchedRoles.length < qCondition.num) {
                    matchedRoles.push(role.id)
                    usedRoles.add(role.id)
                }
            }

            // 如果这个品质条件没满足，直接返回null
            if (matchedRoles.length < qCondition.num) {
                return null
            }

            qualityMatches.set(requiredQuality, matchedRoles)
        }

        // 对剩余的非品质条件使用贪心算法
        const availableRoles = roles.filter(r => !usedRoles.has(r.id))
        const sortedRoles = [...availableRoles].sort((a, b) => {
            const aSatisfy = satisfyMap.get(a.id)
            const bSatisfy = satisfyMap.get(b.id)
            return bSatisfy.size - aSatisfy.size
        })

        const greedySolution: number[] = []
        const remainingConditions = new Map(requiredConditions)

        for (const role of sortedRoles) {
            if (remainingConditions.size === 0 ||
                (greedySolution.length + Array.from(usedRoles).length) >= maxPeople) {
                break
            }

            const satisfySet = satisfyMap.get(role.id)
            let useful = false

            for (const [key, count] of remainingConditions.entries()) {
                if (satisfySet.has(key)) {
                    useful = true
                    const newCount = count - 1
                    if (newCount <= 0) {
                        remainingConditions.delete(key)
                    } else {
                        remainingConditions.set(key, newCount)
                    }
                }
            }

            if (useful) {
                greedySolution.push(role.id)
            }
        }

        // 如果还有未满足的条件，返回null
        if (remainingConditions.size > 0) {
            return null
        }

        // 合并品质匹配和贪心算法的结果
        const finalSolution = [
            ...Array.from(usedRoles),
            ...greedySolution
        ]
        // 如果总人数不够，返回null
        if (finalSolution.length < maxPeople) {
            return null
        }
        return finalSolution
    }
}
