import { Msg } from "../../../proto/msg-define";
import { ChestCfg, ItemCfg } from "../../common/constant/DataType";
import { ConditionType, ItemID, ItemType, MarkNewType } from "../../common/constant/Enums";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import ConditionObj from "../common/ConditionObj";

// 一个背包道具
export default class PropObj {

    public uid: string = ''
    public id: number = 0
    public json: ItemCfg = null
    public _isNew: boolean = false
    private _count: number = 0 //数量
    private endTime: number = 0

    public init(id: number, count: number = 1) {
        this.id = id
        this.count = count
        this.initJson()
        this.initEndTime()
        return this
    }

    public fromDB(data: proto.IItemInfo) {
        this.uid = String(data.uid)
        this.id = Number(data.id)
        this.count = data.num ?? 1
        this.initJson()
        this.updateEndTime(data.surplusTime || 0)
        return this
    }

    public toCondition() {
        return new ConditionObj().init2(
            { type: ConditionType.PROP, id: this.id, num: this.count, extra: { uid: this.uid } }
        )
    }

    private initEndTime() {
        if (this.type == ItemType.TIME_BOX) {
            let timeBox = cfgHelper.getMiscData("timeBox")
            let cfg = timeBox.find(data => +data.id == this.id)
            this.endTime = gameHelper.now() + cfg.time * ut.Time.Hour
        }
    }

    private updateEndTime(surplusTime: number) {
        if (surplusTime <= 0) {
            this.endTime = 0
            return
        }
        this.endTime = gameHelper.now() + surplusTime
    }

    public getSurplusTime() {
        return Math.max(0, this.endTime - gameHelper.now())
    }

    public isTimeMail() {
        return this.type == ItemType.TIME_MAIL
    }

    public canUse() {
        return !!this.isUse
    }

    private initJson() {
        this.json = assetsMgr.getJsonData<ItemCfg>('Item', this.id)
    }

    public hasTime() {
        return this.type == ItemType.TIME_BOX
    }

    public markNew() {
        if (!this.isShow) return
        if (this.isKey) {
            gameHelper.new.pushNew(MarkNewType.PROP, [this.id])
            if (this.isUse) {
                gameHelper.new.pushNew(MarkNewType.PROP_USE, [this.id])
            }
        }
        else if (this.isTimeMail()) {
            gameHelper.new.pushNew(MarkNewType.PROP_USE, [this.id])
        }
    }

    public removeNew(type: MarkNewType) {
        gameHelper.new.removeNew(type, [this.id])
    }

    public isNew(type: MarkNewType) {
        return gameHelper.new.isNew(type, [this.id])
    }

    public get count() { return this._count }
    public set count(val: number) { this._count = val }

    public get type() { return this.json?.type }
    public get level() { return this.json?.level }
    public get sortId() { return this.json?.sortId }
    public get name() { return this.json?.name }
    public get content() { return this.json?.content }
    public get icon() { return this.json?.icon }
    public get isShow() { return this.json?.isShow }
    public get isKey() { return this.json?.isKey }
    public get isUse() { return this.json?.isUse }
    public get message() { return this.json?.message }

    public langName() {
        if (this.type == ItemType.ROLE_TICKET) {
            let cfg = cfgHelper.getCharacter(this.id)
            return cfg ? assetsMgr.lang(this.name, cfg.name) : ''
        }
        return assetsMgr.lang(this.name)
    }
    public showName(it: cc.Node) {
        if (this.type == ItemType.ROLE_TICKET) {
            let cfg = cfgHelper.getCharacter(this.id)
            if (cfg) {
                it.setLocaleKey(this.name, assetsMgr.lang(cfg.name))
            }
        } else {
            it.setLocaleKey(this.name)
        }
    }
    public showContent(it: cc.Node) {
        if (this.type == ItemType.ROLE_TICKET) {
            let cfg = cfgHelper.getCharacter(this.id)
            if (cfg) {
                it.setLocaleKey(this.content, assetsMgr.lang(cfg.name))
            }
        } else {
            it.setLocaleKey(this.content)
        }
    }
    public isInviteCard() {
        return ItemID.InviteCard1 <= this.id && this.id <= ItemID.InviteCard3
    }

    update() {
        if (this.endTime > 0 && this.getSurplusTime() <= 0) {
            this.sync()
        }
    }

    @ut.addLock
    public async sync() {
        let { code, surplusTime } = await gameHelper.net.requestWithData(Msg.C2S_SyncMessage, new proto.C2S_SyncItemMessage({ uid: this.uid }))
        if (code == 0) {
            this.updateEndTime(surplusTime)
        }
    }
}