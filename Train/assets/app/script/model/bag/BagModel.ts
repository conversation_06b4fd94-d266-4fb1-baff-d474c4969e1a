import { Msg } from '../../../proto/msg-define';
import { ItemCfg } from '../../common/constant/DataType';
import { ConditionType, ItemID, ItemType } from '../../common/constant/Enums';
import EventType from '../../common/event/EventType';
import { cfgHelper } from '../../common/helper/CfgHelper';
import { gameHelper } from '../../common/helper/GameHelper';
import { viewHelper } from '../../common/helper/ViewHelper';
import { Arrest } from '../arrest/ArrestModel';
import ConditionObj from '../common/ConditionObj';
import PropObj from "./PropObj";

@mc.addmodel('bag')
export default class BagModel extends mc.BaseModel {

    private props: PropObj[] = [];

    public data: proto.IItemInfo[] = [] // 临时存放数据

    private timeStoneRecord: proto.ITimeStoneRecordData[] = []

    public init() {
        let data = this.data || []
        this.props = data.map((item) => new PropObj().fromDB(item))
    }

    public changePropByCond(cond: ConditionObj, isEmit: boolean = true) {
        let uid = cond?.extra?.uid
        let num = cond.num
        let id = Number(cond.id)
        let json = assetsMgr.getJsonData<ItemCfg>('Item', cond.id)
        if (json.isUnique) {
            let prop
            if (uid) {
                prop = this.getPropByUid(uid)
            }
            if (prop && num > 0) {
                twlog.error("背包道具重复添加", uid, cond.num)
            }
            else if (!prop && num < 0) {
                twlog.error("背包道具重复删除", uid, cond.num)
            }
            if (num > 0) {
                prop = new PropObj().init(id, 0)
                prop.uid = uid
                this.props.push(prop)
            }
            if (!prop) return
            return this.handleChange(prop, num, isEmit)
        }
        else {
            return this.changeProp(id, num)
        }
    }

    public changeProp(id: number, count: number, isEmit: boolean = true): number {
        let prop = this.getPropById(id)
        if (!prop) {
            if (count < 0) {
                twlog.error("背包道具重复删除", id, count)
                return 0
            }
            prop = new PropObj().init(id, 0)
            this.props.push(prop)
        }
        return this.handleChange(prop, count, isEmit)
    }

    private handleChange(prop: PropObj, count: number, isEmit: boolean = true) {
        if (prop.count == 0) {
            prop.markNew()
        }
        prop.count += count
        if (prop.count == 0) {
            this.delProp(prop)
        }
        if (isEmit) {
            eventCenter.emit(EventType.CHANGE_NUM_PROP, prop.id, count)
        }
        return prop.count;
    }

    public delProp(prop: PropObj) {
        this.props.remove(prop)
    }

    public getPropById(id: number) {
        return this.props.find(p => p.id == id)
    }

    public getPropByUid(uid: string) {
        return this.props.find(p => p.uid == uid)
    }

    public getPropCountById(id: number) {
        let prop = this.getPropById(id)
        if (!prop) return 0
        return prop.count
    }

    public getPropCountByUid(uid: string) {
        let prop = this.getPropByUid(uid)
        if (!prop) return 0
        return prop.count
    }

    public getProps() {
        return new Array().concat(this.props).concat(gameHelper.chest.getChests()).concat(gameHelper.arrest.getArrestListInBag())
    }

    public getPropsByType(type: ItemType) {
        return this.getProps().filter(i => i instanceof PropObj && i.type == type)
    }

    public getArrests() {
        return this.getProps().filter(i => i instanceof Arrest)
    }

    public getMaterials(): PropObj[] {
        return this.getProps().filter(i => i instanceof PropObj && i.type != ItemType.TIME_MAIL && i.type != ItemType.COLLECTION)
    }

    public getTimeMalls() {
        return this.getProps().filter(i => i instanceof PropObj && i.isShow && i.type == ItemType.TIME_MAIL)
    }

    public getCollections(): PropObj[] {
        return this.getProps().filter(i => i instanceof PropObj && i.isShow && i.type == ItemType.COLLECTION)
    }

    public getCollectionsNum() {
        let num = 0
        this.getCollections().forEach(t => num += t.count)
        return num
    }

    public async collectionThrow(id: number, num: number) {
        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_DropItemMessage, {
            item: {
                id,
                type: ConditionType.PROP,
                num
            }
        })
        if (code == 0) {
            gameHelper.deductCondition(new ConditionObj().init(ConditionType.PROP, id, num))
            return true
        }
        viewHelper.showNetError(code)
        return false
    }

    public async buyBattery(num: number) {
        let msg = new proto.C2S_BuyBatteryMessage({ num })
        const res = await gameHelper.net.request(Msg.C2S_BuyBatteryMessage, msg, true)
        const { code } = proto.S2C_BuyBatteryMessage.decode(res)
        if (code == 0) {
            gameHelper.grantReward(new ConditionObj().init(ConditionType.PROP, ItemID.ENERGY, num))
            gameHelper.deductCondition(new ConditionObj().init(ConditionType.DIAMOND, -1, num * cfgHelper.getMiscData("speedUp").recoverEnergy))
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public async useItem(prop: PropObj, num: number) {
        let cond = prop.toCondition()
        if (num) {
            cond.num = num
        }
        const {code, rewards} = await gameHelper.net.requestWithDataWait(Msg.C2S_UseItemMessage, { item: cond.toProto() })
        if (code == 0) {
            gameHelper.deductCondition(cond)
            gameHelper.grantRewardAndShowUI(gameHelper.toConditions(rewards))
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public async syncTimeStoneRecordData() {
        const r = await gameHelper.net.requestWithData(Msg.C2S_SyncTimeStoneRecordDataMessage);
        if (r.code != 0) return viewHelper.showNetError(r.code)
        this.timeStoneRecord = r.list?.records.reverse()
    }

    public async useTimeStoneRecordData() {
        await this.syncTimeStoneRecordData()
        if (!this.timeStoneRecord || this.timeStoneRecord.length <= 0) return viewHelper.showAlert('回溯列表为空')
        let id = this.timeStoneRecord.find(e => !e.use)?.id
        if (!id) return viewHelper.showAlert('回溯列表为空')
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_UseTimeStoneMessage, { id });
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        viewHelper.showPnl("timeStone/TimeStoneUse")
    }

    public async ticketMerge(num: number) {
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_TicketMergeMessage, { num });
        if (r.code != 0) return viewHelper.showNetError(r.code)
        let ratio = cfgHelper.getMiscData("ticket").mergeCnt
        gameHelper.deductCondition(new ConditionObj().init(ConditionType.PROP, ItemID.TICKET_FRAG, num * ratio))
        let item = new ConditionObj().init(ConditionType.PROP, ItemID.TICKET, num)
        gameHelper.grantReward(item)
        return item
    }

    public isTimeStoneKey(cond: ConditionObj) {
        if (cond.type != ConditionType.PROP) return false
        let cfg = assetsMgr.getJsonData<ItemCfg>("Item", cond.id)
        return cfg.type == ItemType.TIME_STONE_KEY
    }

    public isTimeStoneKeyFrag(cond: ConditionObj) {
        if (cond.type != ConditionType.PROP) return false
        let cfg = assetsMgr.getJsonData<ItemCfg>("Item", cond.id)
        return cfg.type == ItemType.TIME_STONE_KEY_FRAG
    }

    public isCollectItemFull(id: number, alert: boolean = true): boolean {
        const item = this.getPropById(id)
        if (item && item.count >= 99) {
            alert && viewHelper.showAlert("collect_tips_2", {
                params: [assetsMgr.lang(item.name)]
            })
            return true
        }
        return false
    }

    /**
     * 将乘客贴纸生成物品放到背包
     */
    public loadPassengerProfile() {
        const belongToCharacter = gameHelper.archives.getList()
        const index = this.props.findIndex(i => i.id == ItemID.PASSENGER_PROFILE_CONTAINER)
        if (index != -1) {
            this.props.splice(index, 1)
        }
        if (belongToCharacter?.length <= 0) {
            return
        }
        const item = new PropObj().init(ItemID.PASSENGER_PROFILE_CONTAINER, 0)
        item.count += belongToCharacter.length
        this.props.push(item)
    }

    /**
     * 将星球贴纸生成物品放到背包
     */
    public loadPlanetProfile() {
        const belongToPlanet = gameHelper.planetArchives.getList()
        const index = this.props.findIndex(i => i.id == ItemID.PLANET_PROFILE_CONTAINER)
        if (index != -1) {
            this.props.splice(index, 1)
        }
        if (belongToPlanet?.length <= 0) {
            return
        }
        const item = new PropObj().init(ItemID.PLANET_PROFILE_CONTAINER, belongToPlanet.length)
        this.props.push(item)
    }

    public update() {
        for (let prop of this.props) {
            prop.update()
        }
    }

}
