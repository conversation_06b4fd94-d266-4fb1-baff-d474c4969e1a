import { ConditionType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import ConditionObj from "../common/ConditionObj";

@mc.addmodel('currency')
export default class CurrencyModel extends mc.BaseModel {
    private stardust: number = 0;// 星尘

    private heart: number = 0;// 爱心

    private diamond: number = 0;// 钻石

    public data = null

    private delayId: number = 0
    private delayCurrencyMap = {}

    public init() {
        let data = this.data || {}
        this.stardust = data.starDust || 0
        this.heart = data.heart || 0
        this.diamond = data.diamond || 0

        // gameHelper.net.on.S2C_CurrencyChangeMessage, (byte: Uint8Array) => {
        //     const data = proto.S2C_CurrencyChangeMessage.decode(byte)
        //     const currency = data.currency
        //     twlog.info(`货币同步消息,类型:${currency.type}, 数量:${currency.val}`)
        //     // this.changeCurrency(currency.type, currency.val)
        // })
    }

    /**
     * 获取货币
     * @param type 货币类型
     * @returns
     */
    public getCurrency(type: ConditionType) {
        switch (type) {
            case ConditionType.STAR_DUST:
                return this.stardust
            case ConditionType.HEART:
                return this.heart
            case ConditionType.DIAMOND:
                return this.diamond
        }
        return 0
    }

    /**
     * 变更货币
     * @param type
     * @param val
     * @param isEmit
     */
    public changeCurrency(type: ConditionType, val: number, isEmit: boolean = true): boolean {
        let key, eventKey
        switch (type) {
            case ConditionType.STAR_DUST:
                key = 'stardust', eventKey = EventType.UPDATE_STARDUST; break;
            case ConditionType.HEART:
                key = 'heart', eventKey = EventType.UPDATE_HEART; break;
            case ConditionType.DIAMOND:
                key = 'diamond', eventKey = EventType.UPDATE_DIAMOND; break;
        }
        if (!key) {
            twlog.error("changeCurrency error type:", type)
            return
        }
        this[key] += val
        isEmit && this.emit(eventKey, val)
    }

    /**
     * 货币是否充足
     */
    public getEnough(type: ConditionType, target: number): boolean {
        return this.getCurrency(type) >= target
    }

    public getDelayCurrency(type: ConditionType) {
        let sum = 0
        for (let id in this.delayCurrencyMap) {
            let info = this.delayCurrencyMap[id]
            if (info.type == type) {
                sum += info.count
            }
        }
        return sum
    }

    public addDelayCurrency(type: ConditionType, count: number) {
        let id = ++this.delayId
        this.delayCurrencyMap[id] = { count, type }
        ut.wait(10).then(()=>{
            this.removeDelayCurrency(id)
        })
        return id
    }

    public addDelayRewards(rewards: ConditionObj[]) {
        let map = {}
        for (let reward of rewards) {
            let type = reward.type
            let id = this.addDelayCurrency(type, reward.num)
            map[type] = id
        }
        return map
    }

    public removeDelayCurrency(id: number) {
        delete this.delayCurrencyMap[id]
    }
}
