import Observer from "../../../core/utils/Observer"
import EventType from "../../common/event/EventType"
import { dbHel<PERSON> } from "../../common/helper/DatabaseHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { ConditionType, PlanetMineType, ToolProperty } from "../../common/constant/Enums"
import { Msg } from "../../../proto/msg-define"
import { viewHelper } from "../../common/helper/ViewHelper"
import Tool from "./Tool"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { ChapterPlanetMonsterCfg, ChapterPlanetSpCfg } from "../../common/constant/DataType"

/**工具数据*/
@mc.addmodel('tool', 100)
export default class ToolModel extends mc.BaseModel {

    public data: any = null

    private lv: number = 1 //打造台等级
    private tools: Tool[] = []
    private blessCount: number = 0
    private blessId: string = null

    public init() {
        let data = this.data
        this.fromDB(data)
        return this
    }
    private fromDB(data: proto.IToolModel) {
        this.lv = data.Lv
        let toolDatas = data.tools || {}
        for (let type in toolDatas) {
            this.tools.push(new Tool().init(toolDatas[type]))
        }
        this.blessCount = data.blessCount || 0
        this.blessId = data.blessId
    }

    public getToolByType(type: PlanetMineType) {
        return this.tools.find(t => t.getType() == type)
    }

    public getTools() {
        return this.tools
    }

    public getMainTools() {
        return this.getTools().filter(t => t.getType() != PlanetMineType.SEED)
    }

    public getLv() {
        return this.lv
    }

    public setLv(lv: number) {
        this.lv = lv
    }

    public async lvUp() {
        let msg = new proto.C2S_FurnaceUpgradeMessage()
        const res = await gameHelper.net.request(Msg.C2S_FurnaceUpgradeMessage, msg, false)
        const { code, failList } = proto.S2C_FurnaceUpgradeRespMessage.decode(res)
        if (code == 0) {
            let cfg = cfgHelper.getToolTableByLv(this.lv)
            //客户端消耗物品
            gameHelper.deductConditions(gameHelper.toConditions(cfg.buyCost))
            this.lv++
            eventCenter.emit(EventType.TOOL_TABLE_UP, true)
            viewHelper.showAlert('tool_tips_3')
            return true
        }
        else if (failList?.length > 0) {
            gameHelper.showFailTips(failList)
        }
        else {
            viewHelper.showNetError(code)
        }
        return false
    }


    public checkTableRedDot() {
        if (this.lv == cfgHelper.getMaxToolTableLv()) { return false }
        let cfg = cfgHelper.getToolTableByLv(this.lv)
        //if (cfg.proficiency > this.eLv) { return false }
        if (gameHelper.checkConditions(gameHelper.toConditions(cfg.buyCost))) {
            let res = false
            this.tools.forEach((tool) => {
                if (tool.getLv() == cfgHelper.getToolTableByLv(this.lv)?.maxToolLv) {
                    res = true
                }
            })
            return res
        }
        return false
    }

    public make(type: PlanetMineType) {
        let tool = this.getToolByType(type)
        if (!tool) {
            tool = new Tool().init({type: type})
            this.tools.push(tool)
        }
        tool.make()
        return tool
    }

    public setBlessId(id: string) {
        this.blessId = id
    }

    public changeBlessCount(count: number) {
        this.blessCount += count
    }

    public getBlessCount() {
        return this.blessCount
    }

    public getMaxBlessCount() {
        return assetsMgr.getJsonData<ChapterPlanetSpCfg>("ChapterPlanetSp", this.blessId)?.count || 0
    }

    public getBlessLv() {
        return assetsMgr.getJsonData<ChapterPlanetSpCfg>("ChapterPlanetSp", this.blessId)?.lv || 0
    }
    
    public isBless() {
        let planet = gameHelper.planet.getCurPlanet()
        let map = planet.getCurMap()
        if (map.getBranch()) {
            return false
        }
        return this.blessCount > 0
    }
}
