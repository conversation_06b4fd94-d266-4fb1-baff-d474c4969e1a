import { QteGamePlayCfg } from "../../common/constant/DataType";
import { HeroAction, PlanetMineType } from "../../common/constant/Enums";
import SimplePath from "../../common/curve/SimplePath";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import CurvePathMoveModel from "../map/CurvePathMoveModel";
import MoveModel from "../map/MoveModel";
import PassengerModel from "../passenger/PassengerModel";
import Tool from "../tool/Tool";
import CollectMineModel from "./CollectMineModel";

export default class RoleCollectModel {

    public moveAgent: CurvePathMoveModel = null

    public get planet() {
        return gameHelper.planet.getCurPlanet()
    }

    public target: CollectMineModel = null

    private action: HeroAction = HeroAction.IDLE

    private collectCombo: number = 0

    public get orgMoveSpeed() {
        return cfgHelper.getMiscData("character").moveSpeed
    }

    public orgMoveSpeedRate: number = 1.65

    private moveSpeed: number = 0

    public damageMul: number = 1 //qte倍率, 0为miss

    public roleId: number = 1005

    private flip: boolean = false

    public anim: string = null

    public hasJump: boolean = false

    public tool: Tool = null

    private collectDis: number = 100

    private waitReach: boolean = false

    public errorTarget: CollectMineModel = null

    public init() {
        this.initMoveAgent()
        this.initListener()
        return this
    }

    private initListener() {
        eventCenter.on(EventType.TOOL_CHANGE, () => {
            if (this.getAction() == HeroAction.COLLECT) {
                let mineType = this.getTargetModel()?.type
                this.changeToolByType(mineType)
            }
        }, this)
    }

    public setRole(role: PassengerModel) {
        this.roleId = role.getID()
    }

    public getRole() {
        return gameHelper.passenger.getPassenger(this.roleId)
    }

    public setJump() {
        if (this.hasJump) return
        this.hasJump = true
        eventCenter.emit(EventType.PLAENT_CONTROL_TIP_CHANGE)
    }

    private initMoveAgent() {
        this.moveAgent = new CurvePathMoveModel()
        this.setPosition(cc.v2())
        this.resetMoveSpeed()
    }

    public setTargetPos(targetPos?: cc.Vec2, startPos?: cc.Vec2) {
        if (!targetPos) {
            this.setPosition(this.getPosition())
            return
        }
        startPos = startPos || this.getPosition()
        let simplePath = new SimplePath().init(startPos, targetPos)
        this.moveAgent.init(simplePath)
        this.moveAgent.move()
        this.waitReach = true
    }

    public setTarget(target: CollectMineModel) {
        if (this.target == target) return
        this.target = target
        if (target) {
            let now = this.getPosition()
            let vec = target.getPosition().sub(now)
            let dis = vec.mag()
            let ratio = (this.collectDis - 1) / dis
            if (ratio < 1) {
                let x = vec.x * (1 - ratio)
                let y = vec.y * (1 - ratio)
                this.setTargetPos(cc.v2(now.x + x, now.y + y))
            }
            else {
                this.setTargetPos()
            }
        }
        eventCenter.emit(EventType.TARGET_PLANET_NODE, target)
    }

    public onClickTarget(target = this.target) {
        if (!target.canCollect()) {
            this.errorTarget = target
            return
        }
        this.errorTarget = null

        if (this.target == target) {
            if (this.action == HeroAction.COLLECT) {
                this.combo()
            }
            else {
                let now = this.getPosition()
                let vec = target.getPosition().sub(now)
                let dis = vec.mag()
                if (dis <= this.collectDis) {
                    this.collectStart(target)
                    this.combo()
                }
            }
        }
        else {
            this.setTarget(target)
        }
    }

    private onReach() {
        this.waitReach = false
        eventCenter.emit(EventType.REACH_PLANET_NODE, this.target)
        if (this.target) {
            this.collectStart(this.target)
            this.combo()
        }
    }

    update(dt) {
        this.moveAgent.updateMovePosition(dt)
        this.updateDir(this.moveAgent.getDir())
        if (this.isMoving()) {
            this.setAction(HeroAction.MOVE)
        }
        else {
            if (this.action == HeroAction.MOVE) {
                this.setAction(HeroAction.IDLE)
            }
        }

        if (this.isReachTarget() && this.waitReach) {
            this.onReach()
        }
    }

    public isReachTarget(target?) {
        if (this.moveAgent instanceof MoveModel) {
            if (!this.getPosition().equals(this.target.position) && this.isMoving()) {
                return false
            }
        }
        else {
            return this.moveAgent.isReach()
        }
        return true
    }

    public getPosition() {
        if (this.moveAgent) {
            return this.moveAgent.getPosition()
        }
        return cc.v2()
    }

    public setPosition(pos: cc.Vec2) {
        if (this.moveAgent) {
            let simplePath = new SimplePath().init(pos.clone(), pos.clone())
            this.moveAgent.init(simplePath)
        }
    }

    public isMoving() {
        return this.moveAgent && this.moveAgent.isMoving()
    }

    public isCollect() {
        return this.action == HeroAction.COLLECT
    }

    public combo() {
        let mine = this.getTargetModel()
        if (!this.isCollect() || !mine || mine.dead) return
        let orgCombo = this.collectCombo
        this.collectCombo += 1
        eventCenter.emit(EventType.COLLECT_COMBO, this.collectCombo, orgCombo)
    }

    public deCombo(count = 1) {
        this.collectCombo = Math.max(0, this.collectCombo - count)
    }

    public resetCombo() {
        this.collectCombo = 0
    }

    public getCombo() {
        return this.collectCombo
    }

    public isCollectAct() {
        let target = this.target
        if (!target) return false
        if (target.dead) return false
        return this.collectCombo > 0
    }

    public isFastCollect() {
        return this.collectCombo >= 3
    }

    public setAction(action: HeroAction) {
        if (this.action == action) return
        this.action = action
        eventCenter.emit(EventType.HERO_CHANGE_STATE, action)
    }

    public getAction() {
        return this.action
    }

    public hasAction(action: HeroAction) {
        return this.action == action
    }

    public isTarget(model) {
        return this.target == model
    }

    public resumMove() {
        if (this.moveAgent instanceof CurvePathMoveModel) {
            if (this.moveAgent.isPause()) {
                this.moveAgent.move()
            }
        }
    }

    public getTargetModel() {
        return this.target
    }

    public getCollectTarget() {
        return this.target
    }

    public getMoveSpeed() {
        return this.moveSpeed
    }

    public setMoveSpeed(speed: number) {
        this.moveSpeed = speed
        this.moveAgent.setSpeed(speed)
    }

    public resetMoveSpeed() {
        this.setMoveSpeed(this.orgMoveSpeed * this.orgMoveSpeedRate)
    }

    public changeToolByType(type: PlanetMineType) {
        this.changeTool(gameHelper.tool.getToolByType(type))
    }

    public changeTool(tool: Tool) {
        let old = this.tool
        this.tool = tool
        eventCenter.emit(EventType.CHANGE_TOOL, tool, old)
    }

    private collectStart(mine?: CollectMineModel) {
        this.collectCombo = 0
        mine = mine || this.target
        this.changeToolByType(mine.type)
        this.setAction(HeroAction.COLLECT)
        eventCenter.emit(EventType.ENTER_COLLECT, mine)
    }

    public getAttack() {
        return 1
    }

    public getAttackOrgSpeed() {
        return 1
    }

    public getAttackSpeed() {
        return this.getAttackOrgSpeed()
    }

    public getAmp() {
        return this.tool?.amp || 0
    }

    public getHit() {
        return this.tool?.hit || 0
    }

    public getBreak() {
        return this.tool?.break || 0
    }

    public getCrit() {
        return 5
    }

    public collect(mine?: CollectMineModel, atkRate: number = 1) {
        mine = mine || this.getTargetModel()
        let damageMul = this.damageMul
        let atk = this.getAttack()
        if (mine.type == PlanetMineType.TREE) {
            mine.setAmp(this.getAmp())
        }
        atk = this.calcAtk(atk, mine)
        damageMul = this.calcDamageMul(mine)
        atk *= atkRate
        if (mine.qteId) {
            let datas = assetsMgr.getJson<QteGamePlayCfg>("QteGamePlay").datas.filter(d => Number(d.id.split("-")[0]) == mine.qteId)
            let damgeCfg = cfgHelper.getMiscData("qteDamage") || {}
            let sum = datas.reduce((a, b) => a + damgeCfg[b.lumb.type] || 0, 0) || 1
            atk = Math.ceil(mine.maxHp / sum)
        }
        let damage = atk * damageMul
        mine.hit(damage, damageMul, atkRate > 1)
        // twlog.info("collect damge", damage)
        eventCenter.emit(EventType.HERO_COLLECT, mine)
        this.damageMul = 1

        if (!this.tool || this.tool.attack == 0) {
            eventCenter.emit(EventType.HERO_COLLECT_NOEFFECT)
        }
    }

    private calcAtk(atk: number, mine: CollectMineModel) {
        if (mine.type == PlanetMineType.ORE) {
            let _break = this.getBreak()
            let breakPro = Math.min(cc.misc.clamp01(mine.defense - _break) * 25 + Math.max(mine.defense - _break, 0) * 25, 100)
            atk = Math.floor(atk * (1 - breakPro / 100))
        }
        return atk
    }

    private calcDamageMul(mine: CollectMineModel) {
        let damageMul = 1
        if (mine.type == PlanetMineType.PART) {
            let hit = this.getHit()
            let missPro = Math.min(cc.misc.clamp01(mine.dodge - hit) * 25 + Math.max(mine.dodge - hit, 0) * 25, 100)
            if (ut.chance(missPro)) {
                damageMul = 0
            }
        }
        let critPro = this.getCrit()
        if (ut.chance(critPro)) {
            damageMul *= 2
        }
        return damageMul
    }


    public collectEnd() {
        let mineModel = this.getTargetModel()
        if (!mineModel) return//说明被reset了
        this.collectCombo = 0
        this.target = null
        this.setAction(HeroAction.COLLECT_END)
    }


    private updateDir(dir: cc.Vec2) {
        if (dir.x != 0) { //防止垂直移动的时候转向
            this.setFlip(dir.x < 0)
        }
    }

    public setFlip(flip) {
        this.flip = flip
    }

    public getFlip() {
        return this.flip
    }

    public reset() {
        this.resetCombo()
        this.target = null
        this.action = HeroAction.IDLE
        this.initMoveAgent()
    }

    //----------------- 控制流 ----------------------//

    //-------------------------------------------------
}