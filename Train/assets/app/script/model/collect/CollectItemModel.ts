/**
 * 采集物
 */
import { MAX_ZINDEX } from "../../common/constant/Constant"
import { gameHelper } from "../../common/helper/GameHelper"
import ConditionObj from "../common/ConditionObj"

export default class CollectItemModel {

    protected id: string = null
    public uid: string = null
    protected json: any = null
    public position: cc.Vec2 = null
    public angle: number = 0

    public points: cc.Vec2[] = [] //在地图中的站位点

    public dead: boolean = false

    public center: any = {}

    public rewards: ConditionObj[] = []

    public index: number = -1

    public eventName: string = null

    public scale: number = 1

    public size: cc.Size = null

    public orgPosition: cc.Vec2 = null
    public orgAngle: number = 0

    public endOffset: cc.Vec2 = null

    public init(nodeId, ...params) {
        this.id = nodeId
        return this
    }

    public setPosition(position: cc.Vec2) {
        this.position = position
    }

    public toDB() {
        return {}
    }

    public fromDB(data) {
    }

    protected initJson() {
    }

    public getId() { return this.id }

    get name() { return this.json?.name || "" }


    public getZIndex() {
        return MAX_ZINDEX - this.center.y
    }

    public getActPoints() {
        return this.points
    }

    public async die() {
        if (this.dead) return
        this.dead = true
        gameHelper.grantRewards(this.rewards)
    }

    public update(dt?) { }

    public onClick() {
    }

    public getPosition() {
        return this.position
    }

}