/**
 * 采集物
 */

import { ChapterPlanetMineCfg, Condition, PlanetMineCfg } from "../../common/constant/DataType"
import { ConditionType, ItemID, PlanetMineGameType, PlanetMineType } from "../../common/constant/Enums"
import { gameHelper } from "../../common/helper/GameHelper"
import EventType from "../../common/event/EventType"
import CollectItemModel from "./CollectItemModel"
import { Msg } from "../../../proto/msg-define";
import { viewHelper } from "../../common/helper/ViewHelper";
import { util } from "../../../core/utils/Utils"

export default class CollectMineModel extends CollectItemModel {

    public hp: number = 0
    public damageMul: number = 1
    public mineId: number = null
    public type: PlanetMineType = PlanetMineType.TREE

    public collecting: boolean = false

    public maxHp: number = 0

    protected lvJson: ChapterPlanetMineCfg = null

    public centerOffset: cc.Vec2 = cc.v2()

    public qteSucc: number = 0

    public restoreTime: number = 0
    protected amp: number = 0

    public init(id: string, mineId: number) {
        this.mineId = mineId
        this.initJson()
        let json = this.json
        if (!json) return

        let lvJson: any = {
            lv: 1,
            hp: 3,
        }

        this.lvJson = lvJson
        this.id = id
        this.maxHp = lvJson.hp
        this.hp = this.maxHp
        return this
    }

    protected initJson() {
        let json = assetsMgr.getJsonData<PlanetMineCfg>("CollectMine", this.mineId)
        this.type = json.type
        this.json = json
        if (json.centerOffset) {
            this.centerOffset.x = json.centerOffset.x || 0
            this.centerOffset.y = json.centerOffset.y || 0
        }
    }

    public toDB() {
        return {
            id: this.id,
            hp: this.hp,
        }
    }

    public fromDB(data: any) {
        this.hp = this.hp
        if (this.hp <= 0) { //不好处理死了但没通过的情况，这里设成1，继续走后面的流程
            this.hp = 1
        }
    }

    public get name() { return this.json?.name || "" }
    public get icon() { return this.json?.icon || "" }
    public get gameType() {
        return this.lvJson?.gameType || PlanetMineGameType.CLICK
    }
    public get qteId() { return 0 }
    public get dodge() { return this.lvJson?.dodge || 0 } //闪避
    public get restore() { return this.lvJson?.restore || 0 } //每秒恢复hp
    public get defense() { return this.lvJson?.defense || 0 } //防御
    public get tenacity() { return this.lvJson?.tenacity || 0 } //坚韧
    public get lv() { return this.lvJson?.lv || 0 }

    get centerPos() { return this.position.add(this.centerOffset) }
    get collectEffect() { return this.json?.collectEffect }
    public get isInterference() { return !this.json.reward?.length }

    public setAmp(amp) {
        this.amp = amp
    }

    public update(dt) {
        if (this.dead) return

        if (this.hp < this.maxHp) {
            this.restoreTime += dt
            let speed = this.getRestoreSpeed()
            while (this.restoreTime >= speed) {
                this.restoreTime -= speed
                let restore = this.getRestore()
                if (restore > 0) {
                    this.changeHp(restore)
                }
            }
        }
    }

    public getRestore() {
        return gameHelper.collect.getRole().getAttack()
    }

    //每次恢复间隔时间
    public getRestoreSpeed() {
        let count = Math.max(0, this.restore - this.amp)
        if (count <= 0) return 100
        return 1 / (count * 2)
    }


    public hit(damage, damageMul: number = 1, isAuto: boolean = false) {
        if (this.dead) return
        if (gameHelper.bag.isCollectItemFull(this.mineId)) return
        if (this.hp <= 0) return
        this.changeHp(-damage, damageMul, isAuto)
        if (this.hp <= 0) {
            this.die()
        }
    }

    private changeHp(hp, damageMul = 1, isAuto: boolean = false) {
        this.hp = cc.misc.clampf(this.hp + hp, 0, this.maxHp)
        eventCenter.emit(EventType.PLANET_MINE_HP_CHANGE, this, hp, damageMul, isAuto)
    }

    @util.addLock
    public async die() {
        if (this.dead) return
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_CollectMapMineDoneMessage, { mineUid: this.id });
        if (r && r.code != 0) {
            viewHelper.showNetError(r.code)
            return
        }
        this.dead = true
        eventCenter.emit(EventType.PLANET_GEN_REWARD, this.rewards, this.centerPos)
        gameHelper.grantRewards(this.rewards)
        gameHelper.collect.removeMine(this)
    }


    public canCollect() {
        return this.rewards?.length > 0
    }
}
