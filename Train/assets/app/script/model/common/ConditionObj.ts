import { Condition, ItemCfg } from "../../common/constant/DataType"
import { ConditionType, ItemType } from "../../common/constant/Enums"

// 通用条件信息
export default class ConditionObj {

    public id: number | string = -1
    private _num: number = 0
    private _randomNum: number[] = []
    public type: ConditionType = null
    public extra: any = null
    public isHide: boolean = false

    public init(type: ConditionType | Condition | proto.ICondition, id?: any, num: number = 0) {
        if (typeof type == 'object') {
            this.init2(type)
        }
        else {
            this.type = type
            this.id = id
            this._num = num
        }

        return this
    }

    public init2(obj: Condition | proto.ICondition) {
        this.type = obj.type
        this.id = obj.id
        this._num = obj.num || 0
        this.isHide = obj.isHide
        if (obj.extra) {
            if (typeof obj.extra === 'string') {
                this.extra = JSON.parse(obj.extra)
            }
            else {
                this.extra = obj.extra
            }
        }
        return this
    }

    public get num() {
        if (this._randomNum.length === 2) {
            return ut.random(this._randomNum[0], this._randomNum[1])
        }
        return this._num
    }

    public set num(val: number) {
        this._num = val
    }

    public haveNum(): boolean {
        if (this._randomNum.length > 0) {
            return true
        }
        return !isNaN(this._num)
    }

    // 随机数量
    public randomNum(clean: boolean = true) {
        if (this._randomNum.length === 2) {
            this._num = ut.random(this._randomNum[0], this._randomNum[1])
        }
        if (clean) {
            this._randomNum.length = 0
        }
    }

    public toDB() {
        return { type: this.type, id: this.id, num: this.num }
    }

    public getObMap() {
        return { num: '_num' }
    }

    public isSame(cond: ConditionObj | Condition) {
        if (!cond) return false
        let id1 = !this.id ? -1 : this.id
        let id2 = !cond.id ? -1 : cond.id
        return cond.type == this.type && id1 == id2
    }

    public isPropTimeMail() {
        if (this.type != ConditionType.PROP) return false
        let cfg = assetsMgr.getJsonData<ItemCfg>('Item', this.id)
        return cfg && cfg.type == ItemType.TIME_MAIL
    }

    public clone() {
        return new ConditionObj().init2(this)
    }

    public toProto() {
        return { type: this.type, id: this.id, num: this.num, extra: JSON.stringify(this.extra) } as proto.ICondition
    }
}

window["ConditionObj"] = ConditionObj