import EventType from "../../common/event/EventType";
import { dbHelper } from "../../common/helper/DatabaseHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { localConfig } from "../../common/LocalConfig";
import { Msg } from "../../../proto/msg-define";
import { taHelper } from "../../common/helper/TaHelper";
import { appleHelper } from "../../common/apple/AppleHelper";
import { facebookHelper } from "../../common/facebook/FacebookHelper";
import { googleHelper } from "../../common/google/GoogleHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import ConditionObj from "./ConditionObj";
import { Channel, ConditionType, LangCfgName } from "../../common/constant/Enums";
import { wxHelper } from "../../common/wx/WxHelper";
import { wxAppHelper } from "../../common/wx_app/WxAppHelper";
import { jsbHelper } from "../../common/helper/JsbHelper";
import CryptoJS from "../../common/crypto/CryptoJS";
import { taptapHelper } from "../../common/taptap/taptapHelper";

const GUEST_KEY = "__@guest_id"

@mc.addmodel('user')
export default class UserModel extends mc.BaseModel {

    private data: proto.IPlayer = null
    private uid: string
    private token: string = null // 免密登录token
    private sid: number = 0 //所在服务器

    private openid: string //一些业务如分享需要用到
    private userType: string = null
    private diyName: string = '' //自定义名字
    private diyAvatar: string = '' //自定义头像
    private nickName: string = '' //第三方名字
    private avatarUrl: string = '' //第三方头像 不可变化
    private totalOnlineTime: number = 0
    private gm: boolean = false //不记到存档里面
    private changeTime: number = 0
    private freeTime: number //免费改名次数
    private buyCost: number //改名价格
    public offlineTime: number = 0

    private nonagePlayTime: number = -1
    private age: number = 0
    private preTime: number = -1

    public configMd5: { [key: string]: string } = null
    private md5Checking: boolean = false

    public init() {
        let data = this.data
        this.updateInfo(this.data)

        this.freeTime = cfgHelper.getMiscData('renamed').freeNum
        this.buyCost = cfgHelper.getMiscData('renamed').buyPrice
    }

    private updateInfo(data: proto.IPlayer) {
        this.totalOnlineTime = data.totalOnlineTime as number
        this.diyName = data.nickName
        this.diyAvatar = data.avatarUrl
        this.changeTime = data.changeNameCnt || 0
        this.offlineTime = data.offlineTime as number
        gameHelper.setDebugTime(data.offsetTime)
    }

    public getSid() { return this.sid }

    //账号登录
    public async accountLogin(username, password) {
        let common = await this.getLoginCommon()
        const info = Object.assign({ common }, { username, password })
        let msg = new proto.C2S_LoginAccountMessage(info)
        const res = await gameHelper.net.request(Msg.C2S_LoginAccountMessage, msg, true)
        return this.loginHandle(res)
    }

    //游客登录
    public async guestLogin(inviteCode: string = "") {
        let userId = await this.loadGuestId()
        let common = await this.getLoginCommon()
        common.inviteCode = inviteCode
        let msg = new proto.C2S_LoginGuestMessage({ common, userId })
        const res = await gameHelper.net.request(Msg.C2S_LoginGuestMessage, msg, true)
        let ret = this.loginHandle(res)
        this.saveGuestId(this.uid)
        return ret
    }

    // 加载游客id
    private async loadGuestId() {
        let id = storageMgr.getOrgItem(GUEST_KEY)
        if (!id) {
            id = await jsbHelper.getDeviceData(GUEST_KEY, 'train_account')
        }
        return id
    }

    private saveGuestId(id: string) {
        if (!id) return
        storageMgr.setOrgItem(GUEST_KEY, id)
        jsbHelper.saveDeviceData(GUEST_KEY, id, 'train_account')
    }

    private clearGuestId() {
        storageMgr.setOrgItem(GUEST_KEY, "")
        jsbHelper.delDeviceData(GUEST_KEY, 'train_account')
    }

    //苹果登录
    public async appleLogin() {
        viewHelper.showNet()
        let loginInfo = await appleHelper.login()
        if (loginInfo.errcode) {
            viewHelper.hideNet()
            return { code: loginInfo.errcode }
        }
        let common = await this.getLoginCommon()
        let msg = new proto.C2S_LoginAppleMessage({ common, userId: loginInfo.userId, code: loginInfo.code, token: loginInfo.token, nickName: loginInfo.nickName })
        const res = await gameHelper.net.request(Msg.C2S_LoginAppleMessage, msg)
        viewHelper.hideNet()
        return this.loginHandle(res)
    }

    // 绑定苹果账号
    public async appleBind() {
        viewHelper.showNet()
        const loginInfo = await appleHelper.login()
        if (loginInfo.errcode) {
            viewHelper.hideNet()
            return { code: loginInfo.errcode }
        }
        let msg = new proto.C2S_BindAppleMessage({ userId: loginInfo.userId, code: loginInfo.code, token: loginInfo.token, nickName: loginInfo.nickName })
        const res = await gameHelper.net.request(Msg.C2S_LoginAppleMessage, msg)
        viewHelper.hideNet()
        return this.bindHandle(res)
    }

    //fb登录
    public async facebookLogin() {
        viewHelper.showNet()
        let loginInfo = await facebookHelper.login()
        if (loginInfo.errcode) {
            viewHelper.hideNet()
            return { code: loginInfo.errcode }
        }
        let common = await this.getLoginCommon()
        let msg = new proto.C2S_LoginFBMessage({ common, userId: loginInfo.userId, token: loginInfo.token })
        const res = await gameHelper.net.request(Msg.C2S_LoginFBMessage, msg)
        viewHelper.hideNet()
        return this.loginHandle(res)
    }

    // 绑定fb账号
    public async fbBind() {
        viewHelper.showNet()
        const loginInfo = await facebookHelper.login()
        if (loginInfo.errcode) {
            viewHelper.hideNet()
            return { code: loginInfo.errcode }
        }
        let msg = new proto.C2S_BindFBMessage({ userId: loginInfo.userId, token: loginInfo.token })
        const res = await gameHelper.net.request(Msg.C2S_LoginAppleMessage, msg)
        viewHelper.hideNet()
        return this.bindHandle(res)
    }

    //谷歌登录
    public async googleLogin() {
        viewHelper.showNet()
        let loginInfo = await googleHelper.login()
        if (loginInfo.errcode) {
            viewHelper.hideNet()
            return { code: loginInfo.errcode }
        }
        let common = await this.getLoginCommon()
        let msg = new proto.C2S_LoginGoogleMessage({ common, token: loginInfo.token, nickName: loginInfo.nickName, avatarUrl: loginInfo.avatarUrl })
        const res = await gameHelper.net.request(Msg.C2S_LoginGoogleMessage, msg)
        viewHelper.hideNet()
        return this.loginHandle(res)
    }

    // 绑定google账号
    public async googleBind() {
        viewHelper.showNet()
        const loginInfo = await googleHelper.login()
        if (loginInfo.errcode) {
            viewHelper.hideNet()
            return { code: loginInfo.errcode }
        }
        let msg = new proto.C2S_BindGoogleMessage({ token: loginInfo.token, nickName: loginInfo.nickName, avatarUrl: loginInfo.avatarUrl })
        const res = await gameHelper.net.request(Msg.C2S_LoginAppleMessage, msg)
        viewHelper.hideNet()
        return this.bindHandle(res)
    }

    //微信小程序
    public async wxLogin() {
        let loginInfo = await wxHelper.login()
        if (loginInfo.errcode) {
            return { code: loginInfo.errcode }
        }
        let { code, nickName, avatarUrl } = loginInfo
        let common = await this.getLoginCommon()
        let msg = new proto.C2S_LoginWxMessage({ common, code, nickName, avatarUrl })
        const res = await gameHelper.net.request(Msg.C2S_LoginWxMessage, msg, true)
        return this.loginHandle(res)
    }

    //微信app登录
    public async wxAppLogin() {
        let loginInfo = await wxAppHelper.login()
        if (loginInfo.errcode) {
            return { code: loginInfo.errcode }
        }
        let { code } = loginInfo
        let common = await this.getLoginCommon()
        let msg = new proto.C2S_LoginWxAppMessage({ common, code })
        const res = await gameHelper.net.request(Msg.C2S_LoginWxAppMessage, msg, true)
        return this.loginHandle(res)
    }

    //taptap登录
    public async taptapLogin() {
        mc.lockTouch(true)
        let loginInfo = await taptapHelper.login()
        if (loginInfo.errcode) {
            mc.lockTouch(false)
            return { code: loginInfo.errcode }
        }
        mc.lockTouch(false)
        let common = await this.getLoginCommon()
        let msg = new proto.C2S_LoginTapTapMessage({ common, kid: loginInfo.kid, macKey: loginInfo.mac_key })
        const res = await gameHelper.net.request(Msg.C2S_LoginTapTapMessage, msg, true)
        return this.loginHandle(res)
    }

    // 通用token登录
    public async loginByToken(isReconnect?: boolean) {
        let common = await this.getLoginCommon()
        let msg = new proto.C2S_LoginByTokenMessage({ common, isReconnect, token: this.getToken() })
        const res = await gameHelper.net.request(Msg.C2S_LoginByTokenMessage, msg, true)
        return this.loginHandle(res, { isReconnect })
    }

    public async getLoginCommon(): Promise<proto.ILoginCommon> {
        let packageSign
        if (ut.isAndroid()) {
            packageSign = await jsbHelper.getPackageSign()
        }
        return {
            distanceId: taHelper.getDistinctId(),
            ver: gameHelper.getVersion(),
            os: taHelper.getOs(),
            platform: gameHelper.getPlatform(),
            closeGuide: !localConfig?.openGuide,
            packageSign,
        }
    }

    public async reLogin() {
        let info = await this.setSessionVersion()
        if (info.code != 0) {
            twlog.error('reLogin:setSessionVersion', info.code)
            return false
        }

        let loginInfo = await this.loginByToken(true)
        if (loginInfo.code != 0) {
            twlog.error('reLogin:loginByToken', loginInfo.code)
            return false
        }

        let downloadInfo = await this.downloadRecord()
        if (downloadInfo.code != 0) {
            twlog.error('reLogin:downloadRecord', downloadInfo.code)
            return false
        }

        let plr = downloadInfo.player

        gameHelper.user.updateInfo(plr)
        gameHelper.world.updateInfo(plr)
        gameHelper.planet.updateInfo(plr.planetInfo)
        gameHelper.train.updateInfo(plr.train)
        gameHelper.heartOutput.updateInfo(plr.heartOutput)
        gameHelper.passenger.updateInfo({ passengers: plr.passengers, starOutput: plr.passengerStarOutput })
        gameHelper.deepExplore.updateInfo(plr.explore)
        gameHelper.ore.updateInfo(plr.ore)

        return true
    }

    public loginHandle(res, extraInfo: { [key: string]: any } = {}) {
        let data = proto.S2C_LoginResultMessage.decode(res)
        let info = data.userInfo
        const code = data.code
        if (code == 0) {
            this.uid = info.uid
            this.updateToken(info.lToken)
            this.openid = info.openid
            this.userType = info.userType
            this.nickName = info.nickName || ''
            this.avatarUrl = info.avatarUrl || ''
            this.age = info.age
            // this.gm = info.gm

            this.nonagePlayTime = this.getNonagePlayTime()
            this.preTime = gameHelper.now()

            eventCenter.emit(EventType.LOGIN_COMPLETE)

            if (!extraInfo?.isReconnect) {
                taHelper.login(this.uid)
            }
        }
        return data
    }

    public bindHandle(res, extraInfo: { [key: string]: any } = {}) {
        let data = proto.S2C_BindResultMessage.decode(res)
        const code = data.code
        if (code == 0) {
            this.nickName = data.nickName
            this.avatarUrl = data.avatarUrl
            this.userType = data.userType
            // this.clearGuestId() //先不清理吧
        }
        return data
    }


    public async setSessionVersion() {
        const res = await gameHelper.net.request(`gate/${gameHelper.getVersion()}`, null, true)
        const data = proto.S2C_ErrorResultMessage.decode(res)
        return data
    }

    public async downloadRecord() {
        let msg = new proto.C2S_GetPlayerInfoMessage()
        const res = await gameHelper.net.request(Msg.C2S_GetPlayerInfoMessage, msg, true)
        let info = proto.S2C_GetPlayerInfoResMessage.decode(res)
        return info
    }

    // 服务器下发玩家信息
    public async updateRecord() {
        const info = await this.downloadRecord()
        if (info.code != 0) {
            return info
        }
        let plr = info.player
        // 服务器json配置
        gameHelper.user.configMd5 = plr.configMd5

        // 时间，体力

        gameHelper.world.data = plr

        //货币
        gameHelper.currency.data = ut.setValue("diamond|starDust|heart|paperCrane", plr, {})

        // 背包数据
        gameHelper.bag.data = plr.bag

        // 车厢数据
        gameHelper.train.data = plr.train

        // 乘客
        gameHelper.passenger.data = { passengers: plr.passengers, starOutput: plr.passengerStarOutput, resetTime: plr.passengerRestCdTime, skins: plr.skin, frag: plr.frag }

        gameHelper.planet.data = plr.planetInfo

        gameHelper.task.data = plr.taskInfo

        gameHelper.guide.data = { guideId: plr.guideId, guideList: plr.guideInfo }

        gameHelper.tool.data = plr.toolModel

        gameHelper.achieve.data = plr.achievementInfo

        gameHelper.mail.data = plr.mailList

        gameHelper.user.data = plr

        gameHelper.new.data = plr.newMarkList

        gameHelper.heartOutput.data = plr.heartOutput

        gameHelper.chest.chestData = plr.chest

        gameHelper.tower.data = plr.tower

        gameHelper.blackHole.data = plr.blackHole
        gameHelper.battle.data = plr.battle

        gameHelper.equip.data = plr.equip

        gameHelper.instance.data = plr.instance

        gameHelper.wanted.data = plr.wanted

        gameHelper.store.data = plr.store

        gameHelper.pay.data = plr.pay

        gameHelper.transport.data = plr.transport

        gameHelper.jackpot.data = plr.jackpot

        gameHelper.field.data = plr.field

        gameHelper.ore.data = plr.ore

        gameHelper.collect.data = plr.collect

        gameHelper.arrest.data = plr.arrest

        gameHelper.spaceStone.data = plr.spaceStone

        gameHelper.dailyTask.data = plr.dailyTask

        gameHelper.archives.data = plr.passengerProfiles
        gameHelper.planetArchives.data = plr.planetProfiles

        gameHelper.resonance.data = plr.resonance

        gameHelper.pvp.data = plr.pvpModuleData

        gameHelper.deepExplore.data = plr.explore

        gameHelper.ad.data = plr.ad.data

        gameHelper.profileBranch.data = plr.profileBranch

        gameHelper.trainDailyTask.data = plr.trainDailyTask

        gameHelper.burstTask.data = plr.burstTask

        gameHelper.trainActivity.data = plr.trainActivity

        gameHelper.trainTech.data = plr.techData

        return info
    }

    private onLogout() {
    }


    public updateToken(token, setEmpty: boolean = false) {
        if (!token && !setEmpty) return
        this.token = token
        localStorage.setItem("__@LOGIN_TOKEN", this.token)
    }

    public getUid() {
        return this.uid
    }

    public getOpenid() {
        return this.openid
    }

    public getNickName() { return this.diyName || this.nickName }
    public getTotalOnlineTime() { return this.totalOnlineTime }
    public getAvater() { return this.diyAvatar || this.avatarUrl }
    public getBuyCost() { return this.buyCost }
    public getFreeTime() {
        return Math.max(0, this.freeTime - this.changeTime)
    }

    public getToken() {
        if (!this.token) {
            this.token = localStorage.getItem("__@LOGIN_TOKEN")
        }
        return this.token
    }

    public isAdult() {
        return this.getAge() >= 18
    }

    public getAge() {
        if (!localConfig.isAudit) {
            return 18
        }
        return this.age
    }

    update(dt) {
        this.updateNonagePlayTime()
    }

    public async rename(key: string) {
        let msg = new proto.C2S_DiyPlayerInfoMessage({ nickName: key })
        const res = await gameHelper.net.request(Msg.C2S_DiyPlayerInfoMessage, msg, true)
        const { code } = proto.S2C_DiyPlayerInfoRespMessage.decode(res)
        if (code == 0) {
            if (this.getFreeTime() > 0) {
                this.changeTime++
            }
            else {
                gameHelper.deductCondition(new ConditionObj().init(ConditionType.DIAMOND, -1, this.buyCost))
            }
            viewHelper.showAlert("profile_tips_2")
            this.diyName = key
            //改名成功
            eventCenter.emit(EventType.RENAME_SUCCESS)
        }
        else {
            if (code == 1) {
                viewHelper.showAlert("profile_tips_3")
            }
            else if (code == 2) {
                viewHelper.showAlert("common_tips_5")
            }
            else if (code == 3) {
                viewHelper.showAlert("profile_tips_4")
            }
            else if (code == 4) {
                viewHelper.showAlert("profile_tips_5")
            }
            else {
                viewHelper.showNetError(code)
                await ut.wait(10) //网挂了才走这里
            }
            return false
        }
    }

    // 实名验证
    public async authIDCard(realName: string, idCard: string): Promise<number> {
        let msg = new proto.C2S_CertificationMessage({ idCard, realName })
        const res = await gameHelper.net.request(Msg.C2S_CertificationMessage, msg, true)
        let { code, age } = proto.S2C_CertificationResultMessage.decode(res)
        if (code == 0) {
            this.age = age
        }
        return code
    }

    public async checkCertification() {
        if (localConfig.channel == Channel.TAPTAP) {
            while (true) {
                let code = await taptapHelper.checkRealName(gameHelper.user.getOpenid())
                let next
                let p = new Promise(r => {
                    next = r
                })
                switch (code) {
                    case 500: // LOGIN_SUCCESS 正常进入游戏
                        return;
                    case 1000: // EXITED 退出防沉迷认证及检查
                    case 1001: // SWITCH_ACCOUNT 用户点击切换账号
                        // 游戏应返回到登录页
                        gameHelper.user.updateToken("", true)
                        gameHelper.gameRestart()
                        break;
                    case 1030: // PERIOD_RESTRICT 用户当前时间无法进行游戏
                    case 1050: // DURATION_LIMIT 用户无可玩时长
                        // 提示用户时长或时段限制，并退出游戏
                        viewHelper.showMessageBox("根据未成年人沉迷网络游戏的相关规定，您当前无法进行游戏，请您合理安排游戏时间，注意休息。", () => {
                            cc.game.end()
                        }, null, { lang: LangCfgName.LOAD, lockClose: true })
                        break;
                    case 1100: // AGE_LIMIT 年龄限制无法进入游戏
                        viewHelper.showMessageBox("根据未成年人沉迷网络游戏的相关规定，您的年龄无法进行游戏。", () => {
                            cc.game.end()
                        })
                        break;
                    case 1200: // INVALID_CLIENT_OR_NETWORK_ERROR 数据请求失败
                        viewHelper.showMessageBox("网络请求失败，请检查网络是否正常后，再次重试。", () => {
                            next()
                        }, null, { lang: LangCfgName.LOAD, lockClose: true })
                        break;
                    case 9002: // REAL_NAME_STOP 实名过程中点击了关闭实名窗
                        viewHelper.showMessageBox("完成实名认证后继续游戏。", () => {
                            next()
                        }, null, { lang: LangCfgName.LOAD, lockClose: true })
                        break;
                    default:
                        viewHelper.showMessageBox(`未知错误: ${code}\n请重试`, () => {
                            next()
                        }, null, { lang: LangCfgName.LOAD, lockClose: true })
                        break;
                }
                await p
            }
        }
        else {
            if (!localConfig.isAudit) return
            // app端
            if (!gameHelper.isInland() || !cc.sys.isNative) {
                return
            }
            // 检测实名认证
            if (this.age === 0) {
                viewHelper.showPnl('common/Certification')
                await eventCenter.wait(EventType.CERTIFICATION_COMPLETE)
            }
            // 检测防沉迷
            if (this.isAdult()) {
            } else if (this.nonagePlayTime <= 0) {
                await new Promise(r => {
                    viewHelper.showPnl('common/AntiAddiction', true)
                })
            }
        }
    }

    private updateNonagePlayTime() {
        if (this.isAdult()) return
        if (this.nonagePlayTime <= 0) return
        if (this.age <= 0) return //等待实名
        if (!localConfig.isAudit) return
        let now = gameHelper.now()
        let dt = now - this.preTime
        this.preTime = now
        this.nonagePlayTime -= dt
        if (this.nonagePlayTime <= 0) {
            viewHelper.showPnl('common/AntiAddiction')
        }
    }

    private getNonagePlayTime() {
        let date = new Date()
        let day = date.getDay()
        if (day == 5 || day == 6 || day == 7) {
            let hour = date.getHours()
            if (20 <= hour && hour < 21) {
                return ut.Time.Hour - (date.getMinutes() * ut.Time.Minute + date.getSeconds() * ut.Time.Second)
            }
            return 0
        }
        return 0
    }

    public async exitGame() {
        dbHelper.update(1)
        dbHelper.clear()
        storageMgr.setOrgItem("__@agreement", '1')
        gameHelper.gameRestart()
    }

    public async accountRegister(account, password) {
    }

    public async webLogin() {
    }

    public async checkMd5() {
        if (!CC_DEV) return true
        const md5 = gameHelper.user.configMd5
        if (localConfig.release || !md5) return true
        let str = ""
        if (md5) {
            let hasError = false
            for (const j in md5) {
                const serverMd5 = md5[j]
                const clientMd5 = assetsMgr.localConfigMd5[j]
                if (!clientMd5) {
                    // twlog.info("❌客户端配置缺少:" + j)
                    continue
                }
                if (clientMd5 != serverMd5) {
                    str += j + " "
                    // twlog.info("❌检查配置失败，客户端配置与服务器不一致:" + j)
                    hasError = true
                }
            }
            if (hasError) {
                if (!this.md5Checking) {
                    this.md5Checking = true
                    await new Promise(ok => {
                        viewHelper.showMessageBox("配置不一致\n" + str, ok)
                    })
                    this.md5Checking = false
                }
                return false
            }
        }
    }
}
