import { Msg } from "../../../proto/msg-define"
import { Condition } from "../../common/constant/DataType"
import { UIFunctionType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"

@mc.addmodel('trainActivity', 101)
export default class TrainActivityModel extends mc.BaseModel {
    public data: proto.ITrainActivity = null
    // 可安排的活动列表
    private _serverActivityList: TrainActivityItem[] = []
    // 活动列表
    private _list: TrainActivityItem[] = []
    private _arrangeWorldTime: number = 0
    private _currentActivity: TrainActivityItem = null

    get list() { return this._list }
    get arrangeWorldTime() { return this._arrangeWorldTime }
    get serverActivityList() { return this._serverActivityList }
    get currentActivity() { return this._currentActivity }

    public init() {
        const arrangeWorldTime = this.data.arrangeWorldTime as number
        const list = this.data.list.map(item => new TrainActivityItem().init(item))
        if (arrangeWorldTime == 0) {
            this._serverActivityList = list
        }
        else {
            this._list = list
        }
        this._arrangeWorldTime = arrangeWorldTime
        this.data = null
        this.initEventListener()
    }

    private initEventListener() {
        eventCenter.on(EventType.GAME_DAY_CHANGE, this.onGameDayChange, this)

        eventCenter.on(EventType.UNLOCK_FUNTION, (type) => {
            if (type == UIFunctionType.TRAIN_ACTIVITY) {
                this.sync()
            }
        })
    }

    public async sync() {
        const { code, data } = await gameHelper.net.requestWithDataWait(Msg.C2S_GetTrainActivityMessage)
        if (code != 0) return void viewHelper.showNetError(code)
        this._serverActivityList = data.map(item => new TrainActivityItem().init(item))
    }

    public onGameDayChange() {
        if (!this.currentActivity) return
        if (this.currentActivity.trainId == 0) return
        const remainingDay = Math.floor(this.currentActivity.getSurplusTime() / ut.Time.Day)
        if (Math.abs(remainingDay - this.currentActivity.costDay) % 2 == 1) {
            console.log("活动聚集乘客", remainingDay, this.currentActivity.trainId)
            eventCenter.emit(EventType.TRAIN_ACTIVITY_GATHER, this.currentActivity.trainId)
        }
    }

    // 是否可以安排活动
    public isCanArrange() { return this._arrangeWorldTime == 0 && !this.currentActivity }

    // 获取剩余可用以安排的天数
    public getRemainingDay() { return 30 - this.list.reduce((pre, cur) => pre + cur.costDay, 0) }

    // ui选中事件 -> 添加 || 移除
    public onUiSelect(data: TrainActivityItem) {
        const index = this.list.indexOf(data)
        if (index == -1 && this.getRemainingDay() < data.costDay) {
            return void viewHelper.showAlert("trainActivity_tips_0")
        }
        index == -1 ? this.list.push(data) : this.list.splice(index, 1)
        eventCenter.emit(EventType.TRAIN_ACTIVITY_PNL_SELECT_CHANGE, data)
    }

    // 获取某天需要进行的活动
    public getTrainActivityItemByDay(day: number) {
        if (this._list.length == 0) return null
        let used = 0
        for (const item of this._list) {
            used += item.costDay
            if (day < used) {
                return item
            }
        }
        return null
    }

    public isIconDay(day: number) {
        if (this._list.length == 0) return false
        let used = 0
        for (const item of this._list) {
            if (day == used) return true
            used += item.costDay
        }
        return false
    }

    public async submit() {
        const ids = this._list.map(item => item.id)
        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_ArrangeTrainActivityMessage, { ary: ids })
        if (code != 0) return void viewHelper.showNetError(code)
        let usedTime = gameHelper.world.getTime()
        usedTime -= usedTime % ut.Time.Day
        this.list.forEach(item => {
            // 进入排队
            item.state = proto.CommonState.InProcess
            const cost = item.costDay * ut.Time.Day
            usedTime += cost
            item.endTime = usedTime
        })
        this._arrangeWorldTime = gameHelper.world.getTime()
        eventCenter.emit(EventType.TRAIN_ACTIVITY_ARRANGE)
        eventCenter.emit(EventType.TRAIN_ACTIVITY_STATE_CHANGE)
    }

    public async claimReward(item: TrainActivityItem) {
        const index = this.list.indexOf(item)
        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_GetTrainActivityRewardMessage, { index })
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        const list = gameHelper.toConditions(item.rewards)
        await gameHelper.grantRewardAndShowUI(list)
        item.done()
        return true
    }

    update(dt: number) {
        if (this._arrangeWorldTime == 0) return
        if (this.currentActivity) {
            if (this.currentActivity.getSurplusTime() > 0) return
            this._currentActivity = null
            return
        }
        if (!this.list.length) return
        // 从排队列表取一个
        this._currentActivity = this.list.filter(l => {
            if (l.state == proto.CommonState.DoneWithoutReward && l.getSurplusTime() > 0) return true
            if (l.state == proto.CommonState.FinishWithReward && l.getSurplusTime() > 0) return true
            return l.state == proto.CommonState.InProcess
        }).shift()
        if (!this.currentActivity) return
        this.currentActivity.start()
    }
}


export class TrainActivityItem {
    private _id: number
    private _trainId: number
    private _rewards: Condition[]
    private _endTime: number
    private _cfg: { id: number, name: string, icon: string, spine: string, lizi: string[] }
    private _costDay: number
    private _state: proto.CommonState = proto.CommonState.NotStart

    get id() { return this._id }
    get trainId() { return this._trainId }
    get rewards() { return this._rewards }
    get cfg() { return this._cfg }
    get costDay() { return this._costDay }
    get rewardNum() { return this._rewards[0].num }
    get rewardType() { return this._rewards[0].type }
    get rewardId() { return this._rewards[0].id }
    set endTime(v: number) { this._endTime = v }
    get state() { return this._state }
    set state(v: proto.CommonState) { this._state = v }

    public init(item: proto.ITrainActivityItem): TrainActivityItem {
        this._id = item.id
        this._trainId = item.trainId
        this._rewards = gameHelper.toConditions(item.rewards)
        this._endTime = -1
        const st = item.surplusTime as number
        if (st > 0) {
            this._endTime = st + gameHelper.world.getTime()
        }
        this._state = item.state
        this._cfg = assetsMgr.getJsonData("TrainActivityItem", item.cfgId)
        this._costDay = item.costDay
        return this
    }

    public getSurplusTime() { return this._endTime - gameHelper.world.getTime() }

    // 开始
    public start() {
        console.log("开始活动 表现 ", this)
        let sendReward = this.state == proto.CommonState.DoneWithoutReward || this.state == proto.CommonState.InProcess
        if (this.state == proto.CommonState.InProcess) {
            this.state = proto.CommonState.DoneWithoutReward
            eventCenter.emit(EventType.UI_TIP_TRAIN_ACTIVITY, assetsMgr.lang("trainActivity_tips_2", assetsMgr.lang("name_train_" + this._trainId), assetsMgr.lang(this.cfg.name)))
        }
        if (sendReward) {
            eventCenter.emit(EventType.TRAIN_ACTIVITY_REWARD_SHOW)
        }
        eventCenter.emit(EventType.TRAIN_ACTIVITY_ANIM_SHOW)
        // 聚集乘客
        gameHelper.trainActivity.onGameDayChange()
        return this
    }

    // 领奖后的done 表现的请使用getSurplusTime判断
    public done() { this.state = proto.CommonState.FinishWithReward; eventCenter.emit(EventType.TRAIN_ACTIVITY_REWARD_SHOW); eventCenter.emit(EventType.TRAIN_ACTIVITY_ANIM_SHOW) }

}