import { ChapterPlanetMineCfg, DailyTaskCfg, DailyTaskDialogCfg, NpcCfg, PlanetCfg } from "../../common/constant/DataType"
import { gameHelper } from "../../common/helper/GameHelper"
import { Msg } from "../../../proto/msg-define";
import { cfgHelper } from "../../common/helper/CfgHelper";
import ConditionObj from "../common/ConditionObj"
import { viewHelper } from "../../common/helper/ViewHelper"
import EventType from "../../common/event/EventType";
import { BattleLevelType, ConditionType, DailyTaskState, DailyTaskType, UIFunctionType } from "../../common/constant/Enums";
import { Equip } from "../equip/EquipModel";
import { util } from "../../../core/utils/Utils";
import BattleRole from "../battle/BattleRole";
import Monster from "../battle/Monster";
import { resHelper } from "../../common/helper/ResHelper";

export class DailyTask {
    private id: number = null
    private target: ConditionObj[] = []
    private rewards: ConditionObj[] = []
    private sender: number = 0
    private cfg: DailyTaskCfg = null
    public state: DailyTaskState = DailyTaskState.TAKE
    public uid: string = null
    private progress: ConditionObj[] = []
    private _content: number = 0
    private _dialogData: { planet: PlanetCfg, dialogNpc: NpcCfg, dialog: DailyTaskDialogCfg, dialogGroup: DailyTaskDialogCfg[] } = null
    private _battleInfo: Monster[] = null
    private _battlePlanet: number = 0

    get type() { return this.cfg?.type }
    get content() { return this.cfg?.contents[this._content] }
    get dialogArg() { return this._dialogData }
    get battleInfo() { return this._battleInfo }
    get battlePlanet() { return this._battlePlanet }

    public getTarget() { return this.target }
    public getRewards() { return this.rewards }
    public getSender() { return this.sender }
    public getSenderName() { return cfgHelper.getCharacter(this.sender).name }
    public getId() { return this.id }
    public clearBattle() { this._battleInfo = null }

    public init(data?: proto.IDailyTask) {
        if (data) {
            this.uid = data.uid
            this.target = gameHelper.toConditions(data.target)
            this.rewards = gameHelper.toConditions(data.reward)
            this.id = data.id
            this.cfg = assetsMgr.getJsonData<DailyTaskCfg>('DailyTask', this.id)
            this.sender = data.sender
            this.state = data.state
            this._content = data.content
            this.progress = gameHelper.toConditions(data.progress)
            switch (this.type) {
                case DailyTaskType.DIALOG:
                    const target = this.progress[0]
                    const npcDataId = target.extra.npc
                    const dialogDataId = target.extra.dialog
                    const planet = assetsMgr.getJsonData<PlanetCfg>("Planet", target.id as number)
                    const dialogNpc = assetsMgr.getJsonData<NpcCfg>("Npc", Number(npcDataId))
                    const dialog = assetsMgr.getJsonData<DailyTaskDialogCfg>("DailyTaskDialog", Number(dialogDataId))
                    const dialogGroup = assetsMgr.getJson<DailyTaskDialogCfg>("DailyTaskDialog").datas.filter(d => d.group == dialog.group && d.id != dialog.id)
                    this._dialogData = { planet, dialogNpc, dialog, dialogGroup }
                    break
                case DailyTaskType.BATTLE:
                    this._battleInfo = data.battleInfo.map(info => new Monster().init(info.id, info.lv, info.starLv))
                    this._battlePlanet = data.planet
                    break
            }
        }
        return this
    }

    public async take() { return false }

    public getTaskId() {
        return `daily-${this.uid}`
    }

    public checkProgress() {
        if (this.type != DailyTaskType.DIALOG) return true
        for (let target of this.target) {
            if (!this.checkProgressByTarget(target)) return false
        }
        return true
    }

    public getProgressByTarget(target: ConditionObj) {
        return this.progress.find(t => t.isSame(target))?.num || 0
    }

    public checkProgressByTarget(target: ConditionObj) {
        let num = this.target.find(t => t.isSame(target))?.num || 0
        return this.getProgressByTarget(target) >= num
    }

    public setProgress(targets: ConditionObj[]) {
        for (let target of targets) {
            let progress = this.progress.find(t => t.isSame(target))
            if (progress) {
                progress.num += target.num
            }
            else {
                this.progress.push(target)
            }
        }
    }

    public getDialogOpts(...exclude: DailyTaskDialogCfg[]): DailyTaskDialogCfg[] {
        const arg = this.dialogArg
        let group = [].concat(arg.dialogGroup)
        if (exclude?.length) {
            group = arg.dialogGroup.filter(d => !exclude.includes(d))
        }
        const random = ut.randomArray(group).slice(0, 2)
        return ut.randomArray([arg.dialog, ...random])
    }

    public canFinish() {
        if (this.state == DailyTaskState.FINISH) return false
        if (this.type == DailyTaskType.BATTLE) return this.battleInfo == null || this.battleInfo.length == 0
        return gameHelper.checkConditions(this.getTarget()) && this.checkProgress()
    }

    public async submitDialog(id: number) {
        const taskIndex = gameHelper.dailyTask.getTasks().indexOf(this)
        if (this.dialogArg.dialog.id != id) return false
        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_DialogTaskDoneMessage, { index: taskIndex, dialogIndex: id })
        if (code == 0) {
            this.setProgress(this.getTarget())
            this.target = []
            return true
        }
        return false
    }

}

@mc.addmodel("dailyTask", 200)
export default class DailyTaskModel extends mc.BaseModel {
    public data: proto.IDailyTaskInfo = null
    private tasks: DailyTask[] = []
    private _bigGet: boolean = false
    public pnlOpenRed: boolean = false

    get bigGet() { return this._bigGet }

    public init() {
        this.updateInfo(this.data)
        this.initListener()
    }

    public isInit() { return this.tasks?.length }

    public async checkSync() {
        if (!this.isInit()) return this.sync()
        return true
    }

    public async sync() {
        const { code, info } = await gameHelper.net.requestWithData(Msg.C2S_SyncDailyTaskInfoMessage)
        if (code == 0) {
            this.updateInfo(info)
            return true
        }
        return false
    }

    private initListener() {
        eventCenter.on(EventType.UNLOCK_FUNTION, (type) => {
            if (type == UIFunctionType.PLAY_DAILY_TASK) {
                this.sync()
            }
        })
    }

    public updateInfo(data: proto.IDailyTaskInfo) {
        if (!data) return
        this.tasks = data.tasks.map(t => new DailyTask().init(t))
        this._bigGet = data.bigGet
    }

    public getCollectTasks() { return this.tasks.filter(t => t.type == DailyTaskType.COLLECT) }

    public getTask() { return null }

    public getTasks() { return this.tasks }

    public hasDialogTask(npcId: number) {
        const task = this.tasks.find(task => task.type == DailyTaskType.DIALOG && task.dialogArg.dialogNpc.id == npcId)
        return task?.checkProgress() ? null : task
    }

    public hasBattleTask(planetId: number) {
        return this.tasks.find(task => task.battleInfo?.length && task.type == DailyTaskType.BATTLE && task.battlePlanet == planetId)
    }

    public canGetBit() {
        if (this.bigGet) return false
        const tasks = this.getTasks()
        if (tasks.length <= 0) return false
        const count = tasks.reduce((pre, cur) => {
            if (cur.state == DailyTaskState.FINISH) {
                return pre + 1
            }
            return pre
        }, 0)
        return count == tasks.length
    }

    @util.addLock
    public async finishTask(task: DailyTask) {
        let extras = []
        let equips: Equip[] = []
        let target = []
        let taskData = null
        let index = -1
        if (task) {
            if (!task.canFinish()) {
                return void viewHelper.showAlert("passengerTask_N_tip_1")
            }
            index = this.tasks.indexOf(task)
        }
        if (index != -1) {
            taskData = this.tasks[index]
            target = taskData.getTarget()
            target.forEach(t => {
                if (t.type == ConditionType.EQUIP) {
                    equips.pushArr(gameHelper.equip.getDailyEquip(+t.id, t.num))
                }
            })
            if (equips.length > 0) {
                extras = equips.map(e => {
                    return JSON.stringify({ uid: e.getUid() })
                })
            }
        }
        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_FinishDailyTaskMessage, { id: index, extras })
        if (code == 0) {
            equips.forEach(e => {
                gameHelper.equip.delEquip(e.getUid())
            })
            gameHelper.deductConditions(target.filter(t => t.type != ConditionType.EQUIP))
            if (index == -1) {
                const r = cfgHelper.getMiscData("dailyTask").bigReward
                const rewards = gameHelper.toConditions(r)
                await gameHelper.grantRewardAndShowUI(rewards)
                this._bigGet = true
            }
            else {
                await gameHelper.grantRewardAndShowUI(taskData.getRewards())
                taskData.state = DailyTaskState.FINISH
            }
            eventCenter.emit(EventType.DAILY_TASK_FINISH, taskData)
            return true
        }
        viewHelper.showNetError(code)
        return false
    }

    private async testSubmitBattleTask(task: DailyTask) {
        const index = this.tasks.indexOf(task)
        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_BattleTaskDoneTestMessage, { index })
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        task.clearBattle()
        eventCenter.emit(EventType.DAILY_TASK_BATTLE_SUBMIT, task)
        return true
    }

    public async processBattleTask(task: DailyTask) {
        const res = await viewHelper.showBattle({
            monsters: task.battleInfo,
            battleBg: resHelper.getBattleBg("blackhole"),
            onWin: () => {
                // gameHelper.dailyTask.finishTask(hasBattleTask)
                console.log("win")
            },
            // title: { name: 'daily_task_guiText_1', progress: '' },
            levelType: BattleLevelType.DAILY_TASK, levelId: "0",
        })
        const win = res?.isWin
        if (win && await this.testSubmitBattleTask(task)) {
            return true
        }
        return false
    }
}
