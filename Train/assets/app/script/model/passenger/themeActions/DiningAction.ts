import { ActionNode } from "../ActionTree";
import DiningModel from "../../train/dining/DiningModel";
import BaseAction from "./BaseAction";
import BuildObj from "../../train/common/BuildObj";
import { StateType } from "../StateEnum";
import { TimeStateData } from "../StateDataType";
import { DiningBuildType, GoodsType, PassengerLifeAnimation } from "../../../common/constant/Enums";
import { CARRIAGE_MAX_STAY_TIME } from "./ActionCfg";
import EventType from "../../../common/event/EventType";

export default class DiningAction extends BaseAction {
    protected carriage: DiningModel = null
    protected waitRecord = { food: false, drink: false, wait: false, time: 0 }
    private moveAction: ActionNode = null
    private cfgsBuildPlay = [
        { act: this.toOrderFood, check: this.checkOrderFood, weight: 10 }, //去点餐
        { act: this.toOrderDrink, check: this.checkOrderDrink, weight: 10 }, //去点饮料
    ]

    protected async start(action: ActionNode) {
        if (this.actionAgent.getState(StateType.WANT_EAT)) {
            await action.run(this.handleWantEat)
        }

        await this.onBeforeStart(action)
        if (action.isOK()) return

        if (this.checkPlay()) {
            await action.run(this.toBuildPlay)
            await action.run(this.toFoodDrink)
        } else {
            this.waitRecord.wait = true
            await action.run(this.toRandomPos)
            this.waitRecord.wait = false
        }

        this.waitRecord.wait = true
        await action.run(this.idle)
        this.waitRecord.wait = false

        await this.checkAndLeaveCarriage(action)
    }

    protected async onExit(action) {
        this.actionAgent.popState(StateType.WANT_EAT)
        action.ok()
    }

    protected checkLeaveCarriage() {
        let dic = this.waitRecord
        if (dic.food && dic.drink) return true
        return super.checkLeaveCarriage()
    }

    protected async handleWantEat(action) {
        if (this.checkOrderFood()) {
            let type = StateType.WANT_EAT
            this.actionAgent.popState(type)
            await action.run(this.toOrderFood)
            await action.run(this.toFoodDrink)
        }
        action.ok()
    }

    private async toFoodDrink(action: ActionNode) {
        let foodState = this.actionAgent.getState(StateType.HOLD_FOOD)
        if (!foodState) return action.ok()
        action.onTerminate = () => { this.removeFood() }
        let type = foodState.data.type
        if (type == GoodsType.FOOD) {
            await action.run(this.toEat)
            this.waitRecord.food = true
        } else if (type == GoodsType.DRINK) {
            await action.run(this.toDrink)
            this.waitRecord.drink = true
        }
        eventCenter.emit(EventType.PASSENGER_EAT_FINASH, this.role.id)
        action.onTerminate()
        action.ok()
    }

    private async toBuildPlay(action: ActionNode) {
        await this.runRandomAct(action, this.cfgsBuildPlay)
        action.ok()
    }

    private checkPlay() {
        for (const { check } of this.cfgsBuildPlay) {
            if (check.call(this)) {
                return true
            }
        }
        return false
    }

    protected async toBuildCommon(action: ActionNode, build: BuildObj, call: Function, paths?, pathOut?) {
        super.toBuildCommon(action, build, call, paths || [{ index: 0 }], pathOut)
    }

    protected async closeToBuild(action: ActionNode) {
        let pos: cc.Vec2 = null
        if (this.moveAction == null) {
            action.onTerminate = () => { this.moveAction = null }
        } else {
            if (!this.moveAgent.isMoving()) {
                action.onTerminate()
                return action.ok()
            }
            pos = this.getClosePos(action.params)
            let position: cc.Vec2 = this.moveAction.params
            if (position.equals(pos)) return
            this.moveAction.terminate()
        }
        pos = pos || this.getClosePos(action.params)
        this.moveAction = action.add(this.move, pos)
        action.run(this.moveAction)
    }
    private async waitQueue(action: ActionNode) {
        let { typeFood, typeQueue } = action.params
        if (!action.onTerminate) {
            action.onTerminate = () => {
                if (this.actionAgent.getState(typeQueue)) {
                    this.actionAgent.popState(typeQueue)
                }
            }
        }
        let build = typeFood == GoodsType.FOOD ? this.carriage.getOrderFood() : this.carriage.getOrderDrink()
        let doPos = this.carriage.queue.getDoPos(build)
        let toPos = this.carriage.queue.getQueuePos(this.role, build, doPos, typeFood)
        let rolePos = this.role.getPosition()
        if (!rolePos.equals(toPos)) {
            if (this.actionAgent.getState(typeQueue)) {
                let index = this.carriage.queue.getQueueIndex(this.role, typeFood)
                if (index > 0) await action.wait(index)
            }
            await action.run(this.closeToBuild, typeFood)
            if (!this.actionAgent.getState(typeQueue)) {
                this.actionAgent.pushState(typeQueue)
            }
        }
        if (rolePos.equals(doPos) && build.isEmpty()) {
            action.onTerminate()
            action.ok()
        }
    }
    private getClosePos(type: GoodsType) {
        if (type == GoodsType.FOOD) {
            return this.carriage.queue.getQueueFoodPos(this.role)
        } else if (type == GoodsType.DRINK) {
            return this.carriage.queue.getQueueDrinkPos(this.role)
        }
    }
    private checkOrderFood() {
        if (this.waitRecord.food) return false
        // if (!this.actionAgent.getAnim(PassengerLifeAnimation.SIT_EAT)) return false
        return this.carriage.canOrderFood()
    }
    private async toOrderFood(action: ActionNode) {
        await action.run(this.toQueueFood)
        await this.toBuildCommon(action, this.carriage.getOrderFood(), this.orderFood1)
    }
    private async toQueueFood(action: ActionNode) {
        action.onTerminate = () => { this.carriage.queue.popQueueFood(this.role) }
        this.carriage.queue.pushFoodQueue(this.role)
        await action.run(this.waitQueue, { typeFood: GoodsType.FOOD, typeQueue: StateType.DINING_FOOD_QUEUE })
        action.onTerminate()
        action.ok()
    }
    private async orderFood1(action: ActionNode) {
        action.run(this.rolePullRod, StateType.DINING_FOOD_PULL)
        await action.run(this.orderFood2)
        this.orderFood3()
        action.ok()
    }
    private async orderFood2(action: ActionNode) {
        let timeData = this.waitPullRod()
        await action.wait(timeData)
        let build = this.carriage.getOrderFood()
        let timeStart = build.getAnimTime('start')
        action.onTerminate = () => { build.onTerminate() }
        timeData.init(timeStart)
        build.onStart({ timeData })
        await action.wait(timeData)
        build.random(this.role.getID())
        let timeEnd = build.getAnimTime('end')
        timeData.init(6.15 - timeEnd)
        build.onScreenStart({ timeData })
        await action.wait(timeData)
        timeData.init(timeEnd)
        build.onScreenEnd({ timeData })
        await action.wait(timeData)
        let timeMake = build.getAnimTime('foodDown')
        let waitData = { bol: false }
        timeData.init(timeMake)
        build.onMake({ timeData })
        action.run(this.roleMoveToFood, { timeMake, waitData })
        await action.wait(timeData)
        if (!waitData.bol) await action.run(this.waitRoleMove, waitData)
        action.ok()
    }
    private orderFood3() {
        let build = this.carriage.getOrderFood()
        let bol = build.havePanzi()
        build.onOff()
        build.setCD(1)
        this.addFood({
            slot: bol ? 'panzi' : 'handsFood',
            food: build.getUrlById(),
            type: GoodsType.FOOD,
            isPanzi: bol,
            animMove: bol ? PassengerLifeAnimation.WALK_WITH_PLATE : PassengerLifeAnimation.WALK_WITH_HAND,
            foodObj: build.food,
        })
    }

    private updateFood() {
        let foodState = this.actionAgent.getState(StateType.HOLD_FOOD)
        let data = foodState.data
        let bol = data.isPanzi
        if (!bol) {
            data.slot = 'food'
        }
    }

    private async rolePullRod(action: ActionNode) {
        let type = action.params
        let agnt = this.actionAgent
        let time = agnt.getAnimTime(PassengerLifeAnimation.DINING_WORK)
        let timeData = new TimeStateData().init(time)
        action.onTerminate = () => { agnt.popState(type) }
        agnt.pushState(type, { timeData })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }
    private async roleMoveToDrink(action: ActionNode) {
        let { timeMake, waitData } = action.params
        await action.wait(timeMake * 0.83)
        let build = this.carriage.getOrderDrink()
        await action.run(this.moveToBuild, { build, paths: [{ index: 1 }] })
        waitData.bol = true
        action.ok()
    }
    private async roleMoveToFood(action: ActionNode) {
        let { timeMake, waitData } = action.params
        let baking = this.carriage.getBakingMachine()
        if (baking) {
            let build = baking
            let orgSpeed = this.moveAgent.getSpeed()
            this.moveAgent.setSpeed(orgSpeed * 1.5)
            action.onTerminate = () => { this.moveAgent.setSpeed(orgSpeed) }
            await action.wait(timeMake * 0.4)
            action.run(this.releaseFoodQueue)
            await action.run(this.moveToBuild, { build, paths: [{ index: 0 }] })
            action.onTerminate()
        } else {
            let build = this.carriage.getOrderFood()
            await action.wait(timeMake * 0.4)
            action.run(this.releaseFoodQueue)
            await action.run(this.moveToBuild, { build, paths: [{ index: 1 }] })
        }
        waitData.bol = true
        action.ok()
    }
    private async releaseFoodQueue(action: ActionNode) {
        await action.wait(1)
        let build = this.carriage.getOrderFood()
        build.setQueueLock(false)
    }
    private async waitRoleMove(action: ActionNode) {
        let waitData = action.params
        if (waitData.bol) {
            action.ok()
        }
    }
    private waitPullRod() {
        let waitMake = this.actionAgent.getAnimTime(PassengerLifeAnimation.DINING_WORK) * 0.4
        return new TimeStateData().init(waitMake)
    }

    private checkOrderDrink() {
        if (this.waitRecord.drink) return false
        // if (!this.actionAgent.getAnim(PassengerLifeAnimation.SIT_DRINK)) return false
        return this.carriage.canOrderDrink()
    }
    private async toOrderDrink(action: ActionNode) {
        await action.run(this.toQueueDrink)
        await this.toBuildCommon(action, this.carriage.getOrderDrink(), this.orderDrink1)
    }
    private async toQueueDrink(action: ActionNode) {
        action.onTerminate = () => { this.carriage.queue.popQueueDrink(this.role) }
        this.carriage.queue.pushDrinkQueue(this.role)
        await action.run(this.waitQueue, { typeFood: GoodsType.DRINK, typeQueue: StateType.DINING_DRINK_QUEUE })
        action.onTerminate()
        action.ok()
    }
    private async orderDrink1(action: ActionNode) {
        action.run(this.rolePullRod, StateType.DINING_DRINK_PULL)
        await action.run(this.orderDrink2)
        this.orderDrink3()
        action.ok()
    }
    private async orderDrink2(action: ActionNode) {
        let timeData = this.waitPullRod()
        await action.wait(timeData)
        let build = this.carriage.getOrderDrink()
        let drink = build.random(this.role.getID())
        let timeMake = build.getAnimTime(drink.drinkAnim)
        let waitData = { bol: false }
        action.onTerminate = () => { build.onTerminate() }
        timeData.init(timeMake)
        build.onMake({ timeData })
        action.run(this.roleMoveToDrink, { timeMake, waitData })
        await action.wait(timeData)
        if (!waitData.bol) await action.run(this.waitRoleMove, waitData)
        action.ok()
    }
    private orderDrink3() {
        let build = this.carriage.getOrderDrink()
        build.onOff()
        this.addFood({
            slot: "beizi",
            food: build.getUrlById(),
            type: GoodsType.DRINK,
            animMove: PassengerLifeAnimation.WALK_WITH_DRINK
        })
    }

    private getUseChair(isPlay = true) {
        return this.getUseBuild(this.carriage.getChairs(), isPlay)
    }
    private async toChairEat(action) {
        await this.toChairAct(action, this.toSitEat)
    }
    protected async toSitEat(action) {
        this.updateFood()
        let params = action.params
        let build = params.build as BuildObj
        let foodState = this.actionAgent.getState(StateType.HOLD_FOOD)
        if (foodState.data.isPanzi) {
            let table = this.carriage.getTableByChair(build.type)
            if (table) {
                let index = this.carriage.getPutIndex(table.type, build.type)
                table.addFood(index, foodState.data.foodObj.icon)
                this.removeFood()
                action.onTerminate = () => { table.removeFood(index) }
                params.holdFood = false
                params.eatAnim = PassengerLifeAnimation.SIT_EAT_BY_TABLE
                params.animMunch = PassengerLifeAnimation.SIT_MUNCH_BY_TABLE
                params.sitAnim = PassengerLifeAnimation.SIT_MUNCH_BY_TABLE
            } else {
                params.eatAnim = PassengerLifeAnimation.SIT_EAT_BY_PLATE
                params.animMunch = PassengerLifeAnimation.SIT_MUNCH_BY_PLATE
                params.sitAnim = PassengerLifeAnimation.SIT_MUNCH_BY_PLATE
            }
            this.actionAgent.pushState(StateType.HOLD_TABLEWARE, 'chazi_niupai')
        } else {
            params.eatAnim = PassengerLifeAnimation.SIT_EAT
            params.animMunch = PassengerLifeAnimation.SIT_MUNCH
            params.sitAnim = PassengerLifeAnimation.SIT_MUNCH
        }
        await action.run(this.sitEat, params)
        action.onTerminate && action.onTerminate()
        action.ok()
    }
    private async toChairDrink(action) {
        let params = action.params
        params.anim = PassengerLifeAnimation.SIT_DRINK
        params.animTaste = PassengerLifeAnimation.SIT_TASTE
        params.sitAnim = PassengerLifeAnimation.SIT_TASTE
        await this.toChairAct(action, this.sitDrink)
    }
    private async toChairAct(action, act) {
        let { build, fromType } = action.params
        let paths = this.carriage.getChairPaths(build, this.role, fromType)
        let pathOut = this.carriage.getChairOutPaths(build.type)
        await this.toBuildCommon(action, build, act, paths, pathOut)
    }

    private async toEat(action) {
        let build = this.getUseChair(false)
        if (build && this.actionAgent.getAnim(PassengerLifeAnimation.SIT_EAT)) {
            action.params = { build, fromType: DiningBuildType.ORDER_FOOD }
            await this.toChairEat(action)
        } else {
            await this.toStandEat(action)
        }
    }
    private async toDrink(action) {
        let build = this.getUseChair(false)
        if (build && this.actionAgent.getAnim(PassengerLifeAnimation.SIT_DRINK)) {
            action.params = { build, fromType: DiningBuildType.ORDER_DRINK }
            await this.toChairDrink(action)
        } else {
            await this.toStandDrink(action)
        }
    }
    private async toStandEat(action) {
        await action.run(this.toRandomPos)
        this.updateFood()
        let anim
        let animMunch
        let foodState = this.actionAgent.getState(StateType.HOLD_FOOD)
        if (foodState.data.isPanzi) {
            anim = PassengerLifeAnimation.STAND_EAT_BY_PLATE
            animMunch = PassengerLifeAnimation.STAND_MUNCH_BY_PLATE
            this.actionAgent.pushState(StateType.HOLD_TABLEWARE, 'chazi_niupai')
        } else {
            anim = PassengerLifeAnimation.STAND_EAT
            animMunch = PassengerLifeAnimation.STAND_MUNCH
        }
        await action.run(this.eat, { anim, animMunch })
        action.ok()
    }
    private async toStandDrink(action) {
        await action.run(this.toRandomPos)
        let anim = PassengerLifeAnimation.STAND_DRINK
        let animTaste = PassengerLifeAnimation.STAND_TASTE
        await action.run(this.drink, { anim, animTaste })
        action.ok()
    }

    //重写 搜索路径使用同步 以实现取走饮料/食物后马上就走
    protected async searchPath(action: ActionNode) {
        this.debug('searchPath dining')
        let moveAgent = this.moveAgent
        let position = action.params.pos || action.params
        let suc = moveAgent.searchPathSync(null, position)
        action.ok(suc)
    }
}