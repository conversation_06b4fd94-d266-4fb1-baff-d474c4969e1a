import { TimeStateData } from "./StateDataType"

export class ActionNode {
    public tree: ActionTree = null
    public func: Function = null
    public params: any = null
    public target: any = null
    public parent: ActionNode = null
    public onTerminate: Function = null

    public children: ActionNode[] = []

    public state: ActionState = ActionState.NONE

    public data: any = {} //节点状态

    public res: any = null //节点返回值

    public callback: Function = null

    public debugInfo: any = {}

    public context: Map<Function, any> = null

    public init(func, target?, params?, parentContext?: Map<any, any>) {
        this.target = target
        this.func = func
        this.params = params
        this.context = parentContext ? parentContext : new Map()
        return this
    }

    public setContext(params: Map<Function, any>, extend = true) {
        let context = new Map()
        if (extend) {
            this.context?.forEach((value, key) => {
                context.set(key, value)
            })
        }
        params.forEach((value, key) => {
            context.set(key, value)
        })
        this.context = context
    }

    public getContext(key: Function = this.func) {
        return this.context.get(key)
    }

    public addChild(node) {
        this.children.push(node)
        node.parent = this
    }

    public removeAllChild() {
        for (let child of this.children) {
            child.parent = null
        }
        this.children.length = 0
    }

    public ok(res?) {
        if (this.state == ActionState.OK) return
        this.res = res
        this.state = ActionState.OK
        this.terminateChildren()

        let actions = this.tree.runActions
        let index = actions.findIndex(a => a == this)
        let parent = this.parent
        if (index > 0) {
            actions.splice(index, 1)
        }
        while (parent) {
            if (parent.state != ActionState.OK) {
                if (parent.isAllChildOk()) { //当前全部子节点都完成后才轮到当前节点
                    actions.push(parent)
                }
                break
            }
            parent = parent.parent
        }
        this.callback && (this.callback(res))

        return res
    }

    public isOK() {
        return this.state == ActionState.OK
    }

    public terminateChildren() {
        for (let child of this.children) {
            child.terminate()
        }
    }

    private isAllChildOk() {
        return !this.children.find(child => child.state != ActionState.OK)
    }

    public add(func, params?, target?) {
        let action = this.tree.newAction(func, target || this.target, params, this.context)
        this.addChild(action)
        return action
    }

    public run(func: Function | ActionNode, params?, target?): Promise<any> {
        return this.tree.run(this, func, target, params)
    }

    public race(actionParams: [func: Function | ActionNode, params?: any, target?: any][]) {
        return this.tree.race(this, actionParams)
    }

    public all(actionParams: [func: Function, params?: any, target?: any][]) {
        return this.tree.all(this, actionParams)
    }

    public wait(time: number | TimeStateData) {
        return this.tree.wait(this, time)
    }

    public waitFunc(func: Function, params?: any, target?: any) {
        return this.tree.waitFunc(this, func, params, target)
    }

    public terminate() {
        if (this.state == ActionState.OK) return
        if (this.onTerminate) {
            this.onTerminate()
        }
        this.state = ActionState.OK
        this.terminateChildren()
    }
}

export enum ActionState {
    NONE,
    OK,
    FAIL,
    RUNNING,
    WAIT,
    TERMINATE,
}

function wait(action, dt) {
    let time = action.params
    let timeData = action.data.timeData
    if (!timeData) {
        timeData = time
        if (cc.js.isNumber(time)) {
            timeData = new TimeStateData().init(time)
        }
        action.data.timeData = timeData
    }
    if (timeData.update(dt)) {
        action.ok()
    }
}

function loop(action) {
}

export default class ActionTree {
    public root: ActionNode = null
    public runActions: ActionNode[] = []
    private target: any = null
    public static wait: Function = wait
    public pause: boolean = false

    public init(target?) {
        this.target = target
        return this
    }

    public start(func, target?, params?) {
        this.root = null
        return this.run(this.root, func, target, params)
    }

    public newAction(func, target?, params?, parentContext?: Map<any, any>) { //todo pool
        let action = new ActionNode().init(func, target, params, parentContext)
        action.tree = this
        return action
    }

    public async run(parent, func, target?, params?) {
        if (func instanceof ActionNode) {
            return this.runAction(func, parent)
        }
        else {
            return new Promise(r => {
                let parentContext = parent ? parent.context : undefined
                let action = this.newAction(func, target, params, parentContext)
                action.callback = r
                this.runAction(action, parent)
            })
        }
    }

    public async race(parent, actionParams) {
        let actions = actionParams.map(([func, params, target]) => {
            if (func instanceof ActionNode) return func
            let action = this.newAction(func, target, params, parent ? parent.context : undefined)
            parent.addChild(action)
            return action
        })

        let promises = actions.map((action) => {
            return new Promise(r => {
                action.callback = r
                this.runAction(action, parent)
            })
        })

        let res = await Promise.race(promises)
        actions.forEach(action => {
            action.terminate()
        })
        return res
    }

    public async all(parent, actionParams) {
        let actions = actionParams.map(([func, params, target]) => {
            let action = this.newAction(func, target, params, parent ? parent.context : undefined)
            parent.addChild(action)
            return action
        })
        let promises = actions.map((action) => {
            return new Promise(r => {
                action.callback = r
                this.runAction(action, parent)
            })
        })

        return await Promise.all(promises)
    }

    public wait(parent: ActionNode, time) {
        return this.run(parent, wait, null, time)
    }

    public async waitFunc(parent: ActionNode, func, params?, target?) {
        let loopAction = this.newAction(loop)
        func.call(target, params).then(() => {
            if (!loopAction.isOK()) {
                loopAction.ok()
            }
        })
        return new Promise(r => {
            loopAction.callback = r
            this.runAction(loopAction, parent)
        })
    }

    public runAction(action: ActionNode, parent: ActionNode = this.root) {
        if (action.state != ActionState.NONE) return twlog.error("action.state != ActionState.NONE")
        if (parent && parent.isOK()) {
            return
        }
        if (!parent || action.parent != parent) {
            this.addAction(action, parent)
        }
        let index = this.runActions.findIndex(a => a == parent)
        if (index > -1) {
            this.runActions[index] = action
        }
        else {
            this.runActions.push(action)
        }
        this.exec(action, 0)
    }

    public addAction(action: ActionNode, parent: ActionNode = this.root) {
        if (!parent) {
            this.root = action
        }
        else {
            parent.addChild(action)
        }
    }

    // private nextAction() {
    //     let {value, done} = this.genFunc().next()
    //     if (!done) {
    //         this.curAction = value
    //     }
    //     else {
    //         this.clean()
    //     }
    // }

    public update(dt) {
        if (this.pause) return
        const actions = this.runActions
        for (let i = actions.length - 1; i >= 0; i--) {
            let action = actions[i]
            if (!action) {
                actions.splice(i, 1)
                continue
            }
            if (action.state == ActionState.RUNNING) { //如果是重新执行节点，证明之前的子节点都没用了，直接清掉，防止无限创建子节点
                action.removeAllChild()
            }
            else if (action.state == ActionState.OK) { //被标记为完成的就不继续跑了
                actions.splice(i, 1)
                continue
            }
            this.exec(action, dt)
        }
    }

    private exec(action: ActionNode, dt) {
        action.state = ActionState.RUNNING as any
        action.target = action.target || action.parent?.target || this.target
        let func = action.func
        func.call(action.target, action, dt)
    }

    public terminate() {
        if (this.root) {
            this.root.terminate()
        }
        this.clean()
    }

    public clean() {
        this.runActions.length = 0
        this.root = null
    }

    // public fail() {
    //     let action = this.curAction
    //     while (action) {
    //         action.onTerminate && action.onTerminate()
    //         action = action.parent
    //     }
    //     this.clean()
    // }

    // public clean() {
    //     this.root = null
    //     this.curAction = null
    //     this.genFunc = null
    // }

    // public isEmpty() {
    //     return !!this.curAction
    // }
}
