import { Carriage<PERSON>, RoleGuideActionType, SpeedUpType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { gameHelper } from "../../common/helper/GameHelper"
import BuildObj from "../train/common/BuildObj"
import PassengerModel from "./PassengerModel"
import StateObj from "./StateObj"
import { StateType } from "./StateEnum"
import DormAction from "./themeActions/DormAction"
import CarriageModel from "../train/common/CarriageModel"
import Dorm2Action from "./themeActions/Dorm2Action"
import BaseAction from "./themeActions/BaseAction"
import { USE_BUILD_CD, USE_CARRIAGE_CD } from "./themeActions/ActionCfg"
import EngineAction from "./themeActions/EngineAction"
import { cfgHelper } from "../../common/helper/CfgHelper"
import TrainHeadAction from "./themeActions/TrainHeadAction"
import DiningAction from "./themeActions/DiningAction"
import Dorm3Action from "./themeActions/Dorm3Action"
import Dorm4Action from "./themeActions/Dorm4Action"
import WaterAction from "./themeActions/WaterAction"
import BathAction from "./themeActions/BathAction"
import DanceHallAction from "./themeActions/DanceHallAction"
import CarrriageTopAction from "./themeActions/CarrriageTopAction"

export default class PassengerActionModel {
    public model: PassengerModel = null
    private states: StateObj<StateType>[] = []

    public action: any = null

    public fromCarriage: CarriageModel = null
    public nextCarriage: CarriageModel = null
    public targetCarriage: CarriageModel = null

    public idleAnim: string = null
    private idleAnimMap: Map<StateType, string> = new Map()
    private moveAnimMap: Map<StateType, string> = new Map()

    public exitToTop: boolean = false
    public enterFromTop: boolean = false

    public useBuildRecords: { build: BuildObj, index?: number, cd: number }[] = []

    public carriageCds: { id: number, cd: number }[] = []

    private _isPause: boolean = false

    public guideType: RoleGuideActionType = null

    public lastCheckEventTime: { [ket: string]: number } = {} //记录上次check事件的时间点

    public lockOutput: { [ket: string]: number } = {}

    public trainActivityMoveTarget: number = null //车厢活动聚集地

    //
    private lastMoveData: any = null

    public init(model: PassengerModel) {
        this.model = model
        this.initAction();
        this.initListener()
    }

    private initAction() {
        let cls = this.getAction()
        if (this.action) {
            this.action.clean()
        }
        this.action = new cls().init(this.model)
    }

    public getAction() {
        //todo 看看能不能自动
        let id = this.model.carriage.getID()
        if (id == CarriageID.HEAD) return TrainHeadAction
        if (!this.model.inCarriage()) {
            return CarrriageTopAction
        }
        if (id == CarriageID.DORM) return DormAction
        else if (id == CarriageID.DORM2) return Dorm2Action
        else if (id == CarriageID.DORM3) return Dorm3Action
        else if (id == CarriageID.DORM4) return Dorm4Action
        else if (id == CarriageID.ENGINE) return EngineAction
        else if (id == CarriageID.DINING) return DiningAction
        else if (id == CarriageID.WATER) return WaterAction
        else if (id == CarriageID.BATHROOM) return BathAction
        else if (id == CarriageID.DANCEHALL) return DanceHallAction
        return BaseAction
    }

    private initListener() {
        eventCenter.on(EventType.TRAIN_ACTIVITY_GATHER, (target) => { this.trainActivityMoveTarget = target }, this)
    }

    public update(dt) {
        if (this._isPause) return

        dt = gameHelper.world.transDT(dt, SpeedUpType.S5)

        this.action && this.action.update(dt);

        let useBuildRecords = this.useBuildRecords
        for (let i = useBuildRecords.length - 1; i >= 0; i--) {
            let record = useBuildRecords[i]
            record.cd -= dt
            if (record.cd <= 0) {
                useBuildRecords.splice(i, 1)
            }
        }

        this.updateByWorldTime(dt)
    }

    private updateByWorldTime(dt) {
        let carriageCds = this.carriageCds
        for (let i = carriageCds.length - 1; i >= 0; i--) {
            let info = carriageCds[i]
            info.cd -= dt
            if (info.cd <= 0) {
                carriageCds.splice(i, 1)
            }
        }
    }

    //--------------------public接口-------------
    get state() {
        return this.states.last()
    }

    get moveAgent() {
        return this.model?.moveAgent
    }

    public clearTrainActivityMoveTarget() { this.trainActivityMoveTarget = null }

    public pushState(type: StateType, data = null) {
        let state = this.states.find(s => s.type == type)
        if (state) {
            twlog.error("pushState重复", this.model.id, type)
            return
        }
        state = new StateObj<StateType>().init(type, data)
        this.states.push(state);
        eventCenter.emit(EventType.PASSENGER_STATE_CHANGE, this.model, state);
    }

    public popState(type) {
        this.states.remove("type", type)
        eventCenter.emit(EventType.PASSENGER_STATE_CHANGE, this.model, type)
    }

    public getStates() {
        return this.states
    }

    public getState(type: StateType) {
        return this.states.find(s => s.type == type)
    }

    public clean() {
        this.states = []
        if (this.action) {
            this.action.clean()
        }
    }

    //--------------------action--------------

    public getScale() {
        let map = this.model.getMap()
        let pos = this.model.getPosition()
        let scale = map.getScale(pos)
        return scale
    }

    public getAnim(name) {
        return cfgHelper.getRoleAnim(this.model.id, name)
    }

    public getAnimTime(name) {
        return cfgHelper.getRoleAnimTime(this.model.id, name)
    }

    public getAnimsTime(anims) {
        if (!Array.isArray(anims)) {
            anims = [anims]
        }
        return cfgHelper.getRoleAnimsTime(this.model.id, anims)
    }

    public restart() {
        this.resume()
        this.action?.restart()
    }

    public pause() {
        this._isPause = true
    }

    public resume() {
        this._isPause = false
    }

    public isPause() {
        return this._isPause
    }

    public addUseBuildRecord(build, index: number) {
        let cd = ut.randomRange(USE_BUILD_CD[0], USE_BUILD_CD[1])
        let useBuildRecords = this.useBuildRecords
        useBuildRecords.push({ build, index, cd })
    }

    public isCdBuildRecord(build, i: number) {
        let record = this.useBuildRecords.find(record => record.build == build && (record.index == -1 || i == record.index))
        return record != null
    }

    public clearUseBuildRecord() {
        let useBuildRecords = this.useBuildRecords
        useBuildRecords.length = 0
    }

    public addCarriageCD(carriage: CarriageModel) {
        let id = carriage.getID()
        let cd = gameHelper.world.toRealSecond(ut.randomRange(USE_CARRIAGE_CD[0], USE_CARRIAGE_CD[1]) * ut.Time.Hour)
        this.carriageCds.push({ id, cd })
    }

    public updateLastEventTime(type) {
        let passDay = gameHelper.world.getPassDay()
        let eventInfo = this.model.getLastEventInfo(type)
        if (!eventInfo) return
        let nowEventTime = passDay + eventInfo.time
        this.lastCheckEventTime[type] = nowEventTime
        return nowEventTime
    }

    /**
     * 停止移动，当前有在移动的话
     * @returns 
     */
    public stopMove() {
        const state = this.getState(StateType.MOVE)
        if (!state) return
        this.popState(StateType.MOVE)
        this.lastMoveData = state.data || {}
        this.model.moveAgent.pause = true
    }

    /**
     * 重新开始移动
     */
    public restartMove() {
        const data = this.lastMoveData
        if (data) {
            this.model.moveAgent.pause = false
            this.pushState(StateType.MOVE, data)
            this.lastMoveData = null
        }
    }

    public setIdleAnim(state: StateType, name: string) {
        this.idleAnimMap.set(state, name)
    }

    public setMoveAnim(state: StateType, name: string) {
        this.moveAnimMap.set(state, name)
    }

    public getIdleAnim(state: StateType) {
        return this.idleAnimMap.get(state)
    }

    public getMoveAnim(state: StateType) {
        return this.moveAnimMap.get(state)
    }

}