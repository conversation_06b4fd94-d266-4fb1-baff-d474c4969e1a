import { ConditionType, GoodsObjectType, LifeSkillEffectType, LifeSkillTarget, MarkNewType, PassengerAttr, TalentAttrType, ValueType } from "../../common/constant/Enums";
import PassengerModel from "./PassengerModel";
import ActionTree from "./ActionTree";
import EventType from "../../common/event/EventType";
import LifeSkill from "./Lifeskill";
import PassengerStarOutObj from "./PassengerStarOutputObj";
import { Msg } from "../../../proto/msg-define";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import Monster from "../battle/Monster";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { CharacterFragCfg, CharacterSkinCfg, EquipCfg } from "../../common/constant/DataType";
import ConditionObj from "../common/ConditionObj";
import PassengerTalent from "./PassengerTalent";
import { Equip } from "../equip/EquipModel";
import { TrainDailyTaskItem } from "../trainDailyTask/TrainDailyTaskModel";

export class CharacterSkin {
    private id: string
    private cfg: CharacterSkinCfg

    public init(id: string) {
        this.id = id
        this.initCfg(id)
        return this
    }
    private initCfg(id: string) {
        this.cfg = assetsMgr.getJsonData<CharacterSkinCfg>("CharacterSkin", id)
    }

    public getId() { return this.id }
    public get characterId() { return this.cfg?.characterId }
    public get index() { return this.cfg?.index }
    public get spine() { return this.cfg?.spine }
    public get skin() { return this.cfg?.skin }
    public get effects() { return this.cfg?.effect || [] }
    public get buyCost() { return this.cfg?.buyCost }

    public getAttr(effectType: LifeSkillEffectType, valueType: ValueType) {
        let effect = this.effects.find(e => e.type == effectType && e.valueType == valueType)
        return effect?.value || 0
    }
}

export class CharacterFrag {
    public id: number
    private num: number
    private cfg: CharacterFragCfg


    public init(id: number, num: number) {
        this.id = id
        this.num = num
        this.initCfg(id)
        return this
    }

    private initCfg(id) {
        this.cfg = assetsMgr.getJsonData<CharacterFragCfg>('CharacterFrag', id)
    }

    public getCfg() { return this.cfg }
    public getNum() { return this.num }
    public addNum(val: number) {
        this.num += val
    }
    public get quality() { return this.cfg?.quality }
    public get characterId() { return this.cfg?.characterId }
    public get name() { return this.cfg?.name }
    public get content() { return this.cfg?.content }
    public get icon() { return this.cfg?.icon }

    public markNew() { gameHelper.new.pushNew(MarkNewType.PROP, [this.id]) }

    public removeNew(type: MarkNewType) { gameHelper.new.removeNew(type, [this.id]) }

    public isNew(type: MarkNewType) { return gameHelper.new.isNew(type, [this.id]) }

}

/**
 * 角色管理
 */
@mc.addmodel('passenger', 100)
export default class PassengerMgr extends mc.BaseModel {

    private passengers: PassengerModel[] = []

    public debugInfo: boolean = false;

    public data: { passengers: proto.IPassengerInfo[], starOutput: number, resonances?: number[], resetTime: number, skins: { [k: string]: proto.IPassengerSkinData } | null, frag: { [k: string]: number } | null } = null
    private stopRefMap: { [key: string]: number } = {}

    public starOutputObj: PassengerStarOutObj = null

    private skinList: { [id: string]: CharacterSkin[] } = {}

    private fragList: CharacterFrag[] = []

    public init() {
        let passengersData = this.data.passengers || []
        for (let passengerData of passengersData) {
            let passenger = new PassengerModel().init(passengerData);
            passenger.updateLocation()
            this.passengers.push(passenger);
        }

        this.starOutputObj = new PassengerStarOutObj().init(this.data.starOutput)

        for (let cid in this.data.skins) {
            let data = this.data.skins[cid].list
            if (!this.skinList[cid]) this.skinList[cid] = []
            for (let val of data) {
                this.skinList[cid].push(new CharacterSkin().init(`${cid}-${val.index}`))
            }
        }

        for (let id in this.data.frag) {
            let num = this.data.frag[id]
            this.fragList.push(new CharacterFrag().init(+id, num))
        }

        eventCenter.on(EventType.TRAIN_DAILY_TASK_START, this.onTrainDailyTaskStart, this)
        eventCenter.on(EventType.TRAIN_DAILY_TASK_DONE, this.onTrainDailyTaskDone, this)
    }

    public updateInfo(data: { passengers: proto.IPassengerInfo[], starOutput: number }) {
        let passengerDatas = data?.passengers || []
        for (let data of passengerDatas) {
            let passenger = this.getPassenger(data.id)
            if (passenger) {
                passenger.updateInfo(data)
            }
        }
        this.starOutputObj.updateOfflineOutput(data?.starOutput)
    }

    addPassenger(id: number) {
        let passModel = this.getPassenger(id);
        if (!passModel) {
            passModel = new PassengerModel().initById(id)
            this.skinList[String(id)] = []
            this.skinList[String(id)].push(new CharacterSkin().init(`${id}-${1}`))
            passModel.markNew()
            passModel.updateLocation()
            this.passengers.push(passModel)
            eventCenter.emit(EventType.UNLOCK_PASSENGER, id)
        }
        return passModel;
    }


    deletePasenger(id: number) {
        for (let i = 0; i < this.passengers.length; i++) {
            let model = this.passengers[i];
            if (model.getID() == id) {
                this.passengers.splice(i, 1);
            }
        }
    }

    getPassenger(id: number) {
        for (let i = 0; i < this.passengers.length; i++) {
            let model = this.passengers[i]
            if (model.getID() == id) return model
        }
        return null;
    }

    public getPassengers() {
        return this.passengers
    }

    public getFrags() {
        return this.fragList
    }

    public changeFrag(id: number, count: number, isEmit: boolean = true): number {
        let frag = this.getFragById(id)
        if (!frag) {
            frag = new CharacterFrag().init(id, count)
            frag.markNew()
            this.fragList.push(frag)
        } else {
            frag.addNum(count)
        }
        if (frag.getNum() <= 0) {
            this.delProp(id)
        }
        if (isEmit) {
            eventCenter.emit(EventType.CHANGE_NUM_PROP, id, count)
        }
        return frag.getNum();
    }
    public delProp(id: number) {
        this.fragList.remove("id", id)
    }

    public getFragById(id: number) {
        return this.fragList.find(p => p.id == id)
    }

    public getFragCountById(id: number) {
        let prop = this.getFragById(id)
        if (!prop) return 0
        return prop.getNum()
    }

    update(dt: number) {
        for (let passenger of this.passengers) {
            passenger.update(dt)
        }
        this.starOutputObj.update(dt)
    }

    public startAction() {
        for (let passenger of this.passengers) {
            passenger.startAction()
        }
    }

    public cleanAction() {
        for (let passenger of this.passengers) {
            passenger.cleanAction()
        }
    }

    public stopRole(id: number) {
        if (this.stopRefMap[id]) {
            this.stopRefMap[id]++
            return true
        }
        let role = this.getPassenger(id)
        if (!role) return
        let actionAgent = role.actionAgent
        if (!actionAgent) return
        if (!role.isMoving()) {
            actionAgent.pause()
            this.stopRefMap[id] = 1
        } else {
            let curAction = actionAgent["action"]
            if (!curAction) return
            let actionTree: ActionTree = curAction["actionTree"]
            if (!actionTree) return
            actionTree.terminate()
            this.stopRefMap[id] = 1
        }
        return true
    }

    public terminateRole(id: number) {
        let role = this.getPassenger(id)
        if (!role) return
        let actionAgent = role.actionAgent
        if (!actionAgent) return
        let curAction = actionAgent["action"]
        if (!curAction) return
        let actionTree: ActionTree = curAction["actionTree"]
        if (!actionTree) return
        actionTree.terminate()
        return true
    }

    public restartRole(id: number) {
        let role = this.getPassenger(id)
        this.stopRefMap[id] = 0
        if (!role) return
        let actionAgent = role.actionAgent
        if (!actionAgent) return
        let curAction = actionAgent["action"]
        if (!curAction) return
        actionAgent.restart()
    }

    public startRole(id: number) {
        if (this.stopRefMap[id]) {
            this.stopRefMap[id]--
            if (this.stopRefMap[id]) {
                return
            }
        }
        let role = this.getPassenger(id)
        if (!role) return
        let actionAgent = role.actionAgent
        if (!actionAgent) return
        let curAction = actionAgent["action"]
        if (!curAction) return
        let actionTree: ActionTree = curAction["actionTree"]
        if (!actionTree) return
        if (actionTree.root) {
            actionAgent.resume()
        } else {
            actionTree.start(curAction.start)
        }
    }


    public getAttr(attr: PassengerAttr) {
        let sum = 0
        for (let passenger of this.passengers) {
            sum += passenger.getAttr(attr)
        }
        return sum
    }

    public get isResonacing() {
        return gameHelper.resonance.getResonaceLv() > 0
    }

    public isInResonace(roleId: number) {
        return gameHelper.resonance.isResonance(roleId)
    }

    public getResonaceLv() {
        return gameHelper.resonance.getResonaceLv()
    }

    public convertToResonances(roles: PassengerModel[]) {
        roles = roles.map(r => r.toResonance())
        return roles
    }

    public getSkinList() {
        return this.skinList
    }

    public getSkinsById(id) {
        return this.skinList[id]
    }

    public async unlockSkin(id: string) {
        let msg = new proto.C2S_UnlockSkinMessage({ id })
        const res = await gameHelper.net.request(Msg.C2S_UnlockSkinMessage, msg, true)
        const { code } = proto.S2C_UnlockSkinMessage.decode(res)
        if (code == 0) {
            this.onUnlockSkin(id)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public onUnlockSkin(id: string) {
        let cfg = assetsMgr.getJsonData<CharacterSkinCfg>("CharacterSkin", id)
        let cid = cfg.characterId
        this.skinList[cid].push(new CharacterSkin().init(id))
        gameHelper.deductConditions(gameHelper.toConditions(cfg.buyCost))
        //购买并穿戴
        let passenger = this.getPassenger(Number(cid))
        if (!!passenger) {
            passenger.setSkin(cfg.index)
        }
    }


    public async fragLvUp(id: number, num: number) {
        let msg = new proto.C2S_FragMergeMessage({ id, num })
        const res = await gameHelper.net.request(Msg.C2S_FragMergeMessage, msg, true)
        const { code, rewards } = proto.S2C_FragMergeMessage.decode(res)
        if (code == 0) {
            let frag = this.getFragById(id)
            let rate = cfgHelper.getMiscData('fragUp')[frag.quality - 1]
            this.changeFrag(id, -num * rate)
            await gameHelper.grantRewardAndShowUI(gameHelper.toConditions(rewards))
            eventCenter.emit(EventType.CHANGE_NUM_PROP)
            return true
        }
        viewHelper.showNetError(code)
        return false
    }

    public transPassenger(fromId: number, toId: number) {
        let fromRole = this.getPassenger(fromId)
        let toRole = this.getPassenger(toId)

        let fromLv = fromRole.getLevel()
        let fromEquips = fromRole.getEquips()
        let fromTalents = fromRole.talents

        let toLv = toRole.getLevel()
        let toEquips = toRole.getEquips()
        let toTalents = toRole.talents

        let trans = (role: PassengerModel, lv: number, orgEquips: Equip[], orgTalents: PassengerTalent[]) => {
            for (let orgEquip of orgEquips) {
                gameHelper.equip.delEquip(orgEquip.getUid())
                let cfg = assetsMgr.getJson<EquipCfg>("Equip").datas.find(e => e.index == orgEquip.index && e.roleId == role.id)
                let id = cfg.id
                let effects = orgEquip.effects.map((e) => {
                    let attr = cfgHelper.getEquipEffectAttrByLv(cfg.id, e.cfg, e.level)
                    return { id: e.id, level: e.level, attr }
                })
                let data = { uid: orgEquip.getUid(), level: orgEquip.getLv(), id, effects }
                let equip = gameHelper.equip.addEquip(id, data)
                equip.used = true
            }

            let talents = []
            for (let orgTalent of orgTalents) {
                let id = orgTalent.id
                let level = orgTalent.level
                let talent = new PassengerTalent().init({ id, level }, role)
                talents.push(talent)
            }

            role["level"] = lv
            role.talents = talents
            role.initSkills()
        }

        trans(toRole, fromLv, fromEquips, fromTalents)
        trans(fromRole, toLv, toEquips, toTalents)

        eventCenter.emit(EventType.PASSENGER_TRANS)
    }

    public onTrainDailyTaskStart(task: TrainDailyTaskItem) {
        for (let id of task.roles) {
            let passenger = this.getPassenger(id)
            passenger.onTrainDailyTaskStart()
        }
    }

    public onTrainDailyTaskDone(task: TrainDailyTaskItem) {
        for (let id of task.roles) {
            let passenger = this.getPassenger(id)
            passenger.onTrainDailyTaskDone()
        }
    }
}
