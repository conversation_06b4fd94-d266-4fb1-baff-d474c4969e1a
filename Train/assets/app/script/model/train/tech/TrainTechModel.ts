import { util } from "../../../../core/utils/Utils"
import { Msg } from "../../../../proto/msg-define"
import { TrainTechCfg, TrainTechLevelCfg } from "../../../common/constant/DataType"
import { BuildAttr, TrainTechType } from "../../../common/constant/Enums"
import EventType from "../../../common/event/EventType"
import { gameHelper } from "../../../common/helper/GameHelper"
import { viewHelper } from "../../../common/helper/ViewHelper"

class TrainTech {
    public type: TrainTechType
    public level: number

    private cfg: TrainTechCfg

    public effectValue: number = 0
    public effectTarget: number = 0
    public effectType: number = 0

    public init(id: string, level: number) {
        const cfg = assetsMgr.getJsonData<TrainTechCfg>("TrainTech", id)
        if (!cfg) return
        this.level = level
        this.cfg = cfg
        this.initEffect()
        return this
    }

    private initEffect() {
        let sum = 0
        for (let i = 1; i <= this.level; i++) { 
            const cfg = assetsMgr.getJsonData<TrainTechLevelCfg>("TrainTechLevel", `${this.cfg.id}-${i}`)
            sum += cfg.effect.num
            this.effectTarget = cfg.effect.target
            this.effectType = cfg.effect.type
        }
        this.effectValue = sum
    }

    public getEffectValue() {
        let sum = 0
        for (let i = 1; i <= this.level; i++) { 
            const cfg = assetsMgr.getJsonData<TrainTechLevelCfg>("TrainTechLevel", `${this.cfg.id}-${i}`)
            sum += cfg.effect.num
        }
        return sum
    }

    public levelUp() {
        return this.init(this.cfg.id, this.level + 1)
    }
}

@mc.addmodel('trainTech', 101)
export default class TrainTechModel extends mc.BaseModel {
    public data: proto.ITechData = null

    private _dataMap: Map<string, TrainTech> = new Map()

    public init() {
        for (const key in this.data.data) {
            this._dataMap.set(key, new TrainTech().init(key, this.data.data[key]))
        }
    }

    public getTechLevel(id: string) { return this._dataMap.get(id)?.level || 0 }

    public checkPre(id: string): boolean {
        const cfg = assetsMgr.getJsonData<TrainTechCfg>("TrainTech", id)
        if (!cfg) return false
        const pre = cfg.pre
        if (!pre?.length) return true
        return pre.every(p => this.getTechLevel(p) > 0)
    }


    @util.addLock
    public async upgrade(id: string) {
        if (!this.checkPre(id)) return void viewHelper.showAlert("common_buildType_unlock")
        const targetLevel = this.getTechLevel(id) + 1
        const cfg = assetsMgr.getJsonData<TrainTechLevelCfg>("TrainTechLevel", `${id}-${targetLevel}`)
        if (!cfg) return
        const cost = gameHelper.toConditions(cfg.cost)
        const failList = []
        if (!gameHelper.checkConditions(cost, failList)) {
            return void gameHelper.showFailTips(failList)
        }
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_TrainTechUpgradeMessage, { id })
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        gameHelper.deductConditions(cost)
        this._dataMap.set(id, new TrainTech().init(id, targetLevel))
        eventCenter.emit(EventType.TRAIN_TECH_UPGRADE, id)
    }

    public getCarriageRoleCnt(carriageId: number) {
        return this.getEffectValue(TrainTechType.CARRIAGE_ROLE_CNT, carriageId)
    }

    public getTrainSpeed() {
        return this.getEffectValue(TrainTechType.TRAIN_SPEED)
    }

    public getTrainDailyTask() {
        return this.getEffectValue(TrainTechType.TRAIN_DAILY_TASK)
    }

    public getDeepExplore() {
        return this.getEffectValue(TrainTechType.DEEP_EXPLORE)
    }

    public getTimeMachine() {
        return this.getEffectValue(TrainTechType.TIME_MACHINE)
    }

    public getTrainLoad() {
        return this.getEffectValue(TrainTechType.TRAIN_LOAD)
    }

    public getShip() {
        return this.getEffectValue(TrainTechType.SHIP)
    }

    public getCarriageAttr(carriageId: number, attr: BuildAttr) {
        switch (attr) {
            case BuildAttr.STAR:
                return this.getEffectValue(TrainTechType.STAR_DUST, carriageId)
            case BuildAttr.HEART:
                return this.getEffectValue(TrainTechType.HERT, carriageId)
            case BuildAttr.ELECTRICITY:
                return this.getEffectValue(TrainTechType.ELECTRIC, carriageId)
            case BuildAttr.WATER:
                return this.getEffectValue(TrainTechType.WATER, carriageId)
            case BuildAttr.VITALITY:
                return this.getEffectValue(TrainTechType.VITALITY, carriageId)
        }
        return 0
    }

    public getCarriageAttrPercent(carriageId: number, attr: BuildAttr) {
        let ret = 0
        switch (attr) {
            case BuildAttr.STAR:
                ret = this.getEffectValue(TrainTechType.STAR_DUST_PERCENT, carriageId); break;
            case BuildAttr.HEART:
                ret = this.getEffectValue(TrainTechType.HERT_PERCENT, carriageId); break;
            case BuildAttr.ELECTRICITY:
                ret = this.getEffectValue(TrainTechType.ELECTRIC_PERCENT, carriageId); break;
            case BuildAttr.WATER:
                ret = this.getEffectValue(TrainTechType.WATER_PERCENT, carriageId); break;
            case BuildAttr.VITALITY:
                ret = this.getEffectValue(TrainTechType.VITALITY_PERCENT, carriageId); break;
        }
        return ret / 100
    }

    public getEffectValue(type: TrainTechType, target?) {
        let sum = 0
        this._dataMap.forEach(tech => {
            if (tech.effectType == type && tech.effectTarget == target) {
                sum += tech.effectValue
            }
        })
        return sum
    }
}