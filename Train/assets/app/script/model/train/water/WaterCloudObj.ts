import { Build<PERSON>ttr, RoleDir } from "../../../common/constant/Enums"
import { cfgHelper } from "../../../common/helper/CfgHelper"
import { gameHelper } from "../../../common/helper/GameHelper"
import { mapHelper } from "../../../common/helper/MapHelper"
import MoveModel from "../../map/MoveModel"
import ActionTree, { ActionNode } from "../../passenger/ActionTree"
import { TimeStateData } from "../../passenger/StateDataType"
import StateObj from "../../passenger/StateObj"
import { WATER_OUTPUT_COUNT } from "../../passenger/themeActions/ActionCfg"
import BuildObj from "../common/BuildObj"
import WaterModel from "./WaterModel"

export enum WaterCloudObjState {
    AIR_BIRTH,
    TABLE_BIRTH,
    MOVE,
    RUN,
    ENTER_SAUNA,
    SAUNA,
    OUTPUT,
    EXIT_HIDE,
    EXIT,
}

const ActionCfg = {
    [WaterCloudObjState.RUN]: [10, 15], //跑步
    [WaterCloudObjState.SAUNA]: [10, 15], //桑拿
}

const EXIT_TIME = 120 //x秒后消失
const OUTPUT_TIME = 60 //产出间隔
export const MAX_CLOUD = 3 //最大数量

export default class WaterCloudObj {

    public id: string = ''

    public states: StateObj<WaterCloudObjState>[] = []

    private time: number = 0 //出生到现在的持续时间
    protected outputTime: number = 0 //产出cd

    protected _actionTree: ActionTree = null
    protected get actionTree() {
        if (!this._actionTree) {
            this._actionTree = new ActionTree().init(this)
        }
        return this._actionTree
    }

    public carriage: WaterModel = null
    private get map() {
        return this.carriage.getMap()
    }

    private moveAgent: MoveModel = null
    public dir: RoleDir = RoleDir.NONE

    public lockOutput: number = 0
    private genOutputTime: number = 0

    constructor() {
        this.id = ut.uid()
    }

    public init(carriage) {
        this.carriage = carriage
        this.moveAgent = new MoveModel().initMapInfo(carriage.getMap())
        this.moveAgent.setSpeed(200 + ut.random(-30, 30))
        return this
    }

    public startByTable() {
        this.actionTree.start(async (action) => {
            await action.run(this.birthByTable)
            await action.run(this.loop)
            action.ok()
        })
    }

    public startByAir() {
        this.actionTree.start(async (action) => {
            await action.run(this.birthByAir)
            await action.run(this.loop)
            action.ok()
        })
    }

    public async birthByTable(action: ActionNode) {
        let type = WaterCloudObjState.TABLE_BIRTH
        let build = this.carriage.getTable()
        action.onTerminate = () => {
            this.popState(type)
        }
        let time = 0.5
        let timeData = new TimeStateData().init(time)
        this.pushState(type, { timeData, build, mountPoint: 'yun', loop: true })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    public async birthByAir(action: ActionNode) {
        let type = WaterCloudObjState.AIR_BIRTH
        let anim = "aniAir"
        action.onTerminate = () => {
            this.popState(type)
        }
        let time = this.getAnimTime(anim)
        let timeData = new TimeStateData().init(time)
        this.pushState(type, { timeData, anim })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    public async loop(action: ActionNode) {
        let cfgs = [
            { act: this.toRandomPos, weight: 300 }, //随便走走
            { act: this.toUseBuild, check: this.checkUseBuild, weight: 100 }, //去使用设施
            { act: this.toOutput, check: this.checkOutput, weight: 100 }, //去产出
            { act: this.toExit, check: this.checkExit, weight: 150 }, //离开车厢
        ]
        await this.runRandomAct(action, cfgs)
        await action.wait(ut.random(2, 4))
    }

    private checkUseBuild() {
        return this.checkRun() || this.checkSauna()
    }

    private checkRun() {
        return this.checkBuildPlay(this.carriage.getRun()) && this.getUnlockOutput() > 0
    }

    private checkSauna() {
        return this.checkBuildPlay(this.carriage.getSauna()) && this.getUnlockOutput() > 0
    }

    private checkOutput() {
        return this.outputTime > OUTPUT_TIME && this.getUnlockOutput() > 5 && gameHelper.world.getNextHourSurplusWorldTime() <= 10 * ut.Time.Second
    }

    public checkExit() {
        return this.time > EXIT_TIME
    }

    private getUnlockOutput(isAvg: boolean = false) {
        let lockOutput = 0
        let count = 0
        for (let cloud of this.carriage.clouds) {
            if (cloud.lockOutput) {
                lockOutput += cloud.lockOutput 
                count++
            }
        }
        for (let role of this.carriage.getPassengers()) {
            let val = role.actionAgent?.lockOutput[BuildAttr.WATER]
            if (val) {
                lockOutput += val
                count++
            }
        }
        let val = this.carriage.waterOutputObj.getOutputByPlay() - lockOutput
        if (isAvg) {
            val = Math.floor(val / count)
        }
        return val
    }

    public async toRandomPos(action: ActionNode) {
        let pos = this.getRandomMovePos()
        await action.run(this.move, pos)
        action.ok()
    }

    private async toUseBuild(action: ActionNode) {
        let cfgs = [
            { act: this.toRun, check: this.checkRun, weight: 1 }, //去跑步
            { act: this.toSauna, check: this.checkSauna, weight: 1 }, //去桑拿
        ]
        await this.runRandomAct(action, cfgs)
        action.ok()
    }

    private async toSauna(action: ActionNode) {
        let params = action.params || {}
        let build = this.carriage.getSauna()
        let index = build.getUseIndexById('use')
        let id = this.id
        this.lockOutput = this.getUnlockOutput()
        this.resetOutputTime()
        build.setUseLock(true, index, id)
        action.onTerminate = () => {
            build.setUseLock(false, index, id)
            this.lockOutput = 0
        }
        let shell = this.carriage.getSaunaShell()
        if (shell) {
            await action.run(this.enterSaunaByShell)
        }
        else {
            await action.run(this.moveToBuild, { build, paths: [{ index }] })
        }
        await action.run(this.sauna, Object.assign(params, { build }))

        action.onTerminate()
        // this.actionAgent.addUseBuildRecord(build, index)
        action.ok()
    }

    private async enterSaunaByShell(action: ActionNode) {
        let build = this.carriage.getSaunaShell()
        let type = WaterCloudObjState.ENTER_SAUNA
        let index = build.getUseIndexById('enter')
        action.onTerminate = ()=>{
            this.popState(type)
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        let targetPos = this.carriage.getSauna().getUseById('use').pos
        let dis = targetPos.sub(this.getPosition()).mag()
        let time = dis / this.moveAgent.getSpeed()
        let timeData = new TimeStateData().init(time)
        let mountPoint = "yun"
        this.pushState(type, { timeData, build, mountPoint })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    private async sauna(action: ActionNode) {
        let shell = this.carriage.getSaunaShell()
        let build = this.carriage.getSauna()
        let type = WaterCloudObjState.SAUNA
        let anim = "aniSteam"
        shell && shell.onEnter()
        build.onEnter()
        action.onTerminate = () => {
            shell && shell.onExit()
            build.onExit()
            this.popState(type)
        }
        let time = this.randomTime(ActionCfg[type])
        let mountPos = build.getUseById('use').pos
        let mountPoint = "yun"
        let data = { anim, loop: true, build, mountPoint, mountPos }
        this.setPosition(mountPos)
        this.pushState(type, data)
        if (!this.canOutput() && gameHelper.world.getNextHourSurplusWorldTime() < ut.Time.Minute) {
            await action.run(this.waitOutput)
        }
        if (this.canOutput()) {
            this.doOutput(time, type)
        }
        await action.wait(time)

        await action.run(this.exitByHide, data)

        action.onTerminate()
        // this.actionAgent.addUseBuildRecord(build, index)
        action.ok()
    }

    private async exitByHide(action: ActionNode) {
        let {build, mountPoint, anim, mountPos, loop} = action.params || {}
        let type = WaterCloudObjState.EXIT_HIDE
        let carriage = this.carriage as WaterModel
        action.onTerminate = () => {
            carriage.removeCloud(this.id)
        }
        let time = 1
        let timeData = new TimeStateData().init(time)
        this.pushState(type, { timeData, build, mountPoint, mountPos, anim, loop })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }
    

    private async toRun(action: ActionNode) {
        let params = action.params || {}
        let build = this.carriage.getRun()
        let index =  build.getUseIndexById("use")
        let id = this.id
        this.resetOutputTime()
        this.lockOutput = this.getUnlockOutput()
        build.setUseLock(true, index, id)
        action.onTerminate = () => {
            build.setUseLock(false, index, id)
            this.lockOutput = 0
        }
        await action.run(this.moveToBuild, { build, paths: [{ index }] })
        await action.run(this.run, Object.assign(params, { build }))
        action.onTerminate()
        // this.actionAgent.addUseBuildRecord(build, index)
        action.ok()
    }

    private async run(action: ActionNode) {
        let { build } = action.params || {}
        let anim = "aniRun"
        let type = WaterCloudObjState.RUN
        build.onEnter()
        action.onTerminate = () => {
            build.onExit()
            this.popState(type)
        }
        let time = this.randomTime(ActionCfg[type])
        let data = { build, anim, loop: true }
        this.pushState(type, data)
        if (!this.canOutput() && gameHelper.world.getNextHourSurplusWorldTime() < ut.Time.Minute) {
            await action.run(this.waitOutput)
        }
        if (this.canOutput()) {
            this.doOutput(time, type)
        }
        await action.wait(time)
        await action.run(this.exitByHide, data)
        action.onTerminate()
        action.ok()
    }

    private waitOutput(action: ActionNode) {
        if (this.canOutput()) {
            action.ok()
        }
    }

    private canOutput() {
        let intervalTime = ut.Time.Hour
        return Math.floor(gameHelper.world.getTime() / intervalTime) - Math.floor(this.genOutputTime / intervalTime) > 0
    }

    private async toOutput(action: ActionNode) {
        let params = action.params || {}
        let output = this.getUnlockOutput()
        this.lockOutput = output
        action.onTerminate = () => {
            this.lockOutput = 0
        }
        let area = this.carriage.moneyAreas.random()
        let pos = cc.v2(area.pos.x + ut.randomRange(-100, 100), area.pos.y + ut.randomRange(200, 250))
        await action.run(this.move, pos)
        await action.run(this.output, Object.assign(params, { output }))
        action.onTerminate()
        // this.actionAgent.addUseBuildRecord(build, index)
        action.ok()
    }

    private async output(action: ActionNode) {
        let { output } = action.params || {}
        let anim = "aniOutput"
        let type = WaterCloudObjState.OUTPUT
        action.onTerminate = () => {
            this.popState(type)
        }
        let count = ut.random(Math.min(WATER_OUTPUT_COUNT[0], output), Math.min(WATER_OUTPUT_COUNT[1], output))
        let inteval = this.getAnimTime(anim)
        let time = count * inteval
        let data = { anim, loop: true }
        this.pushState(type, data)
        this._doOutput(inteval, count, output, type)
        await action.wait(time)
        this.outputTime = 0
        action.onTerminate()
        action.ok()
    }

    private async toExit(action) {
        let type = WaterCloudObjState.EXIT
        let carriage = this.carriage as WaterModel
        action.onTerminate = () => {
            carriage.removeCloud(this.id)
        }
        this.pushState(type)
        let pos = cc.v2(ut.random(0, 2100), 950)
        await action.run(this.move, pos)
        action.onTerminate()
        action.ok()
    }

    private doOutput(time, type, cntRange = WATER_OUTPUT_COUNT) {
        this.lockOutput += this.getUnlockOutput(true)
        let output = this.lockOutput
        let count = ut.random(Math.min(cntRange[0], output), Math.min(cntRange[1], output))
        this._doOutput(time / count, count, output, type)
    }

    private _doOutput(inteval, count, output, type) {
        let outputObj = this.carriage.waterOutputObj
        let pos = this.getPosition();
        (async()=> {
            let ary = ut.numAvgSplit(output, count)
            for (let val of ary) {
                await ut.wait(inteval)
                if (!this.getState(type)) return
                if (outputObj.getOutputByPlay() < val) return
                outputObj.genOutputByPlay(val)
                outputObj.addDropByPos(val, pos)
                this.lockOutput -= val
            }
        })()
    }

    private resetOutputTime() {
        this.genOutputTime = gameHelper.world.getTime()
    }

    private pushState(type?, data?) {
        let state = new StateObj<WaterCloudObjState>().init(type, data)
        this.states.push(state)
    }

    private popState(type) {
        this.states.remove('type', type)
    }

    public getState(type) {
        return this.states.find(s => s.type == type)
    }

    public reset() {
        this.actionTree.terminate()
    }

    public restart() {
        this.actionTree.terminate()
        this.actionTree.start(this.loop)
    }

    update(dt: number) {
        if (this.actionTree) {
            this.actionTree.update(dt)
            this.time += dt
            this.outputTime += dt
        }
    }


    //---------------------- 移动 ------------------------------

    public setPosition(pos: cc.Vec2) {
        this.moveAgent?.setPointAndPosition(null, pos)
    }

    public getPosition() {
        return this.moveAgent?.getPosition()
    }

    public setDir(dir: RoleDir) {
        this.dir = dir
    }

    public getScale() {
        let carriage = this.carriage
        let map = carriage.getMap()
        let pos = this.getPosition()
        let scale = map.getScale(pos)
        return scale
    }

    public getDir() {
        return this.dir
    }

    public isMoving() {
        return this.moveAgent?.isMoving()
    }

    public async move(action: ActionNode) {
        let position: cc.Vec2 = action.params
        this.setDir(RoleDir.NONE)
        await action.run(this.forceMove, position)
        action.ok(true)
    }

    // 强行移动
    public async forceMove(action: ActionNode) {
        let position: cc.Vec2 = action.params
        this.moveAgent.setMoveTargetPosition(position)
        await this.handleMove(action)
        action.ok()
    }

    //搜索路径
    protected searchPath(action: ActionNode) {
        let data = action.data
        if (!data.state) {
            data.state = 1
            let moveAgent = this.moveAgent
            let position = action.params
            action.onTerminate = () => {
                this.moveAgent.stop()
            }
            moveAgent.searchPath(null, position).then(suc => {
                data.state = 2
                data.res = suc
            })
        }
        else if (data.state == 2) {
            action.ok(data.res)
        }
    }

    protected async handleMove(action) {
        let type = WaterCloudObjState.MOVE
        this.pushState(type, {loop: true})
        action.onTerminate = () => {
            this.popState(type)
        }
        await action.run(this.onMove)
        action.onTerminate()
    }

    // 移动中
    protected onMove(action: ActionNode, dt) {
        let moveAgent = this.moveAgent
        // 刷新移动
        moveAgent.updateMove(dt)
        // 移动中
        if (!moveAgent.isMoving()) {
            action.ok()
        }
    }
    //---------------------------------------------------------------

    private getRandomMovePos<T extends { rect?: cc.Rect }>(_areas?: T[], out?: { [key: string]: any }) {
        let map = this.map
        let carriage = this.carriage
        let emptyAreas = [carriage.getUseById('cloud')]
        let areas = emptyAreas

        let roles = carriage.clouds
        // 获取当前车厢所有乘客的位置
        let posList = roles.map(p => {
            let pos = p.getPosition()
            if (p.isMoving()) { //如果在移动就选终点位置，否则选当前位置
                pos = p.moveAgent.getPaths().last() || pos
            }
            return pos
        })

        let randomFunc
        if (areas.length > 0) {
            //先选人数最少的区域
            let area = areas.minList(area => {
                let cnt = 0
                for (let pos of posList) {
                    if (area.rect.contains(pos)) {
                        cnt++
                    }
                }
                return cnt
            }).random()
            if (out) {
                out.index = areas.findIndex(a => a == area)
            }
            let map = carriage.getMap()
            let moveAgent = this.moveAgent
            randomFunc = () => {
                let count = 10
                let pos = cc.v2()
                while (count--) {
                    let rdx = ut.randomRange(0, area.rect.width)
                    let rdy = ut.randomRange(0, area.rect.height)
                    pos.set2(area.rect.x + rdx, area.rect.y + rdy)
                    let point = map.getActPointByPixel(pos)
                    if (moveAgent.checkCanPass(point.x, point.y)) {
                        return pos
                    }
                }
                return pos
            }
        }
        else { //没配空地，只能随机个看起来空的地方
            let reachablePoints = map.getMainEmptyPoints();
            randomFunc = () => {
                return map.getActPixelByPoint(reachablePoints.random())
            }
        }

        //尽量找一个远离所有人的位置
        let pos = mapHelper.sparsePosRandom(randomFunc, posList, null, 50)
        return pos
    }

    protected checkBuildPlay(build, useIndexes = ["use"]) {
        if (!build) return false
        return useIndexes.some(i => {
            let index = build.getUseIndexById(i)
            if (!build.canUse(index)) return false
            // if (this.actionAgent.isCdBuildRecord(build, i)) return false
            return true
        })
    }

    //移动到设施的使用点
    protected async moveToBuild(action: ActionNode) {
        let buildData = action.params
        let { paths } = buildData
        let build: BuildObj = buildData.build

        let dir
        for (let { pos, force, index } of paths) {
            let info = build.getUseByIndex(index)
            pos = pos || info.pos
            force = force || info.force
            dir = info.dir
            if (force) {
                await action.run(this.forceMove, pos)
            }
            else {
                await action.run(this.move, pos)
            }
        }
        if (dir) {
            this.setDir(dir)
        }
        action.ok()
    }

    protected async runRandomAct(action, cfgs, params?) {
        cfgs = cfgs.filter(({ check }) => {
            if (check) return check.call(this)
            return true
        })
        if (cfgs.length > 0) {
            let rdIndex = gameHelper.randomByWeight(cfgs)
            let cfg = cfgs[rdIndex]
            await action.run(cfg.act, Object.assign({}, params, cfg.params))
            return true
        }
        return false
    }

    public getPostion() {
        return this.moveAgent.getPosition()
    }

    public getAnim(anim) {
        return cfgHelper.getBuildAnim("1019-1-yun", anim)
    }

    public getAnimTime(anim) {
        return this.getAnim(anim)?.duration
    }

    private randomTime(section) {
        return ut.randomRange(section[0], section[1])
    }
}