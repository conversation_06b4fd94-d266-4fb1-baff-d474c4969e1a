import { db<PERSON><PERSON><PERSON> } from "../../common/helper/DatabaseHelper";
import CarriageModel from "./common/CarriageModel";
import { CarriageID, CarriageType, ConditionType, ItemID } from "../../common/constant/Enums";
import { TrainHeadModel } from "./TrainHeadModel";
import { BuildCfg, TrainCfg } from "../../common/constant/DataType";
import DormModel from "./dorm/DormModel";
import Dorm2Model from "./dorm2/Dorm2Model";
import TrainConnectModel from "./common/TrainConnectModel";
import EngineModel from "./engine/EngineModel";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import DiningModel from "./dining/DiningModel";
import Dorm3Model from "./dorm3/Dorm3Model";
import Dorm4Model from "./dorm4/Dorm4Model";
import { gameHelper } from "../../common/helper/GameHelper";
import { Msg } from "../../../proto/msg-define";
import { viewHelper } from "../../common/helper/ViewHelper";
import WaterModel from "./water/WaterModel";
import ConditionObj from "../common/ConditionObj";
import BathRoomModel from "./bathRoom/BathRoomModel";
import DanceHallModel from "./danceHall/DanceHallModel";
import BuildObj from "./common/BuildObj";

/**
 * 火车管理器
 */
@mc.addmodel('train', 200)
export default class TrainModel extends mc.BaseModel {

    public head: TrainHeadModel = null//车头数据
    public headConnect: TrainConnectModel = null
    private carriages: CarriageModel[] = [] //已经解锁得车厢
    private cfg: JsonConfData<TrainCfg> = null
    public connects: TrainConnectModel[] = []

    public data: proto.ITrainInfo = null

    private electricEndTime: number = 0 //电力结束时间
    private waterEndTime: number = 0 //水里结束时间

    public rowChange: boolean = false//false表示当前为一行，true为两行

    public init() {
        let data = this.data
        let localData = dbHelper.register('train', 1, this.toDB, this)
        this.cfg = assetsMgr.getJson<TrainCfg>('Train')
        this.fromDB(data, localData);
    }

    fromDB(data: proto.ITrainInfo, localData: any = {}) {
        let localHead = localData.head
        this.initHead(Object.assign({}, localHead, data?.head))

        let carriages = data?.carriages || []
        let localCarriages = localData.carriages || []
        for (let data of carriages) {
            let _data = localCarriages.find(({ id }) => data.id == id)
            if (_data) {
                data = Object.assign({}, _data, data)
            }
            let carriageModel = this.createCarriage(data)
            this.carriages.push(carriageModel);
        }
        this.updateElectricEndTime(data.electricTime)
        this.updateWaterEndTime(data.waterTime)
        this.unlockDefault()
        this.updateConnects()
    }

    public updateInfo(data: proto.ITrainInfo) {

        let carriageDatas = data.carriages
        for (let data of carriageDatas) {
            let carriage = this.getCarriageById(data.id)
            if (carriage) {
                carriage.updateInfo(data)
            }
        }

        this.updateElectricEndTime(data.electricTime)
        this.updateWaterEndTime(data.waterTime)
    }

    public updateElectricEndTime(time: number = 0) {
        this.electricEndTime = gameHelper.now() + time
    }

    public updateWaterEndTime(time: number = 0) {
        this.waterEndTime = gameHelper.now() + time
    }

    private initHead(data) {
        data = data || { id: CarriageID.HEAD }
        this.head = this.createCarriage(data) as TrainHeadModel
    }

    private unlockDefault() {
        let data = this.cfg.datas.find(data => data.sortId === 0)
        if (data) {
            this.addCarriage(data.id)
        }
    }

    toDB() {
        return {
            carriages: this.carriages.map(m => m.toDB()),
            head: this.head?.toDB(),
        };
    }

    public update(dt) {
        for (let carriage of this.getAllCarriages()) {
            carriage.update(dt)
        }
    }

    public getBuilds(): BuildObj[] {
        let builds = [];
        for (let carriage of this.carriages) {
            builds.pushArr(carriage.getBuilds())
        }
        return builds
    }

    /**
     * 创建车厢
     * @param data
     * @returns
     */
    createCarriage(data: proto.ICarriageInfo) {
        switch (data.id) {
            case CarriageID.HEAD:
                return new TrainHeadModel().init(data)
            case CarriageID.DORM:
                return new DormModel().init(data)
            case CarriageID.DORM2:
                return new Dorm2Model().init(data)
            case CarriageID.DORM3:
                return new Dorm3Model().init(data)
            case CarriageID.DORM4:
                return new Dorm4Model().init(data)
            case CarriageID.ENGINE:
                return new EngineModel().init(data)
            case CarriageID.DINING:
                return new DiningModel().init(data)
            case CarriageID.WATER:
                return new WaterModel().init(data)
            case CarriageID.BATHROOM:
                return new BathRoomModel().init(data)
            case CarriageID.DANCEHALL:
                return new DanceHallModel().init(data)
            default:
                return new CarriageModel().init(data)
        }
    }

    getCarriageById(id: number) {
        return this.getAllCarriages().find(c => c.getID() === id)
    }

    getCarriageByType(type: CarriageType) {
        return this.getAllCarriages().find(c => c.getType() === type);
    }

    public getCarriagesByType(type: CarriageType) {
        return this.getAllCarriages().filter(c => c.getType() === type);
    }

    /**
     * 添加车厢
     * @param id
     * @param type
     */
    addCarriage(id: number, data?: proto.ICarriageInfo) {
        let carriage = this.getCarriageById(id)
        if (!carriage) {
            if (!data) {
                data = { id }
            }
            carriage = this.createCarriage(data);
            this.carriages.push(carriage);
            this.updateConnects()
            eventCenter.emit(EventType.CREATE_NEW_CARRIAGE, carriage);
        }
        return carriage
    }

    /**
     * 获取场景车厢信息
     * @returns
     */
    public getCarriages() {
        return this.carriages;
    }

    public getBuiltCarriages() {
        return this.carriages.filter(m => m.isBuilt)
    }

    //车头 + 其他车厢
    public getAllCarriages() {
        let carriages: CarriageModel[] = [this.head]
        carriages.pushArr(this.getCarriages())
        return carriages;
    }

    public getCarriageIndex(id: number) {
        return this.carriages.findIndex(c => c.getID() == id)
    }

    public isBuiltCarriage(id: number) {
        let model = this.getCarriageById(id)
        return model && model.isBuilt
    }

    public isCarriageOverBuilt(id: number) {
        let model = this.getCarriageById(id)
        return model && model.overBuilt
    }

    // 某设施是否已经建造完成
    public isUnlockBuild(id: string) {
        let cfg = cfgHelper.getBuildById(id)
        if (!cfg) return false
        let carriage = this.getCarriageById(cfg.carriageId)
        if (!carriage) return false
        return !!carriage.getBuildByOrder(cfg.order)
    }

    public getBuildLevel(key: string) {
        let [carriageId, order] = key.split('-')
        let carriage = gameHelper.train.getCarriageById(Number(carriageId))
        if (!carriage) return 0
        let build = carriage.getBuildByOrder(Number(order))
        if (!build) return 0
        return build.lv
    }

    public isAllBuildTrainItemByIds(ary: string[]) {
        if (ary) {
            for (let id of ary) {
                if (!this.isUnlockBuild(id)) {
                    return false
                }
            }
        }
        return true
    }

    public checkCanBuildByPre(cfg: BuildCfg) {
        return this.isAllBuildTrainItemByIds(cfg.preId)
    }

    // 通过奖励直接解锁某设施
    public unlockBuildByReward(id: string) {
        let cfg = cfgHelper.getBuildById(id)
        if (!cfg) {
            return twlog.error("unlockBuildByReward fail. no cfg:", id)
        }
        let carriage = this.getCarriageById(cfg.carriageId)
        carriage.unlockBuild(cfg.order)
    }

    public getConnect(c1: CarriageModel, c2: CarriageModel) {
        let connects = [this.headConnect]
        connects.pushArr(this.connects)
        return connects.find((connect) => {
            return (connect.left == c1 && connect.right == c2) || (connect.left == c2 && connect.right == c1)
        })
    }

    private updateConnects() {
        if (!this.headConnect) {
            this.headConnect = new TrainConnectModel()
            this.headConnect.left = this.carriages[0]
            this.headConnect.right = this.head
        }

        for (let i = 1; i <= this.carriages.length; i++) {
            let connect = this.connects[i - 1]
            if (!connect) {
                connect = new TrainConnectModel()
                this.connects[i - 1] = connect
            }
            connect.right = this.carriages[i - 1]
            connect.left = this.carriages[i]
        }
    }

    // 判断车厢是否满足解锁条件
    public checkCarriageUnlock(cfg: TrainCfg) {
        if (!this.checkCarriagePreTrain(cfg)) {
            return false
        }
        return true
    }

    public checkCarriagePreTrain(cfg: TrainCfg) {
        let ary = cfg.preTrain//前置车厢
        if (ary) {
            for (const id of ary) {
                if (!this.checkOnePreTrain(id)) {
                    return false
                }
            }
        }
        return true
    }
    private checkOnePreTrain(id: number) {
        let model = this.getCarriageById(id)
        return model && model.isBuilt
    }
    public getDesCarriagePreTrain(cfg: any) {
        let str = ''
        let ary = cfg.preTrain//前置车厢
        if (ary) {
            for (const id of ary) {
                // if (!this.checkOnePreTrain(id)) {
                if (true) {
                    let json = this.cfg.getById(id)
                    if (json) {
                        if (str.length > 0) str += assetsMgr.lang('common_guiText_21')
                        str += assetsMgr.lang(json.name)
                    }
                }
            }
        }
        return str
    }


    public getElectricSurplusTime() {
        return Math.max(0, this.electricEndTime - gameHelper.now())
    }

    public getWaterSurplusTime() {
        return Math.max(0, this.waterEndTime - gameHelper.now())
    }

    public getEnergySurplusTime(id: ItemID) {
        if (id == ItemID.ELECTRIC) {
            return this.getElectricSurplusTime()
        }
        else if (id == ItemID.WATER) {
            return this.getWaterSurplusTime()
        }
        return 0
    }

    public getOutputSum(type: ConditionType) {
        let sum = 0
        let passengers = gameHelper.passenger.getPassengers()
        if (type == ConditionType.STAR_DUST) {
            for (let carriage of this.carriages) {
                sum += carriage.getOutputSum(type)
            }
            sum += Math.floor(gameHelper.passenger.starOutputObj.getOutput())
        }
        else if (type == ConditionType.HEART) {
            for (let p of passengers) {
                sum += p.getHeart()
            }
            sum += gameHelper.heartOutput.getOutput()
        }
        return sum
    }

    public getAttr(cond: ConditionObj) {
        let sum = 0
        for (let carriage of this.carriages) {
            sum += carriage.getAttr(cond)
        }
        return sum
    }

    public addOutputTime(time) {
        for (let carriage of this.carriages) {
            carriage.addOutputTime(time)
        }
        gameHelper.heartOutput.addOutputTime(time)
    }

    public getLoad() {
        return this.getCarriages().reduce((sum, c) => sum + c.getLoad(), 0) + gameHelper.trainTech.getTrainLoad()
    }
}
