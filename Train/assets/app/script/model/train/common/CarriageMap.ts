import AStar from "../../../common/helper/AStart";
import BuildObj from "./BuildObj";
import BaseMap from "../../map/BaseMap";
import UnioinFind from "../../map/UnioinFind";

/**
 * 车厢地图
 */
export default class CarriageMap extends BaseMap {

    protected carriageId: number = null;

    protected unioinFind: UnioinFind = null

    protected scaleRange = [1, 0.91]

    init(carriageId?: number) {
        this.carriageId = carriageId
        this.gridSize = 20
        this.size = cc.size(108, 13)
        this.setBasePoint(cc.v2(0, 50)) //暂定
        this.unioinFind = new UnioinFind().init(this)
        return this
    }

    public getTransPoint(left: boolean) {
        let { width, height } = this.getSize()
        if (left) {
            return cc.v2(0, 3)
        }
        return cc.v2(width - 1, 3)
    }

    public updatePointsByBuild(build: BuildObj, add: boolean) {
        let ary = build.getActPoints(add)
        ary.forEach(tmp => {
            this.updatePoints(tmp.points, tmp.add)
        })
        this.unioinFind.init(this)
    }

    public updatePointsByPoints(points: cc.Vec2[], add: boolean) {
        this.updatePoints(points, add)
        if (points.length > 0) {
            this.unioinFind.init(this)
        }
    }

    update() {
    }

    public getConnectPoints(point: cc.Vec2): cc.Vec2[] {
        return this.unioinFind.getConnectPoints(point)
    }

    public isConnected(p1: cc.Vec2, p2: cc.Vec2) {
        return this.unioinFind.isConnected(p1, p2)
    }

    public getMainEmptyPoints() {
        return this.getConnectPoints(cc.v2(0, 0));
    }

    public getRandomMainEmptyPoint(filter?: Function) {
        let { width, height } = this.size
        filter = filter || function (x, y) {
            return x > 6 && x < width - 6
        }
        let x = 0, y = 0
        let pointList = this.getConnectPoints(cc.v2(x, y));
        let randomCnt = 64;
        while (randomCnt > 0) {
            randomCnt--
            let { x, y } = pointList.random()
            if (filter()) {
                return cc.v2(x, y);
            }
        }
        return pointList.random();
    }

    public isMainPoint(point: cc.Vec2) {
        return this.isConnected(point, cc.v2(0, 0))
    }

    public getScale(pos) {
        let maxY = this.getSize().height * this.getGridSize()
        maxY += this.getBasePoint().y
        let ratio = pos.y / maxY
        let scale = cc.misc.lerp(this.scaleRange[0], this.scaleRange[1], ratio)
        return scale
    }

    public setMapScaleRange(max: number, min: number) {
        this.scaleRange = [max, min]
    }

}


export class CarriageTopMap extends CarriageMap {

    protected scaleRange: number[] = [0.91, 0.91]

    init(carriageId?: number) {
        this.carriageId = carriageId
        this.gridSize = 20
        this.size = cc.size(108, 1)
        this.setBasePoint(cc.v2(0, 0))
        this.unioinFind = new UnioinFind().init(this)
        return this
    }

    // 根据网格点获取像素点
    public getActPixelByPoint(point: cc.Vec2, out?: cc.Vec2) {
        out = out || cc.v2()
        out.x = (point.x + 0.5) * this.gridSize
        out.y = 0
        out.addSelf(this.basePoint)
        return out
    }

    // 根据像素点获取网格点
    public getActPointByPixel(pos: cc.Vec2, out?: cc.Vec2) {
        out = out || cc.v2()
        pos = pos.sub(this.basePoint)
        out.x = Math.floor(pos.x / this.gridSize)
        out.y = 0
        return out
    }
}