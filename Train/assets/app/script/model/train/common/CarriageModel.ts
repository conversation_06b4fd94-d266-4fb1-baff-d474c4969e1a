import { CARRIAGE_CONTENT_SIZE, MAX_VALUE, MAX_ZINDEX } from "../../../common/constant/Constant";
import { AreaInfo, CarriageThemeCfg, CarriageUsePosInfo, MoneyAreaInfo, TrainCfg } from "../../../common/constant/DataType";
import { BuildAttr, CarriageID, CarriageIntoType, CarriageType, CarriageUsePosType, ConditionType, GoodsObjectType, GoodsType, ItemID, MarkNewType, SpeedUpType, TrainBurstTaskType, ValueType } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import { cfgHelper } from "../../../common/helper/CfgHelper";
import { gameHelper } from "../../../common/helper/GameHelper";
import PassengerModel from "../../passenger/PassengerModel";
import { StateType } from "../../passenger/StateEnum";
import BuildObj from "./BuildObj";
import CarriageMap, { CarriageTopMap } from "./CarriageMap";
import CarriageOutputObj from "./CarriageOutputObj";
import DropItemObj from "./DropItemObj";
import DropMoneyObj from "./DropMoneyObj";
import { viewHelper } from "../../../common/helper/ViewHelper";
import { Msg } from "../../../../proto/msg-define";
import ConditionObj from "../../common/ConditionObj";
import { util } from "../../../../core/utils/Utils";
import CarriageAccTotal from "./CarriageAccTotal";
import CarriageEnergyOutputObj from "./CarriageEnergyOutputObj";
import { dropItemHelper } from "../../../common/helper/DropItemHelper";
import { mapHelper } from "../../../common/helper/MapHelper";
import GoodsObj from "./GoodsObj";
import BurstItem from "../burst/BurstItem";

/**
 * 车厢模型
 */
export default class CarriageModel {

    protected id: number = null; //车厢id
    protected type: CarriageType = null; //车厢类型
    public position: cc.Vec2 = null; //当前车厢位置信息世界坐标
    protected map: CarriageMap = null; //车厢地板地图
    protected topMap: CarriageTopMap = null //车厢顶地图
    public curSelectIdx: number = null//记录建造界面当前选中的主题

    public active: boolean = true

    protected themeLv: number = 1

    private builds: BuildObj[]//已解锁设施
    private emptyAreas: AreaInfo[] = [] //空地，空闲状态去这个区域idle
    public moneyAreas: MoneyAreaInfo[] = [] //扔钱区域列表

    protected passengers: PassengerModel[] = []
    protected tempPassengers: PassengerModel[] = []
    protected topPassengers: PassengerModel[] = []

    public cfg: TrainCfg = null;

    private dropMoneys: DropMoneyObj[] = []

    public outputObj: CarriageOutputObj = null
    public waterOutputObj: CarriageEnergyOutputObj = null
    public electricOutputObj: CarriageEnergyOutputObj = null
    public vitalityOutputObj: CarriageOutputObj = null

    private lightUp: boolean = false

    public get isBuilt(): boolean { return this.isTimeOver }
    public get isWorkCarriage() { return this.cfg.type == CarriageType.WORK } //是否是工作车厢
    private isTimeOver: boolean = false//建造倒计时结束
    public isOpenDoor: boolean = false //已开门
    public overBuilt: boolean = false //已建造动画播放完
    public buildTime: number = 0 //建造剩余时间
    private buildEndTime: number = 0 // 建造完成时间点

    protected usePosList: CarriageUsePosInfo[] = []
    protected useLockMap = {}

    public accTotal: CarriageAccTotal = null //累计信息

    protected goods: GoodsObj[] = []

    protected wastes: BurstItem[] = []
    protected fires: BurstItem[] = []

    public init(data: any) {
        data = data || {}
        this.id = data.id
        this.updateBuildSurplusTime(data.buildTime)
        this.accTotal = new CarriageAccTotal().init(this, data.accTotal)
        this.themeLv = data.themeLv

        this.initMap()
        this.initUsePosList()
        this.initEmptyAreas()
        this.initDropMoneyArea()

        let cfg = assetsMgr.getJsonData<TrainCfg>('Train', this.id)
        this.type = cfg.type
        this.cfg = cfg

        this.initBuilt()
        this.initOpenDoor(data.openDoor)
        this.initBuilds(data.builds)
        this.updateMapPointsByBuilds()
        this.initBurst()

        if (data.goods) {
            this.goods = data.goods.map(({ id, lv }) => new GoodsObj().init(id, lv)) || []
        }

        this.outputObj = new CarriageOutputObj().init(this, new ConditionObj().init(ConditionType.STAR_DUST, -1), data.starOutput)
        this.electricOutputObj = new CarriageEnergyOutputObj().init(this, new ConditionObj().init(ConditionType.PROP, ItemID.ELECTRIC), data.electricOutput)
        this.waterOutputObj = new CarriageEnergyOutputObj().init(this, new ConditionObj().init(ConditionType.PROP, ItemID.WATER), data.waterOutput)
        this.vitalityOutputObj = new CarriageOutputObj().init(this, new ConditionObj().init(ConditionType.PROP, ItemID.VITALITY), data.vitalityOutput)

        this.addOutputTime(data.outputTime / ut.Time.Second)

        this.setActive(true)

        return this
    }

    public updateInfo(data: proto.ICarriageInfo) {
        this.outputObj?.updateInfo(data.starOutput)
        this.electricOutputObj?.updateInfo(data.electricOutput)
        this.waterOutputObj?.updateInfo(data.waterOutput)
        this.vitalityOutputObj?.updateInfo(data.vitalityOutput)

        this.updateBuildSurplusTime(data.buildTime)
        this.initBuilt()
        this.initOpenDoor(data.openDoor)
    }

    get name() {
        return this.cfg?.name
    }

    toDB() {
        return {
            id: this.id,
            accTotal: this.accTotal.toDB(),
        };
    }

    protected initMap() {
        this.map = new CarriageMap().init();
        this.topMap = new CarriageTopMap().init()
    }

    protected initUsePosList() {
        let json = cfgHelper.getThemeMapJsonData(this.id)
        if (json?.usePosList) {
            this.usePosList = cfgHelper.mapCarriageUsePosList(json.usePosList, cc.v2())
        }
    }

    protected initBuilds(buildsData: proto.TrainItemInfo[]) {
        this.builds = buildsData.map(data => this.newBuildObj(data.order).fromDB(data, this.id))
    }

    public unlockBuild(order: number) {
        let build: BuildObj;
        build = this.newBuildObj(order).init(order, this.id)
        this.builds.push(build)
        this.map.updatePointsByBuild(build, true)
        eventCenter.emit(EventType.UNLOCK_BUILD, build)
        eventCenter.emit(EventType.LEVEL_UP_BUILD, build)//设施解锁也算成升级
        return build
    }

    public async buildLvUp(order: number) {
        let msg = new proto.C2S_BuildLevelUpMessage({ carriageId: this.getID(), order })
        let res = await gameHelper.net.request(Msg.C2S_BuildLevelUpMessage, msg, true)
        const { code } = proto.S2C_BuildLevelUpMessage.decode(res)
        if (code == 0) {
            this.onBuildLvUp(order)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    private onBuildLvUp(order: number) {
        let build = this.getBuildByOrder(order)
        let isChangeBuild = false
        if (!build) {
            isChangeBuild = true
            build = this.unlockBuild(order)
        }
        else {
            let preSkinLv = build.getAttr(BuildAttr.SKIN)
            build.levelUp()
            let curSkinLv = build.getAttr(BuildAttr.SKIN)
            if (preSkinLv != curSkinLv) {
                build.onChangeSkin(curSkinLv, true)
                isChangeBuild = true
            }
        }
        let cfg = cfgHelper.getBuildLvCfg(this.id, build.order, build.lv)
        let buyCost = gameHelper.toConditions(cfg.buyCost)
        gameHelper.deductConditions(buyCost)

        let themeCfg = assetsMgr.checkJsonData<CarriageThemeCfg>("TrainTheme", `${this.id}-${this.themeLv + 1}`)

        this.checkThemeComplete(isChangeBuild, assetsMgr.getJsonData<CarriageThemeCfg>("TrainTheme", `${this.id}-${this.themeLv}`))

        let isAllBuildsMaxLv = this.isAllBuildsMaxLv()

        if (themeCfg && isAllBuildsMaxLv) {
            gameHelper.new.pushNew(MarkNewType.BUILD_UNLOCK_SKIN, [this.id])
        }
    }

    private async checkThemeComplete(isChangeBuild, cfg) {
        //todo 放到视图层
        let isAllBuildsMaxLv = this.isAllBuildsMaxLv()
        if (isAllBuildsMaxLv) {
            eventCenter.emit(EventType.UNLOCK_ALL_BUILD, this.getID())
            if (isChangeBuild) {
                let check = () => { return this.getID() != CarriageID.HEAD }
                await eventCenter.wait(EventType.BACK_CAMERA_FROM_EDIT, check)
            }
            await ut.wait(0.5)
            viewHelper.showPnl('common/ThemeUnlock', cfg)
        }
    }

    public async themeLvUp() {
        let msg = new proto.C2S_CarriageThemeLvUpMessage({ carriageId: this.getID() })
        let res = await gameHelper.net.request(Msg.C2S_CarriageThemeLvUpMessage, msg, true)
        const { code } = proto.S2C_CarriageThemeLvUpMessage.decode(res)
        if (code == 0) {
            this.themeLv++
            let cfg = assetsMgr.getJsonData<CarriageThemeCfg>("TrainTheme", `${this.id}-${this.themeLv}`)
            let buyCost = gameHelper.toConditions(cfg.buyCost)
            gameHelper.deductConditions(buyCost)
            viewHelper.showPnl('train/ThemeNew', this.getID())
            this.emit(EventType.UNLOCK_THEME)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public saveGuideDrop() {
        this.dropMoneys = this.dropMoneys.map(d => {
            d.isGuide = false
            return d
        })
    }

    // 车厢内所有设施当前等级的和
    public getBuiltCurNum() {
        let cur = 0
        let ary = this.builds.filter(b => b.isShow)
        ary.forEach(b => { cur += b.lv })
        return cur
    }
    // 车厢内所有设施最大等级的和
    public getBuiltMaxNum() {
        let max = cfgHelper.getBuildMax(this.id)
        if (max > 0) {
            let theme = cfgHelper.getThemes(this.id).last()
            max *= theme.unlockLevel
        }
        return max
    }

    public setActive(val: boolean) {
        this.active = val
        // 刷新一下 掉落的钱
        if (val) {
            this.dropMoneys.forEach((m) => {
                this.setDropZIndex(m)
            })
        }
    }

    public getMap() {
        return this.map;
    }

    public getTopMap() {
        return this.topMap
    }

    public getType(): CarriageType {
        return this.type;
    }

    public updateMapPointsByBuilds() {
        for (let build of this.getBuilds()) {
            this.map.updatePointsByBuild(build, true)
        }
    }

    public getBuilds() {
        return this.builds
    }

    public getThemeLv() {
        return this.themeLv
    }

    public getID() {
        return this.id;
    }

    public getIndex() {
        return gameHelper.train.getCarriageIndex(this.id)
    }

    public getLoadRange() {
        const ary = gameHelper.train.getCarriages()
        let min = 0, max = 0
        for (const item of ary) {
            if (item.cfg.load > 0) {
                max += item.cfg.load
            }
            if (item == this) break
            min += item.cfg.load
        }
        return { min, max }
    }

    protected emit(type: string | number, ...params: any) {
        eventCenter.emit(type, ...params)
    }

    public roleEnter(passenger: PassengerModel, intoType: CarriageIntoType = CarriageIntoType.NORMAL) {
        this.removeTempRole(passenger)
        this.passengers.push(passenger)
        passenger.onEnterCarriage(this, intoType)
        this.emit(EventType.PASSENGER_ENTER_CARRIAGE, this.id, passenger)
    }

    public roleExit(passenger: PassengerModel) {
        this.passengers.remove(passenger)
        this.emit(EventType.PASSENGER_EXIT_CARRIAGE, this.id, passenger)
    }

    public roleEnterTop(passenger: PassengerModel, intoType: CarriageIntoType = CarriageIntoType.NORMAL) {
        this.removeTempRole(passenger)
        this.topPassengers.push(passenger)
        passenger.onEnterCarriageTop(this, intoType)
        this.emit(EventType.PASSENGER_ENTER_CARRIAGE_TOP, this.id, passenger)
    }

    public roleExitTop(passenger: PassengerModel) {
        if (!this.topMap) return
        this.topPassengers.remove(passenger)
        this.emit(EventType.PASSENGER_EXIT_CARRIAGE_TOP, this.id, passenger)
    }

    public addTempRole(role: PassengerModel) {
        if (!this.tempPassengers.has(role)) {
            this.tempPassengers.push(role)
        }
    }

    public removeTempRole(role: PassengerModel) {
        this.tempPassengers.remove(role)
    }

    public getRoleCount() {
        return this.passengers.length
    }

    public getPassengers() {
        return this.passengers
    }

    public getTopPassengers() {
        return this.topPassengers
    }

    //这里适配了宿舍
    public getWorker(workIndex: number) {
        return this.type == CarriageType.DORM ? gameHelper.passenger.getPassengers().find(p => p.getDormIndex() == workIndex && p.dormId == this.id)
            : gameHelper.passenger.getPassengers().find(p => p.getWorkIndex() == workIndex && p.workId == this.id)
    }

    public getWorkers() {
        return gameHelper.passenger.getPassengers().filter(p => p.hasWork() && p.workId == this.id)
    }

    public getValidPassengers() {
        return this.passengers.filter(p => !p.hasWork())
    }

    public havePassenger(id: number): boolean {
        return !!this.passengers.find(p => p.getID() == id);
    }

    // 有可在该车厢工作的角色
    //适配了宿舍
    public checkHaveWorker() {
        let all = gameHelper.passenger.getPassengers()
        return all.some(p => (this.type == CarriageType.DORM ? !p.isCheckIn() : !p.hasWork()))
    }

    //车厢里的最大人数
    public getCap() {
        return this.cfg?.cap || 0
    }

    //当前车厢是否已经满
    public isCapFull() {
        let count = this.getAllPassengerCount()
        return count >= this.getCap()
    }

    //获得第i个未解锁的位置对应解锁的场景
    public getUnlockTheme(roleIndex: number): string {
        let cnt = this.cfg?.roleCnt || 0
        let themes = cfgHelper.getThemes(this.id)
        let name = null
        themes.forEach((theme) => {
            cnt += theme.roleCnt || 0
            if (cnt > roleIndex && !name) {
                name = theme.name
            }
        })
        return name
    }

    //寝室入住最大容量
    public getMaxCheckInCnt() {
        return 8
    }

    //寝室入住当前容量
    public getCheckInCnt() {
        let cnt = this.cfg?.roleCnt || 0
        let themes = cfgHelper.getThemes(this.id).filter(d => d.order <= this.themeLv)
        for (let data of themes) {
            if (!this.isThemeComplete(data.order)) continue
            cnt += (data.roleCnt || 0)
        }
        cnt += gameHelper.trainTech.getCarriageRoleCnt(this.id)
        return cnt
    }

    public getNextTheme() {
        return cfgHelper.getThemes(this.id).find(d => d.order == this.getThemeLv() + 1)
    }

    public isAllBuildsMaxLv(giveThemeLv?: number) {
        let lv = giveThemeLv || this.themeLv
        let theme = cfgHelper.getThemes(this.id).find(d => d.order == lv)
        if (!theme) return false
        let max = theme.unlockLevel
        let builds = cfgHelper.getBuilds(this.id)
        for (let { order } of builds) {
            let build = this.getBuildByOrder(order)
            if (!build || !build.isMaxLv(max)) return false
        }
        return true
    }

    public getBuildsCurMaxData(themeLv: number) {
        let theme = cfgHelper.getThemes(this.id).find(d => d.order == themeLv)
        if (!theme) return
        let maxLv = theme.unlockLevel
        let builds = cfgHelper.getBuilds(this.id)
        let cur = 0, max = builds.length
        for (let { order } of builds) {
            let build = this.getBuildByOrder(order)
            if (build && build.isMaxLv(maxLv)) {
                cur++
            }
        }
        return { cur, max }
    }

    //寝室是否已住满
    public isFull() {
        return this.getCheckInRoles().length >= this.getCheckInCnt()
    }

    //包含目前已经在的，和将要来的
    public getAllPassengerCount() {
        return this.passengers.length + this.tempPassengers.length
    }

    public update(dt) {
        if (this.buildTime > 0) {
            if (gameHelper.world.isSpeedUp()) {
                this.buildEndTime -= (gameHelper.world.transDT(dt, SpeedUpType.S8) - dt) * ut.Time.Second
            }
            this.buildTime = Math.max(0, this.buildEndTime - gameHelper.now())
            if (this.buildTime <= 0) {
                this.syncBuildInfo()
            }
        }
        this.map && (this.map.update())

        let worldDt = gameHelper.world.transDT(dt, SpeedUpType.S5)
        this.updateByWorldDt(worldDt)

        if (this.getID() == CarriageID.ENGINE) {
            this.electricOutputObj && (this.electricOutputObj.update(dt))
        }
        else if (this.getID() == CarriageID.WATER) {
            this.waterOutputObj && (this.waterOutputObj.update(dt))
        }
        else if (this.isVitality()) {
            this.vitalityOutputObj && (this.vitalityOutputObj.update(dt))
        }
        this.outputObj && (this.outputObj.update(dt))
        this.updateLight()
    }

    protected updateByWorldDt(dt) {
        this.builds.forEach(t => t.update(dt))
        this.accTotal?.update(dt)
    }

    public getOutputObjByCond(cond: ConditionObj) {
        if (cond.type == ConditionType.PROP) {
            if (cond.id == ItemID.ELECTRIC) {
                return this.electricOutputObj
            }
            else if (cond.id == ItemID.WATER) {
                return this.waterOutputObj
            }
            else if (cond.id == ItemID.VITALITY) {
                return this.vitalityOutputObj
            }
        }
        return this.outputObj
    }

    public getAllDrops() {
        return this.dropMoneys
    }

    public getEmptyAreas() {
        return this.emptyAreas
    }

    private initEmptyAreas() {
        let emptyAreas = cfgHelper.getThemeMapJsonData(this.id)?.emptyAreas || []
        this.emptyAreas = emptyAreas.map(({ x, y, width, height }) => {
            let rect = cc.rect(x, y, width, height)
            return { rect }
        })
    }

    private initDropMoneyArea() {
        const defaultAreas = [
            {
                "x": 733,
                "y": 110,
                "count": 5
            },
            {
                "x": 529,
                "y": 806,
                "up": true,
                "count": 5
            },
        ]
        const areas = cfgHelper.getThemeMapJsonData(this.id)?.moneyAreas || defaultAreas
        this.moneyAreas = areas.map(({ x, y, up, count, tipCount }, i) => {
            let id = `${this.id}-${i}`
            return { id, pos: cc.v2(x, y), count, up, tipCount }
        })

        let tipsAreas = this.moneyAreas.filter(area => !area.up).map((area, i) => {
            let count = area.tipCount || 3
            let offsetX = ut.randomRange(-0.3 * 80, 0.3 * 80)
            let pos = cc.v2(area.pos.x, area.pos.y + 35)
            return { id: `${this.id}-${i + this.moneyAreas.length}`, pos, count, tips: true, offsetX, tag: area.id }
        })
        this.moneyAreas.pushArr(tipsAreas)
    }

    public getAttr(cond: ConditionObj) {
        let attr = gameHelper.transCondToBuildAttr(cond)
        let val = this._getAttr(attr)
        let inc = 0
        if (this.isWorkCarriage) {
            inc = this.getWorkOutputRate(attr)
        }
        else if (this.isDorm()) {
            inc = this.getCheckInOutputRate(attr)
        }
        else if (this.isVitality()) {
            inc = this.goods.reduce((pre, cur) => { return pre + cur.getAttr() }, 0)
        }
        return Math.floor(val + inc)
    }

    //排除入住加成
    public _getAttr(attr: BuildAttr) {
        let absolute = this.getAbsoluteIncrease(attr)
        let perInc = this.getPercentIncrease(attr)
        return absolute * (1 + perInc)
    }

    public getCheckInOutputRate(attr) {
        let abs = this.getAbsoluteIncrease(attr)
        return abs * this.getCheckInOutputPer(attr)
    }

    public getWorkOutputRate(attr) {
        let abs = this.getAbsoluteIncrease(attr)
        return abs * this.getWorkIncPer()
    }

    public getCheckInOutputPer(attr: BuildAttr) {
        let sum = 0
        let roles = this.getCheckInRoles()
        let rate = cfgHelper.getMiscData("checkInOutputRate")
        for (let role of roles) {
            if (role.getCheckInBuildAttr() == attr) {
                sum += rate
            }
        }
        return sum
    }

    public getWorkIncPer() {
        return this.getWorkers().length * cfgHelper.getMiscData("workOutputRate")
    }

    public getAbsoluteIncrease(attr: BuildAttr) {
        let sum: number = 0
        let builds = this.builds
        for (let build of builds) {
            sum += build.getAttr(attr)
        }
        sum += this.getThemeInc(attr)
        sum += gameHelper.trainTech.getCarriageAttr(this.id, attr)
        return sum
    }

    public getPercentIncrease(attr: BuildAttr) {
        return gameHelper.trainTech.getCarriageAttrPercent(this.id, attr)
    }

    public getThemeInc(attr: BuildAttr) {
        let sum = 0
        for (let lv = 1; lv <= this.themeLv; lv++) {
            let cfg = cfgHelper.getThemeLvCfg(this.id, lv)
            if (!cfg?.add) continue
            if (lv == this.themeLv && !this.isAllBuildsMaxLv()) continue
            sum += (cfg.add[attr] || 0)
        }
        return sum
    }

    /*************丢钱相关 */

    // 找最近的扔钱区域
    private findShortestMoneyArea(startPos: cc.Vec2, filter?: (area: MoneyAreaInfo) => boolean): MoneyAreaInfo {
        let areas = this.moneyAreas
        if (filter) {
            areas = areas.filter(filter)
        }
        if (areas.length <= 0) {
            return this.moneyAreas[0]
        }

        return areas.min((area) => {
            let center = area.pos
            return center.sub(startPos).magSqr()
        })
    }

    // 随机一个位置
    public randomDropPosition(arr: DropItemObj[], area: MoneyAreaInfo) {
        let { x, y } = area.pos
        let count = area.count
        let offsetX, offsetY
        let spaceX
        if (area.tips) {
            spaceX = 55, offsetX = 12 + area.offsetX, offsetY = 12
        }
        else {
            spaceX = 20, offsetX = 8, offsetY = 12
        }
        x = x - Math.floor(count / 2) * spaceX //左边距
        let indexes = Array.range(0, count - 1)
        arr = arr.slice()
        arr.sort((a, b) => {
            return a.position.x - b.position.x
        })
        for (let item of arr) {
            let curX = item.position.x - x
            let minIdx = -1
            let minDis = MAX_VALUE
            for (let i = 0; i < indexes.length; i++) {
                let posX = indexes[i] * spaceX
                let dis = Math.abs(posX - curX)
                if (dis < minDis) {
                    minDis = dis
                    minIdx = i
                }
            }
            indexes.splice(minIdx, 1)
        }

        let rdIndex = indexes.random()
        // console.log("randomDropPosition", area.id, rdIndex)
        let rdX = x + rdIndex * spaceX + ut.randomRange(-offsetX, offsetX)
        let rdY = y + ut.randomRange(-offsetY, offsetY)
        if (this.id != CarriageID.HEAD) {
            let maxX = CARRIAGE_CONTENT_SIZE.width - 210, minY = 200
            if (rdX > maxX && rdY < minY) {
                rdY = minY + ut.randomRange(0, offsetY)
            }
            rdX = cc.misc.clampf(rdX, 0, CARRIAGE_CONTENT_SIZE.width)
        }
        return cc.v2(rdX, rdY)
    }

    // 添加掉落的钱 设施
    public async addDropByBuild(item: ConditionObj, build: BuildObj, count?: number) {
        let startPos = build.position
        let area = this.moneyAreas[0]
        if (build.outputPoints.length > 0) {
            let index = gameHelper.randomByWeight(build.outputPoints)
            area = this.moneyAreas[index]
        }
        let iconPer = cfgHelper.getMiscData("iconPer")
        count = count || Math.max(1, Math.floor(item.num / (iconPer[gameHelper.transCondToBuildAttr(item)] || item.num)))
        let list = ut.numAvgSplit(item.num, count)
        for (let num of list) {
            this.addDropMoney(area, startPos, new ConditionObj().init(ConditionType.CARRIAGE, this.id, num), item)
            await ut.wait(0.2)
        }
    }

    public async addDropMoneyByRole(item: ConditionObj, passenger: PassengerModel, count?: number) {
        count = count || passenger.starNum
        let startPos = passenger.getPosition()
        let area = this.findShortestMoneyArea(startPos, (area) => !!area.tips && !area.up)
        let list = ut.numAvgSplit(item.num, count)
        for (let num of list) {
            startPos = passenger.getPosition()
            this.addDropMoney(area, startPos, new ConditionObj().init(ConditionType.PASSENGER, -1, num), item)
            await ut.wait(0.2)
        }
    }

    public addDropByPos(item: ConditionObj, startPos) {
        let up = ut.chance(40)
        let area = this.findShortestMoneyArea(startPos, (area) => !!area.up == up && !area.tips)
        return this.addDropMoney(area, startPos, new ConditionObj().init(ConditionType.CARRIAGE, this.id, item.num), item)
    }

    public addDropMoney(area: MoneyAreaInfo, startPos: cc.Vec2, source: ConditionObj, item: ConditionObj, needNotic: boolean = true) {
        if (!area || source.num < 0) {
            return
        }
        let arr = this.dropMoneys.filter(m => m.areaId === area.id && m.item.isSame(item)), drop: DropMoneyObj = null
        let count: number = arr.length
        let isMerge = count >= area.count;
        if (isMerge) { //是否超过数量限制
            drop = arr.random()
        } else {
            let pos = this.randomDropPosition(arr, area)
            drop = this.dropMoneys.add(new DropMoneyObj().init(area.id, pos, item))
            drop.setArea(area)
            drop.setCarriageId(this.id)
        }
        drop.addMoney(source)
        this.setDropZIndex(drop)
        needNotic && this.emit(EventType.ROLE_DROP_MONEY, { map: this, position: startPos }, drop, isMerge)
        return drop
    }

    public getDropZIndex(pos, up?) {
        if (up) {
            return MAX_ZINDEX - pos.y + 1000
        } else {
            return MAX_ZINDEX - pos.y
        }
    }

    public setDropZIndex(drop: DropItemObj) {
        let zIndex = this.getDropZIndex(drop.position, drop instanceof DropMoneyObj && drop.up)
        drop.zIndex = zIndex
    }

    getPosition() {
        return this.position
    }

    public isLightUp() {
        return this.lightUp
    }

    protected updateLight() {
        let world = gameHelper.world
        if (world.isNight()) {
            let passengers = this.getPassengers()
            if (passengers.length <= 0) {
                this.lightUp = false
            } else {
                let hasSleep = passengers.find(p => p.actionAgent.getState(StateType.SLEEP))
                if (hasSleep) {
                    this.lightUp = false
                } else {
                    this.lightUp = true
                }
            }
        } else {
            this.lightUp = false
        }
    }

    private initBuilt() {
        this.isTimeOver = this.buildTime <= 0
    }

    private initOpenDoor(bol: boolean) {
        this.isOpenDoor = bol
        this.overBuilt = bol
    }

    public isCostOver() {
        return this.isTimeOver
    }

    public async setBuildOverBySever() {
        let msg = new proto.C2S_OpenCarriageDoorMessage({ id: this.id })
        let res = await gameHelper.net.request(Msg.C2S_OpenCarriageDoorMessage, msg, true)
        const { code } = proto.S2C_OpenCarriageDoorResMessage.decode(res)
        if (code == 0) {
            this.setBuildOver()
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public setBuildOver() {
        if (this.isOpenDoor) return
        if (!this.isCostOver()) {
            return twlog.error("setBuildOver fail")
        }
        this.isOpenDoor = true
        eventCenter.emit(EventType.CREATE_NEW_CARRIAGE, this)
    }
    public setBuildAniOver() {
        this.overBuilt = true
        eventCenter.emit(EventType.TRAIN_BUILD_OVER, this)
    }

    public getCheckInRoles() {
        let passengers = gameHelper.passenger.getPassengers()
        return passengers.filter(p => p.dormId == this.getID())
    }

    public getBuildByOrder(order) {
        return this.getBuilds().find(b => b.order == order)
    }

    public newBuildObj(type) { //临时
        return new BuildObj()
    }

    public setUseLock(lock: boolean, index: number) {
        this.useLockMap[index] = lock
    }

    public checkUsePos(index, order?) {
        return !this.useLockMap[index] && (!order || this.getBuildByOrder(order))
    }

    public getUsePos(index: number) {
        return this.usePosList[index].pos
    }

    public getUsePosListByType(type: CarriageUsePosType) {
        return this.usePosList.filter(info => info.type == type)
    }

    public checkUsePosByType(type: CarriageUsePosType) {
        let posList = this.getUsePosListByType(type)
        for (let info of posList) {
            if (this.checkUsePos(info.index, info.buildOrder)) return true
        }
        return false
    }

    public getUseById(id) {
        return this.usePosList.find(p => p.id == id)
    }

    @util.addLock
    public async syncBuildInfo() {
        let msg = new proto.C2S_GetCarriageBuildInfoMessage({ id: this.id })
        let res = await gameHelper.net.request(Msg.C2S_GetCarriageBuildInfoMessage, msg)
        const { code, buildTime, openDoor } = proto.S2C_GetCarriageBuildInfoResMessage.decode(res)
        if (code == 0) {
            this.updateBuildSurplusTime(buildTime)
            this.initBuilt()
            if (openDoor) {
                this.setBuildOver()
            }
            if (this.isBuilt) {
                eventCenter.emit(EventType.CARRIAGE_BUILD_END, this)
            }
        }
        else {
            await ut.wait(100) //除非服务器挂了，不然不会走这里
        }
    }

    public updateBuildSurplusTime(time: number = 0) {
        this.buildTime = time
        this.buildEndTime = this.buildTime + gameHelper.now()
    }

    public addAccTotal(role: PassengerModel, num: number = 1) {
        this.accTotal.add(role, num)
    }

    //适配了宿舍
    public isUnlockWork(index: number) {
        return this.type == CarriageType.DORM ? this.getCheckInCnt() >= index : this.getWorkCnt() >= index
    }

    public getMaxWorkCnt() {
        return 8
    }

    public getWorkCnt() {
        let cnt = this.cfg?.workCnt || 0
        let themes = cfgHelper.getThemes(this.id).filter(d => d.order <= this.themeLv)
        for (let data of themes) {
            if (!this.isThemeComplete(data.order)) continue
            cnt += (data.workCnt || 0)
        }
        cnt += gameHelper.trainTech.getCarriageRoleCnt(this.id)
        return cnt
    }

    public getThemeLvByCnt(cnt: number) {
        let isDorm = this.type == CarriageType.DORM
        let lv = 0
        let unlockCnt = (isDorm ? this.cfg?.roleCnt : this.cfg?.workCnt) || 0
        if (unlockCnt >= cnt) {
            return lv
        }
        let themes = cfgHelper.getThemes(this.id)
        for (let data of themes) {
            unlockCnt += (isDorm ? data.roleCnt : data.workCnt) || 0
            lv = data.order
            if (unlockCnt >= cnt) {
                break
            }
        }
        return lv
    }
    // 解锁
    public async unlockGoodsReq(id: string, extra?: proto.Condition[]) {
        let msg = new proto.C2S_UnlockGoodsMessage({ id, extra })
        let res = await gameHelper.net.request(Msg.C2S_UnlockGoodsMessage, msg, true)
        const { code } = proto.S2C_UnlockGoodsMessage.decode(res)
        code == 0 && this.checkGoods(id, 1)
        return code
    }
    // 升级
    public async levelUpGoodsReq(id: string, level: number) {
        let msg = new proto.C2S_LevelUpGoodsMessage({ id, level })
        let res = await gameHelper.net.request(Msg.C2S_LevelUpGoodsMessage, msg, true)
        const { code } = proto.S2C_LevelUpGoodsMessage.decode(res)
        code == 0 && this.checkGoods(id, level)
        return code
    }
    // goods变动后的处理
    public checkGoods(id: string, level: number) {
        let goods = this.getGoodsObj(id)
        if (!goods) {
            goods = new GoodsObj()
            this.goods.push(goods)
        }
        goods.init(id, level)
        eventCenter.emit(EventType.UNLOCK_TRAIN_GOODS, this.id)
    }

    public getGoodsObj(id: string) {
        return this.goods.find(g => g.id == id)
    }

    //餐厅菜品饮品产出
    public getGoodsAttr() {
        let sum = 0
        this.goods.forEach(e => { sum += e.attrValue })
        return sum
    }

    // 商品解锁或升级消耗
    public getGoodsBuyCost(id: string) {
        let lv = this.getGoodsLv(id) + 1
        let cfg = cfgHelper.getTrainGoodsLevel(id, lv)
        return cfg?.buyCost
    }

    public getGoodsLv(id: string) {
        let obj = this.getGoodsObj(id)
        return obj ? obj.lv : 0
    }

    public isGoodsLvMax(id: string) {
        let obj = this.getGoodsObj(id)
        if (obj) {
            return cfgHelper.getTrainGoodsLevel(id, obj.lv + 1) == null
        }
    }

    public getGoods() {
        return this.goods
    }

    public getGoodsByType(type: GoodsType) {
        return this.goods.filter(f => f.type == type)
    }

    public randomGoodsByRole(type: GoodsType, roleId: number) {
        let likeRate = cfgHelper.getMiscData("trainGoodsLikeWeightRate")
        let ary = this.goods.filter(f => f.type == type).map(item => {
            let weight = 1
            if (item.objects.some(obj => obj.type == GoodsObjectType.CHARACTER && obj.id == roleId)) {
                weight = likeRate
            }
            return { item, weight }
        })
        if (!ary.length) return null
        let rdIndex = gameHelper.randomByWeight(ary)
        return ary[rdIndex].item
    }

    public getFoodItems() {
        let foods = this.getGoodsByType(GoodsType.FOOD)
        let items = []
        for (let food of foods) {
            if (food.foodItem) {
                for (let item of food.foodItem) {
                    if (!items.has(item)) {
                        items.push(item)
                    }
                }
            }
        }
        return items
    }

    public getOutputSum(type: ConditionType) {
        let sum = 0
        sum += Math.floor(this.outputObj.getOutput()) //这里下取整处理，防止两个车厢都是0.5产不出最后一颗星尘的情况
        let drops = this.getAllDrops().filter(d => d.item.type == type)
        for (let drop of drops) {
            sum += drop.getNum()
        }
        return sum
    }

    public getRandomMovePos<T extends { rect?: cc.Rect }>(_areas?: T[], posList?: cc.Vec2[], out?: { [key: string]: any }) {
        let map = this.getMap()
        let reachablePoints = map.getConnectPoints(cc.v2());
        if (reachablePoints.length <= 1) { //与四周都不连通了
            reachablePoints = map.getMainEmptyPoints()
        }
        let retryCount = 10
        let randomFunc = () => {
            while (retryCount--) {
                let point = reachablePoints.random()
                if (map.checkCanPass(pos.x, pos.y)) {
                    return map.getActPixelByPoint(point)
                }
            }
            return cc.v2()
        }

        //尽量找一个远离所有人的位置
        let pos = mapHelper.sparsePosRandom(randomFunc, posList, null, 50)
        return pos
    }

    public isCollectAll() {
        let theme = cfgHelper.getThemes(this.id).find(d => d.collect)
        if (!theme) return false
        return this.isThemeComplete(theme.order)
    }

    private isThemeComplete(lv) {
        if (lv < this.themeLv) {
            return true
        }
        else if (lv == this.themeLv) {
            return this.isAllBuildsMaxLv()
        }
        return false
    }

    public getGoodsCnt() {
        let cnt = 0
        let themes = cfgHelper.getThemes(this.id).filter(d => d.order <= this.themeLv)
        for (let data of themes) {
            if (!this.isThemeComplete(data.order)) continue
            cnt += (data.goodsCnt || 0)
        }
        return cnt
    }

    public setLabelThemeName(label: cc.Label, cfg: CarriageThemeCfg) {
        label.setLocaleKey(cfg.name)
    }

    public setLabelThemeDesc(label: cc.Label, cfg: CarriageThemeCfg) {
        label.setLocaleKey('trainItemBuild_guiText_7', cfg.unlockLevel)
    }

    public themeAddDesc(cfg: CarriageThemeCfg) {
        if (cfg.roleCnt) {
            return assetsMgr.lang('trainItemBuild_guiText_9', cfg.roleCnt)
        } else if (cfg.workCnt) {
            return assetsMgr.lang('trainItemBuild_guiText_10', cfg.workCnt)
        } else if (cfg.collect) {
            return assetsMgr.lang('trainItemBuild_guiText_11')
        } else if (cfg.goodsCnt) {
            return assetsMgr.lang(this.getGoodsText(cfg.carriageId))
        }
    }

    private getGoodsText(id: number) {
        switch (id) {
            case CarriageID.DINING:
                return 'trainItemBuild_guiText_12'
            case CarriageID.BATHROOM:
                return 'trainItemBuild_guiText_13'
            case CarriageID.DANCEHALL:
                return 'trainItemBuild_guiText_14'
            default:
                cc.error("getGoodsText error:", id)
                return '';
        }
    }

    public getGoodsTip() {
        switch (this.id) {
            case CarriageID.DINING:
                return 'trainItemBuild_tips_6'
            case CarriageID.BATHROOM:
                return 'trainItemBuild_tips_7'
            case CarriageID.DANCEHALL:
                return 'trainItemBuild_tips_8'
            default:
                cc.error("getGoodsTip error:", this.id)
                return '';
        }
    }

    public addOutputTime(time) {
        this.outputObj?.addOutputTime(time)
        this.electricOutputObj?.addOutputTime(time)
        this.waterOutputObj?.addOutputTime(time)
        this.vitalityOutputObj?.addOutputTime(time)
    }

    public isVitality() {
        return this.type == CarriageType.VITALITY
    }

    public isDorm() {
        return this.type == CarriageType.DORM
    }

    public getLoad() {
        if (!this.isBuilt) return 0
        return this.cfg.load
    }

    public initBurst() {
        let task = this.getTrainBurstTask()
        if (!task) {
            return
        }

        let count = 20
        let posList = []
        switch (task.id) {
            case TrainBurstTaskType.CLEAN:
                for (let i = 0; i < count; i++  ) {
                    let pos = this.getRandomMovePos(posList)
                    posList.push(pos)
                    this.wastes.push(new BurstItem(pos, task.id))
                }
                break
            case TrainBurstTaskType.FIRE:
                count = 10
                let offsetX = 100
                let offsetY = 300
                let size = CARRIAGE_CONTENT_SIZE
                let randomFunc = () => {
                    let x = ut.randomRange(offsetX, size.width-offsetX)
                    let y = ut.randomRange(100, size.height-offsetY)
                    return cc.v2(x, y)
                }
                for (let i = 0; i < count; i++) {
                    let pos = mapHelper.sparsePosRandom(randomFunc, posList, 200)
                    posList.push(pos)
                    let item = new BurstItem(pos, task.id)
                    item.scale = ut.randomRange(0.5, 1)
                    this.fires.push(item)
                }
                break
        }
    }

    public getTrainBurstTask() {
        return gameHelper.getTrainBurstTask().find(t => t.trainId == this.id)
    }

    public getWastes() {
        return this.wastes
    }

    public getFires() {
        return this.fires
    }

}
