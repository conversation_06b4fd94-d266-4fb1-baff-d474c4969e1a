import { Msg } from "../../../../proto/msg-define";
import { ShopCfg } from "../../../common/constant/DataType";
import { PayPlatformType } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import JsbEvent from "../../../common/event/JsbEvent";
import { gameHelper } from "../../../common/helper/GameHelper";
import { jsbHelper } from "../../../common/helper/JsbHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";

// 支付模块
@mc.addmodel('pay')
export default class PayModel extends mc.BaseModel {

    public data: proto.IPay

    private initFinish: boolean = false
    private iapCfg: any = null

    private restoredSubs: any[] = null

    private notFinishOrders: any[] = []
    private _payCountMap: Map<string, number> = new Map()

    public async init() {
        this.notFinishOrders = this.data.notFinishOrders || []

        this.initFinish = false
        this.iapCfg = null
        this.restoredSubs = null
        if (ut.isMobile()) {
            await this.initIAP()
            await this.initLostOrderList()
        }
        for (const key in this.data.payCountMap) {
            const v = this.data.payCountMap[key]
            if (v <= 0) continue
            this._payCountMap.set(key, this.data.payCountMap[key])
        }
        this.initFinish = true
        eventCenter.emit(EventType.INIT_PAY_FINISH)
    }

    private async initIAP() {
        // 获取商品列表
        let retry = 5
        while (retry > 0) {
            retry--
            const productIds: string[] = assetsMgr.getJson<ShopCfg>('Shop').datas.map(m => m.productId)
            const args = ut.isAndroid() ? { key: productIds } : { key: productIds.join(',') }
            const { error, result, subs } = await jsbHelper.call(JsbEvent.IAP_INIT, args)
            if (error === 'noproducts' || !result) {
                twlog.info('iap init error, no products!')
                break //没有订单
            } else if (!error) {
                let productList: any[] = []
                if (ut.isIos()) {
                    let resultList = String(result).split('|')
                    for (let i = 0; i < resultList.length; ++i) {
                        const res = resultList[i]
                        const [productId, price, currency_pay, currency_price] = res.split(',')
                        productList.push({ productId, price, currency_pay, currency_price })
                    }
                } else if (ut.isAndroid()) {
                    productList = result.concat(subs || [])
                }
                this.iapCfg = {}
                for (let product of productList) {
                    let key = product.productId
                    if (product.offerId) {
                        key = product.offerId
                    } else if (product.planId) {
                        key = product.planId
                    }
                    this.iapCfg[key] = product
                }
                // twlog.info(this.iapCfg)
                break
            } else {
                twlog.info('iap init error,', error)
            }
            await ut.wait(2)
        }
    }

    private async initLostOrderList() {
        // 获取为完成的订单
        const { error, result } = await jsbHelper.getLangOrderList(JsbEvent.GET_LOST_ORDER_LIST)
        if (!error) {
            const platform = this.getPlatform()
            const productMap = {}
            assetsMgr.getJson<ShopCfg>('Shop').datas.forEach(m => productMap[m.productId] = true)
            let orders = result.map(m => this.toOrder(platform, m)).filter(m => {
                if (!m?.orderId) {
                    return false
                } else if (!productMap[m?.productId]) {
                    return false
                }
                return true
            })
            orders.forEach(m => {
                if (m.cpOrderId === 'null') {
                    m.cpOrderId = ''
                }
                const index = this.notFinishOrders.findIndex(o => m.cpOrderId ? o.cpOrderId === m.cpOrderId : o.productId === m.productId)
                if (index >= 0) {
                    const data = this.notFinishOrders[index]
                    if (!m.cpOrderId) {
                        m.cpOrderId = data.cpOrderId
                        m.userId = data.userId || gameHelper.user.getUid()
                    }
                    this.notFinishOrders[index] = m
                } else {
                    this.notFinishOrders.push(m)
                }
            })
        } else {
            twlog.info('GET_LOST_ORDER_LIST error,', error)
        }
    }

    public getNotFinishOrders() {
        return this.notFinishOrders
    }

    // 检测是否有未完成订单
    public checkHasNotFinishOrder(platform: string = this.getPlatform()) {
        // 先过滤一边 删除不是自己的订单
        this.notFinishOrders.delete(m => {
            if (!!m.userId && m.userId !== gameHelper.getUid()) {
                return true
            } else if (!!m.platform && m.platform !== platform) {
                return true
            }
            return false
        })
        return this.notFinishOrders.length > 0
    }

    // 删除未完成的订单
    public removeNotFinishOrders(key: string, val: string) {
        this.notFinishOrders.remove(key, val)
        return this.notFinishOrders
    }

    public clean() {
        this.cleanRestoredSubs()
    }

    public isInitFinish() {
        return this.initFinish
    }

    public isInitSucceed() {
        return !!this.iapCfg
    }

    public async waitInitFinish() {
        if (!this.initFinish) {
            return eventCenter.wait(EventType.INIT_PAY_FINISH)
        }
    }

    // 获取订阅列表
    // public async getSubscriptions() {
    //     if (!this.restoredSubs) {
    //         const { error, result } = await jsbHelper.getLangOrderList(JsbEvent.RESTORED_SUB)
    //         if (!error) {
    //             const platform = this.getPlatform()
    //             const productMap = {}
    //             assetsMgr.getJson<ShopCfg>('Shop').datas.forEach(m => productMap[m.productId] = true)
    //             const subProductMap = {}
    //             if (platform === PayPlatformType.GOOGLE) {
    //                 subProductMap[GOOGLE_SUB_productId] = true
    //             } else if (platform === PayPlatformType.APPLE) {
    //                 APPLE_SUB_productIdS.forEach(m => subProductMap[m] = true)
    //             }
    //             this.restoredSubs = result?.map(m => this.toOrder(platform, m)).filter(m => {
    //                 if (!m?.orderId) {
    //                     return false
    //                 } else if (productMap[m?.productId] || !subProductMap[m?.productId]) {
    //                     return false
    //                 }
    //                 return true
    //             }) || []
    //             this.restoredSubs.sort((a, b) => b.purchaseTime - a.purchaseTime)
    //         } else {
    //             this.restoredSubs = []
    //         }
    //         // twlog.info(this.restoredSubs)
    //         // console.log(JSON.stringify(this.restoredSubs))
    //     }
    //     return this.restoredSubs
    // }

    public cleanRestoredSubs() {
        this.restoredSubs = null
    }

    // 获取订阅商品价格文本
    // public getSubPriceText(type: string) {
    //     if (!this.isInitSucceed()) {
    //         return ''
    //     }
    //     let id = ''
    //     if (ut.isAndroid()) {
    //         id = 'jwm-ad-free-' + type
    //     } else if (ut.isIos()) {
    //         id = 'jwmAdFree' + ut.initialUpperCase(type)
    //     }
    //     const price = this.iapCfg[id]?.price
    //     if (price) {
    //         return price + '/' + assetsMgr.lang('login.time_' + type)
    //     }
    //     return ''
    // }

    // 获取商品价格文本
    public getProductPriceText(id: string) {
        if (this.isInitSucceed()) {
            const iap = this.iapCfg[id]
            if (iap?.price) {
                return iap.price
            }
        }
        const json = assetsMgr.getJson<any>('Shop').get('productId', id)[0]
        if (!json) {
            return ''
        }
        return "￥" + (json?.price || "")
    }

    private lockScreen(val: boolean) {
        mc.lockTouch(val)
        viewHelper.showLoadingWait(val)
    }

    public getPlatform() {
        if (ut.isWechatGame()) {
            return PayPlatformType.WX
        } else if (ut.isQQGame()) {
            return PayPlatformType.QQ
        } else if (!ut.isMobile()) {
            return PayPlatformType.NONE
        } else if (ut.isIos()) {
            return PayPlatformType.APPLE
        } else if (gameHelper.isInland()) {
            return PayPlatformType.APP_WX
        }
        return PayPlatformType.GOOGLE
    }

    public isFirstPay(productId: string): boolean {
        const v = this._payCountMap.get(productId)
        return !v
    }

    // 购买商品
    public async buyProduct(productId: string) {
        this.lockScreen(true)
        twlog.info('0.buyProduct initFinish=' + this.initFinish)
        // 0.检测是否初始化完成
        await this.waitInitFinish()
        // 是否初始化成功
        if (!this.isInitSucceed()) {
            this.lockScreen(false)
            return viewHelper.showAlert("common_tips_6")
        }
        // 1.先请求服务器创建订单
        const platform = this.getPlatform()
        twlog.info('1.createPayOrder productId=' + productId + ', platform=' + platform)
        let res = await gameHelper.net.request(Msg.C2S_CreatePayOrderMessage, proto.C2S_CreatePayOrderMessage.create({ productId, platform }))
        let info = proto.S2C_CreatePayOrderMessage.decode(res)
        if (info.code != 0) {
            this.lockScreen(false)
            return viewHelper.showNetError(info.code)
        }
        // 2.拉起支付
        twlog.info('2.doPay...')
        const order = await this.doPay(platform, productId, info.uid)
        if (!order.orderId) {
            this.lockScreen(false)
            return viewHelper.showAlert("common_tips_6")
        }
        if (!order.cpOrderId) {
            order.cpOrderId = info.uid
        }
        // 3.发送到服务器验证
        twlog.info('3.verifyPayOrder order=', order)
        res = await gameHelper.net.request(Msg.C2S_VerifyPayOrderMessage, proto.C2S_VerifyPayOrderMessage.create(order))
        let info2 = proto.S2C_VerifyPayOrderMessage.decode(res)
        if (info2.code != 0) {
            this.lockScreen(false)
            return viewHelper.showNetError(info2.code)
        }
        // eventReportHelper.reportGlobalEventOne('event_purchase') //付费成功 上报
        // eventReportHelper.reportAppflyerEvent('af_purchase', { af_revenue: order.payAmount * order.quantity, af_currency: order.currencyType, af_quantity: order.quantity, af_content_id: order.productId })
        // 4.标记消费结束
        twlog.info('4.consume...')
        await jsbHelper.call(JsbEvent.CONSUME_ORDER, { token: order.token })
        // 5.领取奖励
        res = await gameHelper.net.request(Msg.C2S_GetPayRewardsMessage, proto.C2S_GetPayRewardsMessage.create({ uid: order.cpOrderId }))
        let info3 = proto.S2C_GetPayRewardsMessage.decode(res)
        if (info3.code != 0) {
            this.lockScreen(false)
            return viewHelper.showNetError(info3.code)
        }
        twlog.info('5.getPayRewards res=', res)
        const json = assetsMgr.getJson<ShopCfg>('Shop').get('productId', productId)[0]
        let rewards = gameHelper.toConditions(json.product.concat(json.gifts))
        gameHelper.grantRewardAndShowUI(rewards)
        this.lockScreen(false)
    }

    // 拉起支付
    private async doPay(platform: string, productId: string, cpOrderId: string, subInfo?: any) {
        const uid = gameHelper.getUid()
        let eventName = '', data: any = { pay_id: productId, cpOrderId: cpOrderId + '_' + uid }
        if (platform === PayPlatformType.GOOGLE) {
            eventName = JsbEvent.GOOGLE_PAY
        } else if (platform === PayPlatformType.APPLE) {
            eventName = JsbEvent.APPLE_PAY
        } else {
            return {}
        }
        const { error, result } = await jsbHelper.call(eventName, data)
        twlog.info('2.doPay result=', result, error)
        return this.toOrder(platform, result || {})
    }

    private toOrder(platform: string, res: any) {
        const order: any = { platform }
        if (platform === PayPlatformType.GOOGLE) {
            const [cpOrderId, userId] = (res.obfuscatedAccountId || '').split('_')
            order.orderId = res.orderId
            order.token = res.purchaseToken
            order.productId = res.productId
            order.cpOrderId = cpOrderId
            order.userId = userId || ''
            order.purchaseTime = Number(res.purchaseTime)
            order.quantity = res.quantity || 1
        } else if (platform === PayPlatformType.APPLE) {
            const [cpOrderId, userId] = (res.applicationUsername || '').split('_')
            order.orderId = res.transactionID
            order.token = res.receiptCipheredPayload
            order.productId = res.id
            order.cpOrderId = cpOrderId
            order.userId = userId || ''
            order.purchaseTime = res.purchaseTime ? Number(res.purchaseTime) * 1000 : Date.now()
            order.quantity = res.quantity || 1
        }
        if (order.cpOrderId === 'null' || !order.cpOrderId) {
            order.cpOrderId = ''
        }
        const iap = this.iapCfg?.[order.productId]
        order.currencyType = iap?.currency_pay || 'none'
        order.payAmount = Number(iap?.currency_price) || 0
        return order
    }

    // // 购买订阅
    // public async buySubscription(type: string, isFree: boolean) {
    //     let productId = ''
    //     if (ut.isAndroid()) {
    //         productId = GOOGLE_SUB_productId
    //     } else if (ut.isIos()) {
    //         productId = 'jwmAdFree' + ut.initialUpperCase(type)
    //     } else {
    //         viewHelper.showAlert(ecode.PAY_FAIL)
    //         return false
    //     }
    //     this.lockScreen(true)
    //     twlog.info('0.buyProduct initFinish=' + this.initFinish)
    //     // 0.检测是否初始化完成
    //     await this.waitInitFinish()
    //     // 是否初始化成功
    //     if (!this.isInitSucceed()) {
    //         this.lockScreen(false)
    //         viewHelper.showAlert(ecode.PAY_FAIL)
    //         return false
    //     }
    //     // 1.先请求服务器创建订单
    //     const platform = this.getPlatform()
    //     twlog.info('1.createSubOrder productId=' + productId + ', platform=' + platform + ', type=' + type + ', isFree' + isFree)
    //     let res = await gameHelper.net.request('login/HD_CreateSubOrder', { productId, platform, type })
    //     if (res.err) {
    //         this.lockScreen(false)
    //         viewHelper.showAlert(res.err)
    //         return false
    //     }
    //     // 2.拉起支付
    //     twlog.info('2.doPay...')
    //     const order = await this.doPay(platform, productId, res.data.uid, { type, isFree })
    //     if (!order.orderId) {
    //         this.lockScreen(false)
    //         viewHelper.showAlert(ecode.PAY_FAIL)
    //         return false
    //     }
    //     // 3.发送到服务器验证
    //     twlog.info('3.verifySubOrder order=', order)
    //     res = await gameHelper.net.request('login/HD_VerifySubOrder', order)
    //     if (res.err) {
    //         this.lockScreen(false)
    //         viewHelper.showAlert(res.err)
    //         return false
    //     }
    //     eventReportHelper.reportGlobalEventOne('event_purchase') //付费成功 上报
    //     eventReportHelper.reportAppflyerEvent('af_purchase', { af_revenue: order.payAmount * order.quantity, af_currency: order.currencyType, af_quantity: order.quantity, af_content_id: order.productId })
    //     // 4.标记消费结束
    //     twlog.info('4.consume...')
    //     await jsbHelper.call(JsbEvent.CONSUME_ORDER, { token: order.token, type: "subs" })
    //     viewHelper.showAlert('toast.buy_gold_succeed')
    //     this.lockScreen(false)
    //     return true
    // }
}