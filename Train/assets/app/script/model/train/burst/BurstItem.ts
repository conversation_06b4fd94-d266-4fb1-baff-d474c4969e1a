import { MAX_ZINDEX } from "../../../common/constant/Constant"
import { TrainBurstTaskType } from "../../../common/constant/Enums"

export default class BurstItem {
    constructor(pos: cc.Vec2, type: TrainBurstTaskType) {
        this.pos = pos
        this.type = type
    }

    public pos: cc.Vec2 = null
    public scale: number = 1
    public type: TrainBurstTaskType = null
    public userId: number = null //占用者的id

    public getZIndex() {
        return MAX_ZINDEX - this.pos.y
    }
}