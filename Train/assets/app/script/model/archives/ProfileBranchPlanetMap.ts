import BranchPlanetMap from "../planet/BranchPlanetMap"
import { ConditionType, PlanetNodeType } from "../../common/constant/Enums"
import ProfileBranchMineModel from "./ProfileBranchMineModel"
import { gameHelper } from "../../common/helper/GameHelper"
import EventType from "../../common/event/EventType"
import PlanetQuestionModel from "../planet/PlanetQuestionModel"
import { CharacterProfileCfg, QuestionCfg } from "../../common/constant/DataType"
import { cfgHelper } from "../../common/helper/CfgHelper"

export default class ProfileBranchPlanetMap extends BranchPlanetMap {

    protected initJson() {
        let json = {
            node: [],
            id: 1,
            width: 0,
            start: {
                x: 249,
                y: 23,
            }
        }
        let datas = gameHelper.profileBranch.getCurLevel().nodes
        let x = 970, y = x * 0.125
        let spaceX = 207 * 0.67
        let typeMap = {}
        let index = 1
        for (let data of datas) {
            let node: any = {}
            let dx = spaceX * 6
            let type = data.type
            if (!typeMap[type]) {
                typeMap[type] = 0
            }
            node.type = type
            if (type == PlanetNodeType.QUESTION) {
                dx += spaceX * 2
            }
            else if (type == PlanetNodeType.MINE) {
                dx += 90 * 2
            }
            node.id = ++typeMap[type]
            node.index = index++
            node.mineId = data.id

            node.x = x
            node.y = y

            x += dx
            y += Math.round(dx * 0.125)

            node.reward = data.reward
            json.node.push(node)
        }
        json.width = x + 1040
        this.json = json
    }

    public initNodes() {
        super.initNodes()
        for (let i = 0; i < this.nodes.length; i++) {
            let node = this.nodes[i]
            let data = this.json.node[i]
            if (node instanceof PlanetQuestionModel) {
                this.initQuestion(node)
            }
            node.rewards = gameHelper.toConditions(data.reward)
        }
    }

    private initQuestion(node: PlanetQuestionModel) {
        let questions = []
        let levels = gameHelper.profileBranch.getLevels()
        let exclusiveMap = {}
        let commonSum = 0
        let curLevel = gameHelper.profileBranch.getCurLevel()
        let curIndex = 0
        for (let level of levels) {
            let nodes = level.getNodes().filter(n => n.type == PlanetNodeType.QUESTION)
            for (let i = 0; i < nodes.length; i++) {
                if (level == curLevel && node.typeIndex == i + 1) {
                    curIndex = i
                    break
                }
                let rewards = nodes[i].reward
                let question = level.cfg.questions[i]
                let exclusive = question.exclusive
                for (let j = 0; j < exclusive; j++) {
                    let index = j % rewards.length
                    let reward = rewards[index]
                    let roleId = assetsMgr.getJsonData<CharacterProfileCfg>("CharacterProfile", reward.id).characterId
                    exclusiveMap[roleId] = (exclusiveMap[roleId] || 0) + 1
                }
                commonSum += question.common
            }
            if (level == curLevel) {
                break
            }
        }

        let curNode = curLevel.getNodes().filter(n => n.type == PlanetNodeType.QUESTION)[curIndex]
        let rewards = curNode.reward
        let question = curLevel.cfg.questions[curIndex]
        let exclusiveNum = question.exclusive
        let commonNum = question.common
        for (let j = 0; j < exclusiveNum; j++) {
            let index = j % rewards.length
            let reward = rewards[index]
            let roleId = assetsMgr.getJsonData<CharacterProfileCfg>("CharacterProfile", reward.id).characterId
            let datas = cfgHelper.getQuestions(roleId)
            let sum = (exclusiveMap[roleId] || 0)
            sum %= datas.length
            questions.push(datas[sum])
            exclusiveMap[roleId] = (exclusiveMap[roleId] || 0) + 1
        }

        let commonDatas = cfgHelper.getQuestions()
        for (let j = 0; j < commonNum; j++) {
            let sum = (commonSum || 0) + j
            sum %= commonDatas.length
            questions.push(commonDatas[sum])
        }

        node.setQuestions(questions.map(q => q.id))
    }

    protected newModel(type: PlanetNodeType) {
        if (type == PlanetNodeType.MINE) {
            return new ProfileBranchMineModel()
        }
        return super.newModel(type)
    }

    public setProgress(progress: number) {
        this.progress = progress
        this.branch.curNode = this.getCurNode()
        gameHelper.profileBranch.setNodeId(progress)
        if (progress > 0) {
            eventCenter.emit(EventType.PLANET_NODE_COMPLETE, this.planetId, this.getPreNode())
        }
    }
}