import { game<PERSON>elper } from "../../common/helper/GameHelper"
import PlanetBranch from "../planet/PlanetBranch"
import PlanetModel from "../planet/PlanetModel"
import ProfileBranchPlanetMap from "./ProfileBranchPlanetMap"

export default class ProfilePlanetBranch extends PlanetBranch {

    private levelId: number = 0

    public init(planet: PlanetModel, data) {
        let id = data.id
        this.levelId = gameHelper.profileBranch.getCurLevel().id
        this.planet = planet
        this.id = id
        this.initJson()
        this.mapId = 1
        let nodeId = gameHelper.profileBranch.getNodeId()
        
        this.initMaps()

        let curMapId = this.mapId
        for (let map of this.maps) {
            if (map.getId() < curMapId || curMapId == -1) {
                map.setDone()
            }
        }
        if (curMapId >= 1) {
            let curMap = this.getCurMap()
            curMap.setProgress(nodeId - 1)
            curMap.initNodeEnd()
        }
        else {
            this.mapId = this.maps.last().getId()
        }
        
        let node = this.getCurMap().getCurNode()
        this.curNode = node
        return this
    }

    protected initMaps() {
        this.maps = [new ProfileBranchPlanetMap().init(this.planet, 1, this)]
    }

    public reset() {
        if (this.levelId == gameHelper.profileBranch.getCurLevel().id) {
            return
        }
        this.init(this.planet, { id: this.id })
    }
}