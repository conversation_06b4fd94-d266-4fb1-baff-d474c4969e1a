import { util } from "../../../core/utils/Utils"
import { Msg } from "../../../proto/msg-define"
import { CharacterProfileCfg, ProfileBranchLevelCfg } from "../../common/constant/DataType"
import { ConditionType, PlanetNodeType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import ProfilePlanetBranch from "./ProfilePlanetBranch"

export class ProfileBranchLevel {
    public id: number
    public roles: number[]
    public nodes: proto.IMapMineItemData[]
    private _nodeId: number = 1
    public cfg: ProfileBranchLevelCfg = null
    public state: proto.ProfileBranchLevelState = proto.ProfileBranchLevelState.Locked
    public unlock: boolean = false

    get nodeId() { return this._nodeId }
    set nodeId(value) {
        this._nodeId = value
    }

    public init(data: proto.IProfileBranchLevel) {
        this.id = data.id
        this.nodes = data.nodes
        this.nodeId = data.nodeId || 1
        // 有没有点 "开启记忆"
        this.unlock = data.unlock
        this.initRoles()
        this.cfg = assetsMgr.getJsonData<ProfileBranchLevelCfg>("ProfileBranchLevel", this.id)
        return this
    }

    public initByCfg(cfg: ProfileBranchLevelCfg) {
        this.id = cfg.id
        this.nodes = []
        this.roles = []
        this.nodeId = 0
        this.cfg = cfg
        this.state = this.isCanUnlock() ? proto.ProfileBranchLevelState.ReadyUnlock : proto.ProfileBranchLevelState.Locked
        return this
    }

    private initRoles() {
        let map = {}
        for (let node of this.nodes) {
            if (node.type != PlanetNodeType.QUESTION) continue
            let rewards = node.reward
            for (let reward of rewards) {
                let roleId = assetsMgr.getJsonData<CharacterProfileCfg>("CharacterProfile", reward.id).characterId
                if (!map[roleId]) {
                    map[roleId] = true
                }
            }
        }
        this.roles = Object.keys(map).map(Number)
    }

    public getNode(nodeId: number) {
        return this.nodes[nodeId]
    }

    public getNodes() {
        return this.nodes
    }

    public setNodeId(nodeId: number) {
        this.nodeId = nodeId
    }

    public isDone() {
        return this.nodeId >= this.nodes.length
    }

    public isCanUnlock(): boolean {
        const pass = gameHelper.checkConditions(gameHelper.toConditions(this.cfg.request))
        if (!pass) return false
        const reach = gameHelper.profileBranch.getProgress(this.id - 1)
        if (this.id > 1 && reach < 100) return false
        const roles = this.roles.filter((roleId) => gameHelper.passenger.getPassenger(roleId))
        if (roles.length < this.roles.length) return false
        return true
    }
}


@mc.addmodel('profileBranch', 100)
export default class ProfileBranchMgr extends mc.BaseModel {

    public data: proto.IProfileBranch

    private levels: ProfileBranchLevel[] = []
    private energy: number = 0
    private id: number = 1
    private energyRecoverEndTime: number = 0

    public init() {
        const levelConfig = assetsMgr.getJson<ProfileBranchLevelCfg>("ProfileBranchLevel").datas
        this.levels = []
        this.id = this.data.id
        for (const l of levelConfig) {
            const level = this.data.levels.find(level => level.id == l.id)
            if (level) {
                this.levels.push(new ProfileBranchLevel().init(level))
            }
        }

        this.energy = this.data.energy
        this.updateEnergyRecoverEndTime(this.data.surplusTime)
    }

    private updateEnergyRecoverEndTime(surplusTime) {
        this.energyRecoverEndTime = surplusTime + gameHelper.now()
    }

    public getRecoverTime() { return this.energyRecoverEndTime - gameHelper.now() }

    public async passNode(nodeId: number) {
        let { code, level } = await gameHelper.net.requestWithData(Msg.C2S_ProfileBranchPassNodeMessage, { nodeId })
        if (code == 0) {
            if (level) {
                let index = this.levels.findIndex(l => l.id == level.id)
                if (index >= 0) {
                    this.levels[index].init(level)
                }
                else {
                    this.levels.push(new ProfileBranchLevel().init(level))
                }
            }
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public getCurLevel() {
        return this.levels[this.id - 1]
    }

    public getLevels() {
        const levels = this.levels
        const ary = []
        for (const lv of levels) {
            if (lv.unlock) {
                lv.state = proto.ProfileBranchLevelState.Unlocked
                if (lv.nodeId >= lv.nodes.length) {
                    lv.state = proto.ProfileBranchLevelState.AnswerDone
                }
            }
            else {
                lv.state = lv.isCanUnlock() ? proto.ProfileBranchLevelState.ReadyUnlock : proto.ProfileBranchLevelState.Locked
            }
            ary.push(lv)
        }

        const levelConfig = assetsMgr.getJson<ProfileBranchLevelCfg>("ProfileBranchLevel").datas
        for (const l of levelConfig) {
            const level = levels.find(level => level.id == l.id)
            if (level) {
                continue
            }
            ary.push(new ProfileBranchLevel().initByCfg(l))
        }
        return ary
    }

    public getNode(nodeId: number) {
        return this.getCurLevel().getNode(nodeId)
    }

    public setNodeId(nodeId: number) {
        let curLevel = this.getCurLevel()
        curLevel.setNodeId(nodeId)
        if (curLevel.isDone()) {
            this.id++
            curLevel.state = proto.ProfileBranchLevelState.AnswerDone
        }
    }

    public getNodeId() {
        return this.getCurLevel().nodeId
    }

    public async startQuestion() {
        let { code, energy, surplusTime } = await gameHelper.net.requestWithDataWait(Msg.C2S_ProfileBranchQuestionMessage)
        if (code == 0) {
            this.energy = energy
            this.updateEnergyRecoverEndTime(surplusTime)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

    public getEnergy() {
        return this.energy
    }

    public changeEnergy(energy: number) {
        this.energy += energy
    }

    public reset() {
        let planet = gameHelper.planet.getPlanet(1001)
        if (!planet) return
        let branch = planet.branches[0] as ProfilePlanetBranch
        branch.reset()
    }

    update(dt: number) {
        if (this.energy < this.getMaxEnergy()) {
            if (this.energyRecoverEndTime <= gameHelper.now()) {
                this.syncEnergy()
            }
        }
    }

    public getMaxEnergy() { return cfgHelper.getMiscData("profileBranch").maxEnergy }
    public getEnergyRecoverTime() { return cfgHelper.getMiscData("profileBranch").recoverTime }

    @util.addLock
    private async syncEnergy() {
        let { code, energy, surplusTime } = await gameHelper.net.requestWithData(Msg.C2S_ProfileBranchSyncEnergyMessage)
        if (code == 0) {
            this.energy = energy
            this.updateEnergyRecoverEndTime(surplusTime)
        }
        else {
            viewHelper.showNetError(code)
        }
    }

    public getProgress(id: number) {
        const level = this.levels.find(l => l.id == id)
        if (!level) return 0
        if (level.nodeId >= level.nodes.length) return 100
        if (level.nodeId <= 0) return 0
        return Math.floor(level.nodeId / level.nodes.length * 100)
    }
    public getCurProgress() { return this.getProgress(this.id) }
    public getLastProgress() { return this.id > 1 ? this.getProgress(this.id - 1) : this.getCurProgress() }

    public async unlock(data: ProfileBranchLevel) {
        if (!data || data.state != proto.ProfileBranchLevelState.ReadyUnlock) return
        if (!data.isCanUnlock()) return void console.error("不满足解锁条件")

        const { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_ProfileBranchUnlockMessage, { id: data.id })
        if (code != 0) return void viewHelper.showNetError(code)
        data.unlock = true
        data.state = proto.ProfileBranchLevelState.Unlocked
        this.reset()
        eventCenter.emit(EventType.UNLOCK_PLANET_QUESTION, data)
    }
}