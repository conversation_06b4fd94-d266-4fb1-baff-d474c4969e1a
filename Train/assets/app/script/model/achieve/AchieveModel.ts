import { AchievementCfg, IdTargets } from "../../common/constant/DataType"
import { getObjectTask } from "../task/TaskObject"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { TaskType } from "../../common/constant/Enums"
import { Msg } from "../../../proto/msg-define"
import EventType from "../../common/event/EventType"
import TaskBaseModel from "../task/TaskBaseModel"

/**成就数据*/
@mc.addmodel('achieve', -1)
export default class AchieveModel extends TaskBaseModel {

    private cacheMap: { [typeId: number]: { trigger: boolean } } = {}

    public data: proto.IAchievementInfo = null

    public init() {
        super.init()
        this.initCache()
        return this
    }

    protected syncTaskProgress() {
        for (let { id, targets } of this.data.tasks) {
            let cfg = cfgHelper.getAchievementCfg(id)
            if (cfg) {
                this.syncOneTargets(String(cfg.typeId), targets)
            }
        }
    }

    private initCache() {
        let all = cfgHelper.getAllAchievement()
        for (const typeId in all) {
            this.cacheMap[typeId] = { trigger: false }
        }
    }

    public getCurAchieves() {
        let ary: AchievementCfg[] = []
        let all = cfgHelper.getAllAchievement()
        for (const typeId in all) {
            let list = all[typeId]
            if (this.checkAchieveCanDo(Number(typeId), list)) {
                ary.push(this.getCurLvCfg(list))
            }
        }
        return ary
    }
    public getCompleteAchieves() {
        let ary: AchievementCfg[] = []
        let all = cfgHelper.getAllAchievement()
        for (const typeId in all) {
            let list = all[typeId]
            for (const cfg of list) {
                if (this.isTaskComplete(cfg.id)) {
                    ary.push(cfg)
                } else {
                    break
                }
            }
        }
        return ary
    }
    private getCurLvCfg(ary: AchievementCfg[]) {
        for (const cfg of ary) {
            if (!this.isTaskComplete(cfg.id)) {
                return cfg
            }
        }
    }
    private changeData(cfg: AchievementCfg): IdTargets {
        return { id: String(cfg.typeId), target: cfg.target }
    }
    private checkAchieveCanDo(typeId: number, ary: AchievementCfg[]) {
        let last = ary[ary.length - 1]
        if (this.isTaskComplete(last.id)) return false
        return this.checkAchieveCanTrigger(typeId)
    }
    private checkAchieveCanTrigger(typeId: number) {
        let dic = this.cacheMap[typeId]
        if (!dic) return false
        if (dic.trigger) return true
        let cfg = cfgHelper.getAchievementTypeCfg(typeId)
        let bol = cfg && this.checkCanTrigger(cfg.trigger)
        if (bol) this.cacheMap[typeId].trigger = true
        return bol
    }
    private getAchieveObject(cfg: AchievementCfg) {
        let cfgT = cfgHelper.getAchievementTypeCfg(cfg.typeId)
        if (!cfgT) return
        let type = cfgT.type
        let obj = getObjectTask(type)
        if (!obj) return twlog.error('getAchieveObject error-type', type)
        return obj
    }
    public checkAchieveCanComplete(cfg: AchievementCfg) {
        let obj = this.getAchieveObject(cfg)
        return obj && obj.checkComplete(this, this.changeData(cfg), obj)
    }
    public checkRedDot() {
        for (const cfg of this.getCurAchieves()) {
            if (this.checkAchieveCanComplete(cfg)) {
                return true
            }
        }
    }
    public getAchieveCurMax(cfg: AchievementCfg) {
        let obj = this.getAchieveObject(cfg)
        return obj && obj.getCurMax(this, this.changeData(cfg), obj)
    }
    public getSimpleParams(cfg: AchievementCfg) {
        let obj = this.getAchieveObject(cfg)
        if (!obj) return
        let fun = obj.getAchiveSimpleParams
        if (!fun) return twlog.error('getSimpleParams no fun', cfg.id)
        return fun(this.changeData(cfg))
    }
    public onAddCommon(giveType: TaskType, conditionFun?: (cfg: IdTargets) => Boolean, addFun?: (cfg: IdTargets) => void) {
        let bolUpdate = false
        for (const cfg of this.getCurAchieves()) {
            let cfgT = cfgHelper.getAchievementTypeCfg(cfg.typeId)
            if (cfgT && cfgT.type == giveType) {
                let data = this.changeData(cfg)
                if (!conditionFun || conditionFun(data)) {
                    bolUpdate = true
                    addFun && addFun(data)
                }
            }
        }
        if (bolUpdate) eventCenter.emit(EventType.ACHIEVE_UPDATE)
    }

    @ut.addLock
    public async completeAchieveAndGetRewardBySever(id: string) {
        let msg = new proto.C2S_ClaimAchievementRewardMessage({ id })
        let res = await gameHelper.net.request(Msg.C2S_ClaimAchievementRewardMessage, msg, true)
        const { code } = proto.S2C_ClaimAchievementRewardMessage.decode(res)
        if (code == 0) {
            await this.completeAchieveAndGetReward(id)
            return true
        } else {
            viewHelper.showNetError(code)
            return false
        }
    }
    public async completeAchieveAndGetReward(id: string) {
        this.setTaskComplete(id)
        let cfg = cfgHelper.getAchievementCfg(id)
        if (cfg) await gameHelper.grantRewardAndShowUI(gameHelper.toConditions(cfg.reward))
        eventCenter.emit(EventType.ACHIEVE_COMPLETE, id)
    }
    protected aryChangeDic() {
        let dic = {}
        let ary1 = this.data.completes || []
        let ary2 = this.data.completeTime || []
        for (let i = 0; i < ary1.length; i++) {
            dic[ary1[i]] = ary2[i]
        }
        this.dicTaskComplete = dic
    }
    protected setTaskComplete(id: string) {
        this.aryTaskComplete.push(id)
        this.dicTaskComplete[id] = gameHelper.now()
    }
    public getCompleteTime(id: string) {
        return this.dicTaskComplete[id]
    }
}