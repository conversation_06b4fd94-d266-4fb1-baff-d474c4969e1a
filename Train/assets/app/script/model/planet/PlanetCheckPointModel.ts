/**
 * 需要战斗的关卡
 */

import { ChapterPlanetMonsterCfg, PlanetMonsterCfg } from "../../common/constant/DataType"
import { gameHelper } from "../../common/helper/GameHelper"
import Monster from "../battle/Monster"
import PlanetNodeModel from "./PlanetNodeModel"

export default class PlanetCheckPointModel extends PlanetNodeModel {

    public monsters: Monster[] = []

    private baseJson: PlanetMonsterCfg = null

    public init(id: string) {
        this.id = id
        this.initJson()
        if (!this.json) return
        this.initReawrds()
        this.initMonsters()
        this.initBaseJson()
        return this
    }

    protected initJson() {
        let name = "ChapterPlanetMonster"
        if (this.map.getBranch()) {
            name = "BranchPlanetMonster"
        }
        this.json = assetsMgr.getJsonData<ChapterPlanetMonsterCfg>(name, this.id)
    }

    protected initBaseJson() {
        let baseJson = assetsMgr.getJsonData<PlanetMonsterCfg>("PlanetMonster", this.roleId)
        this.baseJson = baseJson
        if (baseJson.reachOffset) {
            this.reachOffset = cc.v2(baseJson.reachOffset)
        }
    }

    protected initMonsters() {
        let monster = this.json.monster
        this.monsters = monster.map((m) => {
            let monster = new Monster().init(m.id, m.lv, m.starLv)
            return monster
        })
    }

    get roleId() { return this.json?.iconId || this.monsters[0]?.id }
    get prefab() { return this.json?.prefab}
    get plotKey() { return this.json?.plotKey }
    get type() { return this.json?.type }
    get battleScene() { return this.json?.battleScene }
    get iconBig() { return this.baseJson.iconBig }
    get isWin() { return false }

    public async die() {
        if (this.dead) return true
        this.dead = true
        this.map.nextNode()
        return true
    }

    public async syncDie(res?: any) {
        let succ = await super.syncDie()
        if (succ) {
            gameHelper.grantRewards(this.rewards)
        }
        return succ
    }

    public getLevelIndex() {
        let nodes = []
        let branch = this.getBranch()
        if (branch) {
            nodes = branch.getNodes()
        }
        else {
            let area = this.map.getArea()
            nodes = area.getNodes()
        }
        nodes = nodes.filter(n => n instanceof PlanetCheckPointModel)
        return nodes.indexOf(this) + 1
    }
}
