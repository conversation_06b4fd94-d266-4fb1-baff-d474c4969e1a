/**
 * 星图
 */
import { Msg } from "../../../proto/msg-define"
import { PlanetAreaCfg, PlanetCfg, PlanetProfileCfg, PublicityPlayCfg } from "../../common/constant/DataType"
import { ConditionType, ItemID, PlanetNodeType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import ProfilePlanetBranch from "../archives/ProfilePlanetBranch"
import ConditionObj from "../common/ConditionObj"
import RewardPackage from "../common/RewardPackage"
import PlanetArea from "./PlanetArea"
import PlanetBranch from "./PlanetBranch"
import PlanetMap from "./PlanetMap"
import PlanetNodeModel from "./PlanetNodeModel"

export default class PlanetModel {

    private id: number = 0
    public json: PlanetCfg = null
    private maps: PlanetMap[] = []
    private curMapId: number = 0

    private landed: boolean = false //是否登录过星球
    public reached: boolean = false //是否到达过星球
    public showReachAnim: boolean = false
    public showLandAnim: boolean = false
    private rewards: ConditionObj[] = [] //探索完成奖励

    public immediateDone: boolean = true//默认立即跳转
    private nodeProgress: number = 0 //当前节点进度

    public curNode: PlanetNodeModel = null //当前节点数据，存本地

    public areas: PlanetArea[] = []

    public branches: PlanetBranch[] = []

    public branchId: string = null //当前支线id，如果为空说明是主线

    private profile: { [k: string]: number } = {} // 已经解锁的贴纸
    private profileCollectReward: { [area: number]: number[] } = {}// 领取了奖励的区域
    private roleNum: number = 0 // 宣传局玩法招募人数
    private publicityUnGetTime: number = 0

    public init(id: number) {
        this.id = id
        this.initJson()
        this.initAreas()
        this.initMaps()
        this.curMapId = this.maps[0]?.getId()
        this.curNode = this.getCurNode()
        this.branches = assetsMgr.getJson<PlanetAreaCfg>("PlanetBranch").datas.filter(d => d.planetId == id).map(d => this.newBranch().init(this, { id: d.id }))
        return this
    }

    public toDB() {
        return {
            id: this.id,
            maps: this.maps.map(m => m.toDB()),
            showReachAnim: this.showReachAnim,
            showLandAnim: this.showLandAnim,
            curNode: this.curNode?.toDB(),
            branches: this.branches.map(b => b.toDB())
        }
    }

    public fromDB(data: proto.IPlanet, localData) {
        let { maps, showReachAnim, showLandAnim, curNode } = localData || {}
        this.id = data.id
        this.showReachAnim = showReachAnim
        this.showLandAnim = showLandAnim
        this.initJson()
        this.initAreas()
        this.initMaps(maps)
        this.updateInfo(data, localData)
        if (this.curNode && curNode) {
            if (this.curNode.getId() == curNode.id) {
                this.curNode.fromDB(curNode)
            }
        }
        this.roleNum = data.roleNum
        this.publicityUnGetTime = data.publicityUnGetOutputTime
        return this
    }

    public updateInfo(info: proto.IPlanet, localData?) {
        if (this.maps.length <= 0) {
            return
        }
        let curMapId = info.curMapId
        let curNodeId = info.curNodeId
        this.curMapId = curMapId
        this.landed = info.landed
        this.reached = info.reached
        let oldNode = this.curNode
        for (let map of this.maps) {
            if (map.getId() < curMapId || curMapId == -1) {
                map.setDone()
            }
        }
        if (curMapId >= 1) {
            let curMap = this.getCurMap()
            if (localData) {
                curMap.initNodes()
            }
            curMap.setProgress(curNodeId - 1)
            curMap.initNodeEnd()
        }
        else {
            this.curMapId = this.maps.last().getId()
        }
        let node = this.getCurNode()
        this.curNode = node
        if (node) {
            node.setProgress(info.nodeProgress || 0)
            node.setPathProgress(info.nodePathProgress || 0)
            if (oldNode && this.curNode.getId() == oldNode.getId()) {
                node.fromDB(oldNode.toDB())
            }
        }

        if (localData) {
            let localBranches = localData?.branches || []
            this.branches = info.branches.map(b => {
                let localData = localBranches.find(l => l.id == b.id)
                return this.newBranch().fromDB(this, b, localData)
            })
        }
        else {
            this.branches = info.branches.map(b => {
                return this.newBranch().fromDB(this, b)
            })
        }
        if (info.profileData) {
            this.profile = info.profileData
        }
        if (info.profileCollectReward) {
            for (let i = 0; i < info.profileCollectReward.length;) {
                const area = info.profileCollectReward[i]
                i++
                this.profileCollectReward[area] = this.profileCollectReward[area] || []
                this.profileCollectReward[area].push(info.profileCollectReward[i])
                i++
            }
        }
    }

    private newBranch() {
        if (this.id == 1001) {
            return new ProfilePlanetBranch()
        }
        return new PlanetBranch()
    }

    protected initJson() {
        let json = assetsMgr.getJsonData<PlanetCfg>("Planet", this.id)
        this.json = json
    }

    private initMaps(mapData = []) {
        let mapJson = cfgHelper.getPlanetJson(this.id, "PlanetMap")?.datas || []
        this.maps = mapJson.map(({ id }) => {
            let data = mapData.find(m => m.id == id)
            let map = new PlanetMap()
            if (data) {
                return map.fromDB(data, this)
            }
            else {
                return map.init(this, id)
            }
        })
    }

    private initAreas() {
        let datas = assetsMgr.getJson<PlanetAreaCfg>("PlanetArea").datas.filter(d => d.planetId == this.id)
        this.areas = datas.map(d => new PlanetArea().init(d.id))
    }

    public getMaps() {
        return this.maps
    }
    public haveMaps() {
        return this.maps.length > 0
    }

    public getId() { return this.id }
    public isLanded() { return this.landed }
    public isHide() { return !this.reached }
    public getShowName() {
        if (this.id == 1006 || !this.isHide()) {
            return assetsMgr.lang(this.name)
        }
        return "? ? ?"
    }
    public getMap(id: number) { return this.maps.find(m => m.getId() == id) }
    public getCurMap() { return this.getMap(this.curMapId) }
    public getNextMap() { return this.getMap(this.curMapId + 1) }
    public getNodeCount() {
        let count = 0
        for (let map of this.maps) {
            count += map.getNodeCount()
        }
        return count
    }
    public getNodes() {
        let nodes = []
        for (let map of this.maps) {
            nodes.pushArr(map.getNodes())
        }
        return nodes
    }
    public getCheckPoints() {
        let nodes = this.getNodes()
        return nodes.filter(m => m.nodeType == PlanetNodeType.CHECK_POINT)
    }
    public getNodeProgress() { return this.nodeProgress }
    public setNodeProgress(val) { this.nodeProgress = val }

    public getProgress() {
        let tot = 0
        for (let map of this.maps) tot += map.getProgress()
        return tot
    }

    public getPercent() {
        let completeCnt = this.getProgress()
        let tot = this.getNodeCount()
        return completeCnt / tot
    }

    public get0To100Percent() {
        let float = this.getPercent()
        return float >= 1 ? 100 : Math.floor(float * 100)
    }

    public isDone() {
        return this.getPercent() >= 1
    }

    get name() { return this.json?.name || "" }
    get content() { return this.json?.content || "" }
    get icon() { return this.json?.icon || "" }
    get iconHide() { return this.json?.iconHide || "" }
    get games() { return this.json?.games || [] }
    get entryName() { return this.json?.entryName || "" }

    public get nodeCount() {
        let tot = 0
        for (let map of this.maps) tot += map.getNodeCount()
        return tot
    }

    public getAreas() {
        return this.areas
    }

    public getCurArea() {
        return this.getCurMap().getArea()
    }

    public isUnlockEntry() {
        return this.isDone() || this.areas.length > 1
    }

    update(dt?: number) {
        this.maps.forEach(m => m.update(dt))
        this.branches.forEach(b => b.update(dt))
    }

    public getCurNode() {
        return this.getCurMap()?.getCurNode()
    }

    public getPreNode() {
        return this.getCurMap()?.getPreNode()
    }

    // 宣传局玩法 当前星球人口数
    public getRoleNum() { return this.roleNum }
    public addRoleNum(v: number) { this.roleNum += v; eventCenter.emit(EventType.PUBLICITY_ROLE_NUM_CHANGE, this.id) }
    public getPublicityUnGetTime() { return this.publicityUnGetTime }
    public setPublicityUnGetTime(v: number) { this.publicityUnGetTime = v; eventCenter.emit(EventType.PUBLICITY_DURATION_TIME_CHANGE, this.id) }
    public addPublicityUnGetTime(v: number) { this.publicityUnGetTime += v; eventCenter.emit(EventType.PUBLICITY_DURATION_TIME_CHANGE, this.id) }
    public getPublicityCfg() { return assetsMgr.getJson<PublicityPlayCfg>("PublicityPlay").datas.filter(d => d.planetId == this.id) }
    // 宣传局玩法 当前星球生产时长百分比
    public getPublicityTimePercent() {
        return this.publicityUnGetTime / gameHelper.world.getMaxOfflineTime()
    }
    // 宣传局玩法 当前星球阶段信息
    public getPublicityInfo() {
        const data = this.getPublicityCfg()
        let info = null
        for (let i = 0; i < data.length; i++) {
            if (this.getRoleNum() >= data[i].req) {
                info = data[i]
            }
        }
        return info
    }
    // 宣传局玩法 下一阶段信息
    public getPublicityNextInfo() {
        const cur = this.getPublicityInfo()
        const data = this.getPublicityCfg()
        if (cur == null) return data[0]
        const idx = data.findIndex(d => d == cur)
        if (idx == -1 || idx == data.length - 1) return null
        return data[idx + 1]
    }
    // 宣传局玩法 上一阶段信息
    public getPublicityLastInfo() {
        const data = this.getPublicityCfg()
        const cur = this.getPublicityInfo()
        if (cur == null) return null
        const idx = data.findIndex(d => d == cur)
        if (idx == -1 || idx == 0) return null
        return data[idx - 1]
    }

    public getPreCheckPoint() {
        let nodes = this.getNodes();
        let curNode = this.getCurNode();
        if (!curNode) return null;
        // 找到当前节点在数组中的索引
        let curIdx = nodes.findIndex(n => n === curNode);
        if (curIdx <= 0) return null;
        // 从当前节点往前找最近的战斗节点
        for (let i = curIdx - 1; i >= 0; i--) {
            if (nodes[i].nodeType === PlanetNodeType.CHECK_POINT) {
                return nodes[i];
            }
        }
        return null;
    }

    public getNextNode() {
        return this.getCurMap()?.getNextNode()
    }

    public getCurBranch() {
        return this.branches.find(b => b.id == this.branchId)
    }

    public getBranchCurMap(): PlanetMap {
        let branch = this.getCurBranch()
        if (branch) {
            return branch.getCurMap()
        }
        return this.getCurMap()
    }

    public getBranchCurNode() {
        let map = this.getBranchCurMap()
        return map.getCurNode()
    }

    public getBranchPreNode() {
        let map = this.getBranchCurMap()
        return map.getPreNode()
    }

    public getBranchNodes() {
        let branch = this.getCurBranch()
        if (branch) {
            return branch.getNodes()
        }
        return this.getNodes()
    }

    public setBranch(branchId?: string) {
        this.branchId = branchId
    }

    public changeMap(mapId: number, isEmit: boolean = true) {
        let map = this.getMap(mapId)
        let oldMap = this.getCurMap()
        if (map && map != oldMap) {
            this.curMapId = mapId
            this.curNode = this.getCurNode()
            if (isEmit) {
                eventCenter.emit(EventType.CHANGE_PLANET_MAP, map, oldMap)
            }
        }
    }

    public updateMap() {
        if (this.getCurMap()?.isDone()) {
            this.toNextMap()
        }
    }

    public toNextMap() {
        let map = this.getNextMap()
        if (map) {
            return this.changeMap(map.getId())
        }
    }

    public getRewards() {
        return this.rewards
    }

    public land() {
        this.landed = true
    }

    public isOpen() {
        return true
    }

    // 是否解锁了某个图鉴资料 大于1
    public isUnlockProfile(id: number) { return this.profile[id] }

    public unlockProfile(id: number, index: number) {
        if (this.isUnlockProfile(id)) return
        this.profile[id] = index
    }

    public getProfiles() { return this.profile }

    // area 分区域
    public getProfileActiveProgress(area?: number) {
        const configs = cfgHelper.getPlanetProfileByCond(this.id, void 0, void 0, area)
        const total = configs.length
        let cur = 0
        for (const id in this.profile) {
            const cfg = cfgHelper.getPlanetProfileById(Number(id))
            if (area == void 0 || cfg.area == area) {
                cur++
            }
        }
        return { cur, total }
    }

    // 区域对应贴纸进度奖励是否领取过
    public isProfileCollectRewardGet(area: number, step: number) { return this.profileCollectReward[area]?.includes(step) }
    // 设置区域对应贴纸进度奖励领取
    public setProfileCollectRewardGet(area: number, step: number) {
        this.profileCollectReward[area] = this.profileCollectReward[area] || []
        this.profileCollectReward[area].push(step)
    }

    // 是否有未领取的贴纸进度奖励
    public hasUnGetProfileCollectReward() {
        const areaCfg = assetsMgr.getJson<PlanetAreaCfg>("PlanetArea").datas
            .filter(d => d.planetId == this.id)
        for (const area of areaCfg) {
            const { cur, total } = this.getProfileActiveProgress(area.index)
            if (cur <= 0) continue
            const reward = cfgHelper.getPlanetProfileReward(this.id, area.index)
            if (!reward) continue
            for (const step in reward.grouped) {
                if (cur < +step) continue
                if (!this.isProfileCollectRewardGet(area.index, +step)) return true
            }
        }
        return false
    }

    public getExportTimeCost() { return this.json.explore.time * ut.Time.Hour }

    public needLandAnim() {
        if (this.showLandAnim) return
        if (this.areas.length > 1 && this.getProgress() > 0) return
        return this.entryName
    }

    public getPreEmptyNode(eventName: string) {
        let map = this.getCurMap()
        return map.getPreEmptyNode(eventName)
    }

    // 进行人口宣传
    public async doPublicity() {
        const next = this.getPublicityNextInfo()
        const cost = new ConditionObj().init(ConditionType.PROP, ItemID.PUBLICITY, next.cost)
        if (!gameHelper.checkCondition(cost)) {
            viewHelper.showAlert("planet_publicity_tips_0")
            return null
        }
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_DoPublicityMessage, { planetId: this.id })
        if (r.code != 0) {
            viewHelper.showNetError(r.code)
            return null
        }
        gameHelper.deductCondition(cost)
        return r
    }

    public canGotoPlanet() {
        let planets = gameHelper.planet.getPlanets()
        for (let planet of planets) {
            if (planet == this) break
            if (!planet.isDone()) {
                return false
            }
        }
        return true
    }
}
