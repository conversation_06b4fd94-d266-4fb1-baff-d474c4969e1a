
import { util } from "../../../core/utils/Utils";
import { Msg } from "../../../proto/msg-define";
import { Condition, GalaxyCfg, PlanetCfg } from "../../common/constant/DataType";
import { ConditionType, MarkNewType, PlanetMoveState, PlanetNodeType, SpeedUpType } from "../../common/constant/Enums";
import { dbHelper } from "../../common/helper/DatabaseHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import EventType from "../../common/event/EventType";
import PlanetModel from "./PlanetModel";
import GalaxyModel from "./GalaxyModel";
import { unlockHelper } from "../../common/helper/UnlockHelper";

/**
 * 星图管理
 */
@mc.addmodel('planet')
export default class PlanetMgr extends mc.BaseModel {

    private planets: PlanetModel[] = []; //行星
    private galaxys: GalaxyModel[] = []; //星系
    public curPlanetId: number = 0
    private moveSurplusTime: number = -1
    private moveEndTime: number = -1
    public moveTargetId: number = null
    private movePause: boolean = false

    public get moveTarget(): PlanetModel {
        return this.getPlanet(this.moveTargetId)
    }

    public data: proto.IPlanetInfo = null
    private inited: boolean = false

    public isEntry: boolean = true

    public init() {
        let localData = dbHelper.register('planet', 1, this.toDB, this)
        this.fromDB(this.data, localData);
        this.inited = true
    }

    public unlockPlanet(id: number) {
        if (!this.inited) return
        let planet = this.getPlanet(id)
        if (!planet && this.haveMaps(id)) {
            planet = new PlanetModel().init(id)
            this.planets.push(planet)

            gameHelper.new.pushNew(MarkNewType.PLANET, [id])
        }
        return planet
    }

    public haveMaps(planetId: number) {
        return cfgHelper.getPlanetJson(planetId, "PlanetMap") != null
    }

    public getPlanets() {
        return this.planets;
    }

    public getPlanet(planetId: number) {
        return this.planets.find(p => p.getId() == planetId)
    }

    public getSchoolPlanet() {//获取学院星
        return this.getPlanet(1001)
    }

    public getGardenPlanet() {
        return this.getPlanet(1005)
    }

    public getCurPlanet() {
        return this.getPlanet(this.curPlanetId)
    }

    public getLastPlanet() {
        let datas = assetsMgr.getJson<PlanetCfg>("Planet").datas
        for (let i = datas.length - 1; i >= 0; i--) {
            let data = datas[i]
            let planet = this.getPlanet(data.id)
            if (planet) return planet
        }
    }

    public isMeetMonsters() {
        return this.movePause
    }

    public setCurPlanet(id) {
        this.curPlanetId = id
    }

    private fromDB(data: proto.IPlanetInfo, localData) {
        let planets = data.planets || [];
        let localPlanets = localData.planets || []
        let localGalaxys = localData.galaxys || []
        for (let planet of planets) {
            let localPlanet = localPlanets.find(p => p.id == planet.id)
            this.planets.push(new PlanetModel().fromDB(planet, localPlanet));
        }
        for (const galaxy of localGalaxys) {
            this.galaxys.push(new GalaxyModel().fromDB(galaxy))
        }
        this.curPlanetId = data.curPlanetId
        this.updateMoveSurplusTime(data.moveSurplusTime)
        this.moveTargetId = data.moveTargetId
    }

    public updateInfo(data: proto.IPlanetInfo) {
        let planets = data.planets
        this.curPlanetId = data.curPlanetId
        this.moveSurplusTime = data.moveSurplusTime
        this.moveEndTime = gameHelper.now() + this.moveSurplusTime
        this.setMoveTarget(data.moveTargetId)
    }

    private toDB() {
        return {
            planets: this.planets.map(m => m.toDB()),
            galaxys: this.galaxys.map(m => m.toDB()),
        }
    }

    public update(dt: number) {
        if (this.moveTarget) {
            let battle = gameHelper.transport.getCurBattle()
            if (!battle) {
                this.movePause = false
            }
            else if (!this.movePause) {
                let resTime = (this.moveEndTime - gameHelper.now()) / ut.Time.Second
                if (resTime <= battle.second) {
                    this.movePause = true
                    eventCenter.emit(EventType.PLANET_MOVE_PAUSE)
                    if (battle) {
                        eventCenter.emit(EventType.TRANSPORT_MEET_MONSTER)
                    }
                }
            }
            if (this.movePause) {
                this.moveEndTime = gameHelper.now() + (battle.second + 0.1) * ut.Time.Second
                // 同步一次
                if (this.getMoveSurplusTime() <= 0) {
                    this.syncMoveState()
                }
            }
            if (!this.movePause && gameHelper.world.isSpeedUp()) {
                this.moveEndTime -= (gameHelper.world.transDT(dt, SpeedUpType.S6) - dt) * ut.Time.Second
            }

            if (this.getMoveSurplusTime() <= 0) {
                this.syncMoveState()
            }
        }
        else {
            this.movePause = false
        }

        this.getCurPlanet()?.update(dt)

        const isSpeedUp = gameHelper.world.isSpeedUp()
        this.planets.forEach(planet => {
            if (planet.getRoleNum() <= 0) return
            if (isSpeedUp) {
                planet.addPublicityUnGetTime((gameHelper.world.transDT(dt, SpeedUpType.S3) - dt) * ut.Time.Second)
                return
            }
            planet.addPublicityUnGetTime(dt * ut.Time.Second)
        })
    }

    public async moveToPlanet(id: number, isJumpTo: boolean = false, cost?) {
        if (!this.getPlanet(id)?.haveMaps()) {
            viewHelper.showAlert('common_guiText_3')
            return false
        }
        let msg = new proto.C2S_TrainNavigationMessage({ planetId: id, plus: isJumpTo })
        let res = await gameHelper.net.request(Msg.C2S_TrainNavigationMessage, msg, true)
        const { code, time } = proto.S2C_TrainNavigationResultMessage.decode(res)
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        this.updateMoveSurplusTime(time)
        this.setMoveTarget(id)
        eventCenter.emit(EventType.TRAIN_MOVING_PLANET, id)

        // 更新当前进行中的运送任务
        gameHelper.transport.updateCurrentTransportOnPlanetMove(id)
        if (isJumpTo) {
            this.setCurPlanet(id)
            gameHelper.deductCondition(cost)
            viewHelper.gotoPlanetEntry()
        }
        return true
    }

    public getMoveState() {
        if (!this.moveTarget) {
            return PlanetMoveState.REACH
        }
        else {
            return PlanetMoveState.MOVE
        }
    }

    public isMoving() {
        return !!this.moveTarget
    }

    public getMoveEndTime() {
        return this.moveEndTime
    }

    @util.addLock
    public async syncMoveState() {
        let msg = new proto.C2S_GetPlanetMoveSurplusTimeMessage()
        let res = await gameHelper.net.request(Msg.C2S_GetPlanetMoveSurplusTimeMessage, msg)
        const { code, time } = proto.S2C_GetPlanetMoveSurplusTimeRespMessage.decode(res)
        if (code == 0) {
            this.updateMoveSurplusTime(time)
            if (time <= 0) {
                this.onReach()
            }
        }
        else {
            await ut.wait(100) //除非服务器挂了，不然不会走这里
        }
    }

    private updateMoveSurplusTime(time: number) {
        this.moveSurplusTime = time
        this.moveEndTime = this.moveSurplusTime + gameHelper.now()
    }

    private onReach() {
        let id = this.moveTarget?.getId()
        if (id) {
            this.curPlanetId = id
        }
        this.setMoveTarget(null)
        this.isEntry = true

        gameHelper.transport.tryDoneTransportByPlanetId(id)

        let planet = this.getCurPlanet()
        planet.reached = true
        eventCenter.emit(EventType.REACH_PLANET, id)

        viewHelper.showTips(() => {
            return assetsMgr.lang("planet_reach_tips_1", assetsMgr.lang(planet.name))
        })
    }

    private setMoveTarget(id) {
        this.moveTargetId = id
        eventCenter.emit(EventType.PLANET_MOVE_CHANGE)
    }

    public reach(id) {
        this.moveTargetId = id
        this.onReach()
    }

    public isReach() {
        return this.moveTargetId == null
    }

    public async land() {
        let planet = this.getCurPlanet()
        if (!planet.isLanded()) {
            let msg = new proto.C2S_LandPlanetMessage()
            let res = await gameHelper.net.request(Msg.C2S_LandPlanetMessage, msg)
            const { code } = proto.S2C_LandPlanetRespMessage.decode(res)
            if (code != 0) {
                return viewHelper.showNetError(code)
            }
        }

        planet.land()
    }

    public getMovePercent() {
        let surplusTime = this.getMoveSurplusTime()
        if (surplusTime > 0) {
            let costTime = this.getPlanetMoveTime(this.getCurPlanet().getId(), this.moveTargetId)
            return Math.max(0, 1 - (surplusTime / costTime))
        }
        return 1
    }

    public getMoveSurplusTime() {
        if (!this.isMoving()) return 0
        return Math.max(0, this.moveEndTime - gameHelper.now())
    }

    public isUnlock(id: number) {
        return this.planets.has("id", id)
    }

    //传入解锁条件的id
    public unlockByLockId(id?: number | string) {
        if (!this.inited) return
        let datas = assetsMgr.getJson<PlanetCfg>('Planet').datas
        for (let data of datas) {
            if (!data.lock) {
                this.unlockPlanet(data.id)
            }
            else if (data.lock.id == id) {
                this.unlockPlanet(data.id)
            }
        }
    }

    public getNodeByEvent(eventName) {
        let planets = gameHelper.planet.getPlanets()
        for (let planet of planets) {
            let nodes = planet.getNodes()
            let node = nodes.find(n => n.eventName === eventName)
            if (node) {
                return node
            }
        }
    }

    public isFirstQTE() {
        for (let planet of this.planets) {
            let nodes = planet.getNodes()
            let node = nodes.find(node => !!node.qteId)
            if (node?.isPass()) {
                return false
            }
        }
        return true
    }

    //id为PlanetNodes id
    public isPassNode(id: string, checkEnd: boolean = false) {
        let [planetId, mapId, index] = id.split("-")
        let planet = this.getPlanet(Number(planetId))
        if (!planet) return false
        let map = planet.getMap(Number(mapId))
        if (!map) return false
        let node = map.getNodes().find(node => node.index == Number(index))
        if (!node) return false
        return node.isPass(checkEnd)
    }

    //id为 ChapterPlanetMonster id
    public isPassBattle(id: string) {
        let node = this.getNodeByBattleId(id)
        return node?.isPass()
    }

    public getNodeByBattleId(battleId: string) {
        let [planetId, mapId] = battleId.split("-")
        let planet = this.getPlanet(Number(planetId))
        if (!planet) return null
        let map = planet.getMap(Number(mapId))
        return map?.getNodes().find(node => node.nodeType == PlanetNodeType.CHECK_POINT && node.getId() == battleId)
    }

    //是否可以前往
    public checkRedDot1() {
        return !this.isMoving() && this.planets.find(p => p.isHide() && p.isOpen())
    }

    //是否到达
    public checkRedDot2() {
        return !this.isMoving() && this.getCurPlanet().isHide() && this.getCurPlanet().isOpen()
    }

    public getCurGalaxy() {
        return this.getGalaxy(this.getCurGalaxyId())
    }
    public getGalaxy(galaxyId: number) {
        let json = assetsMgr.checkJsonData<GalaxyCfg>("Galaxy", galaxyId)
        if (!json) return
        return this.galaxys.find(m => m.id == galaxyId) || this.newGalaxy(galaxyId)
    }
    public getCurGalaxyId() {
        let json = assetsMgr.getJsonData<PlanetCfg>("Planet", this.curPlanetId)
        return json.galaxy
    }
    private newGalaxy(galaxyId: number) {
        let m = new GalaxyModel().init(galaxyId)
        this.galaxys.push(m)
        return m
    }
    public canGoNextGalaxy(curId: number) {
        // 有下一个星系(且首个星球可用) 且 当前星系完成探索
        let nextId = curId + 1
        let json = assetsMgr.checkJsonData<GalaxyCfg>("Galaxy", nextId)
        if (!json) return false
        let planetId = this.getFirstPlanetIdInGalaxy(nextId)
        if (!this.haveMaps(planetId)) return false
        return this.isPassGalaxy(curId)
    }
    public isPassGalaxy(galaxyId: number) {
        let ary = assetsMgr.getJson<PlanetCfg>("Planet").datas.filter(m => m.galaxy == galaxyId)
        if (ary.length == 0) return false
        for (const json of ary) {
            let planet = this.getPlanet(json.id)
            if (!planet) return false
            if (!planet.isDone()) return false
        }
        return true
    }
    public getFirstPlanetIdInGalaxy(galaxyId: number) {
        let m = assetsMgr.getJson<PlanetCfg>("Planet").datas.find(m => m.galaxy == galaxyId)
        return m ? m.id : 0
    }

    public getCurPlanetMoveTime() {
        let planet = this.getCurPlanet()
        if (!this.isMoving()) return 0
        return gameHelper.getPlanetMoveTime(planet.getId(), this.moveTargetId)
    }

    public getCurPlanetMoveInfo() {
        let planet = this.getCurPlanet()
        if (!this.isMoving()) return
        const info = cfgHelper.getPlanetMoveTime(planet.getId(), this.moveTarget.getId())
        info.time = gameHelper.getPlanetMoveTime(planet.getId(), this.moveTarget.getId())
        return info
    }

    public getPlanetMoveTime(from: number, to: number) { return gameHelper.getPlanetMoveTime(from, to) }

    public async cancelMove() {
        let { code } = await gameHelper.net.requestWithDataWait(Msg.C2S_CancelMoveToPlanetMessage)
        if (code == 0) {
            this.reach(this.curPlanetId)
            return true
        }
        else {
            viewHelper.showNetError(code)
        }
    }
}
