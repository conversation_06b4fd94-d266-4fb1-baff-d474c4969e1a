/**
 * 解谜
 */
import Observer from "../../../core/utils/Observer"
import PlanetEmptyNode from "./PlanetEmptyNode"

export default class PlanetPuzzleModel extends PlanetEmptyNode {
    public state = 0
    private puzzleInfo = {}

    public toDB() {
        return {
            id: this.id,
            progress: this.progress,
            state: this.state,
            puzzleInfo: this.puzzleInfo,
        }
    }

    public fromDB(data) {
        this.state = data.state || 0
        this.puzzleInfo = data.puzzleInfo || {}
    }

    public getPuzzleKeyValue(key: string) {
        return this.puzzleInfo[key]
    }

    public setPuzzleKeyValue(key: string, value: any) {
        let dic = this.puzzleInfo
        if (dic[key] == null) {
            Observer.addKey(dic, key, value)
        } else {
            dic[key] = value
        }
    }

}
