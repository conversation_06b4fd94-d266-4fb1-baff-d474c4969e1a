/**
 * 采集物
 */
import { Msg } from "../../../proto/msg-define"
import { ChapterControlCfg } from "../../common/constant/DataType"
import { ConditionType, PlanetNodeType } from "../../common/constant/Enums"
import { gameHelper } from "../../common/helper/GameHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import EventType from "../../common/event/EventType"
import ConditionObj from "../common/ConditionObj"
import PlanetMap from "./PlanetMap"
import PlanetModel from "./PlanetModel"
import { localConfig } from "../../common/LocalConfig"
import BranchPlanetMap from "./BranchPlanetMap"

export default class PlanetNodeModel {

    protected id: string = null
    public uid: string = null
    protected json: any = null
    protected map: PlanetMap = null
    public position: cc.Vec2 = null
    public angle: number = 0
    public nodeType: PlanetNodeType = null

    public points: cc.Vec2[] = [] //在地图中的站位点

    public dead: boolean = false
    public isEnd: boolean = false

    public guideParam: any = null//教程自定义

    public center: any = {}

    public rewards: ConditionObj[] = []

    public noShowReward: boolean = false//不展示掉落奖励

    public delayRewardMap: { [key: string]: number } = {} //延迟奖励id:类型

    public get planet(): PlanetModel {
        return this.map?.getPlanet()
    }

    public index: number = -1
    public typeIndex: number = -1

    public eventName: string = null

    public reachOffset: cc.Vec2 = null
    public get reachPosition() {
        return this.getPosByOffset(this.reachOffset)
    }
    public setJson(json: any) {
        this.json = json
    }

    public scale: number = 1

    public size: cc.Size = null

    public orgPosition: cc.Vec2 = null
    public orgAngle: number = 0

    public endOffset: cc.Vec2 = null
    public get endPos() {
        return this.getPosByOffset(this.endOffset)
    }

    private controlFlag: boolean = false

    public progress: number = 0 //当前进度
    public pathProgress: number = 0 //上个节点到当前节点的进度

    private controlCfg: ChapterControlCfg = null

    public init(nodeId, ...params) {
        this.id = nodeId
        return this
    }

    public setMap(map: PlanetMap) {
        this.map = map
    }

    public setPosition(position: cc.Vec2) {
        this.position = position
    }

    public setProgress(val: number) {
        this.progress = val
    }

    public setPathProgress(val: number) {
        this.pathProgress = val
    }

    private getPosByOffset(offset) {
        if (!offset) return this.position
        let absScale = Math.abs(this.scale) 
        let scale = 1 + (absScale - 1) * 0.5
        let offsetX = offset.x * scale
        let offsetY = offset.y * scale
        return cc.v2(this.position.x + offsetX, this.position.y + offsetY)
    }

    public setIndex(index: number) {
        this.index = index
        let mapId = this.map.getId()
        let planetId = this.map.getPlanetId()
        this.uid = `${planetId}-${mapId}-${this.index}`
    }

    public isPassContorl() {
        let data = this.getChapterControl()
        let before = data?.before
        if (!before) return true
        if (before.index < 0) return true
        let controlNode = this.map.getNodeByIndex(before.index)
        if (!controlNode) return true
        let checkEnd = false
        if (controlNode.nodeType == PlanetNodeType.NONE) {
            checkEnd = true
        }
        return controlNode.isPass(checkEnd)
    }

    public checkShow() {
        let data = this.getChapterControl()
        let before = data?.before
        if (!before) return true
        let pass = this.isPassContorl()
        if (before.type == 1 && localConfig.openGuide) { //再次进入星球时才显示
            return pass && this.controlFlag
        }
        return pass
    }

    private getChapterControl() {
        if (this.controlFlag) return this.controlCfg
        let name = "PlanetNodes"
        let isBranch = this.map.getBranch()
        if (isBranch) {
            name = "BranchPlanetNodes"
        }
        let cfg = assetsMgr.checkJsonData<ChapterControlCfg>(name, this.uid)
        if (!cfg) {
            cfg = {}
        }
        if (!cfg.before) {
            if (this.nodeType == PlanetNodeType.MINE) {
                let nodes = this.map.getNodes()
                for (let i = this.index - 2; i >= 0; i--) {
                    let node = nodes[i]
                    if (node.nodeType != PlanetNodeType.MINE) {
                        cfg.before = {
                            index: node.index,
                        }
                        break
                    }
                }
            }
            else {
                let preNode = this.getPreNode()
                if (preNode) {
                    cfg.before = {
                        index: preNode.index,
                    }
                }
            }
        }
        this.controlCfg = cfg
        return cfg
    }

    public updateContorlFlag() {
        this.controlFlag = this.isPassContorl()
    }

    public toDB() {
        return {}
    }

    public fromDB(data) {
    }

    protected initJson() {
    }

    protected initReawrds() {
        if (this.json?.reward) {
            this.rewards = this.json.reward.map(r => {
                let { type, id, num } = r
                let data = { type: type || ConditionType.PROP, id, num }
                return new ConditionObj().init2(data)
            })
        }
    }

    public getId() { return this.id }
    public getMap() { return this.map }

    get name() { return this.json?.name || "" }

    //临时
    public setSize({ height, width }) {
        this.size = cc.size(width, height)
    }


    public getZIndex() {
        return 0
    }

    public getActPoints() {
        return this.points
    }

    public async die() {
        if (this.dead) return true
        this.dead = true
        gameHelper.grantRewards(this.rewards)
        this.map.nextNode()
        return true
    }

    public async syncDie() {
        let map = this.map
        let branch = map.getBranch()
        let data
        if (branch) {
            data = await gameHelper.net.requestWithData(Msg.C2S_PassBranchPlanetNodeMessage, { branchId: branch.id, mapId: map.getId(), nodeId: this.index })
        }
        else {
            data = await gameHelper.net.requestWithData(Msg.C2S_ChapterPassMessage, { planetId: this.planet.getId(), mapId: map.getId(), nodeId: this.index })
        }

        let code = data.code
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        if (data.rewards.length > 0) {
            this.rewards = gameHelper.toConditions(data.rewards)
        }
        eventCenter.emit(EventType.PLANET_NODE_SEVER_DIE)

        return true
    }

    public end() {
        this.isEnd = true
        eventCenter.emit(EventType.PLANET_NODE_END, this)
    }

    public isPass(checkEnd: boolean = false) {
        if (checkEnd && !this.isEnd) return false
        return this.index - 1 < this.map.getProgress()
    }

    public update(dt?) { }

    public isReach() {
        let hero = gameHelper.hero
        return hero.isTarget(this) && hero.isReachTarget()
    }

    public onClick() {
    }

    public getPreNode() {
        return this.map.getNodeByIndex(this.index - 1)
    }

    public isLast() {
        let nodes = this.planet.getNodes()
        let arr = nodes.slice(nodes.length - 1)
        return !!arr.find(m => m == this)
    }

    public getBranch() {
        return this.map.getBranch()
    }
}