import { Msg } from "../../../../proto/msg-define";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import PlanetEmptyNode from "../PlanetEmptyNode";

export default class PlanetRageMode extends PlanetEmptyNode {

    public reachOffset: cc.Vec2 = cc.v2(-220, 0)

    private isSyncDie: boolean = false

    public async syncDie() {
        if (this.isSyncDie) return true
        this.isSyncDie = true

        let map = this.map
        let data = await gameHelper.net.requestWithDataWait(Msg.C2S_ChapterPassRageModeMessage, { 
            planetId: this.planet.getId(), mapId: map.getId(), nodeId: this.index 
        })

        let code = data.code
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        let from = this.index
        let to = Math.min(this.map.getNodeCount(), this.index + this.json.count)
        gameHelper.hero.enterRageMode(from, to, this.map)
        eventCenter.emit(EventType.PLANET_NODE_SEVER_DIE)
        return true
    }
}