import { Msg } from "../../../../proto/msg-define";
import { Condition } from "../../../common/constant/DataType";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import ConditionObj from "../../common/ConditionObj";
import PlanetEmptyNode from "../PlanetEmptyNode";

export enum PlanetTimeLimitBoxState {
    NONE = -2,
    READY_START, //已经点击开始，等网络请求回来
    READY, //还没开始
    START, //点击开始采集
    END, //采集结束
}

export default class PlanetTimeLimitBox extends PlanetEmptyNode {

    public reachOffset: cc.Vec2 = cc.v2(-220, 0)

    private state: PlanetTimeLimitBoxState = PlanetTimeLimitBoxState.NONE
    public clicks: number = 0
    private endTime: number = 0

    public diyRewards: ConditionObj[] = []

    public async start() {
        this.setState(PlanetTimeLimitBoxState.READY_START)
        let { code, surplusTime } = await gameHelper.net.requestWithData(Msg.C2S_ChapterStartTimeLimitBoxMessage, {
            planetId: this.planet.getId(), mapId: this.map.getId(), nodeId: this.index
        })
        if (code == 0) {
            this.setState(PlanetTimeLimitBoxState.START)
            this.updateEndTime(surplusTime)
        }
        else {
            viewHelper.showNetError
        }
    }

    private updateEndTime(surplusTime) {
        this.endTime = gameHelper.now() + surplusTime
    }

    public getSurplusTime() {
        return Math.max(0, this.endTime - gameHelper.now())
    }

    public getState() {
        return this.state
    }

    public setState(state: PlanetTimeLimitBoxState) {
        this.state = state
    }

    public update() {
        if (this.state == PlanetTimeLimitBoxState.NONE || this.state == PlanetTimeLimitBoxState.START) {
            if (this.getSurplusTime() <= 0) {
                this.sync()
            }
        }
    }

    @ut.addLock
    private async sync() {
        let { code, state, surplusTime, rewards } = await gameHelper.net.requestWithData(Msg.C2S_ChapterSyncTimeLimitBoxMessage, {
            planetId: this.planet.getId(), mapId: this.map.getId(), nodeId: this.index, clicks: this.clicks
        })
        if (code == 0) {
            this.clicks = 0
            this.setState(state)
            this.updateEndTime(surplusTime)
            this.diyRewards = rewards.map(r => new ConditionObj().init(r))
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }


    public async syncDie() {
        let { code } = await gameHelper.net.requestWithData(Msg.C2S_ChapterPassTimeLimitBoxMessage, {
            planetId: this.planet.getId(), mapId: this.map.getId(), nodeId: this.index
        })
        if (code == 0) {
            gameHelper.grantRewards(this.diyRewards)
            this.delayRewardMap = gameHelper.currency.addDelayRewards(this.diyRewards)
            eventCenter.emit(EventType.PLANET_NODE_SEVER_DIE)
            return true
        }
        else {
            viewHelper.showNetError(code)
            return false
        }
    }

}