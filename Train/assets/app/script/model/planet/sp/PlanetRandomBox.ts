import { Msg } from "../../../../proto/msg-define";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import PlanetEmptyNode from "../PlanetEmptyNode";

export default class PlanetRandomBox extends PlanetEmptyNode {

    public reachOffset: cc.Vec2 = cc.v2(-220, 0)

    private isSyncDie: boolean = false

    public async die() {
        let rewards = gameHelper.mergeCondition(this.rewards)
        this.delayRewardMap = gameHelper.currency.addDelayRewards(rewards)
        return super.die()
    }

    public async syncDie() {
        if (this.isSyncDie) return true
        this.isSyncDie = true

        let map = this.map
        let data = await gameHelper.net.requestWithDataWait(Msg.C2S_ChapterPassRandomBoxMessage, { 
            planetId: this.planet.getId(), mapId: map.getId(), nodeId: this.index 
        })

        let code = data.code
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        this.rewards = gameHelper.toConditions(data.rewards)
        eventCenter.emit(EventType.PLANET_NODE_SEVER_DIE)

        return true
    }
}