import { Msg } from "../../../../proto/msg-define";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import PlanetEmptyNode from "../PlanetEmptyNode";

export default class PlanetToolBless extends PlanetEmptyNode {

    public reachOffset: cc.Vec2 = cc.v2(-220, 0)

    private isSyncDie: boolean = false

    public async syncDie() {
        if (this.isSyncDie) return true
        this.isSyncDie = true

        let map = this.map
        let data = await gameHelper.net.requestWithDataWait(Msg.C2S_ChapterPassToolBlessMessage, { 
            planetId: this.planet.getId(), mapId: map.getId(), nodeId: this.index 
        })

        let code = data.code
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        gameHelper.tool.setBlessId(this.json.id)
        gameHelper.tool.changeBlessCount(this.json.count)
        eventCenter.emit(EventType.PLANET_NODE_SEVER_DIE)
        return true
    }
}