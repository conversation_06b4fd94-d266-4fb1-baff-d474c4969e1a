import { Msg } from "../../../../proto/msg-define";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import ConditionObj from "../../common/ConditionObj";
import PlanetEmptyNode from "../PlanetEmptyNode";

export default class PlanetMonsterBox extends PlanetEmptyNode {

    public reachOffset: cc.Vec2 = cc.v2(-250, -2)

    private isSyncDie: boolean = false

    public diyRewards: ConditionObj[] = []

    public async die() {
        let succ = await this.syncDie()
        if (succ) {
            if (this.dead) return succ
            this.dead = true
            this.map.nextNode()
        }
        return succ
    }

    public async syncDie(res?: any) {
        if (this.isSyncDie) return true
        this.isSyncDie = true

        let map = this.map
        let num = res?.monsterHitCnt || 0
        let data = await gameHelper.net.requestWithDataWait(Msg.C2S_ChapterPassMonsterBoxMessage, {
            planetId: this.planet.getId(), mapId: map.getId(), nodeId: this.index, num
        })

        let code = data.code
        if (code != 0) {
            viewHelper.showNetError(code)
            return false
        }
        this.diyRewards = gameHelper.toConditions(data.rewards)
        eventCenter.emit(EventType.PLANET_NODE_SEVER_DIE)

        return true
    }
}