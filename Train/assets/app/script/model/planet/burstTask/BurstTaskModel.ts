import { Msg } from "../../../../proto/msg-define";
import { BurstTaskItemCfg } from "../../../common/constant/DataType";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import TrainDailyTaskModel, { TrainDailyTaskItem } from "../../trainDailyTask/TrainDailyTaskModel";
import { WantedCondition } from "../../wanted/WantedModel";


@mc.addmodel('burstTask', 101)
export default class BurstTaskModel extends TrainDailyTaskModel<BurstTaskItem> {
    public data: proto.IBurstTask = null

    public init() {
        super.init()
        gameHelper.net.on(Msg.S2C_OnGetBurstTaskMessage, this.addTask, this)
    }

    public createTaskItem(item: proto.IBurstTaskItem) {
        const taskItem = new BurstTaskItem().init(item)
        taskItem.index = this._list.length
        this._list.push(taskItem)
    }

    private addTask(msg: proto.IS2C_OnGetBurstTaskMessage) {
        const { data } = msg
        this.createTaskItem(data)
    }

}


export class BurstTaskItem extends TrainDailyTaskItem {

    protected _cfg: BurstTaskItemCfg = null
    get cfg() { return this._cfg }
    get costTime() { return this._cfg.costTime }

    public init(data: proto.IBurstTaskItem) {
        this._id = data.id
        let preState = this._state
        this._state = data.state
        this._endTime = -1
        if (this.inProcess()) {
            this._endTime = data.surplusTime + gameHelper.now()
        }
        this._conditions = data.conditions.map(c => new WantedCondition(c))
        this._rewards = gameHelper.toConditions(data.rewards).sort((a, b) => a.type - b.type)
        this._roles = data.roles
        this._people = data.people
        this._trainId = data.trainId
        this._cfg = assetsMgr.getJsonData<BurstTaskItemCfg>("BurstTaskItem", this._id)
        const ary: WantedCondition[] = []
        for (const sig of this.conditions) {
            const exists = ary.find(c => c.type == sig.type && c.value == sig.value)
            if (!exists) {
                ary.push(sig)
                continue
            }
            exists.num += sig.num
        }
        this._conditions = ary
        if (preState == proto.CommonState.InProcess && this._state == proto.CommonState.DoneWithoutReward) {
            eventCenter.emit(EventType.TRAIN_DAILY_TASK_DONE, this)
        }
        return this
    }

    @ut.addLock
    public async requestStart(roles: number[]) {
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_StartBurstTaskMessage, { index: this.index, roles })
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        this._state = proto.CommonState.InProcess
        this._roles = roles

        this._endTime = this.costTime * ut.Time.Minute + gameHelper.now()
        eventCenter.emit(EventType.BURST_TASK_START, this)
        return true
    }

    @ut.addLock
    public async requestFinish() {
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_ClaimBurstTaskRewardMessage, { index: this.index })
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        this._state = proto.CommonState.FinishWithReward
        this._roles.length = 0
        await gameHelper.grantRewardAndShowUI(this.rewards)
        eventCenter.emit(EventType.BURST_TASK_FINISH, this)
        return true
    }

}