import Random from "../../../core/utils/Random";
import { util } from "../../../core/utils/Utils";
import { BattleSkillCfg } from "../../common/constant/DataType";
import { ROLE_ATTR_ID, SkillType } from "../../common/constant/Enums";
import { gameHelper } from "../../common/helper/GameHelper";
import PassengerModel from "../passenger/PassengerModel";
import { BattleLogType, BattleSkillTriggerType, BattleSkillEffectType, BattleSkillCampType, BattleRoleType, BattleSkillObjectType, BattleEffectValueType, BattleSummonID, BattleHitType, BattleDeathType, SkillEffectPerObjType, SkillEffectPerAttrType, SkillEffectRateType, BattleStage } from "./BattleEnum";
import BattleRole, { mergeEffects } from "./BattleRole";
import BattleSkill, { BattleSkillEffect, BattleSkillObject, BattleSkillTrigger } from "./BattleSkill";
import Monster from "./Monster";

export type BattleLog = {
    type: BattleLogType,
    sender?: string,
    receiver?: string,
    next?: BattleLog[],
    data?: any,
}
export class BattleAction {
    func?: Function
    args?: any[]
    children?: BattleAction[]
    log?: BattleLog
    dep?: number
    order?: number

    private battle: Battle = null

    public init(battle, func, ...args) {
        this.battle = battle
        this.func = func
        this.args = args
        return this
    }

    public static cmpPriority(act1: BattleAction, act2: BattleAction) {
        let cmpFuncs = act1.getPriorityFuncs()
        for (let func of cmpFuncs) {
            let pri1 = func.call(act1)
            let pri2 = func.call(act2)
            if (pri1 != pri2) return pri1 - pri2 //从小到大
        }
        return 0
    }

    public getPriorityFuncs() {
        return [this.getTrigger, this.getDepPriority, this.getIndexPriority, this.getCampPriority, this.isEquipSkill]
    }

    public getTrigger() {
        let skill: BattleSkill = this.args[1]
        if (skill.trigger.type == BattleSkillTriggerType.DEATH_KILL) return -1
        if (skill.trigger.type == BattleSkillTriggerType.LIVE) return 100
        if (skill.trigger.type == BattleSkillTriggerType.NEAR_DEATH) return 100
        
        return 0
    }

    public getDepPriority() { //dep大的优先
        return -this.dep
    }

    public getCampPriority() {
        let role: BattleRole = this.args[0]
        return role.type
    }

    public getIndexPriority() {
        let role: BattleRole = this.args[0]
        let index = this.battle.getOrgRoleIndex(role)
        return index
    }

    public isEquipSkill() {
        let skill: BattleSkill = this.args[1]
        if (skill.getType() == SkillType.EQUIP) {
            return 0
        }
        return 1
    }
}
export class BattleDebug {

    private index: number = 0

    private debug(...param) {
        this.index++
        twlog.debug(this.index, ...param)
    }

    public parseLog(log: BattleLog) {
        let { type, sender, receiver, data } = log
        if (type == BattleLogType.USE_SKILL) {
            this.debug(`${log.sender} 发动技能: [${JSON.stringify(data)}]`)
        }
        else if (type == BattleLogType.CHANGE_HP) {
            let hp = data.change
            let role = receiver
            this.debug(`${role}生命值${hp}`)
        }
        else if (type == BattleLogType.CHANGE_ATTACK) {
            let attack = data.change
            let role = receiver
            this.debug(`${role}攻击力${attack}`)
        }
        else if (type == BattleLogType.ATTACK) {
            this.debug(`${sender}攻击${receiver}`)
        }
        else if (type == BattleLogType.HIT) {
            this.debug(`${sender}对${receiver}造成${data?.damage}点伤害`)
        }
        else if (type == BattleLogType.DEATH) {
            this.debug(`${receiver}死亡`, data.deathType)
        }
        else if (type == BattleLogType.SUMMON) {
            let summons = data.summons
            this.debug(`${sender}召唤: ${summons.map(r => `[uid: ${r.uid}, id: ${r.id}, 血量: ${r.hp}, 攻击: ${r.attack}, ${r.type}]`)}`)
        }
        else if (type == BattleLogType.ADD_BUFF) {
            this.debug(`${sender}给${receiver}增加[${data.type}]buff次数${data.times || "无限"}`)
        }
        else if (type == BattleLogType.REMOVE_BUFF) {
            this.debug(`${receiver}消耗[${data.type}]buff次数1`)
        }
        else if (type == BattleLogType.COLLIDE) {
            this.debug(`${sender}和${receiver}对撞`)
        }
        else if (type == BattleLogType.CHANGE_POSITION) {
            this.debug(`${receiver}被交换到第${data.index}位`)
        }
        else if (type == BattleLogType.USE_BUFF_SKILL) {
            this.debug(`${sender} 触发buff效果 [${JSON.stringify(data)}]`)
        }
        else if (type == BattleLogType.COPY) {
            this.debug(`${sender} 复制 ${receiver} `)
        }
        else if (type == BattleLogType.REBIRTH) {
            this.debug(`${receiver} 重生 `, JSON.stringify(data))
        }
    }
}

type UseSkillParams = {
    receivers?: BattleRole[], //技能作用对象，如作用于受伤角色
    from?: BattleRole, //触发技能的来源角色
    deathType?: BattleDeathType,
    skill?: BattleSkill, //触发技能的来源技能
    copyRole?: BattleRole, //复制的角色
    log?: BattleLog
}

const TRIGGER = new BattleSkillTrigger()
export default class Battle {

    private passengers: BattleRole[] = []
    private monsters: BattleRole[] = []

    private round: number = 0

    private actQueue: BattleAction[] = []

    private step: number = 0

    private actions: BattleAction[] = []

    private logs: BattleLog[] = []

    private debug: BattleDebug = null
    public openDebug: boolean = true
    public openLog: boolean = true

    //保存所有的战斗角色，包括死掉的角色；用于辅助死掉后取角色原本的站位
    private allPassengers: BattleRole[] = []
    private allMonsters: BattleRole[] = []

    private orgPassengers: BattleRole[] = []
    private orgMonsters: BattleRole[] = []

    private buffs: BattleRole[] = [] //场地效果，装备

    private summonSelfMap = {}

    private record: {
        runAwayCnt?: number, //敌方方逃跑次数
        monsterHitCnt?: number //敌人被击中的次数
    } = {} //记录战斗结果

    private stage: BattleStage = BattleStage.BEFORE_BATTLE

    public init(passengers: BattleRole[], monsters: BattleRole[], buffs: BattleRole[] = []) {
        this.passengers = passengers
        this.monsters = monsters

        passengers.forEach((p, index) => {
            p.orgIndex = index + 1
            p.type = BattleRoleType.PASSENGER
            p.mergeSkills()
        })
        monsters.forEach((p, index) => {
            p.orgIndex = index + 1
            p.type = BattleRoleType.MONSTER
            p.mergeSkills()
        })
        buffs = buffs.filter(b => !!b)
        this.buffs = buffs
        this.buffs.forEach(p => {
            if (!p.type) {
                p.type = BattleRoleType.PASSENGER
            }
        })

        this.allPassengers = passengers.slice()
        this.allMonsters = monsters.slice()

        this.orgPassengers = passengers.slice()
        this.orgMonsters = monsters.slice()

        if (this.openDebug) {
            this.debug = new BattleDebug()
        }
        return this.startRound()
    }

    private startRound() {
        if (!this.isEnd()) {
            this.onStart()
        }

        while (!this.isEnd() && this.round < 1000) {
            this.startCollide()
            this.round++
        }
        if (this.round >= 1000) {
            console.error("battle fail: round over")
            return false
        }
        // this.logs.forEach(log => debug.parseLog(log, 1))
        return !this.isLose(this.passengers)
    }

    public getRound() {
        return this.round
    }

    private handleActQueue() {
        if (this.actQueue.length <= 0) return
        let step = this.step
        let actions = this.actQueue.slice()
        for (let action of actions) {
            action.dep = 1
        }
        while (this.actQueue.length > 0) {
            // if (this.isEnd()) return

            this.actQueue.sort(BattleAction.cmpPriority)

            let action = this.actQueue.shift()
            let { func, args } = action
            args = args || []
            let len = this.actQueue.length
            let res = func.call(this, ...args)
            action.children = this.actQueue.slice(len)
            for (let child of action.children) {
                child.dep = action.dep + 1
            }
        }
        if (this.isEnd()) return
        this.step++
    }

    public getLogs() {
        return this.logs
    }

    private pushAction(func, ...args) {
        let action = new BattleAction().init(this, func, ...args)
        this.actQueue.push(action)
    }

    public isEnd() {
        if (this.actQueue.length > 0) return false
        return this.isLose(this.passengers) || this.isLose(this.monsters)
    }

    private isLose(roles: BattleRole[]) {
        return roles.filter(r => r && !r.isDeath()).length <= 0
    }

    //---------------trigger-------------------
    private onStart() {
        this.onBattleBefore(this.passengers.concat(this.monsters))
        this.handleActQueue()
        if (this.isEnd()) return

        this.onBattleStart(this.passengers.concat(this.monsters))
        this.handleActQueue()
        if (this.isEnd()) return

        this.onPosChange(this.passengers[0], 0)
        this.onPosChange(this.monsters[0], 0)
        this.handleActQueue()
    }


    private onBattleBefore(roles: BattleRole[]) {
        for (let buff of this.buffs) {
            TRIGGER.set(BattleSkillTriggerType.LIVE, BattleSkillObjectType.ALL, BattleSkillCampType.TEAMMATE, this.getRolesByType(buff.type).length)
            this.checkUseSkill(TRIGGER, buff)

            TRIGGER.set(BattleSkillTriggerType.BATTLE_BEFORE)
            this.checkUseSkill(TRIGGER, buff)
        }

        roles.for(role => {
            this.checkUseSkill(TRIGGER.set(BattleSkillTriggerType.BATTLE_BEFORE), role)
        })
    }

    private onBattleStart(roles: BattleRole[]) {
        for (let buff of this.buffs) {
            this.checkUseSkill(TRIGGER.set(BattleSkillTriggerType.BATTLE_START), buff)
        }
        roles.for(role => {
            this.checkUseSkill(TRIGGER.set(BattleSkillTriggerType.BATTLE_START), role)
            this.checkHpTrigger(role)
        })
    }

    private startCollide() {
        this.onAttackBefore()
        if (this.isEnd()) return

        this.onRoundStart()
        if (this.isEnd()) return

        let roles = this.getCollideRoles()
        if (roles.length < 2) {
            this.round = 1000
            console.error("startCollide error")
            return
        }
        let behinds = roles.map(role => this.getBehind(role))
        this.collide(roles)
        this.onAttackAfter(roles, behinds)
    }

    private onRoundStart() {
        this.stage = BattleStage.ROUND_START
        let roles = this.passengers.concat(this.monsters, this.buffs)
        let type = BattleSkillTriggerType.ROUND_START
        TRIGGER.set(type)

        let roundBuffs: {buff: BattleSkillEffect, role: BattleRole}[] = []
        for (let role of roles) {
            let skills = role.skills.concat(role.buffSkills)
            for (let skill of skills) {
                for (let effect of skill.effects) {
                    effect.resetRoundTimes()
                }

                if (skill.delayRounds > 0) {
                    skill.delayRounds--
                    continue
                }
         
                let trigger = skill.trigger
                if (trigger.delayRounds > 0) {
                    trigger.delayRounds--
                    continue
                }

                for (let effect of skill.effects) {
                    if (effect.cd > 0) {
                        effect.cd--
                        continue
                    }
                    if (effect.rounds > 0) {
                        roundBuffs.push({buff: effect, role})
                    }
                }
            }

            for (let buff of role.buffs) {
                if (buff.delayRounds > 0) {
                    buff.delayRounds--
                    continue
                }
                if (buff.rounds > 0) {
                    roundBuffs.push({buff, role})
                }
            }
        }

        // console.log("onRoundStart", this.round)

        for (let role of roles) {
            this.checkUseSkill(TRIGGER, role)
        }
        this.handleActQueue()

        let log: BattleLog = {
            type: BattleLogType.ROUND_START,
        }
        this.addLog(log)

        let len = this.logs.length

        for (let {buff, role} of roundBuffs) {
            buff.rounds--
            this.onRemoveBuff(role, buff)
            this.checkAndRemoveBuffs(role)
        }

        if (len == this.logs.length) {
            // this.logs.length = len - 1
        }
        else {
            log.next = this.logs.slice(len)
            this.logs.length = len
        }

    }

    private onAttackBefore() {
        this.stage = BattleStage.BEFORE_ATTACK
        let roles = this.getCollideRoles()
        for (let role of roles) {
            this.checkUseSkill(TRIGGER.set(BattleSkillTriggerType.ATTACK_BEFORE, BattleSkillObjectType.SELF), role)
        }

        this.handleActQueue()
    }

    private onAttack(role: BattleRole, rev: BattleRole) {
        let skillParams = { from: role }
        this.checkUseSkill(TRIGGER.set(BattleSkillTriggerType.ATTACK), role, skillParams, true)
    }

    private onAttackAfter(roles: BattleRole[], behindRoles: BattleRole[]) {
        this.stage = BattleStage.AFTER_ATTACK
        if (this.isEnd()) return
        let type = BattleSkillTriggerType.ATTACK_AFTER
        for (let i = 0; i < roles.length; i++) {
            let role = roles[i]
            let params = { from: role, receivers: roles.filter(r => r != role) }
            this.checkUseSkill(TRIGGER.set(type), role, params)

            TRIGGER.set(type, BattleSkillObjectType.INDEX, BattleSkillCampType.TEAMMATE, role.orgIndex)
            for (let buff of this.buffs) {
                if (buff.type == role.type) {
                    this.checkUseSkill(TRIGGER, buff, params)
                }
            }

            TRIGGER.set(type, BattleSkillObjectType.ANIMAL_TYPE, BattleSkillCampType.TEAMMATE, role.animalType)
            for (let buff of this.buffs) {
                if (buff.type == role.type) {
                    this.checkUseSkill(TRIGGER, buff, params)
                }
            }

            let behindRole = behindRoles[i]
            if (behindRole) {
                TRIGGER.set(type, BattleSkillObjectType.FRONT, BattleSkillCampType.TEAMMATE, 1)
                this.checkUseSkill(TRIGGER, behindRole, params)
            }
        }
        this.handleActQueue()
    }

    private onHit(role: BattleRole, sender: BattleRole, effect?: BattleSkillEffect) {
        let skillParams = { receivers: [role], from: sender,  skill: effect?.skill?.orgSkill || effect?.skill }

        let type = BattleSkillTriggerType.HIT
        TRIGGER.set(type, BattleSkillObjectType.SELF)
        this.checkUseSkill(TRIGGER, role, skillParams)

        let behindRole = this.getBehind(role)
        if (behindRole) {
            TRIGGER.set(type, BattleSkillObjectType.FRONT, BattleSkillCampType.TEAMMATE, 1)
            this.checkUseSkill(TRIGGER, behindRole, skillParams)
        }

        let roles = this.getRolesByType(role.type)
        TRIGGER.set(type, BattleSkillObjectType.ALL, BattleSkillCampType.TEAMMATE)
        roles.for((_role) => {
            if (role == _role) return
            this.checkUseSkill(TRIGGER, _role, skillParams)
        })

        TRIGGER.set(type, BattleSkillObjectType.ALL, BattleSkillCampType.SELF_TEAMMATE)
        roles.for((_role) => {
            this.checkUseSkill(TRIGGER, _role, skillParams)
        })

        TRIGGER.set(type, BattleSkillObjectType.INDEX, BattleSkillCampType.TEAMMATE, role.orgIndex)
        for (let buff of this.buffs) {
            if (buff.type == role.type) {
                this.checkUseSkill(TRIGGER, buff, skillParams)
            }
        }

        TRIGGER.set(type, BattleSkillObjectType.ANIMAL_TYPE, BattleSkillCampType.TEAMMATE, role.animalType)
        for (let buff of this.buffs) {
            if (buff.type == role.type) {
                this.checkUseSkill(TRIGGER, buff, skillParams)
            }
        }
    }

    private onHpReduce(role: BattleRole, hitType: BattleHitType) {
        while (1) {
            let per = role.accHit / role.orgHp * 100
            TRIGGER.set(BattleSkillTriggerType.HP_REDUCE, BattleSkillObjectType.SELF, null, per)
            let skills = this.checkUseSkill(TRIGGER, role)
            if (skills.length <= 0) break
            let val = role.orgHp * role.skills[0].trigger.count / 100
            role.accHit -= val
        }

        if (hitType != BattleHitType.COLLIDE) {
            this.checkHpGTBuff(role)
        }

        let per2 = role.hp / role.orgHp * 100
        TRIGGER.set(BattleSkillTriggerType.HP_LTE, BattleSkillObjectType.SELF, null, per2)
        this.checkUseSkill(TRIGGER, role)
    }

    private checkHpGTBuff(role: BattleRole) {
        role.buffs.for(buff => {
            if (buff.dep == BattleSkillEffectType.TRIGGER) {
                let trigger = buff.skill.trigger
                TRIGGER.set(BattleSkillTriggerType.HP_GT, BattleSkillObjectType.SELF, null, role.hp / role.orgHp * 100)
                if (trigger.isSame(TRIGGER) && !trigger.check(TRIGGER)) {
                    let orgAtk = role.getAttack()
                    role.removeBuff(buff)
                    let atk = role.getAttack()
                    this.onRemoveBuff(role, buff, atk - orgAtk)
                }
            }
        })
    }

    private checkHpTrigger(role: BattleRole) {
        this.checkUseSkill(TRIGGER.set(BattleSkillTriggerType.HP_GT, BattleSkillObjectType.SELF, null, role.hp / role.orgHp * 100), role)
    }

    // 被攻击
    private onAttacked(role: BattleRole, sender: BattleRole, effect?: BattleSkillEffect) {
        let skillParams = { from: sender, skill: effect?.skill?.orgSkill || effect?.skill }
        TRIGGER.set(BattleSkillTriggerType.ATTACKED, BattleSkillObjectType.SELF)
        this.checkUseSkill(TRIGGER, role, skillParams)
    }

    private onDeath(role: BattleRole, sender: BattleRole, hitType: BattleHitType = BattleHitType.NORMAL, effect?: BattleSkillEffect) {
        let log: BattleLog = {
            type: BattleLogType.DEATH,
            receiver: role.uid,
        }
        let deathType = BattleDeathType.DIRECT //直接死亡
        let skillParams = { receivers: [role], from: sender, skill: effect?.skill?.orgSkill || effect?.skill, log }
        let roles = this.getRolesByType(role.type)
        let behindRole = this.getBehind(role)

        TRIGGER.set(BattleSkillTriggerType.DEATH, BattleSkillObjectType.SELF)
        let selfSkills = this.checkUseSkill(TRIGGER, role, skillParams)

        let skills = []
        if (behindRole) {
            TRIGGER.set(BattleSkillTriggerType.DEATH, BattleSkillObjectType.FRONT, BattleSkillCampType.TEAMMATE, 1)
            skills.pushArr(this.checkUseSkill(TRIGGER, behindRole, skillParams))
        }

        TRIGGER.set(BattleSkillTriggerType.DEATH, BattleSkillObjectType.ALL, BattleSkillCampType.TEAMMATE)
        roles.for((_role) => {
            if (role == _role) return
            skills.pushArr(this.checkUseSkill(TRIGGER, _role, skillParams))
        })

        if (!role.summoner) {
            TRIGGER.set(BattleSkillTriggerType.DEATH, BattleSkillObjectType.ROLE, BattleSkillCampType.TEAMMATE)
            roles.for((_role) => {
                if (role == _role) return
                skills.pushArr(this.checkUseSkill(TRIGGER, _role, skillParams))
            })
        }

        TRIGGER.set(BattleSkillTriggerType.KILL, BattleSkillObjectType.ALL, BattleSkillCampType.TEAMMATE)
        let enemies = this.getRolesByType(sender.type)
        enemies.for((_role) => {
            if (sender == _role) return
            skills.pushArr(this.checkUseSkill(TRIGGER, _role, skillParams))
        })

        TRIGGER.set(BattleSkillTriggerType.LIVE, BattleSkillObjectType.ALL, BattleSkillCampType.TEAMMATE, roles.filter(r => !r.isDeath()).length)
        for (let buff of this.buffs) {
            if (role.type == buff.type) {
                this.checkUseSkill(TRIGGER, buff, skillParams)
            }
        }

        TRIGGER.set(BattleSkillTriggerType.DEATH, BattleSkillObjectType.INDEX, BattleSkillCampType.TEAMMATE, role.orgIndex)
        for (let buff of this.buffs) {
            if (role.type == buff.type) {
                this.checkUseSkill(TRIGGER, buff, skillParams)
            }
        }

        TRIGGER.set(BattleSkillTriggerType.KILL, BattleSkillObjectType.SELF)
        this.checkUseSkill(TRIGGER, sender, skillParams)

        TRIGGER.set(BattleSkillTriggerType.DEATH_KILL, BattleSkillObjectType.SELF)
        this.checkUseSkill(TRIGGER, sender, skillParams)

        if (hitType != BattleHitType.COLLIDE) {
            TRIGGER.set(BattleSkillTriggerType.SKILL_KILL, BattleSkillObjectType.SELF)
            this.checkUseSkill(TRIGGER, sender, skillParams)
        }

        TRIGGER.set(BattleSkillTriggerType.DEATH, BattleSkillObjectType.ALL, BattleSkillCampType.ENEMY)
        let enemyType = role.type == BattleRoleType.PASSENGER ? BattleRoleType.MONSTER : BattleRoleType.PASSENGER
        enemies = this.getRolesByType(enemyType)
        enemies.for((_role) => {
            this.checkUseSkill(TRIGGER, _role, skillParams)
        })

        if (role.summoner) {
            TRIGGER.set(BattleSkillTriggerType.DEATH, BattleSkillObjectType.SUMMON, BattleSkillCampType.TEAMMATE)
            roles.for((_role) => {
                if (role == _role) return
                skills.pushArr(this.checkUseSkill(TRIGGER, _role, skillParams))
            })

            TRIGGER.set(BattleSkillTriggerType.DEATH, BattleSkillObjectType.SUMMON, BattleSkillCampType.ENEMY)
            enemies.for((_role) => {
                this.checkUseSkill(TRIGGER, _role, skillParams)
            })
        }

   
        if (selfSkills.length > 0) {
            deathType = BattleDeathType.DEATH
        }

        //能否靠别人的技能复活
        let rebirthSkills = []
        for (let skill of skills) {
            if (this.isRebirthSenderSkill(skill)) {
                deathType = BattleDeathType.DEATH
                rebirthSkills.push(skill)
            }
        }

        if (deathType == BattleDeathType.DIRECT) {
            this.onRemove(role)
        }
        else if (skills.find(skill => this.isSummonSkill(skill))) {
            deathType = BattleDeathType.SUMMON
        }

        log.data = { deathType, skills: selfSkills, rebirthSkills}
        this.addLog(log)
    }

    private onSummon(role: BattleRole, summons: BattleRole[]) {
        let type = summons[0].type
        let roles: BattleRole[] = this.getRolesByType(type)
        let skillParams = { receivers: summons, from: role }
        TRIGGER.set(BattleSkillTriggerType.SUMMON, BattleSkillObjectType.ALL, BattleSkillCampType.TEAMMATE)
        roles.for((_role) => {
            if (role == _role) return
            this.checkUseSkill(TRIGGER, _role, skillParams)
        })

        for (let buff of this.buffs) {
            if (summons[0].type == buff.type) {
                this.checkUseSkill(TRIGGER, buff, skillParams)
            }
        }

        TRIGGER.set(BattleSkillTriggerType.SUMMON, BattleSkillObjectType.ALL, BattleSkillCampType.ENEMY)
        let enemyType = type == BattleRoleType.PASSENGER ? BattleRoleType.MONSTER : BattleRoleType.PASSENGER
        let enemies: BattleRole[] = this.getRolesByType(enemyType)
        enemies.for((_role) => {
            this.checkUseSkill(TRIGGER, _role, skillParams)
        })
        for (let buff of this.buffs) {
            if (summons[0].type != buff.type) {
                this.checkUseSkill(TRIGGER, buff, skillParams)
            }
        }
    }

    private onRemove(role: BattleRole) {
        if (this.buffs.has(role)) {
            this.buffs.remove(role)
            return
        }
        let roles: BattleRole[] = this.getRolesByType(role.type)
        let index = roles.findIndex(r => r == role)
        if (index < 0) return
        roles.splice(index, 1)
        for (let i = index; i < roles.length; i++) {
            this.onPosChange(roles[i], i)
        }
    }

    private onPosChange(role: BattleRole, index = -1) {
        let roles: BattleRole[] = this.getRolesByType(role.type)
        if (roles.length <= 0) return
        index = index >= 0 ? index : roles.findIndex(r => r == role)
        if (index == 0) {
            let r = roles[0]
            let skillParams = { receivers: [r]}
            this.checkUseSkill(TRIGGER.set(BattleSkillTriggerType.POS_HEAD), r, skillParams)

            TRIGGER.set(BattleSkillTriggerType.POS_HEAD, BattleSkillObjectType.INDEX, BattleSkillCampType.TEAMMATE, r.orgIndex)
            for (let buff of this.buffs) {
                if (buff.type == role.type) {
                    this.checkUseSkill(TRIGGER, buff, skillParams)
                }
            }
        }
    }

    //------------------------------------

    private getCollideRoles() {
        let passenger = this.passengers.find(p => !p.isDeath())
        let monster = this.monsters.find(p => !p.isDeath())
        return [passenger, monster].filter(r => !!r)
    }

    private collide(roles: BattleRole[]) {
        this.stage = BattleStage.ATTACK
        let [passenger, monster] = roles
        let log: BattleLog = {
            type: BattleLogType.COLLIDE,
            sender: passenger.uid,
            receiver: monster.uid
            // params: [{uid: monster.uid, damage: damage1}, {uid: passenger.uid, damage: damage2}]
        }
        this.addLog(log)
        let len = this.logs.length

        this.attack(passenger, monster)
        this.attack(monster, passenger)

        this.checkHpGTBuff(passenger)
        this.checkHpGTBuff(monster)

        log.next = this.logs.slice(len)
        this.logs.length = len

        this.handleActQueue()
        return [passenger, monster]
    }

    private attack(sender: BattleRole, receiver: BattleRole) {
        this.onAttack(sender, receiver)
        let damage = this.hit(receiver, sender.getAttack(), sender, BattleHitType.COLLIDE)
        return damage
    }

    private isDeathTrigger(trigger: BattleSkillTrigger) {
        let type = trigger.type
        if (type == BattleSkillTriggerType.DEATH  || type == BattleSkillTriggerType.ATTACK || type == BattleSkillTriggerType.DEATH_KILL) {
            return trigger.object == BattleSkillObjectType.SELF
        }
        return false
    }

    private canTriggerSkill(role: BattleRole, skill: BattleSkill, params?: UseSkillParams, strict: boolean = false) {
        if (params?.skill == skill) { //相同一个技能不互相触发
            return
        }
        let trigger = skill.trigger
        if (role.isDeath() && !this.isDeathTrigger(trigger)) return
        if (trigger.type == BattleSkillTriggerType.USE_SKILL && params.skill?.trigger?.type == BattleSkillTriggerType.USE_SKILL) { //使用技能不互相触发
            return
        }

        if (trigger.type == BattleSkillTriggerType.ROUND_START && (trigger.count > 0 || trigger.delayRounds > 0)) { //回合开始时触发检测是否是当前回合触发
            return
        }

        if (trigger.type == BattleSkillTriggerType.DEATH_KILL) { //吸血鬼击杀吸血鬼无法复活
            let rev = params.receivers[0]
            if (rev.skills.some(s => s.objects.some(o => o.type == BattleSkillObjectType.REBIRTH))) {
                return
            }
        }

        if (!strict) return true

        for (let object of skill.objects) {
            for (let effect of object.effects) {
                if (effect.type == BattleSkillEffectType.CHANGE_HP) { //血量不足时发动不了
                    let objs = this.getObjects(role, object, params)
                    for (let obj of objs) {
                        let val = this.int(this.calcEffectValByObject(effect, role))
                        if (val < 0 && obj.hp + val <= 0) {
                            return false
                        }
                    }
                }
            }
        }

        if (role.buffs.some(b => b.type == BattleSkillEffectType.SILENCE)) { //被沉默
            return
        }

        if (trigger.type == BattleSkillTriggerType.USE_SKILL && this.buffs.has(params.from)) { //场地，装备技能不会触发这个
            return
        }

        if (trigger.type == BattleSkillTriggerType.LIVE) {
            let roles = this.getRolesByType(role.type)
            if (roles.length != trigger.count) {
                return
            }
        }

        return true
    }


    private checkUseSkill(trigger: BattleSkillTrigger, role: BattleRole, params?: UseSkillParams, immediate?: boolean) {
        if (!role) return
        let skills = []
        for (let skill of role.skills) {
            if (!skill.trigger.check(trigger)) continue
            if (!this.canTriggerSkill(role, skill, params)) {
                continue
            }
            if (!skill.canUse()) {
                // twlog.info("checkUseSkill canUse fail", role.id, skill.getId(), trigger.type)
                continue
            }
            if (immediate) {
                this.useSkill(role, skill, params)
            }
            else {
                this.pushAction(this.useSkill, role, skill, params)
            }
            skills.push(skill)
        }
        skills.pushArr(this.checkUseBuffSkill(trigger, role, params, immediate))
        return skills
    }

    private checkUseBuffSkill(trigger: BattleSkillTrigger, role: BattleRole, params?: UseSkillParams, immediate?: boolean) {
        let skills = []
        for (let skill of role.buffSkills) {
            if (!skill.trigger.check(trigger)) {
                // console.log("checkUseBuffSkill trigger fail", role.id, skill.getId(), trigger.type, trigger.object, trigger.camp)
                continue
            }
            if (!this.canTriggerSkill(role, skill, params)) {
                // console.log("checkUseBuffSkill fail: canTriggerSkill", role.id, skill.getId(), params)
                continue
            }
            if (!skill.canUse()) {
                // console.log("checkUseBuffSkill fail: canUse", role.id, skill.getId(), skill.effects[0].times)
                continue
            }
            if (immediate) {
                this.useBuffSkill(role, skill, params)
            }
            else {
                this.pushAction(this.useBuffSkill, role, skill, params)
            }
            skills.push(skill)
        }
        return skills
    }

    private isBuffEffect(type: BattleSkillEffectType) {
        return type == BattleSkillEffectType.IMMUNE_DAMAGE || type == BattleSkillEffectType.CHANGE_DAMAGE
            || type == BattleSkillEffectType.ATTACK_BUFF
    }

    public isSummonSkill(skill: BattleSkill, object?: BattleSkillObject) {
        object = object || skill.objects[0]
        let type = object.type
        return type == BattleSkillObjectType.SUMMON || type == BattleSkillObjectType.SUMMON_MUMMY
    }

    public isRebirthSenderSkill(skill: BattleSkill, object?: BattleSkillObject) {
        object = object || skill.objects[0]
        let type = object.type
        return type == BattleSkillObjectType.REBIRTH_SENDER
    }

    private checkTriggerEffect(role: BattleRole, effect: BattleSkillEffect, params: UseSkillParams) {
        if (effect.type != BattleSkillEffectType.TRIGGER) return false
        let buffSkill = effect.buffSkill
        let trigger = buffSkill.trigger
        if (trigger.type == BattleSkillTriggerType.KILL) {
            let rev = params.receivers[0]
            return rev.isDeath()
        }
        else if (trigger.type == BattleSkillTriggerType.LIVE) {
            let roles = this.getRolesByType(role.type)
            roles = roles.filter(r => !r.isDeath())
            return roles.length == trigger.count
        }
        return false
    }

    private useSkill(role: BattleRole, skill: BattleSkill, params?: UseSkillParams) {
        if (!this.canTriggerSkill(role, skill, params, true)) {//如果在发动技能前已经死了，直接跳过
            this.onUseSkillFail(role, skill, params)
            return
        }

        let log: BattleLog = {
            type: BattleLogType.USE_SKILL,
            sender: role.uid,
            data: {
                skillId: skill.getId(),
                skillType: skill.getType(),
                skillHash: skill.getHash(),
            }
        }
        this.addLog(log)
        let len = this.logs.length

        let {excludeMap, receivers} = this.handleSkill(role, skill, params)

        if (len == this.logs.length) { //中间没产生日志，那本条日志就可以去掉了
            this.logs.length = len - 1
            this.onUseSkillFail(role, skill, params)
        }
        else {
            log.data.skills = skill.skills.map(s => s.getId())
            if (excludeMap) {
                log.data.skills = log.data.skills.filter(id => !excludeMap[id])
            }
            for (let rev of receivers) {
                let usedObjectMap = skill.usedObjectMap
                if (!usedObjectMap[rev.uid]) usedObjectMap[rev.uid] = 0
                usedObjectMap[rev.uid]++
            }

            log.next = this.logs.slice(len)
            this.logs.length = len
            skill.use()
            this.onUseSkill(role, skill)
        }

        this.checkRemove(role, params)
    }

    private handleSkill(role: BattleRole, skill: BattleSkill, params?: UseSkillParams) {
        let objects = skill.objects
        let receiverMap: Map<BattleSkillObject, BattleRole[]> = new Map()
        let receivers = []
        for (let object of objects) {
            if (this.isSummonSkill(skill, object)) {
                this.summon(role, object, params)
            }
            else if (object.type == BattleSkillObjectType.REBIRTH) {
                this.rebirth(role, object, params)
            }
            else if (object.type == BattleSkillObjectType.REBIRTH_SENDER) {
                receivers = this.getObjects(role, object, params)
                if (receivers.length > 0) {
                    this.rebirth(receivers[0], object, params)
                }
            }
            else {
                let revs = this.getObjects(role, object, params)
                receiverMap.set(object, revs)
                receivers.pushArr(revs)
            }
        }
        let excludeMap = null

        let len = this.logs.length
        for (let [object, receivers] of receiverMap.entries()) {
            for (let receiver of receivers) {
                let effects = this.checkAndMergeEffects(role, object.effects, params, receiver)
                for (let effect of effects) {
                    if (effect.type == BattleSkillEffectType.TRIGGER) {
                        if (!excludeMap) excludeMap = {}
                        excludeMap[effect.buffSkill.getId()]
                    }
                }
           
                for (let effect of effects) {
                    if (receiver.isDeath() && effect.type != BattleSkillEffectType.COPY) break
                    if (!effect.canUse()) continue

                    if (effect.roleTimes > 0) {
                        let usedObjectMap = skill.usedObjectMap
                        let val = usedObjectMap[receiver.uid]
                        if (val && val >= effect.roleTimes) continue
                    }

                    this.useEffect(role, receiver, effect, skill, params)
                }
            }
            if (len == this.logs.length) break //暂时这么处理，不同object效果需要同时发生
        }
        return {excludeMap, receivers}
    }

    private checkAndMergeEffects(role: BattleRole, orgEffects: BattleSkillEffect[], params: UseSkillParams, receiver: BattleRole): BattleSkillEffect[] {
        let effects = []
        let tmpEffects = []
        let needMerge = false
        for (let effect of orgEffects) {
            if (!effect.canUse()) continue
            if (this.checkTriggerEffect(role, effect, params)) {
                needMerge = true
                tmpEffects.pushArr(effect.buffSkill.objects[0].effects)
            }
            else if (effect.rateType) {
                let rate = 1
                if (effect.rateType == SkillEffectRateType.ANIMAL_TYPE) {
                    if (this.buffs.has(role)) {
                        rate = this.allPassengers.filter(p => p.animalType == effect.skill["target"]).length
                    }
                    else {
                        rate = this.allPassengers.filter(p => p.animalType == role.animalType).length
                    }
                }
                else if (effect.rateType == SkillEffectRateType.REV_COUNT) {
                    rate = params.receivers.length
                }
                else if (effect.rateType == SkillEffectRateType.USE_COUNT) {
                    rate = effect.useCount
                }
                needMerge = true
                let orgValue = effect.value
                let orgRateType = effect.rateType
                effect.rateType = null
                effect.value = Number(effect.value) * rate
                tmpEffects.push(effect)
                effects = mergeEffects(effects, tmpEffects)
                effect.rateType = orgRateType
                effect.value = orgValue
                tmpEffects.length = 0
            }
            else if (effect.valueType == BattleEffectValueType.PER && effect.perObjType) {
                needMerge = true
                tmpEffects.push(effect)
                let orgValue = effect.value
                let orgValueType = effect.valueType
                let baseObject = this.getEffectBaseObject(effect, role, receiver)
                effect.value = this.calcEffectValByObject(effect, baseObject)
                effect.valueType = BattleEffectValueType.INT
                effects = mergeEffects(effects, tmpEffects)
                effect.valueType = orgValueType
                effect.value = orgValue
                tmpEffects.length = 0
            }
            else {
                tmpEffects.push(effect)
            }
        }

        if (needMerge) {
            effects = mergeEffects(effects, tmpEffects)
        }
        else {
            effects = tmpEffects
        }
        return effects
    }

    private onUseSkill(role: BattleRole, skill: BattleSkill) {
        skill.delayRounds = this.getSkillDelayRound()
        if (this.buffs.has(role)) {
            return
        }
        let enemyType = role.type == BattleRoleType.PASSENGER ? BattleRoleType.MONSTER : BattleRoleType.PASSENGER
        let enemies: BattleRole[] = this.getRolesByType(enemyType)
        TRIGGER.set(BattleSkillTriggerType.USE_SKILL, BattleSkillObjectType.ALL, BattleSkillCampType.ENEMY)
        let params = { from: role, skill }
        enemies.for((_role) => {
            this.checkUseSkill(TRIGGER, _role, params)
        })
    }

    private useBuffSkill(role: BattleRole, skill: BattleSkill, params?: UseSkillParams) {
        if (!this.canTriggerSkill(role, skill, params, true)) {
            this.onUseSkillFail(role, skill, params)
            return
        }
        let orgSkill = skill.orgSkill
        let log: BattleLog = {
            type: BattleLogType.USE_BUFF_SKILL,
            sender: role.uid,
            data: {
                id: orgSkill.getRole().id,
                skillId: skill.orgSkill.getId(),
                skillType: skill.orgSkill.getType(),
                skillHash: skill.orgSkill.getHash(),
            }
        }
        this.addLog(log)

        let len = this.logs.length

        this.handleSkill(role, skill, params)
      
        if (len == this.logs.length) { //中间没产生日志，那本条日志就可以去掉了
            this.logs.length = len - 1
            this.onUseSkillFail(role, skill, params)
        }
        else {
            let times = skill.effects[0].times
            skill.use()
            if (skill.effects[0].times < times) {
                this.onRemoveBuffSkill(role, skill)
            }

            log.next = this.logs.slice(len)
            this.logs.length = len
        }
        if (!skill.canUse()) {
            role.removeBuffSkill(skill)
        }

        this.checkRemove(role, params)
    }

    private checkRemove(role, params) {
        if (role.isDeath()) {
            //用完技能可以删掉了
            let log = params?.log
            if (log) {
                let rebirthSkills = log.data.rebirthSkills
                if (!this.hasNextAction(role) && rebirthSkills.length <= 0) {
                    this.onRemove(role)
                }
            }
        }
    }

    private hasNextAction(role: BattleRole) {
        return this.actQueue.some(action => action.args[0] == role)
    }

    private onUseSkillFail(role, skill: BattleSkill, params: UseSkillParams) {
        let log = params?.log
        if (!log) return
        let skills = log.data.skills
        let rebirthSkills = log.data.rebirthSkills
        if (role.isDeath()) {
            let s = skills.find(s => s.isSame(skill))
            skills.remove(s)
            if (!skills.some(s => this.isSummonSkill(s))) {
                if (log) {
                    log.data.deathType = BattleDeathType.DEATH
                }
            }
            if (skills.length <= 0 && rebirthSkills.length <= 0) {
                if (log) {
                    log.data.deathType = BattleDeathType.DIRECT //改为直接死亡
                }
                this.onRemove(role)
            }
        }

        if (this.isRebirthSenderSkill(skill)) {
            let role = params.receivers[0]
            let s = rebirthSkills.find(s => s.isSame(skill))
            rebirthSkills.remove(s)
            if (skills.length <= 0 && rebirthSkills.length <= 0) {
                if (log) {
                    log.data.deathType = BattleDeathType.DIRECT //改为直接死亡
                }
                this.onRemove(role)
            }
        }
    }

    private onUseEffectFail(sender: BattleRole, receiver: BattleRole) {
        let log: BattleLog = {
            type: BattleLogType.EFFECT_FAIL,
            sender: sender.uid,
            receiver: receiver.uid,
        }
        this.addLog(log)
    }

    private findLogs(callback) {
        let travl = (log) => {
            if (log.next) {
                for (let _log of log.next) {
                    if (callback(_log)) {
                        return _log
                    }
                    let ans = travl(_log)
                    if (ans) return ans
                }
            }
        }
        return travl({ next: this.logs })
    }

    private useEffect(sender: BattleRole, recv: BattleRole, effect: BattleSkillEffect, skill: BattleSkill, skillParams?: UseSkillParams) {
        let { type } = effect
        let baseObject = this.getEffectBaseObject(effect, sender, recv)
        let val = this.calcEffectValByObject(effect, baseObject)
        if (type == BattleSkillEffectType.CHANGE_HP) {
            this.changeHp(recv, val, sender, baseObject)
        }
        else if (type == BattleSkillEffectType.HP) {
            this.setHp(recv, val, sender, baseObject)
        }
        else if (type == BattleSkillEffectType.ATTACK) {
            this.setAttack(recv, val, sender, baseObject)
        }
        else if (type == BattleSkillEffectType.CHANGE_ATTACK) {
            this.changeAttack(recv, val)
        }
        else if (type == BattleSkillEffectType.DAMAGE) {
            this.hit(recv, val, sender, BattleHitType.NORMAL, effect)
        }
        else if (type == BattleSkillEffectType.TURE_DAMAGE) {
            this.hit(recv, val, sender, BattleHitType.NORMAL, effect)
        }
        else if (type == BattleSkillEffectType.IMMUNE_DAMAGE) {
            this.addBuff(recv, skill, effect, sender, val)
        }
        else if (type == BattleSkillEffectType.CHANGE_DAMAGE) {
            this.addBuff(recv, skill, effect, sender)
        }
        else if (type == BattleSkillEffectType.ATTACK_BUFF) {
            this.addBuff(recv, skill, effect, sender)
        }
        else if (type == BattleSkillEffectType.ATTACK_GAIN_BUFF || type == BattleSkillEffectType.HP_GAIN_BUFF ||
            type == BattleSkillEffectType.ATTACK_GAIN_DEBUFF || type == BattleSkillEffectType.HP_GAIN_DEBUFF || 
            type == BattleSkillEffectType.SILENCE || type == BattleSkillEffectType.SKILL_DAMAGE) {
            this.addBuff(recv, skill, effect, sender)
        }
        else if (type == BattleSkillEffectType.CHANGE_POSITION) {
            this.changePos(recv, val, sender)
        }
        else if (type == BattleSkillEffectType.MOVE_POSITION) {
            this.movePos(recv, val, sender)
        }
        else if (type == BattleSkillEffectType.CHANGE_SKILL) {
            this.changeSkill(recv, effect, skill)
        }
        else if (type == BattleSkillEffectType.BUFF) {
            this.addBuffSkill(recv, effect, sender)
        }
        else if (type == BattleSkillEffectType.COPY) {
            this.copySkill(sender, skill, recv, skillParams.skill)
        }
        else if (type == BattleSkillEffectType.RUN_AWAY) {
            this.runAway(recv)
        }
        else if (type == BattleSkillEffectType.USE_BUFFER_SKILL) {
            this.useSkill(recv, effect.skill.orgSkill, skillParams)
        }
    }

    private setHp(role, hp, sender, baseObject) {
        if (!baseObject) return
        hp = this.int(hp)
        let change = hp - role.hp
        role.hp = hp
        let log: BattleLog = {
            type: BattleLogType.CHANGE_HP,
            receiver: role.uid,
            data: {
                change,
                from: baseObject?.uid,
            }
        }
        this.addLog(log)
    }

    private changeHp(role: BattleRole, hp, sender, baseObject?) {
        if (!baseObject) return
        if (hp >= 0) {
            hp += this.calcValByHpGainBuff(role, hp)
            hp = Math.max(0, this.int(hp))
            role.hp += hp
            let log = {
                type: BattleLogType.CHANGE_HP,
                receiver: role.uid,
                data: {
                    change: hp,
                    from: baseObject?.uid,
                }
            }
            this.addLog(log)
            this.checkHpTrigger(role)
        }
        else {
            this.hit(role, -hp, sender, BattleHitType.CHANGE_HP)
        }
    }

    private setAttack(role: BattleRole, attack, sender, baseObject) {
        if (!baseObject) return
        attack = this.int(attack)
        let change = attack - role.attack
        role.attack = attack
        let log: BattleLog = {
            type: BattleLogType.CHANGE_ATTACK,
            receiver: role.uid,
            data: {
                change,
                from: baseObject?.uid,
            }
        }
        this.addLog(log)
    }

    private changeAttack(role: BattleRole, attack: number) {
        let orgAttack = role.getAttack()
        if (attack > 0) {
            attack += this.calcValByAttackGainBuff(role, attack)
            attack = Math.max(0, this.int(attack))
        }
        role.attack += this.int(attack)

        let log: BattleLog = {
            type: BattleLogType.CHANGE_ATTACK,
            receiver: role.uid,
            data: {
                change: role.getAttack() - orgAttack
            }
        }
        this.addLog(log)
    }
    private addBuff(role: BattleRole, skill: BattleSkill, effect: BattleSkillEffect, sender: BattleRole, calcValue?: number) {
        let buffs = role.buffs
        let repeat = effect.buff.repeat
        let orgBuffs = buffs.filter(b => b.type == effect.type && b.skill.isSame(skill))

        if (repeat >= 0 && orgBuffs.length > repeat) {
            // orgBuffs.min(b => b.times)?.resetTimes()
            return
        }

        let valueType = effect.valueType
        let value = effect.value
        if (calcValue !== undefined) {
            valueType = BattleEffectValueType.INT
            value = this.int(calcValue)
        }
        let buff = new BattleSkillEffect().init({ type: effect.type, rounds: effect.buff.rounds, times: effect.buff.times, valueType, value })
        buff.delayRounds = this.getSkillDelayRound()
        if (effect.buff.activeObject) {
            buff.activeObject = effect.buff.activeObject
        }
        if (effect.buff.dep) {
            buff.dep = effect.buff.dep
        }
        buff.skill = skill
        skill.sender = sender

        let orgAtk = role.getAttack()
        role.addBuff(buff)
        let atk = role.getAttack()

        let log: BattleLog = {
            type: BattleLogType.ADD_BUFF,
            sender: sender.uid,
            receiver: role.uid,
            data: {
                type: buff.type,
                times: buff.times > 0 ? buff.times : buff.rounds,
                skillId: buff.skill.getId(),
                skillType: buff.skill.getType(),
                skillHash: buff.skill.getHash(),
                value: value,
                changeAtk: atk - orgAtk
            }
        }
        this.addLog(log)
    }

    private addBuffSkill(role: BattleRole, effect: BattleSkillEffect, sender: BattleRole) {
        let skill = effect.buffSkill
        let buffSkills = role.buffSkills.filter(b => b.isSame(skill))

        let repeat = effect.buff.repeat

        if (repeat >= 0 && buffSkills.length > repeat) {
            // let buffSkill = buffSkills.min(b => b.effects[0].times)
            // buffSkill.effects.forEach(e => e.resetTimes())
            return
        }

        let buffSkill: BattleSkill = skill.clone()
        buffSkill.delayRounds = this.getSkillDelayRound()

        buffSkill.orgSkill = skill.orgSkill
        buffSkill.sender = sender
        buffSkill.trigger.delayRounds =  effect.buff.delayRounds || 0
        if (effect.buff.dep) {
            buffSkill.dep = effect.buff.dep
        }
        for (let e of buffSkill.effects) {
            e.times = effect.buff.times
            e.rounds = effect.buff.rounds
        }
        role.buffSkills.push(buffSkill)

        let log: BattleLog = {
            type: BattleLogType.ADD_BUFF,
            sender: sender.uid,
            receiver: role.uid,
            data: {
                type: BattleSkillEffectType.BUFF,
                skillId: skill.orgSkill.getId(),
                skillType: skill.orgSkill.getType(),
                skillHash: skill.orgSkill.getHash(),
                times: effect.buff.times > 0 ? effect.buff.times : effect.buff.rounds
            }
        }
        this.addLog(log)
    }

    private copySkill(role: BattleRole, _skill: BattleSkill, copy: BattleRole, copySkill: BattleSkill) {
        if (copySkill.trigger.type == BattleSkillTriggerType.ATTACK) return
        if (copySkill.getId() == 1024) return //不复制神灯

        let log: BattleLog = {
            type: BattleLogType.COPY,
            sender: role.uid,
            receiver: copy.uid,
            data: {
                copyId: copy.id
            }
        }
        this.addLog(log)
        let len = this.logs.length

        let skill = new BattleSkill().init(copySkill.getId(), copySkill.getLevel(), _skill.getRole())
        this.useSkill(role, skill, { skill: _skill, copyRole: copy })

        if (len == this.logs.length) { //中间没产生日志，那本条日志就可以去掉了
            this.logs.length = len - 1
        }
        else {
            log.next = this.logs.slice(len)
            this.logs.length = len

            log.data.isDeath = this.isDeathTrigger(skill.trigger)
        }
    }

    private runAway(role: BattleRole) {
        let log: BattleLog = {
            type: BattleLogType.RUN_AWAY,
            receiver: role.uid,
            data: {
            }
        }
        this.addLog(log)
        this.onRemove(role)
        this.addRecord("runAwayCnt", 1)
    }

    private summon(role: BattleRole, object: BattleSkillObject, params?: UseSkillParams) {
        let roles = this.getRolesByCamp(role, object)
        let roleType = this.getObjectRoleType(role, object)
        let allRoles = this.getAllRolesByType(roleType)
        let effects = object.effects
        let summons = []
        let effect1 = effects.find(e => e.type == BattleSkillEffectType.HP)
        let effect2 = effects.find(e => e.type == BattleSkillEffectType.ATTACK)

        let type = object.type
        let factorFunc = this.createSummon
        let indexRole = null //召唤的位置为这个角色后面，null代表插到第一个
        let rev = params?.receivers && params.receivers[0]
        if (type == BattleSkillObjectType.SUMMON_MUMMY) {
            for (let role of allRoles) { //找到from前面没死的
                if (role == rev) {
                    break
                }
                if (!role.isDeath()) {
                    indexRole = role
                }
            }
        }

        let count = object.count
        let index = roles.findIndex(r => r == indexRole)
        let allIndex = allRoles.findIndex(r => r == rev)
        let summonId = params?.copyRole?.id || role.id

        let liveCount = roles.filter(r => !r.isDeath()).length
        count = Math.min(count, 6 - liveCount) //最多只能x个人

        for (let i = 0; i < count; i++) {
            let summon = factorFunc.call(this, summonId, role, object, this.calcEffectVal(effect1, role, rev), this.calcEffectVal(effect2, role, rev))
            if (!summon) continue
            summon.type = roleType
            roles.splice(index + 1, 0, summon)
            allRoles.splice(allIndex + 1, 0, summon)
            summons.push(summon)
        }

        if (summons.length <= 0) return

        this.onSummon(role, summons)

        let log: BattleLog = {
            type: BattleLogType.SUMMON,
            sender: role.uid,
            data: {
                summons: summons.map(({ uid, id, attack, hp, type, skill }) => {
                    return { uid, id, attack, hp, type }
                }),
                indexUid: indexRole?.uid
            }
        }
        this.addLog(log)

        effects = effects.filter(e => e != effect1 && e != effect2)
        for (let summon of summons) {
            let _effects = this.checkAndMergeEffects(role, effects, params, summon)
            for (let effect of _effects) {
                if (effect.canUse()) {
                    this.useEffect(role, summon, effect, object.skill)
                }
            }
        }

        return summons
    }

    private createSummon(id: number, role: BattleRole, object: BattleSkillObject, hp: number, attack: number) {
        let skill = object.skill
        id = BattleSummonID.BASE + id
        if (skill.getType() == SkillType.EQUIP) {
            id = BattleSummonID.EQUIP_BASE + 2009
        }
        let summonSkills = []
        let idEffect = object.effects.find(e => e.type == BattleSkillEffectType.SUMMONN_ID)
        if (idEffect) {
            id = Number(idEffect.value)
            if (id == 0) {
                id = role.id
                if (!this.summonSelfMap[id]) {
                    this.summonSelfMap[id] = 0
                }
                if (this.summonSelfMap[id] >= 8) {
                    return
                }
                this.summonSelfMap[id]++
            }
            if (gameHelper.checkPassengerById(id)) {
                let p = new PassengerModel().init({ id, level: role.lv, starLv: role.starLv })
                summonSkills = p.getSkills()
            }
            else {
                let p = new Monster().init(id, role.lv, role.starLv)
                summonSkills = p.getSkills()
            }
        }
        let summon = new BattleRole().initData({ id, hp: this.int(hp), attack: this.int(attack), uid: `${id}_${util.uid()}`, skills: summonSkills, lv: role.lv, starLv: role.starLv })
        summon.mergeSkills()
        summon.summoner = role
        return summon
    }

    //把角色移动到index的位置
    private changePos(role: BattleRole, index: number, sender: BattleRole) {
        let roles = this.getRolesByType(role.type)
        let orgIndex = roles.findIndex(r => r == role)
        if (index < 0) {
            index = roles.length - index + 1
        }
        index = cc.misc.clampf(index, 1, roles.length)
        roles.splice(orgIndex, 1)
        roles.splice(index - 1, 0, role)

        let allRoles = this.getAllRolesByType(role.type)
        allRoles.splice(allRoles.findIndex(r => r == role), 1)
        allRoles.splice(index - 1, 0, role)

        let log: BattleLog = {
            type: BattleLogType.CHANGE_POSITION,
            sender: sender.uid,
            receiver: role.uid,
            data: {
                index
            }
        }
        this.addLog(log)

        let left = index - 1, right = orgIndex
        if (left == right) return
        if (left > right) {
            let t = left
            left = right
            right = t
        }
        for (let i = left; i < right; i++) {
            this.onPosChange(roles[i], i)
        }
    }

    //角色向前/后移动x个位置
    private movePos(role: BattleRole, count: number, sender: BattleRole) {
        let roles = this.getRolesByType(role.type)
        let orgIndex = roles.findIndex(r => r == role)
        let index = cc.misc.clampf(orgIndex - count + 1, 1, roles.length)
        this.changePos(role, index, sender)
    }

    private changeSkill(role: BattleRole, effect: BattleSkillEffect, fromSkill: BattleSkill) {
        for (let skill of role.skills) {
            if (skill.getType() != fromSkill.getType()) continue
            for (let e of skill.effects) {
                if (e == effect) continue
                let type = String(effect.perAttrType)
                if (type != "ALL" && type != e.type) continue
                let val = Number(e.value)
                if (effect.valueType == BattleEffectValueType.PER) {
                    val *= (1 + Number(effect.value))
                }
                else if (effect.valueType == BattleEffectValueType.INT) {
                    val += Number(effect.value)
                }
                e.value = this.int(val)
            }
        }
    }

    private rebirth(role: BattleRole, object: BattleSkillObject, params?: UseSkillParams, sender?: BattleRole) {
        if (!role.isDeath()) return

        let effects = object.effects
        let effect1 = effects.find(e => e.type == BattleSkillEffectType.HP)
        let effect2 = effects.find(e => e.type == BattleSkillEffectType.ATTACK)
        let effect = effects.find(e => e.type == BattleSkillEffectType.REBIRTH)

        if (effect) {
            if (effect.roleTimes > 0) {
                let usedObjectMap = object.skill.usedObjectMap
                let val = usedObjectMap[role.uid]
                if (val && val >= effect.roleTimes) return
            }
        }

        let orgHp = role.hp
        let orgAttack = role.getAttack()

        sender = sender || role

        let hp = role.orgHp
        let attack = role.orgAttack

        if (effect1) {
            hp = Number(effect1.value)
        }
        if (effect2) {
            attack = Number(effect2.value)
        }

        role.hp = hp
        role.attack = attack

        let log: BattleLog = {
            type: BattleLogType.REBIRTH,
            sender: role.uid,
            receiver: role.uid,
            data: {
                hp: role.hp - orgHp,
                attack: role.getAttack() - orgAttack,
            }
        }
        this.addLog(log)

        effects = effects.filter(e => e != effect1 && e != effect2)
        for (let effect of effects) {
            if (effect.roleTimes > 0) {
                let usedObjectMap = object.skill.usedObjectMap
                let val = usedObjectMap[role.uid]
                if (val && val >= effect.roleTimes) continue
            }
            if (effect.canUse()) {
                this.useEffect(sender, role, effect, object.skill)
            }
        }
    }

    private calcEffectVal(effect: BattleSkillEffect, sender: BattleRole, recv?: BattleRole): number {
        let role = this.getEffectBaseObject(effect, sender, recv)
        return this.calcEffectValByObject(effect, role)
    }

    private getEffectBaseObject(effect: BattleSkillEffect, sender: BattleRole, recv?: BattleRole) {
        let role = sender
        let { perObjType } = effect
        if (perObjType == SkillEffectPerObjType.RECIPIENT) {
            role = recv
        }
        else if (perObjType == SkillEffectPerObjType.MAX_HP) { //最高血的队友，不包括自己
            let roles = this.getRolesByType(recv.type).filter(r => r != recv)
            role = roles.max(r => r.orgHp)
        }
        return role
    }

    private calcEffectValByObject(effect: BattleSkillEffect, role: BattleRole) {
        let base = 0
        let { perObjType, perAttrType, valueType, value } = effect
        if (!perObjType || valueType == BattleEffectValueType.INT) return value as number
        if (!role) return value as number
        if (perAttrType == SkillEffectPerAttrType.HP || perAttrType == SkillEffectPerAttrType.ORG_HP) {
            base = role.orgHp
        }
        else if (perAttrType == SkillEffectPerAttrType.ATTACK) {
            base = role.orgAttack
        }
        else if (perAttrType == SkillEffectPerAttrType.DAMAGE) {
            base = role.preDamage
        }
        else if (perAttrType == SkillEffectPerAttrType.HIT) [
            base = role.preHit
        ]
        else if (perAttrType == SkillEffectPerAttrType.ACC_HIT) {
            base = role.accHit
        }
        return base * value
    }

    private getObjects(role: BattleRole, object: BattleSkillObject, params?: UseSkillParams) {
        let { type, count, camp } = object
        let objects = []

        if (type == BattleSkillObjectType.REBIRTH) {
            return [role]
        }
        if (type == BattleSkillObjectType.REBIRTH_SENDER) {
            return params.receivers || []
        }

        let roles = this.getRolesByCamp(role, object)

        roles = roles.filter(r => !r.isDeath()) //过滤掉死掉的

        if (type == BattleSkillObjectType.SELF) {
            objects.push(role)
        }
        else if (type == BattleSkillObjectType.HEAD) {
            objects = roles.slice(0, count)
            objects = this.checkAndFilterSelf(role, camp, objects)
        }
        else if (type == BattleSkillObjectType.TAIL) {
            objects = roles.slice(roles.length - count, roles.length)
            objects = this.checkAndFilterSelf(role, camp, objects)
        }
        else if (type == BattleSkillObjectType.HEAD_INDEX) {
            if (role.id == ROLE_ATTR_ID.TRAIN) {
                let enemieTrain = this.buffs.find(b => b.id == role.id && b.type != role.type)
                if (enemieTrain) {
                    roles = [enemieTrain].concat(roles)
                }
            }
            count = Math.min(roles.length, count)
            objects = [roles[count - 1]]
            objects = this.checkAndFilterSelf(role, camp, objects)
        }
        else if (type == BattleSkillObjectType.TAIL_INDEX) {
            count = Math.min(roles.length, count)
            objects = [roles[roles.length - count]]
            objects = this.checkAndFilterSelf(role, camp, objects)
        }
        else if (type == BattleSkillObjectType.FRONT) {
            objects = this.getRolesByIndex(role, count)
        }
        else if (type == BattleSkillObjectType.BEHIND) {
            objects = this.getRolesByIndex(role, -count)
        }
        else if (type == BattleSkillObjectType.SELF_INDEX) {
            let index = this.getRoleIndex(role)
            let target = roles[index]
            if (!target || target.isDeath()) {
                target = roles.last()
            }
            objects = [target]
        }
        else if (type == BattleSkillObjectType.SENDER) {
            if (!params.copyRole && !params?.from) {
                twlog.error(role.id, "sender not found")
                return objects
            }
            if (!this.buffs.has(params.from)) {
                objects.push(params.from)
            }
            if (object.effects.some(e => e.type == BattleSkillEffectType.COPY)) {
                return objects
            }
        }
        else if (type == BattleSkillObjectType.RECIPIENT) {
            if (!params.copyRole && !params?.receivers?.length) {
                twlog.error(role.id, "receivers not found")
                return objects
            }
            objects = params.receivers || []
        }
        else if (type == BattleSkillObjectType.RECIPIENT_BEHIND) {
            if (!params.copyRole && !params?.receivers?.length) {
                twlog.error(role.id, "RECIPIENT_BEHIND not found")
                return objects
            }
            objects = this.getRolesByIndex(params.receivers[0], -count)
        }
        else if (type == BattleSkillObjectType.SAME_BUFF) {
            objects = roles.filter(r => r.buffSkills.some(b => b.isSame(object.skill)))
            objects = this.checkAndFilterSelf(role, camp, objects)
        }
        else if (type == BattleSkillObjectType.BUFFER) {
            objects.push(object.skill.sender)
        }
        else if (type == BattleSkillObjectType.ANIMAL_TYPE) {
            objects = roles.filter(r => r.animalType == object.count)
            objects = this.checkAndFilterSelf(role, camp, objects)
        }
        else if (type == BattleSkillObjectType.INDEX) {
            objects = roles.filter(r => r.orgIndex == object.count)
        }
        else { //以下与站位无关
            if (camp == BattleSkillCampType.TEAMMATE) { //自己不属于队友
                roles = roles.filter(r => r != role)
            }
            if (roles.length <= 0) return objects
            else if (type == BattleSkillObjectType.MIN_HP) {
                objects = roles.slice().sort((a, b) => {
                    return a.orgHp - b.orgHp
                }).slice(0, count)
            }
            else if (type == BattleSkillObjectType.MIN_HP_ROLE) {
                objects = roles.filter(r => !r.summoner ).sort((a, b) => {
                    return a.orgHp - b.orgHp
                }).slice(0, count)
            }
            else if (type == BattleSkillObjectType.MAX_ATTACK) {
                objects = roles.slice().sort((a, b) => {
                    return b.orgAttack - a.orgAttack
                }).slice(0, count)
            }
            else if (type == BattleSkillObjectType.ALL) {
                objects = roles
            }
            else if (type == BattleSkillObjectType.MAX_HP) {
                objects = roles.slice().sort((a, b) => {
                    return b.orgHp - a.orgHp
                }).slice(0, count)
            }
        }

        return objects.filter(r => {
            if (!r || r.isDeath()) return
            if (this.buffs.has(r)) {
                if (role.id != ROLE_ATTR_ID.TRAIN || r.id != role.id) { //只能列车攻击列车
                    return 
                }
            }
            return true
        })
    }

    private checkAndFilterSelf(role: BattleRole, camp, roles: BattleRole[]) {
        if (camp == BattleSkillCampType.TEAMMATE) {
            roles = roles.filter(r => r != role)
        }
        return roles
    }

    public getRoleIndex(role) {
        let roles = role.type == BattleRoleType.PASSENGER ? this.passengers : this.monsters
        let index = roles.findIndex(r => r == role || r.uid == role.uid)
        return index
    }

    private getRolesByIndex(role, frontCnt) {
        let roles = role.type == BattleRoleType.PASSENGER ? this.passengers : this.monsters
        let index = this.getRoleIndex(role)
        if (index < 0) return []
        let res = []
        if (frontCnt > 0) { //往前找
            for (let i = index - 1; i >= Math.max(0, index - frontCnt); i--) {
                if (roles[i].isDeath()) continue
                res.push(roles[i])
            }
        }
        else { //往后找
            for (let i = index + 1; i <= Math.min(roles.length - 1, index - frontCnt); i++) {
                if (roles[i].isDeath()) continue
                res.push(roles[i])
            }
        }
        return res
    }

    //根据作用对象的阵营，获取角色
    private getRolesByCamp(role: BattleRole, object: BattleSkillObject) {
        let { camp } = object
        let roles: BattleRole[] = []
        if (camp == BattleSkillCampType.ALL) {
            roles = this.passengers.concat(this.monsters)
        }
        else {
            let roleType = this.getObjectRoleType(role, object)
            roles = roleType == BattleRoleType.PASSENGER ? this.passengers : this.monsters
        }
        return roles
    }

    //获取技能作用的对象的类别(乘客/怪物)
    private getObjectRoleType(role: BattleRole, object: BattleSkillObject) {
        let { camp } = object
        let roleType = role.type
        if (camp == BattleSkillCampType.ENEMY) {
            roleType = role.type == BattleRoleType.PASSENGER ? BattleRoleType.MONSTER : BattleRoleType.PASSENGER
        }
        return roleType
    }

    private hit(role: BattleRole, damage: number, sender: BattleRole, hitType: BattleHitType, effect?: BattleSkillEffect) {
        let log: BattleLog = {
            type: BattleLogType.HIT,
            sender: sender.uid,
            receiver: role.uid,
            data: {
                hitType
            }
        }
        this.addLog(log)

        let len = this.logs.length

        if (damage < 0.5) {
            damage = 0.5
        }

        if (hitType != BattleHitType.CHANGE_HP) {
            this.onAttacked(role, sender, effect)
        }

        if (hitType == BattleHitType.COLLIDE) {
            damage = this.calcSenderDamgeByBuff(sender, damage)
        }

        if (hitType != BattleHitType.CHANGE_HP) {
            let damageAdd = 0
            if (hitType == BattleHitType.NORMAL) { //技能增伤
                damageAdd += this.calcValByBuffByBase(sender, BattleSkillEffectType.SKILL_DAMAGE, damage) 
            }

            damageAdd += this.calcValByBuffByBase(role, BattleSkillEffectType.CHANGE_DAMAGE, damage, (buff)=>{ //易伤
                if (buff.activeObject == BattleSkillObjectType.SENDER) {
                    return buff.skill.sender == sender
                }
                else if (buff.activeObject == BattleSkillObjectType.SKILL) {
                    return hitType == BattleHitType.NORMAL   
                }
                return true
            })

            let damageReduce = this.calcValByBuffByBase(role, BattleSkillEffectType.IMMUNE_DAMAGE, damage) //盾
    
            if (hitType == BattleHitType.TRUE) {
                damage += Math.max(0, damageAdd - damageReduce)
            }
            else {
                damage += (damageAdd - damageReduce)
            }
        }

        damage = Math.max(damage, 0)

        damage = this.int(damage)

        let dead = role.isDeath() //这时候已经死了
        if (damage > 0) {

            if (damage >= role.hp) { //进入濒死
                let type = BattleSkillTriggerType.NEAR_DEATH
                TRIGGER.set(type, BattleSkillObjectType.SELF)
                let skills = this.checkUseSkill(TRIGGER, role)
                if (skills.length > 0) {
                    damage = role.hp - 1
                }
            }

            role.hp -= damage
            role.preHit = damage
            role.accHit += damage
     
            if (role.type == BattleRoleType.MONSTER) {
                this.addRecord("monsterHitCnt", 1)
            }

            if (!dead) {
                if (hitType != BattleHitType.CHANGE_HP) {
                    this.onHit(role, sender, effect)
                }
                if (role.isDeath()) {
                    this.onDeath(role, sender, hitType, effect)
                }
                else {
                    this.onHpReduce(role, hitType)
                }
            }
        }

        log.data = { damage, hitType }
        sender.preDamage = damage

        log.next = this.logs.slice(len)
        this.logs.length = len

        return damage
    }

    //计算攻击者的buff加成
    private calcSenderDamgeByBuff(role: BattleRole, damage) {
        let buffs = role.buffs

        let checkLog = (buff: BattleSkillEffect, times) => {
            if (buff.times < times) {
                this.onRemoveBuff(role, buff)
            }
        }

        let orgAttack = role.getAttack()
        buffs.forEach((buff, i) => {
            if (buff.type != BattleSkillEffectType.ATTACK_BUFF) return
            let times = buff.times
            buff.use()
            checkLog(buff, times, )
        })

        this.checkAndRemoveBuffs(role)

        let attack = role.getAttack()
        if (attack != orgAttack && !role.isDeath()) {
            let log = {
                type: BattleLogType.CHANGE_ATTACK,
                receiver: role.uid,
                data: {
                    change: attack - orgAttack,
                    isBuff: true,
                }
            }
            this.addLog(log)
        }
        return damage
    }

    private calcValByHpGainBuff(role: BattleRole, base: number) {
        let add = this.calcValByBuffByBase(role, BattleSkillEffectType.HP_GAIN_BUFF, base, (buff)=> buff.value > 0)
        let reduce = this.calcValByBuffByBase(role, BattleSkillEffectType.HP_GAIN_BUFF, base, (buff)=> buff.value < 0)
        let reduce2 = this.calcValByBuffByBase(role, BattleSkillEffectType.HP_GAIN_DEBUFF, base, (buff) => buff.value < 0)
        reduce = Math.min(0, reduce - reduce2)
        return add + reduce
    }

    private calcValByAttackGainBuff(role: BattleRole, base: number) {
        let add = this.calcValByBuffByBase(role, BattleSkillEffectType.ATTACK_GAIN_BUFF,base, (buff)=> buff.value > 0)
        let reduce = this.calcValByBuffByBase(role, BattleSkillEffectType.ATTACK_GAIN_BUFF, base, (buff)=> buff.value < 0)
        let reduce2 = this.calcValByBuffByBase(role, BattleSkillEffectType.ATTACK_GAIN_DEBUFF, base, (buff) => buff.value < 0)
        reduce = Math.min(0, reduce - reduce2)
        return add + reduce
    }

    private calcValByBuffByBase(role: BattleRole, type: BattleSkillEffectType, base: number, filter?: Function) {
        let filter2 = (valueType)=>{
            return (buff)=>{
                if (filter && !filter(buff)) return
                return buff.valueType == valueType
            }
        }
        let abs = this.calcValByBuff(role, type, filter2(BattleEffectValueType.INT))
        let per = this.calcValByBuff(role, type, filter2(BattleEffectValueType.PER))
        return abs + base * per
    }

    private calcValByBuff(role: BattleRole, type: BattleSkillEffectType, filter?: Function) {
        let buffs = role.buffs

        let checkLog = (buff: BattleSkillEffect, times) => {
            if (buff.times < times) {
                this.onRemoveBuff(role, buff)
            }
        }

        let sum = 0
        buffs.forEach((buff, i) => {
            if (buff.type != type) return
            if (filter && !filter(buff)) return
            let times = buff.times
            buff.use()
            sum += Number(buff.value)
            checkLog(buff, times)
        })

        this.checkAndRemoveBuffs(role)

        return sum
    }

    private checkAndRemoveBuffs(role: BattleRole) {
        let buffs = role.checkAndRemoveBuffs()
        let depBuffs = []
        for (let buff of buffs) {
            depBuffs.pushArr(role.checkAndRemoveByDepBuff(buff))
        }
        for (let buff of depBuffs) {
            if (buff instanceof BattleSkillEffect) {
                this.onRemoveBuff(role, buff)
            }
            else {
                this.onRemoveBuffSkill(role, buff)
            }
        }
    }

    private onRemoveBuff(role: BattleRole, buff: BattleSkillEffect, changeAtk = 0) {
        let log: BattleLog = {
            type: BattleLogType.REMOVE_BUFF,
            receiver: role.uid,
            data: {
                type: buff.type,
                roleId: buff.skill.getRole().id,
                skillId: buff.skill.getId(),
                skillType: buff.skill.getType(),
                skillHash: buff.skill.getHash(),
                changeAtk,
            }
        }
        this.addLog(log)
    }

    private onRemoveBuffSkill(role: BattleRole, skill: BattleSkill) {
        let log: BattleLog = {
            type: BattleLogType.REMOVE_BUFF,
            receiver: role.uid,
            data: {
                type: BattleSkillEffectType.BUFF,
                roleId: skill.orgSkill.getRole().id,
                skillId: skill.orgSkill.getId(),
                skillType: skill.orgSkill.getType(),
                skillHash: skill.orgSkill.getHash(),
            }
        }
        this.addLog(log)
    }

    private getRolesByType(type: BattleRoleType) {
        return type == BattleRoleType.PASSENGER ? this.passengers : this.monsters
    }

    private getAllRolesByType(type: BattleRoleType) {
        return type == BattleRoleType.PASSENGER ? this.allPassengers : this.allMonsters
    }

    public getOrgRoleIndex(role: BattleRole) {
        if (this.buffs.has(role)) {
            return -1
        }
        let orgRoles = role.type == BattleRoleType.PASSENGER ? this.orgPassengers : this.orgMonsters
        let roles = this.getAllRolesByType(role.type)
        let index = -1
        for (let _role of roles) {
            if (orgRoles.some(r => r == _role)) {
                index++
            }
            if (role == _role) {
                break
            }
        }
        return index
    }

    private getBehind(role: BattleRole) {
        let behindRole = this.getRolesByIndex(role, -1)[0]
        return behindRole
    }

    private addLog(log) {
        this.logs.push(log)

        if (false) {
            this.debug?.parseLog(log)
        }
        else if (this.debug) {
            util.wait(0.1).then(() => {
                this.debug?.parseLog(log)
            })
        }
    }

    private int(val) {
        return Math.round(Math.abs(val)) * ut.normalizeNumber(val)
    }
    
    private getSkillDelayRound() {
        let stage = this.stage
        if (stage == BattleStage.ROUND_START) {
            return 0
        }
        return 1
    }

    private addRecord(key: string, value: number) {
        if (!this.record[key]) {
            this.record[key] = 0
        }
        this.record[key] += value
    }

    public getRecord(key: string) {
        return this.record[key] || 0
    }
}