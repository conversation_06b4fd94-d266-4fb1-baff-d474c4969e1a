import { MAX_VALUE } from "../../common/constant/Constant";
import { PlanetMonsterCfg } from "../../common/constant/DataType";
import { LifeSkillEffectType, PassengerAttr, SkillType, ValueType } from "../../common/constant/Enums";
import { cfgHelper } from "../../common/helper/CfgHelper";
import LifeSkill from "../passenger/Lifeskill";
import BattleSkill from "./BattleSkill";

export default class Monster {

    public id: number = null; //id
    public lv: number = 0
    private starLv: number = 0
    private json: PlanetMonsterCfg = null
    private skills: BattleSkill[] = []
    private lifeSkills: LifeSkill[] = []

    public isBoss() {
        return this.json.isBoss
    }

    public init(id, lv = 1, starLv?) {
        this.id = id
        this.lv = lv
        this.starLv = isNaN(starLv) ? cfgHelper.getInitStarLv(id) : starLv
        this.initJson()

        return this
    }

    private initJson() {
        let json = assetsMgr.getJsonData<PlanetMonsterCfg>("PlanetMonster", this.id)
        this.json = json
        this.initSkills()
    }

    private initSkills() {
        let skills = cfgHelper.getSkillsByLv(this.id, this.lv)
        this.skills = skills.map(({ id, lv }) => {
            return new BattleSkill().init(id, lv, this)
        })
    }

    public get hp() {
        return this.getAttr(PassengerAttr.HP) || MAX_VALUE
    }

    public get attack() {
        return this.getAttr(PassengerAttr.ATTACK)
    }

    public get iconCircle() {
        return this.json.iconCircle
    }

    public get iconBig() {
        return this.json.iconBig
    }

    public get name() {
        return this.json.name
    }

    public getAttack() {
        return this.attack
    }

    public getHp() {
        return this.hp
    }

    public getAttr(type: PassengerAttr) {
        return cfgHelper.getRoleAttr(this.id, this.getLevel(), this.starLv, type)
    }

    public getLevel() {
        return this.lv
    }

    public getSkills() {
        return this.skills
    }

    public getLifeSkills() {
        return this.lifeSkills
    }

    public getStarLv() {
        return this.starLv
    }

    public getTalentLevel() {
        return 0
    }
}