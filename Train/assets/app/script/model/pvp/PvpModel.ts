import { util } from "../../../core/utils/Utils"
import { Msg } from "../../../proto/msg-define"
import { BattleLevelType } from "../../common/constant/Enums"
import EventType from "../../common/event/EventType"
import { cfgHelper } from "../../common/helper/CfgHelper"
import { gameHelper } from "../../common/helper/GameHelper"
import { resHelper } from "../../common/helper/ResHelper"
import { viewHelper } from "../../common/helper/ViewHelper"
import BattleRole from "../battle/BattleRole"

@mc.addmodel('pvp')
export default class PvpModel extends mc.BaseModel {
    public data: proto.IPvpModuleData = null

    private _info: Map<proto.PvpType, PvpInfo> = null
    private _synced: boolean = false

    public init() {
        this._info = new Map<proto.PvpType, PvpInfo>()
        if (this.data) {
            this.setWithPvpData(this.data)
            this.data = null
        }
    }

    @util.addLock
    async syncPvpData(force: boolean = false) {
        // if (this._synced && !force) return
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_PvpModuleDataMessage)
        if (!r) return
        if (r.normal) {
            this.getPvpInfo(proto.PvpType.NORMAL).setExtData(r.normal)
        }
        this._synced = true
    }

    setWithPvpData(data: proto.IPvpModuleData, normalData?: proto.IPvpNormalData) {
        for (let k in data.ticket) {
            let t = Number(k) as unknown as proto.PvpType
            const i = new PvpInfo().init(t, data.ticket[k], data.duration[k])
            this._info.set(t, i)
            if (t == proto.PvpType.NORMAL && normalData) {
                i.setExtData(normalData)
            }
        }
    }

    // 获取pvp信息
    public getPvpInfo(type: proto.PvpType) { return this._info.get(type) }

    @util.addLock
    public async updateFormation(type: proto.PvpType, idAry: string[]) {
        const info = this.getPvpInfo(type)
        if (!info) return false
        return await info.updateFormation(idAry)
    }

    public resetTicket() {
        this._info.forEach(info => {
            info._ticket = info.ticketMax
            eventCenter.emit(EventType.PVP_TICKET_CHANGED, info.type)
        })
    }

    update(dt: number) {
        for (let info of this._info.values()) {
            info.update(dt)
        }
    }

}


export class PvpInfo {
    _ticket: number = 0
    _duration: number = 0
    _type: proto.PvpType = null

    _score: number = 0
    _rank: number = 0
    _battleRole: BattleRole[] = []

    _lastReqRankTime: number = 0
    _lastReqRivalTime: number = 0
    _rankList: proto.IPvpSimplePlayerData[] = []
    _rival: proto.IPvpSimplePlayerData[] = []
    _recordList: proto.IPvpBattleRecordData[] = []

    get ticket() { return this._ticket }
    get ticketMax() { return cfgHelper.getMiscData("pvp")[this._type].ticketMax }
    get duration() { return this._duration }
    get type() { return this._type }
    get score() { return this._score }
    get rank() { return this._rank }
    get battleRole() { return this._battleRole?.filter(r => !!r) }
    get needSetFormation() { return !this.battleRole || this.battleRole.length != 5 }

    public init(type: proto.PvpType, ticket: number, duration: number) {
        this._type = type
        this._ticket = ticket
        this._duration = duration
        return this
    }

    public setExtData(data: proto.IPvpNormalData) {
        if (!data) return
        this._score = data.score
        this._rank = data.rank
        this._battleRole = []
        data.battleRoles.forEach(r => this._battleRole.push(gameHelper.toBattleRole(r)))
    }

    public update(dt: number) {
        if (this._duration > 0) {
            this._duration -= dt * 1000
        }
    }

    public async updateFormation(uidAry: string[]) {
        this._battleRole.length = 5
        const idAry = []
        const tmp = []
        tmp.length = 5
        let sameCnt = 0
        for (let i = 0; i < uidAry.length; i++) {
            const uid = uidAry[i]
            const r = gameHelper.passenger.getPassenger(Number(uid))
            idAry.push(r.id)
            if (this._battleRole[i] && this._battleRole[i].uid == uid) {
                sameCnt++
            }
            tmp[i] = new BattleRole().initData({ uid, id: r.id, hp: r.getHp(), attack: r.getAttack(), skills: r.getSkills(), lv: r.getLevel(), starLv: r.getStarLv(), role: r })
        }
        if (sameCnt == uidAry.length) return true
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_UpdateFormationMessage, { type: this._type, idAry })
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        this._battleRole = tmp
        return true
    }

    // 获取排行榜
    @util.addLock
    public async getRank(force: boolean = false) {
        const now = gameHelper.now()
        if (!force && now - this._lastReqRankTime < 10000 && this._rankList && this._rankList.length) return this._rankList
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_GetRankListMessage, { type: this._type })
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        this._rankList = r.list.sort((a, b) => a.rank - b.rank)
        this._lastReqRankTime = now
        this._score = r.score
        this._rank = r.rank
        eventCenter.emit(EventType.PVP_NORMAL_MY_SCORE_CHANGED)
        eventCenter.emit(EventType.PVP_NORMAL_MY_RANK_CHANGED)
        return this._rankList
    }

    // 获取对手
    @util.addLock
    public async getRival(force: boolean = false) {
        const now = gameHelper.now()
        if (now - this._lastReqRivalTime < 10000 && !force) {
            if (this._rival?.length) return this._rival
        }
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_GetRivalMessage, { type: this._type, refresh: !!force })
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        this._rival = r.list
        this._lastReqRivalTime = now
        return this._rival
    }

    @util.addLock
    public async battle(index: number) {
        const rival = this._rival[index]
        if (!rival) return
        const monsters = rival.battleRoles.map(pbr => {
            const br = gameHelper.toBattleRole(pbr, false)
            br.uid = ""
            return br
        })

        const data = {
            monsters, rewards: null, noAgain: true,
            battleBg: resHelper.getBattleBg("blackhole"),
            levelType: BattleLevelType.PVP_NORMAL, levelId: '0',
        }
        let res = await viewHelper.showBattle(data, false)
        if (!res) return
        let result = res?.isWin ? 1 : 2
        if (result == 2) {
            if (res?.roles?.length == 0) {
                result = 3
            }
        }
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_PvpFightMessage, { type: this.type, rivalIndex: index, result })
        if (r.code != 0) return void viewHelper.showNetError(r.code)

        // 扣除挑战次数
        this._ticket -= 1
        eventCenter.emit(EventType.PVP_NORMAL_BATTLE_RESULT)
        // 清空对手
        this._rival.length = 0

        const pvpData = {
            score: r.score - this.score,
            rank: r.rank - this.rank
        }
        let updateRankList = false
        if (pvpData.score != this.score) {
            this._score = r.score
            eventCenter.emit(EventType.PVP_NORMAL_MY_SCORE_CHANGED)
            updateRankList = true
        }
        if (pvpData.rank != this.rank) {
            this._rank = r.rank
            eventCenter.emit(EventType.PVP_NORMAL_MY_RANK_CHANGED)
            updateRankList = true
        }
        if (updateRankList) {
            eventCenter.emit(EventType.PVP_NORMAL_UPDATE_RANK_LIST)
        }

        viewHelper.showPnl('battle/BattleResult', Object.assign(data, { isWin: res.isWin, pvpData }))
        if (res?.isWin) {
            return true
        }
        return false
    }

    @util.addLock
    public async getBattleRecordList(force: boolean = false) {
        if (!force && this._recordList?.length) return this._recordList
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_PvpBattleRecordListMessage, { type: this.type })
        if (r.code != 0) return void viewHelper.showNetError(r.code)

        this._recordList = r.list
        return this._recordList
    }

    @util.addLock
    public async replayBattle(record: proto.IPvpBattleRecordData) {
        if (!record) return
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_PvpBattleReplayMessage, { docId: record.docId })
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        if (!r.attacker || !r.defender) return

        const monsters = r.defender.map(pbr => {
            const br = gameHelper.toBattleRole(pbr, false)
            br.uid = ""
            return br
        })
        const battleRoles = r.attacker.map(pbr => {
            const br = gameHelper.toBattleRole(pbr, false)
            br.uid = ""
            return br
        })
        if (!battleRoles.length || !monsters.length) return

        const data = {
            monsters, battleRoles, rewards: null, noAgain: true,
            battleBg: resHelper.getBattleBg("blackhole"),
            levelType: BattleLevelType.PVP_NORMAL, levelId: '0',
        }
        let res = await viewHelper.showBattle(data, true)

    }
}