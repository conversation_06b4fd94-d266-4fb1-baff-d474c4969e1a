const { ccclass, property } = cc._decorator;

@ccclass
export default class MountPointCmpt extends cc.Component {

    @property([cc.Node])
    public points: cc.Node[] = []

    getPoint(name: string | number) {
        let prefix = "ATTACHED_NODE:"
        return this.points.find((p) => {
            if (p.name.startsWith(prefix)) {
                return p.name == prefix + name
            }
            return p.name == name
        })
    }

    hideAllPoint() {
        this.points.forEach(p => p.active = false)
    }

}