import { AchievementCfg, SpaceStoneCfg, TrainCfg, TrainWorkCfg } from "../../../common/constant/DataType";
import { CarriageID, CarriageType, DailyTaskState, ItemID, ItemType, MarkNewType, UIFunctionType, WantedState } from "../../../common/constant/Enums";
import { cfgHelper } from "../../../common/helper/CfgHelper";
import { gameHelper } from "../../../common/helper/GameHelper";
import { unlockHelper } from "../../../common/helper/UnlockHelper";
import PropObj from "../../../model/bag/PropObj";
import CarriageModel from "../../../model/train/common/CarriageModel";
import PassengerModel from "../../../model/passenger/PassengerModel";
import { CharacterFrag } from "../../../model/passenger/PassengerMgr";
import { DailyTask } from "../../../model/daily_task/DailyTaskModel";

const { ccclass, property } = cc._decorator;

//注意不要改变已有的值
enum RedType {
    Null,
    TrainConsole,//车厢可建造(1级入口)
    Role,//角色(1级入口)
    RoleItem1,//角色(2级入口)是新获得
    RoleItem2,//角色(2级入口)可突破
    Entrust, //委托任务是否可领奖
    SpeedUp, //如果加速能量为已经耗尽，且玩家还有免费补充的次数
    ExploreEnergy, //如果加速能量为已经耗尽，且玩家还有免费补充的次数
    Mail, //邮箱
    Achievement,//成就
    Bag,//背包(1级入口)
    BagItem1,//背包(3级入口)是新获得
    BagItem2,//背包(3级入口)红点
    BuildTrainItem,//建设设施(1级入口)
    BuildTrainItemTheme,//建设设施之解锁新主题皮肤
    RoleItem3,//是否阅读过角色档案
    ToolMake,//是否有剩余星锤
    TimeMail,//背包信件
    JackPot,//抽奖
    PlanetMove,//列车可以前往下一个星球
    PlanetArrive,//列车到达下一个星球
    Planet,//主界面进入星球按钮
    BagTabMaterials,//背包标签页材料
    BagTabTimeMail,//背包标签页信件
    EngineWork,//工作安排-动力室
    TrainWorkItem,//工作安排-单个工牌
    WaterWork,//工作安排-造水间
    Chest, //宝箱
    ChestUse, //有没有多余宝箱可用
    BagItemUse,//(背包4级入口)交互按钮红点
    StarMap,//玩法入口
    Wanted,//悬赏
    DormCheckIn,//寝室入住
    NEW_AREA, //星球新区域
    ROLE_EQUIP, //乘客装备
    PROFILE, // 贴纸(背包)
    BagTabFragment,//背包碎片页材料
    PLANET_PROFILE, // 星球贴纸
    PASSENGER_PROFILE, // 乘客贴纸
    TRAIN_DAILY_TASK, // 车厢每日任务(原打工)
    PASSENGER_DAILY_TASK, // 乘客每日任务
    TASK_ENTRANCE, // 任务入口
    PASSENGER_DAILY_TASK_CAN_FINISH, // 乘客每日任务可完成
    ACHIEVEMENT_CAN_FINISH, // 成就可领奖
}

@ccclass('RedObject')
class RedObject {
    @property({ type: cc.Enum(RedType) })
    type: RedType = RedType.Null
    @property(cc.Node)
    node: cc.Node = null
}

let RedFunType = {
    [RedType.TrainConsole]: function (): boolean {
        let ary = assetsMgr.getJson<TrainCfg>('Train').datas
        for (const cfg of ary) {
            if (cfg.isOpen && !gameHelper.train.getCarriageById(cfg.id) && gameHelper.train.checkCarriageUnlock(cfg) && gameHelper.checkConditions(gameHelper.toConditions(cfg.buyCost))) {
                return true
            }
        }
        return false
    },
    [RedType.Role]: function (): boolean {
        let ary = gameHelper.passenger.getPassengers()
        for (const model of ary) {
            if (RedFunType[RedType.RoleItem1](model)) return true
            if (RedFunType[RedType.RoleItem2](model)) return true
        }
        return false
    },
    [RedType.RoleItem1]: function (model: PassengerModel): boolean {
        if (!model) return false
        return gameHelper.new.isNew(MarkNewType.ROLE_NEW, [model.id])
    },
    [RedType.RoleItem2]: function (model: PassengerModel): boolean {
        if (!model) return false
        let bol = unlockHelper.isGuideOverByUnlockFunc(UIFunctionType.CHARACTER_DEVELOP)
        if (!bol) return false
        let needFrag = cfgHelper.getStarLvCfg(model.getStarLv()).upCost
        //todo 这里需要取对应品质的碎片
        let fragId = cfgHelper.getFragByCharacterIdAndQuality(String(model.getID()), model.quality).id
        let hasFrag = gameHelper.passenger.getFragCountById(+fragId)
        return !!needFrag && hasFrag >= needFrag
    },
    [RedType.RoleItem3]: function (model: PassengerModel): boolean {
        if (!model) return false
        return gameHelper.new.isNew(MarkNewType.ROLE_UNREAD, [model.id])
    },
    [RedType.SpeedUp]: function (): boolean {
        return gameHelper.world.energy.getEnergy() <= 0 && gameHelper.world.energy.freeRecoverNum > 0
    },
    [RedType.ExploreEnergy]: function (): boolean {
        return false
    },
    [RedType.Mail]: function (): boolean {
        return gameHelper.mail.checkRedDot()
    },
    [RedType.Achievement]: function (): boolean {
        return gameHelper.achieve.checkRedDot()
    },
    [RedType.Bag]: function (): boolean {
        return RedFunType[RedType.BagTabMaterials]() || RedFunType[RedType.BagTabTimeMail]() || RedFunType[RedType.BagTabFragment]()
    },
    [RedType.BagTabMaterials]: function (): boolean {
        let ary = gameHelper.bag.getMaterials()
        for (const obj of ary) {
            if (!obj.isShow) continue
            if (RedFunType[RedType.BagItem1](obj)) return true
            if (RedFunType[RedType.BagItem2](obj)) return true
        }
        return false
    },
    [RedType.BagTabTimeMail]: function (): boolean {
        let ary = gameHelper.bag.getPropsByType(ItemType.TIME_MAIL)
        for (const obj of ary) {
            if (!obj.isShow) continue
            if (RedFunType[RedType.TimeMail](obj)) return true
        }
        return false
    },
    [RedType.BagItem1]: function (obj: PropObj | CharacterFrag): boolean {
        if (!obj) return false
        switch (true) {
            case obj instanceof PropObj:
                return obj.isKey && obj.isNew(MarkNewType.PROP)
            case obj instanceof CharacterFrag:
                return obj.isNew(MarkNewType.PROP)
        }
        return false
    },
    [RedType.BagItem2]: function (obj: PropObj | CharacterFrag): boolean {
        if (!obj) return false
        return RedFunType[RedType.BagItemUse](obj)
    },
    [RedType.BagItemUse]: function (obj: PropObj | CharacterFrag): boolean {
        if (!obj) return false

        if (obj instanceof PropObj) {
            if (obj.isKey) {
                return obj.isNew(MarkNewType.PROP_USE)
            }
            if (obj.canUse()) {
                switch (obj.id) {
                    case ItemID.InviteCard1:
                    case ItemID.InviteCard2:
                    case ItemID.InviteCard3:
                    case ItemID.TICKET:
                        return true
                    case ItemID.TICKET_FRAG:
                        return obj.count >= cfgHelper.getMiscData("ticket").mergeCnt
                    case ItemID.SPACE_STONE_FRAG:
                        let cfgNext = assetsMgr.getJsonData<SpaceStoneCfg>("SpaceStone", gameHelper.spaceStone.getLv() + 1)
                        if (cfgNext) {
                            return gameHelper.checkConditions(cfgNext.buyCost)
                        }
                }
                if (obj.hasTime() && obj.getSurplusTime() <= 0) {
                    return true
                }
            }
            return false
        }
        if (obj instanceof CharacterFrag) {
            let min = cfgHelper.getMiscData('fragUp')[obj.quality - 1]
            return obj.getNum() >= min
        }
        return false
    },
    [RedType.TimeMail]: function (obj: PropObj): boolean {
        return obj.isTimeMail() && obj.isNew(MarkNewType.PROP_USE)
    },
    [RedType.BuildTrainItem]: function (id: CarriageID): boolean {
        if (gameHelper.new.isNew(MarkNewType.BUILD_CAN_LVUP, [id])) return true
        if (RedFunType[RedType.BuildTrainItemTheme](id)) return true
        return false
    },
    [RedType.BuildTrainItemTheme]: function (id: CarriageID): boolean {
        return gameHelper.new.isNew(MarkNewType.BUILD_UNLOCK_SKIN, [id])
    },
    [RedType.ToolMake]: function (): boolean {
        return gameHelper.tool.checkTableRedDot()
    },
    [RedType.JackPot]: function (): boolean {
        return gameHelper.getTicket() > 0
    },
    [RedType.PlanetMove]: function (): boolean {
        return !!gameHelper.planet.checkRedDot1()
    },
    [RedType.PlanetArrive]: function (): boolean {
        return gameHelper.planet.checkRedDot2()
    },
    [RedType.Planet]: function (): boolean {
        return !gameHelper.planet.isMoving() && !gameHelper.planet.getCurPlanet().isLanded()
    },
    [RedType.EngineWork]: function (): boolean {
        return RedFunType.TrainWorkList(CarriageID.ENGINE)
    },
    [RedType.WaterWork]: function (): boolean {
        return RedFunType.TrainWorkList(CarriageID.WATER)
    },
    [RedType.DormCheckIn]: function (id: CarriageID): boolean {
        return RedFunType.TrainWorkList(id)
    },
    TrainWorkList: function (id: CarriageID): boolean {
        let model = gameHelper.train.getCarriageById(id)
        if (!model) return false
        let maxCnt = 0
        if (model.getType() == CarriageType.DORM) { maxCnt = model.getMaxCheckInCnt() }
        else { maxCnt = model.getMaxWorkCnt() }
        let ary = new Array(maxCnt).fill(1).map((v, i) => ++i)
        for (const index of ary) {
            if (RedFunType[RedType.TrainWorkItem]({ index, model })) return true
        }
        return false
    },
    [RedType.TrainWorkItem]: function (data: { index: number, model: CarriageModel }): boolean {
        let { index, model } = data
        if (!model) return false
        if (!model.isUnlockWork(index)) return false
        if (model.getWorker(index)) return false
        return model.checkHaveWorker()
    },
    [RedType.Chest]: function (): boolean {
        let res: boolean = true
        gameHelper.chest.getChests().forEach(chest => {
            res ||= RedFunType[RedType.ChestUse](chest.id)
        })
        return res
    },
    [RedType.ChestUse]: function (id: number): boolean {
        return gameHelper.chest.chestUseRedDot(id)
    },
    [RedType.StarMap]: function (): boolean {
        return !!gameHelper.planet.checkRedDot1()
    },
    [RedType.Wanted]: function (): boolean {
        return gameHelper.wanted.getWanteds().find(m => m.canReward()) != null
    },
    [RedType.NEW_AREA]: function (): boolean {
        let planet = gameHelper.planet.getCurPlanet()
        let area = planet?.getCurArea()
        if (!area) return false
        if (area.getProgress() > 0) return false
        return !area.getMaps()[0].showLandAnim
    },
    [RedType.ROLE_EQUIP]: function (data: { roleId: number, index: number }): boolean {
        if (!data) return false
        let { roleId, index } = data
        let role = gameHelper.passenger.getPassenger(roleId)
        if (!gameHelper.resonance.isCanOperate(role)) return false
        let curEquip = role.getEquip(index)
        if (!curEquip) {
            let equips = gameHelper.equip.getEquipsByRole(roleId, index)
            return equips.length > 0
        }
    },
    [RedType.PROFILE]: function (): boolean {
        return gameHelper.bag.getPropCountById(ItemID.PLANET_PROFILE_CONTAINER) > 0 || gameHelper.bag.getPropCountById(ItemID.PASSENGER_PROFILE_CONTAINER) > 0
    },
    [RedType.BagTabFragment]: function (): boolean {
        let ary = gameHelper.passenger.getFrags()
        for (const obj of ary) {
            if (obj.getNum() <= 0) continue
            if (RedFunType[RedType.BagItem1](obj)) return true
            if (RedFunType[RedType.BagItem2](obj)) return true
        }
        return false
    },
    [RedType.PLANET_PROFILE]: function (planetId: number): boolean {
        if (planetId == null) {
            return gameHelper.bag.getPropCountById(ItemID.PLANET_PROFILE_CONTAINER) > 0
        }
        const planet = gameHelper.planet.getPlanet(planetId)
        if (!planet || !planet.isDone()) return false
        let is = !!gameHelper.planetArchives.getList().filter(pro => pro.planetId == planetId).length
        if (!is) {
            is = planet.hasUnGetProfileCollectReward()
        }
        return is
    },
    [RedType.PASSENGER_PROFILE]: function (passengerId: number): boolean {
        if (passengerId == null) {
            return gameHelper.bag.getPropCountById(ItemID.PASSENGER_PROFILE_CONTAINER) > 0
        }
        if (!gameHelper.passenger.getPassenger(passengerId)) return false

        return !!gameHelper.archives.getList().filter(pro => pro.characterId == passengerId).length
    },
    [RedType.TASK_ENTRANCE]: function (): boolean {
        return RedFunType[RedType.TRAIN_DAILY_TASK]() || RedFunType[RedType.PASSENGER_DAILY_TASK]() || RedFunType[RedType.Achievement]()
    },
    [RedType.TRAIN_DAILY_TASK]: function (): boolean {
        const ary = gameHelper.wanted.getWanteds()
        let hasEmptyWanted = false
        for (const obj of ary) {
            if (!obj) continue
            if (obj.canReward()) return true
            if (obj.getState() == WantedState.DEFAULT) hasEmptyWanted = true
        }

        if (hasEmptyWanted && !gameHelper.wanted.pnlOpenRed) return true
        return false
    },
    [RedType.PASSENGER_DAILY_TASK]: function (): boolean {
        const ary = gameHelper.dailyTask.getTasks()
        if (ary.length <= 0) return false
        let hasEmptyTask = false
        for (const obj of ary) {
            if (!obj) continue
            if (obj.state == DailyTaskState.TAKE) {
                if (obj.canFinish()) return true
                hasEmptyTask = true
            }
        }
        if (!gameHelper.dailyTask.bigGet) {
            const count = ary.reduce((pre, cur) => {
                if (cur.state == DailyTaskState.FINISH) {
                    return pre + 1
                }
                return pre
            }, 0)
            if (count == ary.length) return true
        }
        if (hasEmptyTask && !gameHelper.dailyTask.pnlOpenRed) return true
        return false
    },
    [RedType.PASSENGER_DAILY_TASK_CAN_FINISH]: function (data: { task: DailyTask, big: boolean }): boolean {
        if (data.task) return data.task.canFinish()
        if (data.big) return gameHelper.dailyTask.canGetBit()
        return false
    },
    [RedType.ACHIEVEMENT_CAN_FINISH]: function (cfg: AchievementCfg): boolean {
        let dic = gameHelper.achieve.getAchieveCurMax(cfg)
        return dic && dic.cur >= dic.max
    }
}

export {
    RedObject,
    RedFunType,
}
