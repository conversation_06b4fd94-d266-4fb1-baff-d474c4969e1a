import BaseCmptCtrl from "../../../../core/base/BaseCmptCtrl";
import { ConditionType, ItemID, UIFunctionType } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import NodeType from "../../../common/event/NodeType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { unlockHelper } from "../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";

const { ccclass, property } = cc._decorator;

const propMap = {
    [ConditionType.WATER]: ItemID.WATER,
    [ConditionType.ELECTRIC]: ItemID.ELECTRIC,
    [ConditionType.VITALITY]: ItemID.VITALITY,
    [ConditionType.TECH]: ItemID.TECH,
    [ConditionType.PUBLICITY]: ItemID.PUBLICITY,
}
const listenerEventMap = {
    [ConditionType.HEART]: EventType.UPDATE_HEART,
    [ConditionType.DIAMOND]: EventType.UPDATE_DIAMOND,
    [ConditionType.STAR_DUST]: EventType.UPDATE_STARDUST,
    [ConditionType.BLACK_HOLE_CURRENCY]: EventType.UPDATE_CURRENCY,
}
const listenerNodeMap = {
    [ConditionType.STAR_DUST]: NodeType.GUIDE_UI_CURRENCY_STAR,
}

enum Currency {
    WATER = ConditionType.WATER,
    HEART = ConditionType.HEART,
    DIAMOND = ConditionType.DIAMOND,
    ELECTRIC = ConditionType.ELECTRIC,
    STAR_DUST = ConditionType.STAR_DUST,
    BLACK_HOLE_CURRENCY = ConditionType.BLACK_HOLE_CURRENCY,
    VITALITY = ConditionType.VITALITY,
    ARREST_CURRENCY = ConditionType.ARREST_CURRENCY,
    PROFILE_BRANCH_ENERGY = ConditionType.PROFILE_BRANCH_ENERGY,
    TECH = ConditionType.TECH,
    PUBLICITY = ConditionType.PUBLICITY,
}

@ccclass
export default class CurrencyUICmpt extends BaseCmptCtrl {

    @property({ type: cc.Enum(Currency) })
    public type: ConditionType = ConditionType.STAR_DUST

    private lb: cc.Label = null

    // private deleteTipsLb: cc.Label = null

    private preVal: number = 0;

    private preDrt: number = 0;

    @property(cc.Node)
    public icon: cc.Node = null

    private ref: number = 0

    public listenEventMaps() {
        let ary = []
        let name = listenerEventMap[this.type]
        if (name) ary.push({ [name]: this.onUpdate })
        let node = listenerNodeMap[this.type]
        if (node) ary.push({ [node]: () => this.icon })
        if (propMap[this.type]) ary.push({ [EventType.CHANGE_NUM_PROP]: this.onUpdateProp })
        ary.push({ [EventType.UPDATE_ONE_CURRENCY]: this.updateOne })
        return ary
    }

    public onCreate(): void {
        this.lb = this.Child("currency_bg/lb", cc.Label)
        // this.deleteTipsLb = this.Child("deleteTips/lb", cc.Label)
    }

    public onEnter(): void {
        this.preVal = this.getCurVal()
        this.lb.Component(cc.LabelRollNumber).set(this.preVal)
        this.checkUnlock()
    }

    private getCurVal() {
        let propId = propMap[this.type]
        if (propId) {
            return gameHelper.bag.getPropCountById(propId)
        } else if (this.type == ConditionType.BLACK_HOLE_CURRENCY) {
            return gameHelper.blackHole.getCurrency()
        } else if (this.type == ConditionType.ARREST_CURRENCY) {
            return gameHelper.arrest.getCurrency()
        } else if (this.type == ConditionType.PROFILE_BRANCH_ENERGY) {
            return gameHelper.profileBranch.getEnergy()
        }
        return gameHelper.getClientCurrency(this.type)
    }

    private checkUnlock() {
        let cfg = {
            [ConditionType.STAR_DUST]: UIFunctionType.CURRENCY_1,
            [ConditionType.HEART]: UIFunctionType.CURRENCY_2,
            [ConditionType.DIAMOND]: UIFunctionType.CURRENCY_3,
        }
        let fun = cfg[this.type]
        if (!fun) return
        unlockHelper.setNodeUnlock(this.node, fun)
    }

    private onUpdate(add: number, playAnim: boolean = false, isMoneyBack: boolean = false) {
        let nowVal = this.getCurVal()
        if (add) this.preDrt = add
        if (nowVal == this.preVal) this.preVal = nowVal - this.preDrt
        if (isMoneyBack) this.lb.Component(cc.LabelRollNumber).set(this.preVal)
        if (playAnim) {
            this.playIconAnim(this.icon)
        }
        this.lb.Component(cc.LabelRollNumber).to(nowVal)
        this.preDrt = nowVal - this.preVal
        this.preVal = nowVal
    }

    public playIconAnim(node: cc.Node = this.icon) {
        let sk = node.Component(sp.Skeleton)
        if (sk) {
            sk.playAnimation(["animation", "animation2", "animation3"].random())
        } else {
            cc.tween(node).to(0.15, { scale: 1.3 }).delay(0.04).to(0.1, { scale: 0.85 }).to(0.05, { scale: 1 }).start()
        }
    }

    private updateOne(type: ConditionType, add: number) {
        if (this.type == type) {
            this.onUpdate(add)
        }
    }

    private onUpdateProp(id: number, add: number) {
        if (propMap[this.type] == id) {
            this.onUpdate(add)
        }
    }

    public show(show: boolean) {
        if (show) {
            this.ref++
        }
        else {
            this.ref--
        }
        this.node.active = this.ref > 0
    }

}
