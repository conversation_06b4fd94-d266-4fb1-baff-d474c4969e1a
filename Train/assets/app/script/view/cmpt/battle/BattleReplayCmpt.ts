import { localConfig } from "../../../common/LocalConfig";
import { MAX_VALUE } from "../../../common/constant/Constant";
import { BattleSkillViewCfg, PlanetMonsterCfg } from "../../../common/constant/DataType";
import { ConditionType, HeroAnimation, ItemType, LongPress, PassengerAnimation, PassengerAnimationType, PassengerBattleAnimation, PassengerLifeAnimation, ROLE_ATTR_ID, SkillType } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import NodeType from "../../../common/event/NodeType";
import NotEvent from "../../../common/event/NotEvent";
import { animHelper } from "../../../common/helper/AnimHelper";
import { cfgHelper } from "../../../common/helper/CfgHelper";
import { gameHelper } from "../../../common/helper/GameHelper";
import { resHelper } from "../../../common/helper/ResHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import Battle, { BattleAction, BattleDebug, BattleLog } from "../../../model/battle/Battle";
import { BattleDeathType, BattleEffectValueType, BattleHitType, BattleLogType, BattleRoleType, BattleSkillCampType, BattleSkillEffectType, BattleSkillObjectType, BattleSummonID } from "../../../model/battle/BattleEnum";
import BattleRole from "../../../model/battle/BattleRole";
import BattleSkill from "../../../model/battle/BattleSkill";
import Monster from "../../../model/battle/Monster";
import ConditionObj from "../../../model/common/ConditionObj";
import { ActionNode } from "../../../model/passenger/ActionTree";
import PassengerModel from "../../../model/passenger/PassengerModel";
import MountPointCmpt from "../common/MountPointCmpt";
import TimeScaleCmpt from "../common/TimeScaleCmpt";
import PlanetNodeRewardCmpt from "../planet/PlanetNodeRewardCmpt";
import { LOAD_RES_CFG, SP_SKILL, SP_ANIM, EQUIP_SKILL } from "./BattleCfg";

const STD_ROLE_BOX = cc.rect(-216 * 0.5, -15, 216, 360)
const STD_SKILL_SIZE = cc.size(89, 94)
const HIT_EFFECT_TIME = 0.06

const { ccclass, property } = cc._decorator;
@ccclass
export default class BattleReplayCmpt extends mc.BaseCmptCtrl {

    public listenEventMaps() {
        return [
            { [EventType.BATTLE_PAUSE_NEXT]: () => this.setPause(true) },
            { [EventType.BATTLE_RESUME_NEXT]: () => this.setPause(false) },
            { [NotEvent.ON_HIDE_BUBBLE]: () => this.resume() },

            { [NodeType.GUIDE_BATTLE_GUIDER]: () => this.Child("guider") }
        ]
    }

    private actions: BattleLog[] = []
    private step: number = 0

    private passengers: cc.Node[] = []
    private monsters: cc.Node[] = []

    private buffs: cc.Node[] = []

    private roleTemplate: cc.Node = null
    private skillTemplate: cc.Node = null
    private damgeFloatTemplate: cc.Node = null

    private skillPool: cc.NodePool = new cc.NodePool()
    private skillRoot: cc.Node = null
    private sendSkillRoot: cc.Node = null

    public roleRoot: cc.Node = null
    public floatRoot: cc.Node = null

    public get isAuto() {
        return gameHelper.battle.isAuto
    }

    private nextStepCallback: Function = null
    private nextActionCallback: Function = null
    private pause: boolean = false

    private isEnterArea: boolean = false

    private debug: BattleDebug = null

    private roleViewMap = {}
    private skillMap = {}

    public get tipsRoot() { return this.node.Child('tips') }

    public get speed() {
        return gameHelper.battle.speed
    }

    private get effectRoot() { return this.node.Child('effect') }

    private roles: BattleRole[] = [] //记录所有的乘客，不删除
    private posMap: { [key: string]: { [key: string]: BattleRole } } = {} //记录当前站位

    private result: any = {}
    private skipCb = null
    private _isClear = false

    public round: number = 0

    private rewardPosAry: cc.Vec2[] = []

    public async init(actions: BattleLog[], passengers: BattleRole[], monsters: BattleRole[], buffs: BattleRole[] = []) {
        this.actions = actions

        let templateNode = this.Child('template')
        this.roleTemplate = templateNode.Child('BattleRole')
        this.skillTemplate = templateNode.Child('Skill')
        this.damgeFloatTemplate = templateNode.Child('DamageFloat')

        this.skillRoot = this.node.Child("skills")
        this.roleRoot = this.node.Child("roles")
        this.floatRoot = this.node.Child('floats')
        this.sendSkillRoot = this.node.Child("sendSkills")

        this.nextStepCallback = null

        this.debug = new BattleDebug()
        this.round = 0

        passengers.forEach(p => p.type = BattleRoleType.PASSENGER)
        monsters.forEach(p => p.type = BattleRoleType.MONSTER)

        this.buffs = buffs.map(buff => {
            let buffNode
            if (buff.id == ROLE_ATTR_ID.TRAIN) {
                buffNode = cc.instantiate(templateNode.Child('train'))
                if (buff.type == BattleRoleType.MONSTER) {
                    buffNode.x = -buffNode.x
                    let body = buffNode.Child("body")
                    body.scaleX = -body.scaleX
                    let ui = buffNode.Child("ui")
                    ui.x = -ui.x
                }
                buffNode.Data = buff
                this.changeHp(buffNode)
                this.changeAttack(buffNode)
            }
            else {
                buffNode = new cc.Node("buff")
            }
            buffNode.parent = this.roleRoot
            buffNode.zIndex = 0
            buffNode.Data = buff
            return buffNode
        })

        this.roles = passengers.concat(monsters, buffs)

        await this.initView(passengers, monsters)
        return this.startBattle()
    }

    onClean() {
        for (let key in this.skillMap) {
            let node = this.skillMap[key]
            if (node instanceof cc.Node && cc.isValid(node)) {
                node.destroy()
            }
        }
    }

    public setResult(result) {
        this.result = result
    }

    public async skip() {
        if (!this.result || !this.skipCb) return
        this.tipsRoot.active = false
        this.clear()
        let passengers = this.result.isWin ? this.result.roles : []
        let monsters = !this.result.isWin ? this.result.roles : []
        this.result = null
        await this.initView(passengers, monsters)
        this.skipCb()
        this.skipCb = null
    }

    public canSkip() {
        return !!this.skipCb
    }

    public setAuto(auto: boolean) {
        if (auto) {
            this.nextStep()
        }
    }

    private async startBattle() {
        // await this.enterArea()
        // await this.checkNextStep()
        await ut.wait(0.5, this) //进场
        let skipPromise = new Promise((r) => { this.skipCb = r })
        await Promise.race([skipPromise, this.handleStep()])
        // await this.handleStep()
        await ut.wait(0.3, this) //战斗结束
        await this.onEnd()
    }

    private async onEnd() {
        this.nextStepCallback = null
        let update = (node: cc.Node) => {
            let ui = node.Child("ui")
            if (ui) {
                ui.active = false
            }
        }
        this.roleRoot.children.forEach(update)
        // await ut.wait(0.3, this) //隐藏ui
    }

    private async enterArea() {
        let dis = 600
        let time = 2
        let tweens = []
        for (let role of this.passengers) {
            role.x -= dis
            let tween = cc.tween(role).by(time, { x: dis })
            tweens.push(tween)
        }
        for (let role of this.monsters) {
            role.x += dis
            let tween = cc.tween(role).by(time, { x: -dis })
            tweens.push(tween)
        }
        this.isEnterArea = true
        await ut.promiseMap(tweens, (tween) => {
            return new Promise(r => {
                tween.call(r).start()
            })
        })
        this.isEnterArea = false
        let roles = this.passengers.concat(this.monsters)
        for (let role of roles) {
            this.resetAnim(role)
        }
    }

    private clear() {
        this._isClear = true
        this.step = 0
        this.floatRoot.removeAllChildren()
        this.roleRoot.removeAllChildren()
        this.skillRoot.removeAllChildren()
        this.sendSkillRoot.removeAllChildren()
        this.effectRoot.removeAllChildren()
        this.passengers = []
        this.monsters = []
        this.skillPool.clear()
        this.roleViewMap = {}
        this.posMap = {}
        this.actions = []
        this.unscheduleAllCallbacks()
        cc.Tween.stopAllByTarget(this)
    }

    private async initView(passengers: BattleRole[], monsters: BattleRole[]) {
        await this.preloadView(passengers, monsters)
        let root = this.node.Child("roles")

        let init = (role, i) => {
            let node = this.createRoleNode(role, 100 - i)
            let body = node.Child('body')
            let sk = body.Component(sp.Skeleton)

            sk.mix2(PassengerBattleAnimation.ATTACK_IDLE, PassengerBattleAnimation.ATTACK_READY)
            sk.mix2(PassengerBattleAnimation.ATTACK_IDLE + "2", PassengerBattleAnimation.ATTACK_READY)
            sk.mix2(PassengerBattleAnimation.ATTACK, PassengerBattleAnimation.ATTACK_DIAUP)
            this.resetAnim(node)
            return node
        }
        let initPos = (node, i) => {
            let role = node.Data
            node.setPosition(this.getPosByIndex(i, role.type, node))
            if (!this.posMap[role.type]) this.posMap[role.type] = {}
            this.posMap[role.type][i] = role
            return node
        }
        this.passengers = passengers.map(init)
        this.passengers.forEach(initPos)

        this.monsters = monsters.map(init)
        this.monsters.forEach(initPos)
    }

    private async preloadView(passengers, monsters) {
        let roleMap = {}
        let skillResMap = {}

        let preloadRole = async (id) => {
            id = Number(id)
            let type = gameHelper.getRoleType(id)
            let skeletonData
            if (type == BattleRoleType.SUMMON) {
                skeletonData = await resHelper.preloadSummonSk(id, this.getTag())
            }
            else {
                if (type == BattleRoleType.PASSENGER) {
                    skeletonData = await resHelper.preloadRoleSk(id, this.getTag())
                }
                else {
                    skeletonData = await resHelper.preloadMonsterSk(id, this.getTag())
                }
            }
            if (!cc.isValid(this)) return
            this.roleViewMap[id] = skeletonData
        }

        let roles = passengers.concat(monsters)
        let hasBoss = false
        for (let role of roles) {
            roleMap[role.id] = true
            if (this.getSkillScale({ Data: role }) > 1) {
                hasBoss = true
            }
        }

        let markSkill = (roleId, skillId, type) => {
            let id = skillId
            let cfg
            if (type == SkillType.BATTLE) {
                id = roleId
                cfg = assetsMgr.getJsonData<BattleSkillViewCfg>("BattleSkillControl", `${id}-${skillId}`)
            }
            else if (type == SkillType.BLACKHOLE_EQUIP) {
                cfg = assetsMgr.getJsonData<BattleSkillViewCfg>("BlackHoleEquip", skillId)
            }
            else if (type == SkillType.INSTANCE) {
                cfg = assetsMgr.getJsonData<BattleSkillViewCfg>("InstanceSkill", skillId)
            }
            else {
                return
            }
            let key = `${id}-${type}`

            if (cfg?.load) {
                skillResMap[key] = cfg.load
            }
        }

        let travlActions = (callback) => {
            let travl = (log) => {
                if (!log.next) return
                for (let _log of log.next) {
                    callback(_log)
                    travl(_log)
                }
            }
            return travl({ next: this.actions })
        }
        travlActions((action) => {
            if (action.type == BattleLogType.SUMMON) { //预加载召唤物
                let summons = action.data.summons
                for (let data of summons) {
                    roleMap[data.id] = true
                }
            }
            else if (action.type == BattleLogType.USE_SKILL) {
                let skillId = action.data.skillId
                let type = action.data.skillType
                let id = action.sender.split("_")[0]
                markSkill(id, skillId, type)
            }
        })

        for (let buff of this.buffs) {
            let role = buff.Data as BattleRole
            let skills = role.getSkills()
            for (let skill of skills) {
                if (skill.viewCfg?.load == 2) {
                    markSkill(role.id, skill.getId(), skill.getType())
                }
            }
        }

        let pList = []
        if (hasBoss) {
            let p = assetsMgr.loadTempRes("battle/effect/boss", cc.Prefab, this.getTag())
            p.then((pfb) => {
                if (!cc.isValid(this)) return
                cc.instantiate2(pfb, this.Child(`template/effect`))
            })
            pList.push(p)
        }
        pList.push(
            ut.promiseMap(Object.keys(roleMap), async (id) => {
                return preloadRole(id)
            }),
            ut.promiseMap(Object.keys(skillResMap), async (key) => {
                let [id, type] = key.split("-")
                let resName = skillResMap[key]
                if (typeof resName == 'string') {
                    id = resName
                }
                this.skillMap[key] = await this.loadSkill(id, type)
            })
        )
        await Promise.all(pList)
        if (!cc.isValid(this)) return

        for (let buff of this.buffs) {
            let role = buff.Data as BattleRole
            let skills = role.getSkills()
            for (let skill of skills) {
                if (skill.viewCfg?.load == 2) {
                    let node = this.createSkill(skill.getId(), skill.getType())
                    node.parent = buff
                }
            }
        }
    }

    private createRole(data) {
        let id = data.id
        if (id < BattleSummonID.BASE) {
            let isPassenger = gameHelper.checkPassengerById(id)
            if (isPassenger) {
                let role = new PassengerModel().init({ id, level: data.lv, starLv: data.starLv })
                data.skills = role.getSkills()
            }
            else {
                let role = new Monster().init(data.id, data.lv, data.starLv)
                data.skills = role.getSkills()
            }
        }
        return new BattleRole().initData(data)
    }

    private createRoleNode(role: BattleRole, zIndex = 0) {
        let root = this.node.Child("roles")
        let node = cc.instantiate2(this.roleTemplate, root)
        node.active = true
        node.Data = role
        let body = node.Child("body")
        let sk = body.Component(sp.Skeleton)
        body.active = true
        let icon = node.Child('icon')
        let id = role.id
        sk.skeletonData = this.roleViewMap[id]

        if (id < BattleSummonID.BASE) {
            let data = cfgHelper.getCharacter(id)
            if (data?.copy) {
                body.color = cc.Color.BLACK
                let debugNode = node.Child('debug')
                debugNode.active = true
                debugNode.Component(cc.Label).setLocaleKey(data.name)
            }

            let isPassenger = gameHelper.checkPassengerById(id)
            if (isPassenger) {
                if (role.type == BattleRoleType.PASSENGER) {
                    resHelper.initOwnRoleSkin(id, sk)
                }
                else {
                    resHelper.initRoleSkin(id, 1, sk)
                }
            }
            else {
                resHelper.initMonsterSk(id, sk)
            }
        }
        let buffs = node.Child("ui/buffs")
        if (buffs) {
            buffs.active = localConfig.debug
        }

        if (this.isEnterArea) {
            if (sk.findAnimation(PassengerLifeAnimation.WALK)) {
                sk.playAnimation(PassengerLifeAnimation.WALK, true)
            }
        }
        this.changeHp(node)
        this.changeAttack(node)

        if (role.type == BattleRoleType.MONSTER) {
            icon.scaleX = -icon.scaleX
            body.scaleX = -body.scaleX
            let attackNode = node.Child("ui/attack")
            attackNode.scaleX = -1
            attackNode.Child("count").scaleX = -1
        }
        node.zIndex = zIndex

        let touch = node.Child("ui/touch")
        touch.setContentSize(body.getContentSize())
        touch.on(LongPress.LPSTART, () => {
            if (!this.isDetailMode()) return
            this.stop()
            let info = { id: role.id, skills: role.getSkills() }
            viewHelper.showBubble("SkillBubble", touch, info)
        })

        return node
    }

    public stop() {
        this.Component(TimeScaleCmpt).setScale(0)
    }

    public resume() {
        this.Component(TimeScaleCmpt).setScale(1)
    }

    private async handleStep() {
        for (let action of this.actions) {
            if (action.type == BattleLogType.COLLIDE) {
                await this.updatePos()
                await this.handleCollide(action)
            }
            else if (action.type == BattleLogType.USE_SKILL) {
                await this.handleUseSkill(action)
            }
            else if (action.type == BattleLogType.USE_BUFF_SKILL) {
                await this.handleUseBuffSkill(action)
            }
            else if (action.type == BattleLogType.ROUND_START) {
                await this.handleRoundStart(action)
            }
            this.step++
            await this.checkNextAction()
        }
    }

    private async updatePos() {
        return await Promise.all([this.moveQueue(this.passengers), this.moveQueue(this.monsters)])
    }

    public getStep() {
        return this.step
    }

    public nextStep() {
        this.nextStepCallback && this.nextStepCallback()
        this.nextStepCallback = null
    }

    public canNextStep() {
        if (this.pause) return false
        return this.nextStepCallback != null
    }

    private async checkNextStep() {
        if (!this.isAuto) {
            await new Promise(r => this.nextStepCallback = r)
        }
    }

    private async checkNextAction() {
        if (this.pause) {
            await new Promise(r => this.nextActionCallback = r)
        }
    }

    private setPause(pause: boolean) {
        this.pause = pause
        if (!pause) {
            this.nextActionCallback && this.nextActionCallback()
            this.nextActionCallback = null
        }
    }

    private async waitPauseOver() {
        await ut.waitNextFrame(1, this)
        if (!this.pause) return
        await new Promise<void>((resolve) => {
            this.scheduleUpdate(() => {
                if (this.pause) return
                this.unscheduleAllCallbacks()
                resolve()
            })
        })
    }

    private isDetailMode() {
        return this.speed == 1
    }

    private async checkAndShowSkillTips(role: cc.Node, skills: BattleSkill[]) {
        if (!this.isDetailMode()) return
        await this.showSkillTips(role, skills)
    }

    private async handleUseSkill(action: BattleLog, onAttack?: boolean) {
        this.debug.parseLog(action)
        let mergeMap = this.mergeEffectActions(action)
        let pList = []
        let { sender, data } = action
        let role = this.getRole(sender)
        let skillId = data.skillId
        let skillType = data.skillType
        let skillHash = data.skillHash
        let skill: BattleSkill = role.Data.skills.find(s => s.getHash() == skillHash)
        role.Data.curSkill = skill

        let orgZIndex = role.zIndex
        if (role.Data.id != ROLE_ATTR_ID.TRAIN) {
            role.zIndex = 1000
        }

        let skillReadyEnd
        if (!onAttack) {
            let skills = [skill]
            if (skillType == SkillType.BATTLE) {
                skills.pushArr(role.Data.skills.filter(s => data.skills.has(s.getId()) && s.getType() == skillType))
            }
            await this.checkAndShowSkillTips(role, skills)
            await new Promise((onSkill) => {
                skillReadyEnd = this.playSkillReady(role, onSkill, mergeMap)
            })
        }

        let cfg = this.getSkillCfg(role)
        if (cfg?.show) {
            let func = this[cfg.show.func]
            await func.call(this, role, mergeMap, action)
        }
        else {
            for (let receiver in mergeMap) { //对n个人
                let actions = mergeMap[receiver]
                let senderRole = sender && this.getRole(sender)
                let cfg = senderRole && this.getSkillCfg(senderRole)
                pList.push(this.handleEffect({ sender, receiver, next: actions }, onAttack))
                let delay = cfg?.skill?.delay
                if (delay) {
                    await ut.wait(delay, this)
                }
            }
            await Promise.all(pList)
        }

        if (skillReadyEnd) {
            await skillReadyEnd
        }

        if (!onAttack) {
            this.checkDeathSkill(role)
        }

        // if (cfg?.end) {
        //     let func = this[`playSkillEnd_${id}`]
        //     await func.call(this, role)
        // }

        role.Data.isGainBySelf = false
        role.zIndex = orgZIndex

        role.Data.curSkill = null
    }

    private async handleEffect(data, onAttack?: boolean) {
        let { sender, receiver, next } = data
        let senderRole = sender && this.getRole(sender)
        let receiverRole = receiver && receiver != 'undefined' && this.getRole(receiver)

        let handleFuncs = this.getEffectHandles(next)

        let spCfg = senderRole && this.getSkillCfg(senderRole)
        let pList = []
        for (let  i = 0; i < handleFuncs.length; i++) {
            let { func, args } = handleFuncs[i]
            let [action] = args
            let p
            if (i == 0) {
                if (receiverRole && !onAttack) {
                    if (spCfg?.skill?.func) {
                        let skillCfg = spCfg.skill
                        await this[skillCfg.func](senderRole, receiverRole, action)
                    }
                }
                p = func.call(this, ...args)
            }
            else {
                p = func.call(this, ...args)
            }
            pList.push(p)
        }
        await Promise.all(pList)
    }

    private async handleUseBuffSkill(action: BattleLog, onAttack?: boolean) {
        this.debug.parseLog(action)
        let mergeMap = this.mergeEffectActions(action)
        let pList = []
        let { sender, data } = action
        let role = this.getRole(sender)
        let skillCfg = this.getSkillCfgByHash(data.id, data.skillHash)

        let orgZIndex = role?.zIndex
        let buffNode = role.Child("ui/buffs").children.find(n => n.Data && n.Data.skillId == data.skillId)
        if (cc.isValid(buffNode)) {
            buffNode.color = cc.Color.BLUE
        }
        role.zIndex = 1000

        let skillReadyEnd
        if (!onAttack) {
            await new Promise((onSkill) => {
                skillReadyEnd = this.playBuffSkillReady(role, skillCfg, onSkill, mergeMap)
            })
        }

        for (let receiver in mergeMap) { //对n个人
            let actions = mergeMap[receiver]
            pList.push(this.handleBuffSkillEffect({ sender, receiver, next: actions, skillCfg }, onAttack))
        }
        await Promise.all(pList)

        await skillReadyEnd

        this.checkDeathSkill(role)
        role.Data.isGainBySelf = false
        role.zIndex = orgZIndex

        role.Data.curSkill = null

        if (cc.isValid(buffNode)) {
            buffNode.color = cc.Color.WHITE
        }
    }

    private async handleBuffSkillEffect(data, onAttack?: boolean) {
        let { sender, receiver, next, skillCfg } = data
        let senderRole = sender && this.getRole(sender)
        let receiverRole = receiver && receiver != 'undefined' && this.getRole(receiver)

        let handleFuncs = this.getEffectHandles(next)

        let spCfg = skillCfg
        let pList = []
        for (let { func, args } of handleFuncs) {
            let p
            let [action] = args
            if (receiverRole && !onAttack) {
                if (spCfg?.buffSkill) {
                    let skillCfg = spCfg.buffSkill
                    p = this[skillCfg.func](receiverRole, spCfg, action)
                }
            }
            let go = async () => {
                if (p) {
                    await p
                }
                if (func) {
                    await func.call(this, ...args)
                }
            }
            pList.push(go())
        }
        await Promise.all(pList)

        receiverRole.Data.isGainBySelf = false
    }

    private async handleRoundStart(action) {
        this.round++
        eventCenter.emit(EventType.ON_ROUND_START, this.round)

        let mergeMap = this.mergeEffectActions(action)

        let go = async (actions) => {
            let handleFuncs = this.getEffectHandles(actions)

            let pList = []
            for (let { func, args } of handleFuncs) {
                let p
                let go = async () => {
                    if (p) {
                        await p
                    }
                    if (func) {
                        await func.call(this, ...args)
                    }
                }
                pList.push(go())
            }
            await Promise.all(pList)
        }

        let pList = []
        for (let receiver in mergeMap) { //对n个人
            let actions = mergeMap[receiver]
            pList.push(go(actions))
        }
        await Promise.all(pList)
    }

    private getEffectHandles(actions) {
        let handleFuncs = []
        if (this.isClear()) return handleFuncs
        for (let action of actions) { //一个技能里多种效果
            let type = action.type
            if (type == BattleLogType.HIT) {
                handleFuncs.push({ func: this.handleHit, args: [action] })
            }
            else if (type == BattleLogType.CHANGE_HP) {
                handleFuncs.push({ func: this.handleChangeHp, args: [action] })
            }
            else if (type == BattleLogType.CHANGE_ATTACK) {
                handleFuncs.push({ func: this.handleChangeAttack, args: [action] })
            }
            else if (type == BattleLogType.ADD_BUFF) {
                handleFuncs.push({ func: this.handleAddBuff, args: [action] })
            }
            else if (type == BattleLogType.REMOVE_BUFF) {
                handleFuncs.push({ func: this.handleRemoveBuff, args: [action, false] })
            }
            else if (type == BattleLogType.SUMMON) {
                handleFuncs.push({ func: this.handleSummon, args: [action] })
            }
            else if (type == BattleLogType.CHANGE_HP_ATTACK) {
                handleFuncs.push({ func: this.handleChangeHpAttack, args: [action] })
            }
            else if (type == BattleLogType.CHANGE_POSITION) {
                handleFuncs.push({ func: this.handleChangePos, args: [action] })
            }
            else if (type == BattleLogType.COPY) {
                handleFuncs.push({ func: this.handleCopy, args: [action] })
            }
            else if (type == BattleLogType.REBIRTH) {
                handleFuncs.push({ func: this.handleRebirth, args: [action] })
            }
            else if (type == BattleLogType.RUN_AWAY) {
                handleFuncs.push({func: this.handleRunAway, args: [action]})
            }
            else if (type == BattleLogType.USE_SKILL) {
                handleFuncs.push({func: this.handleUseSkill, args: [action]})
            }
        }
        return handleFuncs
    }

    private getEffectTemplate(name, node?: cc.Node) {
        if (node && this.getSkillScale(node) > 1) {
            name = `boss/${name}`
        }
        return this.Child(`template/effect/${name}`)
    }

    //------------------ attack hp ----------------------------------------
    private async handleChangeHp(action, showAnim: boolean = true) {
        this.debug.parseLog(action)
        let { data, receiver } = action
        let role = this.getRole(receiver)
        let { change, isBuff } = data
        if (isBuff) showAnim = false

        if (role.Data.isGainBySelf) {
            showAnim = false
        }

        if (data?.hitEffectFunc) {
            this.changeHp(role, change, false)
            await data.hitEffectFunc()
        }
        else {
            await this.changeHp(role, change, showAnim)
        }
    }

    private async handleChangeAttack(action, showAnim: boolean = true) {
        this.debug.parseLog(action)
        let { data, receiver } = action
        let role = this.getRole(receiver)
        let { change, isBuff } = data
        if (isBuff) showAnim = false
        if (role.Data.isGainBySelf) {
            showAnim = false
        }
        if (data?.hitEffectFunc) {
            this.changeAttack(role, change, false)
            await data.hitEffectFunc()
        }
        else {
            await this.changeAttack(role, change, showAnim)
        }
    }

    private async handleChangeHpAttack(action) {
        let { hp, attack, hitEffectFunc } = action.data
        let showAnim = true
        if (hitEffectFunc) {
            showAnim = false
        }
        await Promise.all([this.handleChangeHp(hp, showAnim), this.handleChangeAttack(attack, showAnim)])
        if (hitEffectFunc) {
            await hitEffectFunc()
        }
    }

    private async changeAttack(node: cc.Node, val = 0, showAnim: boolean = true) {
        let attack = node.Child("ui/attack")
        node.Data.attack += val
        let count = attack.Child('count')
        count.Component(cc.Label).string = String(Math.max(0, node.Data.attack))
        viewHelper.adapterAttrSize(count)
        if (val) {
            this.playAttrScale(count)
            if (val > 0 && showAnim) {
                await this.playGainEffect(node, "gain", this.playGainAnim.bind(this))
            }
        }
    }

    private async changeHp(node: cc.Node, val = 0, showAnim: boolean = true) {
        let hp = node.Child("ui/hp")
        let data = node.Data
        let count = hp.Child('count')
        let bar = hp.Child('bar', cc.Sprite)
        if (!data.maxHp) {
            data.maxHp = data.hp
        }
        if (data.maxHp >= MAX_VALUE) {
            count.Component(cc.Label).string = viewHelper.getHpStr(data.hp)
            viewHelper.adapterAttrSize(count)
            bar.fillRange = 1
            return
        }

        data.hp += val
        data.maxHp = Math.max(node.Data.maxHp, data.hp)
        let curHp = data.hp, maxHp = data.maxHp
        count.Component(cc.Label).string = String(Math.max(0, curHp))
        viewHelper.adapterAttrSize(count)
        let ratio = cc.misc.clamp01(curHp / maxHp)
        bar.fillRange = ratio
        if (val) {
            this.playAttrScale(count)
            if (val > 0 && showAnim) {
                await this.playGainEffect(node, "add_hp", this.playGainAnim.bind(this))
            }
        }
    }

    private playAttrScale(count) {
        let orgScaleX = ut.normalizeNumber(count.scaleX)
        let orgScaleY = Math.abs(orgScaleX)
        let scaleX = count.scaleX * 2
        let scaleY = Math.abs(scaleX)
        cc.Tween.stopAllByTarget(count)
        cc.tween(count).to(0.1, { scaleX, scaleY }).delay(0.5).to(0.1, { scaleX: orgScaleX, scaleY: orgScaleY }).start()
    }

    private async playGainAnim(node: cc.Node) {
        let sk = node.Child('body', sp.Skeleton)
        if (sk.animation == PassengerBattleAnimation.GAIN_BUFF || node.Data.isGainBySelf) return
        await sk.playAnimation(PassengerBattleAnimation.GAIN_BUFF)
        this.resetAnim(node)
    }

    //增益特效
    private async playGainEffect(node: cc.Node, name, cb) {
        let play = async (type, cb?) => {
            return this.playEffect(node, name, type, cb)
        }
        await Promise.all([play('front', cb), play('back')])
    }

    private async playEffect(node: cc.Node, name: string, type: string, cb?: Function) {
        let gainFunc = node.Data.gainFunc
        if (gainFunc) {
            return gainFunc(node, name, type, cb)
        }
        let parent = node.Child(`effect_${type}`)
        let effect = cc.instantiate2(this.getEffectTemplate(`${name}_${type}`, node), parent)
        let sk = effect.Component(sp.Skeleton)
        let p
        sk.setEventListener(({ animation }, { data }) => {
            if (data.name == "effect") {
                sk.setEventListener(null)
                if (cb) {
                    p = cb(node)
                }
            }
        })
        await sk.playAnimation("animation")
        effect.destroy()
        if (p) {
            await p
        }
    }

    protected async playGainCommon(role: cc.Node, skill: BattleSkill, action) {
        let backParent = role.Child("effect_back")
        let frontParent = role.Child("effect_front")
        let effect = this.createSkill(skill.getId(), skill.getType(), "impact")
        let scaleX = ut.normalizeNumber(role.Child("body").scaleX)
        let back = effect.Child("back")
        back.parent = backParent
        back.scaleX = scaleX
        let p1 = back.Component(sp.Skeleton).playAnimation("Skill_Back")
        p1.then(()=>{
            back.destroy()
        })

        let front = effect.Child("front")
        front.parent = frontParent
        front.scaleX = scaleX
        let p2 = front.Component(sp.Skeleton).playAnimation("Skill_Front")
        p2.then(()=>{
            front.destroy()
        })
        effect.destroy()

        await this.playGainAnim(role)
    }

    //-------------------------------------------------------------------

    //-------------------- 受伤 ---------------------------------

    private async handleHit(action) {
        let { data, receiver, next } = action
        this.debug.parseLog(action)
        let damage = data.damage
        let role = this.getRole(receiver)
        let playEffect = damage > 0 && data.hitType != BattleHitType.CHANGE_HP

        let onHitEffect: any = async () => {
            this.changeHp(role, -damage)

            if (playEffect) {
                if (data?.hitEffectFunc) {
                    data.hitEffectFunc()
                }
                else {
                    this.playHitEffect(role)
                }
            }

            let p1 = ut.promiseMap(next, async (action) => {
                let type = action.type
                if (type == BattleLogType.DEATH) return
                if (type == BattleLogType.REMOVE_BUFF) {
                    await this.handleRemoveBuff(action)
                }
                else if (type == BattleLogType.CHANGE_ATTACK) {
                    await this.handleChangeAttack(action)
                }
            })

            let p2 = ut.wait(0.1, this)
            p2.then(()=>{
                this.showFloat(role, damage)
            })

            await Promise.all([p1, p2])
        }

        let deathAction
        if (next) {
            deathAction = next.find(a => a.type == BattleLogType.DEATH)
        }

        let p
        if (deathAction) {
            p = this.handleDeath(deathAction)
        }
        else {
            let sk = role.Child('body', sp.Skeleton)
            if (sk) {
                let anim = PassengerBattleAnimation.HIT
                p = (async()=>{
                    await sk.playAnimation(anim)
                    if (sk.animation == anim) {
                        this.resetAnim(role)
                    }
                })()
            }
        }

        if (p) {
            let p2 = (async()=>{
                await ut.wait(HIT_EFFECT_TIME, this)
                this.playReward(role)
                await onHitEffect()
            })()
            await Promise.all([
                p, p2
            ])
        }

        await this.checkDelayRemoveBuff(role)
    }

    private async playHitAnim(role: cc.Node) {
        let sk = role.Child('body', sp.Skeleton)
        this.playHitEffect(role)
        await sk.playAnimation(PassengerBattleAnimation.HIT)
    }

    private async handleHitByCollide(info) {
        let action = info.hitAction
        let { data, receiver, next } = action
        this.debug.parseLog(action)
        let damage = data.damage
        let role = this.getRole(receiver)
        this.changeHp(role, -damage)

        let pList = []
        let p: any = this.showFloat(role, damage, true, info.isBig)
        pList.push(p)

        p = new Promise(r => r(null))
        if (next) {
            p = ut.promiseMap(next, async (action) => {
                let type = action.type
                if (type == BattleLogType.REMOVE_BUFF) {
                    await this.handleRemoveBuff(action)
                }
                else if (type == BattleLogType.CHANGE_ATTACK) {
                    await this.handleChangeAttack(action)
                }
            })
        }
        pList.push(p)

        this.playReward(role)

        return Promise.all(pList)
    }

    private async playHitEffect(role: cc.Node) {
        let template = this.getEffectTemplate("hit", role)
        let node = cc.instantiate2(template, this.effectRoot)

        if (role.Data.type == BattleRoleType.MONSTER) {
            node.scaleX = -1
        }
        let pos = ut.convertToNodeAR(role, node.parent)
        node.setPosition(pos)
        await node.Component(sp.Skeleton).playAnimation('shouji')
        node.destroy()
    }
    //-----------------------------------------------------


    //---------------------- 召唤 ---------------------------
    private async handleSummon(action) {
        this.debug.parseLog(action)
        let { data } = action
        let { summons, indexUid } = data
        let pList = []

        let senderRole = this.getRole(action.sender)
        let senderData = senderRole.Data

        let isDeathSkill = senderData.deathSkill
        if (isDeathSkill && !this.hasNextSkillAction(senderRole, action)) {
            this.removeRole(senderRole)
        }

        let nodes = []
        let moveMap = {} //只移动有改变的阵营
        for (let data of summons) {
            let model = this.createRole(data)
            let roles = this.getRolesByType(model.type)
            let index = roles.findIndex(r => r.Data?.uid == indexUid)
            let node = this.createRoleNode(model, 100 - (index + 1))
            node.active = false
            roles.splice(index + 1, 0, node)
            nodes.push(node)

            let orgRole = this.posMap[model.type][index + 1]
            if (!orgRole?.isDeath() || orgRole?.isCopy) {
                moveMap[model.type] = true
            }
            else {
                this.posMap[model.type][index + 1] = model
            }
        }

        for (let node of nodes) {
            let model = node.Data
            let roles = this.getRolesByType(model.type)
            let index = roles.findIndex(r => r.Data?.uid == model.uid)
            let pos = this.getPosByIndex(index, model.type)
            node.setPosition(pos)
        }

        ut.promiseMap(Object.keys(moveMap), async (type) => {
            await this.moveQueue(this.getRolesByType(type))
        })

        for (let i = 0; i < nodes.length; i++) {
            let node: cc.Node = nodes[i]
            node.active = true
            let p
            if (isDeathSkill) {
                p = this.playDeathSummon(node, senderRole, action)
            }
            else {
                p = this.playSummonRole(node, senderRole)
            }
            pList.push(p)
        }
        await Promise.all(pList)
    }

    // 角色死亡后召唤
    private async playDeathSummon(role: cc.Node, summoner: cc.Node, action: BattleLog) {
        let body = role.Child('body')
        let ui = role.Child('ui')

        let orgZIndex = role.zIndex
        role.zIndex = 1000

        if (summoner) {
            let summonerBody = summoner.Child('body')
            let hasNextSkillAction = this.hasNextSkillAction(summoner, action)
            if (!hasNextSkillAction) {
                summonerBody.Component(sp.Skeleton).paused = false
            }
            let effect = cc.instantiate2(this.getEffectTemplate("summon", role), this.effectRoot)
            let pos = ut.convertToNodeAR(summoner, this.effectRoot)
            effect.setPosition(pos)
            let effectSk = effect.Component(sp.Skeleton)
            if (!summoner.Data.isCopy) {
                let time = effectSk.getEvent("animation")?.time
                ut.wait(time, this).then(() => {
                    if (!hasNextSkillAction) {
                        if (cc.isValid(summoner)) {
                            summoner.opacity = 0
                        }
                    }
                })
            }
            effectSk.playAnimation("animation").then(() => {
                effect.destroy()
            })
            body.setPosition(ut.convertToNodeAR(summonerBody, role))
        }

        ui.active = false
        let sk = body.Component(sp.Skeleton)
        let anim = PassengerBattleAnimation.SKILL
        //起跳开始，结束时间
        let startTime = sk.getEvent(anim, "effect")?.time || 0
        let endTime = sk.getEvent(anim, "effect2")?.time || 0
        let dur = Math.max(0, endTime - startTime)
        cc.tween(body).delay(startTime).to(dur, { x: 0, y: 0 }).start()
        await sk.playAnimation(PassengerBattleAnimation.SKILL) //出现动画
        ui.active = true
        this.resetAnim(role)
        role.zIndex = orgZIndex
    }

    // 角色直接召唤
    private async playSummonRole(role: cc.Node, summoner: cc.Node) {
        let sk = role.Child("body").Component(sp.Skeleton)
        let summonerSk = summoner.Child("body")?.Component(sp.Skeleton)
        let mountPoint = summonerSk?.getAttachedNode("guadian_jineng")
        let body = role.Child('body')
        let ui = role.Child('ui')
        ui.active = false
        if (mountPoint) {
            body.setPosition(ut.convertToNodeAR(mountPoint, body.parent))
            let dur = 0.5
            await cc.tween(body).then(cc.jumpTo(dur, 0, 0, 200, 1)).promise()
        }
        else {
            let effect = cc.instantiate2(this.getEffectTemplate("summon", role), this.effectRoot)
            let pos = ut.convertToNodeAR(role, this.effectRoot)
            effect.setPosition(pos)
            effect.Component(sp.Skeleton).playAnimation("animation").then(() => {
                effect.destroy()
            })
            await sk.playAnimation(PassengerBattleAnimation.SKILL) //出现动画
        }

        ui.active = true
        this.resetAnim(role)
    }

    //-----------------------------------------------------

    //-------------------- Buff ---------------------------------

    private async handleAddBuff(action) {
        this.debug.parseLog(action)
        let { sender, receiver, data } = action
        let role = this.getRole(sender)
        let cfg = this.getSkillCfg(role)
        if (action.data.hitEffectFunc) {
            action.data.hitEffectFunc()
        }
        let func = this.playAddBuff
        if (cfg?.addBuff) {
            func = this[cfg.addBuff.func]
        }
        let rev = this.getRole(receiver)
        await func.call(this, role, rev, action)
        this.onAddBuff(rev, role, action)
        if (data?.changeAtk) {
            this.changeAttack(rev, data.changeAtk, false)
        }
    }

    private async playAddBuff(role: cc.Node, target: cc.Node, action: BattleLog) {
        let { type, times, skillId, value, skillType } = action.data
        let sender = role.Data.uid
        let buffsRoot: cc.Node = this.getRoleBuffRoot(target)
        if (!buffsRoot) {
            return
        }
        let buffNode = this._checkAddBuff(target, action, buffsRoot)
        if (buffNode) return
        if (type == BattleSkillEffectType.CHANGE_DAMAGE) {
            if (value > 0) {  //易伤
                buffNode = cc.instantiate2(this.getEffectTemplate("weak"), buffsRoot)
            }
        }
        else if (type == BattleSkillEffectType.ATTACK_BUFF) {
            if (value < 0) { //减攻击
                buffNode = cc.instantiate2(this.getEffectTemplate("dizzy"), buffsRoot)
            }
        }
        else if (type == BattleSkillEffectType.ATTACK_GAIN_BUFF || type == BattleSkillEffectType.HP_GAIN_BUFF) {
            if (value < 0) { //增益减少
                buffNode = cc.instantiate2(this.getEffectTemplate("zhongshang"), buffsRoot)
            }
        }
        else {
            // console.error("playBuffSkill error type", type, role.Data.id)
        }
        if (!buffNode) return
        this.fixedMountPointAngle(buffNode, buffsRoot, target)
        buffNode.Data = { sender, times, type, skillId, skillType }
        let sk = buffNode.Component(sp.Skeleton)
        let anim = sk.getAnimations().random()
        buffNode.Component(sp.Skeleton).playAnimation(anim, true)
    }

    protected async playAddBuff_Tech(role: cc.Node, target: cc.Node, action: BattleLog) {
        let { type, times, skillId, value, skillType } = action.data
        let buffsRoot = target.Child('body', sp.Skeleton).getAttachedNode('zhixin')
        let buffNode = this._checkAddBuff(target, action, buffsRoot)
        if (buffNode) return
        buffNode = this.createSkill(skillId, skillType)
        buffNode.parent = buffsRoot
        buffNode.Data = { times, type, skillId, skillType }
    }

    protected playRemoveBuff_Tech(role: cc.Node, action, cfg) {
        let buffsRoot = role.Child('body', sp.Skeleton).getAttachedNode('zhixin')
        let buffNode = this._checkRemoveBuff(role, action, buffsRoot)
        if (buffNode && buffNode.Data.times == 0) {
            buffNode.parent = null
            buffNode.destroy()
        }
    }

    private _checkAddBuff(target: cc.Node, action, buffsRoot?) {
        let { type, times, skillId, value, skillType } = action.data
        buffsRoot = buffsRoot || this.getRoleBuffRoot(target)
        if (!buffsRoot) return
        let buffNode: cc.Node = buffsRoot.children.find(s => s.Data?.type == type && s.Data?.skillId == skillId && s.Data?.skillType == skillType)
        if (buffNode) {
            if (buffNode.Data.times > 0) {
                buffNode.Data.times += times
            }
        }
        return buffNode
    }

    private async handleRemoveBuff(action, delay = true) {
        let { receiver, data } = action
        let role = this.getRole(receiver)
        let { type, times, roleId, skillId, skillType, skillHash } = data
        times = times || 1

        let cfg = this.getSkillCfgByHash(roleId, skillHash)
        let removeBuff = cfg?.removeBuff
        if (removeBuff?.delay && delay) {
            if (!role.Data.delayRemoveBuffs) role.Data.delayRemoveBuffs = []
            role.Data.delayRemoveBuffs.push(action)
            return
        }

        this.debug.parseLog(action)

        let pList = []
        for (let i = 0; i < times; i++) {
            pList.push(this.playRemoveBuff(role, action, cfg))
            this.onRemoveBuff(role, action)
        }
        await Promise.all(pList)
        if (data?.changeAtk) {
            this.changeAttack(role, data.changeAtk, false)
        }
    }

    private playRemoveBuff(role: cc.Node, action, cfg) {
        let func = this._playRemoveBuff
        let removeBuff = cfg?.removeBuff
        if (removeBuff) {
            func = this[removeBuff.func]
        }
        return func.call(this, role, action, cfg)
    }

    private _playRemoveBuff(role: cc.Node, action, cfg) {
        let buffsRoot
        if (cfg?.buffMountPoint) {
            buffsRoot = role.Child('body', sp.Skeleton).getAttachedNode(cfg.buffMountPoint)
        }
        let buffNode = this._checkRemoveBuff(role, action, buffsRoot)
        if (buffNode && buffNode.Data.times == 0) {
            buffNode.parent = null
            buffNode.destroy()
        }
    }

    private _checkRemoveBuff(role: cc.Node, action, buffsRoot?) {
        let { type, skillId, skillType } = action.data
        buffsRoot = buffsRoot || this.getRoleBuffRoot(role)
        if (!buffsRoot) return
        let buffNode: cc.Node = buffsRoot.children.find(s => s.Data?.type == type && s.Data?.skillId == skillId && s.Data?.skillType == skillType)
        if (!buffNode) {
            console.error("buffNode not found", role.Data.id, type)
            return
        }
        let data = buffNode.Data
        if (data.times == -1) {
            data.times = 0
        }
        else {
            data.times--
        }
        return buffNode
    }

    private async checkDelayRemoveBuff(role: cc.Node) {
        if (!cc.isValid(role) || !role.parent) return
        if (role.Data.delayRemoveBuffs) {
            let pList = []
            for (let action of role.Data.delayRemoveBuffs) {
                pList.push(this.handleRemoveBuff(action, false))
            }
            await Promise.all(pList)
            role.Data.delayRemoveBuffs = null
        }
    }

    private getRoleBuffRoot(role: cc.Node) {
        let sk = role.Child('body', sp.Skeleton)
        let node = sk.getAttachedNode('buff') || sk.getAttachedNode('tou')
        if (!node) {
            return console.error("getRoleBuffRoot tou not found", role.Data.id)
        }
        let buffRooot = node.Child("buff")
        if (!buffRooot) {
            buffRooot = new cc.Node("buff")
            buffRooot.parent = node
        }
        return buffRooot
    }


    private async onAddBuff(role: cc.Node, sender: cc.Node, action: BattleLog) {
        let { type, times, value, skillId, skillType } = action.data
        let root = role.Child("ui/buffs")

        let buffNode: cc.Node = root.children.find(node => {
            return node.Data?.type == type && node.Data?.skillId == skillId && node.Data?.skillType == skillType
        })
        let updateView = (node: cc.Node) => {
            let times = node.Data.times
            if (times == -1) {
                times = ""
            }
            node.Child("times", cc.Label).string = times || ""
            node.Child("num", cc.Label).string = node.Data.value || ""
        }
        if (buffNode) {
            if (buffNode.Data.times > 0) {
                buffNode.Data.times += times
            }
            buffNode.Data.value += value
            updateView(buffNode)
            return
        }
        buffNode = cc.instantiate2(root.Child("item"), root)
        buffNode.active = true
        let name = ""
        if (type == BattleSkillEffectType.BUFF) {
            name = sender.Data.id
        }
        else if (type == BattleSkillEffectType.ATTACK_BUFF) {
            name = "ATK"
        }
        else if (type == BattleSkillEffectType.IMMUNE_DAMAGE) {
            name = "SHD"
        }
        else if (type == BattleSkillEffectType.CHANGE_DAMAGE) {
            name = "DAG"
        }
        else if (type == BattleSkillEffectType.HP_GAIN_BUFF) {
            name = "+HP"
        }
        else if (type == BattleSkillEffectType.ATTACK_GAIN_BUFF) {
            name = "+ATK"
        }
        else if (type == BattleSkillEffectType.ATTACK_GAIN_DEBUFF) {
            name = "-ATK"
        }
        else if (type == BattleSkillEffectType.HP_GAIN_DEBUFF) {
            name = "-HP"
        }
        else if (type == BattleSkillEffectType.SILENCE) {
            name = "SLC"
        }
        else if (type == BattleSkillEffectType.SKILL_DAMAGE) {
            name = "SDAG"
        }
        else {
            console.warn("unknown type", type)
        }
        buffNode.Child("type", cc.Label).string = name
        buffNode.Data = { skillId, skillType, times, value, type }
        updateView(buffNode)
    }

    private onRemoveBuff(role: cc.Node, action) {
        let { type, skillId, skillType } = action.data
        let buffsRoot = role.Child("ui/buffs")
        let buffNode: cc.Node = buffsRoot.children.find(node => {
            return node.Data?.type == type && node.Data?.skillId == skillId && node.Data?.skillType == skillType
        })
        console.log("onRemoveBuff", role.Data.id, type, skillId, skillType)
        if (!buffNode) {
            console.error("buffNode not found", role.Data.id, type)
            return
        }
        let updateView = (node: cc.Node) => {
            let times = node.Data.times
            if (times == -1) {
                times = ""
            }
            node.Child("times", cc.Label).string = times || ""
            node.Child("num", cc.Label).string = node.Data.value || ""
        }
        let data = buffNode.Data
        if (data.times == -1) {
            data.times = 0
        }
        else {
            data.times--
        }
        if (data.times == 0) {
            buffNode.parent = null
            buffNode.destroy()
        }
        else {
            updateView(buffNode)
        }
    }

    //-----------------------------------------------------------

    //-------------------- 攻击 ---------------------------------

    private async handleCollide(action: BattleLog) {
        let { sender, receiver, next } = action

        let passengerId = sender
        let monsterId = receiver
        let getInfo = (uid) => {
            let hitAction = next.find(a => a.type == BattleLogType.HIT && a.receiver == uid)
            let deathAction = hitAction.next && hitAction.next.find((a) => a.type == BattleLogType.DEATH && a.receiver == uid)
            let damage = hitAction.data.damage
            let isDeath = !!deathAction
            return { damage, isDeath, hitAction, deathAction }
        }
        let info1 = getInfo(passengerId)
        let info2 = getInfo(monsterId)

        let role1 = this.getRole(passengerId)
        let role2 = this.getRole(monsterId)

        let jumpType = 0
        if (this.isAuto) {
            await ut.wait(0.5, this)
        }
        else {
            await Promise.all([this.playCollideReady(role1), this.playCollideReady(role2)])
            await this.checkNextStep()
            if (!this.isAuto) {
                jumpType = 1
            }
        }
        this.debug.parseLog(action)

        let list = [
            { role: role1, info: info1 },
            { role: role2, info: info2 },
        ]

        let handleFlag = false
        let onAttack
        let waitAttack = new Promise(cb => {
            onAttack = (role: cc.Node) => {
                if (handleFlag) return
                handleFlag = true

                for (let { role, info } of list) {
                    this.handleHitByCollide(info).catch(err => console.error(err))

                    let getSkillActions = next.filter(a => a.type == BattleLogType.USE_SKILL && a.sender == role.Data.uid)
                    for (let action of getSkillActions) {
                        this.handleUseSkill(action, true).catch(err => console.error(err))
                    }

                    getSkillActions = next.filter(a => a.type == BattleLogType.USE_BUFF_SKILL && a.sender == role.Data.uid)
                    for (let action of getSkillActions) {
                        this.handleUseBuffSkill(action, true).catch(err => console.error(err))
                    }

                    getSkillActions = next.filter(a => a.type == BattleLogType.REMOVE_BUFF && a.receiver == role.Data.uid)
                    for (let action of getSkillActions) {
                        this.handleRemoveBuff(action)
                    }
                }

                this.playCollideEffect(this.getCollidePos())
                this.playRolesJump([role1, role2])

                cb(true)
            }
        })

        let pList = []
        for (let { role, info } of list) {
            let p = this.playCollideJump(role, onAttack, jumpType)
            pList.push(p)
        }
        await Promise.all(pList)

        pList = []
        let play = ({ role, info }, other) => {
            let p
            let isBig = info.damage > other.info.damage
            if (info.isDeath && other.info.isDeath) { //互相击飞
                p = this.playAttackDiaupDouble(role, info)
            }
            else if (!info.isDeath && !other.info.isDeath) { //无人被击飞
                p = this.playAttack(role, info)
            }
            else { //其中一个被击飞
                if (info.isDeath) {
                    isBig = true
                }
                else if (other.info.isDeath) {
                    isBig = false
                }
                p = this.playAttackDiaupOne(role, info, waitAttack)
            }
            info.isBig = isBig
            pList.push(p)
        }
        play(list[0], list[1])
        play(list[1], list[0])
        await Promise.all(pList)

        await Promise.all([
            this.checkDelayRemoveBuff(role1),
            this.checkDelayRemoveBuff(role2)
        ])

        // eventCenter.emit(EventType.GUIDE_BATTLE_COLLIDE)
    }

    private getCollidePos() {
        return this.roleRoot.convertToWorldSpaceAR(cc.v2(0, 275))
    }

    private async playCollideReady(role: cc.Node) {
        let body = role.Child('body')
        let sk = body.Component(sp.Skeleton)
        let anim = this.getAnim(role)
        await sk.tryPlayAnimations([anim?.ready, PassengerBattleAnimation.ATTACK_READY, PassengerBattleAnimation.ATTACK_IDLE], true)
    }

    private async playCollideJump(role: cc.Node, onAttack, jumpType: number = 0) {
        let body = role.Child('body')
        let sk = body.Component(sp.Skeleton)
        let anim = this.getAnim(role)
        if (jumpType == 0) {
            await sk.tryPlayAnimations([anim?.jump, PassengerBattleAnimation.ATTACK_JUMP])
        }
        else {
            await sk.tryPlayAnimations([anim?.readyJump, PassengerBattleAnimation.ATTACK_READY_JUMP])
        }

        sk.setEventListener(({ animation }, { data }) => {
            if (animation.name == PassengerBattleAnimation.ATTACK || animation.name == anim?.attack) {
                if (data.name == "effect") {
                    onAttack && onAttack(role)
                    sk.setEventListener(null)
                }
            }
        })
    }

    //普通对撞
    private async playAttack(role: cc.Node, { damage, isDeath, deathAction, hitAction }, onAttack?) {
        let body = role.Child('body')
        let sk = body.Component(sp.Skeleton)
        let anim = this.getAnim(role)
        await sk.tryPlayAnimations([anim?.attack, PassengerBattleAnimation.ATTACK])
        await sk.tryPlayAnimations([anim?.fall, PassengerBattleAnimation.ATTACK_FALL])
        this.changeBossStage(role)
        this.resetAnim(role)
    }

    //其中一个被击飞
    private async playAttackDiaupOne(role: cc.Node, { damage, isDeath, deathAction, hitAction }, waitAttack) {
        let body = role.Child('body')
        let sk = body.Component(sp.Skeleton)
        let anim = this.getAnim(role)
        if (isDeath) { //被击飞
            let type = this.getDeathType(deathAction)
            if (type != BattleDeathType.DIRECT) { //如果后面要放技能，先播攻击动画
                await sk.tryPlayAnimations([anim?.attack, PassengerBattleAnimation.ATTACK])
            }
            else {
                await sk.tryPlayAnimations([anim?.attack, PassengerBattleAnimation.ATTACK])
                // await Promise.race([sk.tryPlayAnimations([anim?.attack, PassengerBattleAnimation.ATTACK]), waitAttack])
            }
            await this.handleDeath(deathAction, true, PassengerBattleAnimation.ATTACK_DIAUP)
        }
        else {
            await sk.tryPlayAnimations([anim?.attack, PassengerBattleAnimation.ATTACK])
            await sk.tryPlayAnimations([anim?.fall, PassengerBattleAnimation.ATTACK_FALL2])
            this.changeBossStage(role)
            this.resetAnim(role)
        }
    }

    //互相击飞
    private async playAttackDiaupDouble(role: cc.Node, { damage, isDeath, deathAction, hitAction }, onAttack?) {
        let body = role.Child('body')
        let sk = body.Component(sp.Skeleton)
        let anim = this.getAnim(role)
        await sk.tryPlayAnimations([anim?.attack, PassengerBattleAnimation.ATTACK])
        await this.handleDeath(deathAction, true, PassengerBattleAnimation.ATTACK_DIAUP2)
    }

    private playCollideEffect(pos: cc.Vec2) {
        this.shake(2)
        let template = this.getEffectTemplate("collide")
        let node = cc.instantiate2(template, this.effectRoot)
        node.Component(sp.Skeleton).playAnimation('Attack').then(() => {
            node.destroy()
        })
        pos = node.parent.convertToNodeSpaceAR(pos)
        node.setPosition(pos)
    }

    private playRolesJump(ignoreRoles: cc.Node[] = [], factor = 1) {
        let roles = this.passengers.concat(this.monsters).filter(r => !ignoreRoles.includes(r))
        let time = 0.15 * factor
        for (let role of roles) {
            let node = role.Child('body')
            cc.Tween.stopAllByTarget(node)
            cc.tween(node).then(cc.jumpTo(time, cc.v2(0, 0), 50 * factor, 1)).start()
        }
    }

    //-----------------------------------------------------------

    //---------------死亡相关-------------------------------

    private async handleDeath(action, isCollide?, diaupAnim?) {
        this.debug.parseLog(action)
        let { receiver } = action
        let role = this.getRole(receiver)
        let deathType = this.getDeathType(action)
        if (isCollide) {
            await this.playDiaupDeath(role, deathType, diaupAnim)
        }
        else {
            await this.playDeath(role, deathType)
        }
        eventCenter.emit(EventType.GUIDE_BATTLE_DEATH, role.Data.id)
    }

    private getDeathType(action) {
        let { data } = action
        let deathType = data.deathType
        return deathType
    }

    //击飞
    private async playDiaupDeath(role: cc.Node, deathType: BattleDeathType, diaupAnim?) {
        let cfg = SP_SKILL[role.Data.id]
        if (deathType == BattleDeathType.DIRECT) {
            this.checkDiaupEnd(role)
            await this.playDiaupDeathAnim(role, diaupAnim)
            this.onDeath(role)
        }
        else if (deathType == BattleDeathType.SUMMON && cfg?.deathSkillFly) { //要召唤的一般是飞出去的
            role.Data.deathSkill = true
            ut.wait(0.1, this).then(() => {
                this.onDiaupDeathStart(role)
            }).catch(err => {
                console.error(err)
            })
            this.checkDiaupEnd(role)
            await this.playDeathSkillAnim(role, PassengerBattleAnimation.DEATH_SKILL)
        }
        else {
            role.Data.deathSkill = true
            await this.playDeathSkillAnim(role, PassengerBattleAnimation.DEATH_SKILL)
        }
    }

    private onDiaupDeathStart(role) {
        if (!cc.isValid(role)) return
        let sk = role.Child('body', sp.Skeleton)
        let node = this.getEffectTemplate("diaup")
        let parent = sk.getAttachedNode("zhixin")
        if (!cc.isValid(parent)) return
        cc.instantiate2(node, parent)
    }

    private checkDiaupEnd(role: cc.Node, cb?: Function) {
        let sk: sp.Skeleton = role.Child('body', sp.Skeleton)
        let node = sk.getAttachedNode("zhixin")
        if (!node) return
        let root = this.node.parent
        let prePos = ut.convertToNodeAR(node, root)
        let minX = -cc.winSize.width / 2, minY = -cc.winSize.height / 2
        let rect = cc.rect(minX, minY, cc.winSize.width, cc.winSize.height)
        let checkOut = (pos) => {
            return !rect.contains(pos)
        }
        let check = () => {
            let pos = ut.convertToNodeAR(node, root)
            if (checkOut(pos)) {
                let effectPos = cc.v2()
                ut.lineRect(pos, prePos, rect, effectPos)
                this.onDiaupDeathEnd(role, ut.convertToNodeAR(root, this.effectRoot, effectPos))
                cb && cb()
                sk.unschedule(check)
            }
            prePos = pos
        }
        sk.scheduleUpdate(check)
    }

    private async onDiaupDeathEnd(role, pos) {
        let sk: sp.Skeleton = role.Child('body', sp.Skeleton)
        sk.getAttachedNode("zhixin")?.getChildByName("diaup")?.destroy()

        let template = this.getEffectTemplate("diaup_out")
        let node = cc.instantiate2(template, this.effectRoot)
        node.setPosition(pos)
        node.Component(sp.Skeleton).playAnimation("animation").then(() => {
            node.destroy()
        })

        this.shake(1)
        return this.playRolesJump([role], 0.5)
    }

    //普通受击死亡
    private async playDeath(role: cc.Node, deathType: BattleDeathType) {
        if (deathType == BattleDeathType.DIRECT) {
            await this.playNormalDeathAnim(role)
            this.onDeath(role)
        }
        else {
            role.Data.deathSkill = true
            return this.playDeathSkillAnim(role, PassengerBattleAnimation.DEATH_SKILL2)
        }
    }

    private checkDeathSkill(role) {
        if (role.Data?.deathSkill) {
            if (!this.hasNextSkillAction(role)) {
                this.onDeath(role)
            }
        }
    }

    private hasNextSkillAction(role: cc.Node, curAction?: BattleLog) {
        if (role.Data.isCopy) return false

        let action = this.actions[this.step]
        if (action && curAction) {
            for (let next of action.next) {
                if (next == curAction) continue
                if (next.sender == role.Data.uid) {
                    return true
                }
            }
        }

        for (let i = this.step + 1; i < this.actions.length; i++) {
            let action = this.actions[i]
            if (action.sender == role.Data.uid) {
                return true
            }
        }
        return false
    }

    //普通受击死亡的动作
    private async playNormalDeathAnim(role) {
        this.hideUIByDeath(role)
        let body = role.Child("body")
        let sk: sp.Skeleton = body.Component(sp.Skeleton)
        if (!sk) {
            return ut.wait(0.3, this)
        }
        let anim = PassengerBattleAnimation.DEAD
        if (sk.findAnimation(anim)) {
            return sk.playAnimation(anim)
        }

        //废弃
        let data = role.Data
        let angle = 90
        if (data.type == BattleRoleType.MONSTER) {
            angle = -angle
        }
        let time = 0.3
        sk.paused = true
        await cc.tween(body).to(time, { angle }).promise()
    }

    //对撞死亡动作
    private async playDiaupDeathAnim(role: cc.Node, diaupAnim = PassengerBattleAnimation.ATTACK_DIAUP) {
        let body = role.Child('body')
        let sk = body.Component(sp.Skeleton)
        ut.wait(0.1, this).then(() => {
            this.hideUIByDeath(role)
        }).catch(err => console.error(err))

        let time = sk.getEvent(diaupAnim)?.time || 0
        ut.wait(time, this).then(() => {
            this.onDiaupDeathStart(role)
        })
        await sk.playAnimation(diaupAnim)
    }

    //亡语
    private async playDeathSkillAnim(role: cc.Node, anim) {
        this.hideUIByDeath(role)
        let sk = role.Child('body', sp.Skeleton)
        if (!sk.findAnimation(anim)) {
            anim = PassengerBattleAnimation.DEAD
            await sk.playAnimation(anim)
        }
        else {
            let time = sk.getEvent(anim)?.time || sk.getAnimationDuration(anim)  //有些情况下需要播到某个时刻暂停，等后续逻辑处理
            sk.playAnimation(anim)
            await ut.wait(time, this)
        }

        if (sk.findAnimation(PassengerBattleAnimation.DEATH_SKILL_LOOP)) {
            sk.playAnimation(PassengerBattleAnimation.DEATH_SKILL_LOOP, true)
        }
        else {
            sk.paused = true
        }
    }

    private hideUIByDeath(role: cc.Node) {
        let ui = role.Child('ui')
        cc.tween(ui).to(0.3, { opacity: 0 }).start()
    }

    private onDeath(role: cc.Node) {
        if (role.Data?.isCopy) return
        let sourceNode = role.Data.sourceNode
        this.removeRole(role)
        if (sourceNode) {
            sourceNode.parent = null
            sourceNode.destroy()
        }
        if (cc.isValid(role)) {
            role.parent = null
            role.destroy()
        }
    }
    //---------------------------------------------------------


    private async handleChangePos(action, time?) {
        this.debug.parseLog(action)
        let { data, receiver } = action
        let index = data.index - 1
        let role = this.getRole(receiver)
        let sk = role.Child("body", sp.Skeleton)
        let anim = PassengerBattleAnimation.HIT
        let dur = sk.getAnimationDuration(anim)
        sk.playAnimation(PassengerBattleAnimation.HIT, false, dur * 0.5)
        sk.paused = true
        await this.changePos(role, index, time)
        sk.paused = false
        this.resetAnim(role)
    }

    private async changePos(role, index, time?: number) {
        let data = role.Data
        let roles = this.getRolesByType(data.type)
        let orgIndex = roles.findIndex(r => r == role)
        roles.splice(orgIndex, 1)
        roles.splice(index, 0, role)
        await this.moveQueue(roles, time)
    }

    private handleCopy() {
    }

    private async handleRebirth(action: BattleLog) {
        this.debug.parseLog(action)
        let { data, receiver } = action
        let role = this.getRole(receiver)
        role.Data.deathSkill = null
        let body = role.Child("body")
        let sk = body.Component(sp.Skeleton)
        sk.paused = false
        let ui = role.Child('ui')

        let resetNode = (node) => {
            cc.Tween.stopAllByTarget(node)
            node.opacity = 255
            node.active = true
        }
        resetNode(ui)
        resetNode(role)
        resetNode(body)

        await Promise.all([
            this.changeHp(role, data.hp),
            this.changeAttack(role, data.attack)
        ])
    }

    private async handleRunAway(action: BattleLog) {
        this.debug.parseLog(action)
        let { data, receiver } = action
        let role = this.getRole(receiver)
        let body = role.Child("body")
        let sk = body.Component(sp.Skeleton)
        let ui = role.Child('ui')
        ui.active = false

        let skillId = 1
        body.scaleX = -body.scaleX

        let effect = cc.instantiate2(this.getEffectTemplate("summon", role), this.effectRoot)
        let pos = ut.convertToNodeAR(role, this.effectRoot)
        effect.setPosition(pos)
        effect.Component(sp.Skeleton).playAnimation("animation").then(() => {
            effect.destroy()
        })
        // sk.playAnimation(`${PassengerBattleAnimation.SKILL}_${skillId}_loop`, true)
        sk.playAnimation(`battle/Skill`, true)
        let targetPos = cc.v2(cc.winSize.width * 0.5 + 300, body.y)
        if (body.scaleX < 0) {
            targetPos.x = -targetPos.x
        }

        let dis = Math.abs(targetPos.x - body.x)
        let speed = 1600
        let time = dis / speed
        await cc.tween(body).to(time, { x: targetPos.x }).promise()
        this.onDeath(role)
    }

    //---------------- 公共 -----------------------------------

    // 合并同类项
    private mergeEffectActions(action) {
        let map = {}
        /*map = {
            角色uid: [] //技能效果列表
        }
        */
        let { sender, data, next } = action
        if (!next) return map

        for (let action of next) {
            let { type, receiver } = action
            if (!map[receiver]) map[receiver] = []
            let acts = map[receiver]
            acts.push(action)
        }

        //把同时有attack,hp的合并成hp_attack
        for (let receiver in map) {
            let actions = map[receiver]
            let newActions = []
            for (let action of actions) {
                let act = newActions.find(a => {
                    return action.type == a.type && (a.type == BattleLogType.CHANGE_HP || a.type == BattleLogType.CHANGE_ATTACK)
                })
                if (act) {
                    if (action.data.fromUid) {
                        act.data.fromUid = action.data.fromUid
                    }
                    act.data.change += action.data.change
                }
                else {
                    newActions.push(action)
                }
            }

            let hp = newActions.find(a => a.type == BattleLogType.CHANGE_HP)
            let attack = newActions.find(a => a.type == BattleLogType.CHANGE_ATTACK)
            if (hp && attack) {
                let d = { type: BattleLogType.CHANGE_HP_ATTACK, data: { hp, attack } }
                newActions.remove(hp)
                newActions.remove(attack)
                newActions.push(d)
            }

            map[receiver] = newActions
        }

        //合并召唤物
        for (let receiver in map) {
            let actions = map[receiver]

            let newActions = []
            for (let action of actions) {
                let act = newActions.find(a => {
                    return action.type == a.type && a.type == BattleLogType.SUMMON && a.data.indexUid == action.data.indexUid
                })
                if (act) {
                    act.data.summons.pushArr(action.data.summons)
                }
                else {
                    newActions.push(action)
                }
            }
            map[receiver] = newActions
        }

        //处理buff给多个人，视图上需要特殊处理的情况
        // let buffRoles = []
        // for (let receiver in map) {
        //     let actions = map[receiver]
        //     if (actions[0].type == BattleLogType.ADD_BUFF) {
        //         buffRoles.push(receiver)
        //     }
        // }
        // let senderRole = this.getRole(sender)
        // let senderPos = senderRole.getPosition()
        // if (buffRoles.length > 1) {
        //     buffRoles.sort((uid1, uid2) => { //从近到远排序
        //         let dis1 = this.getRole(uid1).getPosition().sub(senderPos).magSqr()
        //         let dis2 = this.getRole(uid2).getPosition().sub(senderPos).magSqr()
        //         return dis1 - dis2
        //     })
        //     for (let i = 0; i < buffRoles.length; i++) {
        //         let receiver = buffRoles[i]
        //         let action = map[receiver][0]
        //         if (!action.data) action.data = {}
        //         action.data.iconId = i % 3 //多加个参数来指定丢哪个buff图标
        //     }
        // }

        //多目标
        let receivers = Object.keys(map)
        if (receivers.length > 1) {
            for (let receiver in map) {
                let actions = map[receiver]
                for (let action of actions) {
                    if (!action.data) action.data = {}
                    action.data.isMulti = true
                }
            }
        }

        return map
    }

    //伤害飘字
    private async showFloat(role, val, isCollide: boolean = false, isBig: boolean = false) {
        if (this.isClear()) return

        let float = cc.instantiate2(this.damgeFloatTemplate, this.floatRoot)
        let body = role.Child('body')
        let pos = ut.convertToNodeAR(body, this.floatRoot, cc.v2(0, STD_ROLE_BOX.yMax - 50))
        float.setPosition(pos)
        float.opacity = 255
        float.Child('count').Component(cc.Label).string = `${val}`
        let orgScale = 2, bigScale = 3
        let targetScale = 0.6
        float.scale = orgScale
        if (isBig) {
            float.scale = bigScale
            targetScale = bigScale / orgScale * targetScale
        }
        if (isCollide) { //对撞的情况下，延迟出现
            float.active = false
            await ut.wait(0.1, this)
            float.active = true
        }

        let p1 = cc.tween(float).delay(0.3).by(0.1, { y: 30 }).promise()
        let p2 = cc.tween(float).to(0.2, { scale: targetScale }).delay(1.1).removeSelf().promise()

        p1.then(() => {
            eventCenter.emit(EventType.GUIDE_BATTLE_COLLIDE)
        })

        return Promise.all([p1, p2])
    }

    //技能释放准备动作
    private async playSkillReady(role: cc.Node, onSkill: Function, mergeMap) {
        let cfg = this.getSkillCfg(role)
        if (cfg?.ready?.isGainBySelf) {
            role.Data.isGainBySelf = true
        }
        if (cfg?.ready?.func) {
            let func = this[cfg.ready.func]
            await func.call(this, role, onSkill, mergeMap)
        }
        else {
            onSkill()
        }
    }

    private async playBuffSkillReady(role: cc.Node, cfg, onSkill: Function, mergeMap) {
        //@ts-ignore
        if (cfg?.buffSkillReady) {
            //@ts-ignore
            let func = this[cfg.buffSkillReady.func]
            await func.call(this, role, cfg, onSkill, mergeMap)
        }
        else {
            onSkill()
        }
    }
    

    private async playSkillReadyCommon(role: cc.Node, onSkill, mergeMap?, anim?: string) {
        let sk = role.Child('body', sp.Skeleton)
        let defaultAnim = String(PassengerBattleAnimation.SKILL)
        if (role.Data.id > 3000) {
            defaultAnim = PassengerBattleAnimation.SKILL + "2"
        }
        anim = anim || defaultAnim
        let time = sk.getEvent(anim)?.time || sk.getEvent(anim, "Cast")?.time || sk.getEvent(anim, "Effect")?.time
        if (time > 0) {
            ut.wait(time, this).then(() => {
                onSkill()
                onSkill = null
            })
        }
        await sk.playAnimation(anim)
        if (onSkill) {
            onSkill()
        }
        this.resetAnim(role)
    }

    protected async playSkillReadyWithEffect(role: cc.Node, onSkill, mergeMap?, anim?: string) {
        return this.playSkillReadyCommon(role, async()=>{
            let curSkill = role.Data.curSkill
            let effect = this.createSkill(curSkill.getId(), curSkill.getType(), "ready")
            effect.parent = role.Child("effect_front")
            effect.scaleX = ut.normalizeNumber(role.Child("body").scaleX) * effect.scaleX
            let sk = effect.Component(sp.Skeleton)
            let anim = "skill"
            sk.playAnimation(anim).then(()=>{
                effect.destroy()
            })
            let time = sk.getEventTime(anim)
            await ut.wait(time, this)
            onSkill()
        }, mergeMap, anim)
    }

    //------------------ 科技装备 -----------------------------------------------------------------
    protected async playSkillReady_TechCommon(role: cc.Node, onSkill, mergeMap?, anim?: string) {
        let sk = role.Component(sp.Skeleton) || role.Child('body', sp.Skeleton)
        let time = sk.getEvent(anim)?.time || 0
        if (time > 0) {
            ut.wait(time, this).then(() => {
                onSkill()
                onSkill = null
            })
        }
        await sk.playAnimation(anim)
        if (onSkill) {
            onSkill()
        }
    }

    protected async playSkillReady_Tech1005(role: cc.Node, onSkill, mergeMap?) {
        let skill = this.createSkill(role.Data.curSkill.getId(), role.Data.curSkill.getType())
        if (!skill) return
        skill.parent = this.roleRoot
        skill.setPosition(role.getPosition())
        let sk = skill.Component(sp.Skeleton) || skill.Child('body', sp.Skeleton)
        await sk.playAnimation("animation1")
        await this.playSkillReady_TechCommon(skill, onSkill, mergeMap, "animation2")
        skill.destroy()
    }

    protected async playSkillReady_Tech1004(role: cc.Node, onSkill, mergeMap?) {
        let curSkill = role.Data.curSkill
        let skill = role.children.find(n => n.name == `tech_skill_${curSkill.id}`)
        if (!skill) return
        let body = skill.Child('body')
        await this.playSkillReady_TechCommon(body, onSkill, mergeMap, "animation2")
        body.Component(sp.Skeleton).playAnimation("animation1", true)
    }
    //-----------------------------------------------------------------------------

    protected async playSkillEmpty(role: cc.Node, target: cc.Node, action) {
        let cfg = this.getSkillCfg(role)
        let skill
        let curSkill: BattleSkill = role.Data.curSkill
        let type = curSkill.getType()
        if (type == SkillType.BATTLE) {
            skill = this.createSkill(role.Data.id, type, "skill")
        }
        else {
            skill = this.createSkill(curSkill.getId(), type)
        }
        if (!skill) return
        if (cfg?.skill?.hitEffectFunc) {
            action.data.hitEffectFunc = ()=>{
                return this[cfg?.skill?.hitEffectFunc](target, curSkill, action)
            }
        }
    }

    // 技能为直线的弹道
    protected async playSkillStraightLine(role: cc.Node, target: cc.Node, action) {
        let cfg = this.getSkillCfg(role)
        let skill
        let curSkill: BattleSkill = role.Data.curSkill
        let type = curSkill.getType()
        if (type == SkillType.BATTLE) {
            skill = this.createSkill(role.Data.id, type, "skill")
        }
        else {
            skill = this.createSkill(curSkill.getId(), type)
        }
        if (!skill) return
        let mountPoint = this.getSkillMountPoint(role)
        if (!mountPoint) return
        skill.parent = this.skillRoot
        skill.setPosition(ut.convertToNodeAR(mountPoint, this.skillRoot))
        if (cfg?.skill?.hitEffectFunc) {
            action.data.hitEffectFunc = ()=>{
                return this[cfg?.skill?.hitEffectFunc](target, curSkill, action)
            }
        }
        return this.playStraightLineAnim(skill, target, cfg)
    }

    protected async playSkillStraightLine2(role: cc.Node, target: cc.Node, action: BattleLog) {
        let curSkill: BattleSkill = role.Data.curSkill
        let type = curSkill.getType()
        let skill = this.createSkill(role.Data.id, type)
        if (!skill) return
        let mountPoint = this.getSkillMountPoint(role)
        if (!mountPoint) return
        skill.parent = this.skillRoot
        skill.setPosition(ut.convertToNodeAR(mountPoint, this.skillRoot))
        skill.scaleX = role.Child("body").scaleX

        let skill2 = cc.instantiate2(skill, this.skillRoot)
        let skill3 = cc.instantiate2(skill, this.skillRoot)
        skill3.active = false
        skill.zIndex = 1000

        let sk1 = skill.Component(sp.Skeleton)
        let castTime = 0
        let p1
        if (sk1.findAnimation("cast")) {
            castTime = sk1.getEventTime("cast") || 0
            p1 = sk1.playAnimation("cast")
        }
        if (p1) {
            p1.then(()=>{
                skill.destroy()
            })
        }
        else {
            skill.destroy()
        }

        // let castTime = role.Child("body", sp.Skeleton).getEventTime(PassengerBattleAnimation.SKILL, "Cast")
        // let vfxTime = role.Child("body", sp.Skeleton).getEventTime(PassengerBattleAnimation.SKILL, "Effect")
        // if (castTime && vfxTime) {
        //     await ut.wait(vfxTime - castTime, this)
        // }

        if (castTime > 0) {
            await ut.wait(castTime, this)
        }
        
        let cfg = this.getSkillCfg(role)
        let sk2 = skill2.Component(sp.Skeleton)
        let time = this.getStraightLineAnimTime(skill2, target, cfg)
        this.playStraightLineAnim(skill2, target, cfg, false)
        if (sk2.findAnimation("start")) {
            time -= sk2.getAnimationDuration("start")
        }
        await sk2.tryPlayAnimations(["VFX_star", "VFX_start", "VFX_Start", "Fly_start", "start"])

        sk2.tryPlayAnimations(["VFX_cycle", "VFX_Cycle", "dandao", "cycle"], true)

        let handleEnd = async()=>{
            let endTime = 0
            if (sk2.findAnimation("end")) {
                endTime = sk2.getEventTime("end") ||  sk2.getAnimationDuration("end")
            }
            if (time > endTime) {
                await ut.wait(time - endTime, this)
                let p2: Promise<any> = sk2.tryPlayAnimations(["VFX_end", "Fly_end", "end"], false)
                if (!p2) {
                    p2 = ut.waitNextFrame(2, this)
                }
                await p2
            }
            skill2.destroy()
        }
        handleEnd()

        action.data.hitEffectFunc = async()=>{
            if (!cc.isValid(skill3)) return
            skill3.active = true
            let sk3 = skill3.Component(sp.Skeleton)
            let targetPos = ut.convertToNodeAR(target, skill3.parent, this.getRoleCenter(target))
            skill3.setPosition(targetPos)
            await sk3.tryPlayAnimations(["Impact", "Impact_Toon", "impact"])
            skill3.destroy()
        }

        if (time > HIT_EFFECT_TIME) {
            await ut.wait(time - HIT_EFFECT_TIME, this)
        }
    }

    private async playStraightLineAnim(skill: cc.Node, target, cfg?, autoRemove = true) {
        let targetPos = ut.convertToNodeAR(target, skill.parent, this.getRoleCenter(target))
        let speed = cfg?.skill?.speed || 2400
        let dir = targetPos.sub(skill.getPosition())
        if (dir.x < 0) {
            dir = skill.getPosition(dir).sub(targetPos)
            skill.scaleX = -1
        }
        skill.angle = dir.xAngle()
        let dis = dir.mag()
        let time = cfg?.skill?.time || dis / speed
        await cc.tween(skill).to(time, { x: targetPos.x, y: targetPos.y }).promise()
        if (autoRemove) {
            skill.parent = null
            skill.destroy()
        }
    }

    private getStraightLineAnimTime(skill: cc.Node, target, cfg?) {
        let targetPos = ut.convertToNodeAR(target, skill.parent, this.getRoleCenter(target))
        let speed = cfg?.skill?.speed || 2400
        let dir = targetPos.sub(skill.getPosition())
        if (dir.x < 0) {
            dir = skill.getPosition(dir).sub(targetPos)
            skill.scaleX = -1
        }
        let dis = dir.mag()
        let time = cfg?.skill?.time || dis / speed
        return time
    }

    // 技能为抛物线
    protected async playSkillCurve(role: cc.Node, target: cc.Node, action: ActionNode) {
        let id = role.Data.id
        let skill
        let curSkill: BattleSkill = role.Data.curSkill
        let type = curSkill.getType()
        let cfg = this.getSkillCfg(role)
        if (type == SkillType.BATTLE) {
            skill = this.createSkill(id, type, "skill")
        }
        else {
            skill = this.createSkill(curSkill.getId(), type)
        }
        if (!skill) return
        skill.parent = this.skillRoot
        let skillType = 0
        if (this.isBehind(role, target)) {
            skillType = 1
        }
        let pos = this.getSendSkillPos(role, skillType, true)
        skill.setPosition(pos)

        if (cfg?.skill?.hitEffectFunc) {
            action.data.hitEffectFunc = ()=>{
                return this[cfg?.skill?.hitEffectFunc](target, curSkill, action)
            }
        }
        await this.playCurveSkillAnim(skill, target, skillType, false)
        skill.destroy()
    }

    protected async playSkillCurve2(role: cc.Node, target: cc.Node, action: ActionNode) {
        let id = role.Data.id
        let skill
        let curSkill: BattleSkill = role.Data.curSkill
        let type = curSkill.getType()
        let cfg = this.getSkillCfg(role)
        if (type == SkillType.BATTLE) {
            skill = this.createSkill(id, type, "skill")
        }
        else {
            skill = this.createSkill(curSkill.getId(), type)
        }
        if (!skill) return
        skill.parent = this.skillRoot
        let skillType = 0
        if (this.isBehind(role, target)) {
            skillType = 1
        }
        let pos = this.getSendSkillPos(role, skillType, true)
        skill.setPosition(pos)

        let skill2 = cc.instantiate2(skill, this.skillRoot)
        let skill3 = cc.instantiate2(skill, this.skillRoot)
        skill3.active = false
        skill.zIndex = 100

        let sk1 = skill.Component(sp.Skeleton)
        if (!sk1) return
        let castTime = 0
        let p1
        if (sk1.findAnimation("cast")) {
            castTime = sk1.getEventTime("cast") || 0
            p1 = sk1.playAnimation("cast")
        }
        if (p1) {
            p1.then(()=>{
                skill.destroy()
            })
        }
        else {
            skill.destroy()
        }

        // let castTime = role.Child("body", sp.Skeleton).getEventTime(PassengerBattleAnimation.SKILL, "Cast")
        // let vfxTime = role.Child("body", sp.Skeleton).getEventTime(PassengerBattleAnimation.SKILL, "Effect")
        // if (castTime && vfxTime) {
        //     await ut.wait(vfxTime - castTime, this)
        // }

        if (castTime > 0) {
            await ut.wait(castTime, this)
        }
        
        let sk2 = skill2.Component(sp.Skeleton)
        let time = this.getCurveSkillAnimTime(skill2, target, skillType)
        this.playCurveSkillAnim(skill2, target, skillType, false)
        await sk2.tryPlayAnimations(["VFX_star", "VFX_start", "VFX_Start", "Fly_start", "start"])

        sk2.tryPlayAnimations(["VFX_cycle", "VFX_Cycle", "dandao", "cycle"], true)

        let handleEnd = async()=>{
            let endTime = 0
            if (sk2.findAnimation("end")) {
                endTime = sk2.getEventTime("end") ||  sk2.getAnimationDuration("end")
            }
            await ut.wait(time - endTime, this)
            let p2: Promise<any> = sk2.tryPlayAnimations(["VFX_end", "Fly_end", "end"], false)
            if (!p2) {
                p2 = ut.waitNextFrame(2, this)
            }
            await p2
            skill2.destroy()
        }
        handleEnd()

        action.data.hitEffectFunc = async ()=>{
            if (!cc.isValid(skill3)) return
            skill3.active = true
            let sk3 = skill3.Component(sp.Skeleton)
            let targetPos = ut.convertToNodeAR(target, skill3.parent, this.getRoleCenter(target))
            skill3.setPosition(targetPos)
            await sk3.tryPlayAnimations(["Impact", "Impact_Toon", "impact"])
            skill3.destroy()
        }

        await ut.wait(time - HIT_EFFECT_TIME, this)
    }

    //------------- 赏金猎犬 -----------------
    protected async playAddBuff_1005(role: cc.Node, target: cc.Node, action) {
        let { data } = action
        let { type, times, skillId, skillType } = data

        let cfg = this.getSkillCfg(role)
        let mountPoint = cfg?.buffMountPoint || "buff"
        let _sk = target.Child("body", sp.Skeleton)
        let buffRoot: cc.Node = _sk.getAttachedNode(mountPoint) || _sk.getAttachedNode("tou")
        if (!buffRoot) {
            console.error("playAddBuff_1005 not found ", target.Data.id)
            return
        }
        let skill = this._checkAddBuff(target, action, buffRoot)
        if (skill) return
        skill = this.createSkill(role.Data.id)
        if (!skill) return
        skill = skill.Child("1")
        skill.active = true
        skill.parent = buffRoot
        this.fixedMountPointAngle(skill, buffRoot, target)

        let scale = this.getSkillScale(target)
        skill.scaleX *= scale
        skill.scaleY *= scale

        let sk = skill.Component(sp.Skeleton)
        await sk.playAnimation("animation")
        sk.playAnimation("jingzhi", true)
        skill.Data = { sender: role.Data.uid, times, type, skillId, skillType }
    }


    protected async playBuffSkill_1005(target: cc.Node, cfg, action) {
        let mountPoint = cfg?.buffMountPoint || "buff"
        let _sk = target.Child("body", sp.Skeleton)
        let buffRoot: cc.Node = _sk.getAttachedNode(mountPoint) || _sk.getAttachedNode("tou")
        target.Data.isGainBySelf = true
        let skill = this.createSkill(cfg.id.split("-")[0])
        if (!skill) return
        skill = skill.Child("2")
        skill.active = true
        skill.parent = this.effectRoot

        let sk = skill.Component(sp.Skeleton)
        let y = 150

        let skillScale = this.getSkillScale(target)
        skill.scaleX *= skillScale
        skill.scaleY *= skillScale
        let pos = ut.convertToNodeAR(buffRoot, this.effectRoot)
        pos.y += y
        skill.setPosition(pos)

        await sk.playAnimation("animation1")
        sk.playAnimation("animation2").then(() => {
            skill.destroy()
        })
    }
    //---------------------------------------

    //------------- 浪客喵 ---------------------------
    protected async playSkillReady_1007(role: cc.Node, onSkill, mergeMap) {
        let sk = role.Child('body', sp.Skeleton)
        sk.useTint = true
        let count = 0
        let mergeHit = (actions) => {
            let c = 0
            let next = [], damage = 0
            let newHit
            for (let i = actions.length - 1; i >= 0; i--) {
                let action = actions[i]
                if (action.type == BattleLogType.HIT) {
                    c++
                    damage += action.data.damage
                    actions.splice(i)
                    next = next.concat(action.next || [])
                    if (!newHit) {
                        newHit = action
                    }
                }
            }
            count += c
            newHit.data.count = c
            newHit.data.damage = damage
            newHit.next = next
            actions.push(newHit)
        }
        for (let receiver in mergeMap) {
            let actions = mergeMap[receiver]
            mergeHit(actions)
        }
        let node = this.createSkill(role.Data.id, SkillType.BATTLE, "cycle")
        if (!node) return

        sk.setEventListener(({ animation }, { data }) => {
            if (data.name == "effect") { //剑出现时机
                let start = 3
                for (let i = start; i < start + count; i++) {
                    let mountPoint = sk.getAttachedNode(`jian${i}`)
                    cc.instantiate2(node, mountPoint)
                }
            }
            else if (data.name == "effect2") { //剑发出去时机
                sk.setEventListener(null)
                onSkill()
            }
        })
        await sk.playAnimation(PassengerBattleAnimation.SKILL)
        this.resetAnim(role)
    }

    protected async playSkill_1007(role: cc.Node, target: cc.Node, action: BattleLog) {
        let sk = role.Child('body', sp.Skeleton)
        let count = action.data.count
        let times = [0.05, 0.05] //飞剑发射间隔

        action.data.hitEffectFunc = async()=>{
            let impact = this.createSkill(role.Data.id, SkillType.BATTLE, "impact")
            impact.parent = this.skillRoot
            let sk = impact.Component(sp.Skeleton)
            let targetPos = ut.convertToNodeAR(target, impact.parent, this.getRoleCenter(target))
            impact.setPosition(targetPos)
            impact.scaleX = ut.normalizeNumber(role.Child("body").scaleX)
            await sk.playAnimation("impact")
            impact.destroy()
        }

        for (let i = 0; i < count; i++) {
            let mountPoint = sk.getAttachedNode(`jian${i + 3}`)
            let node: cc.Node = mountPoint.children[0]
            if (!node) break
            node.parent = null
            node.destroy()
            node = this.createSkill(role.Data.id, SkillType.BATTLE, "skill")
            node.parent = this.skillRoot
            if (target.Data.type == BattleRoleType.PASSENGER) {
                node.scaleX = -1
            }

            let pos = ut.convertToNodeAR(target, node.parent, this.getRoleCenter(target))
            node.setPosition(pos)
            let animName = "animation"
            node.Component(sp.Skeleton).playAnimation(animName)
            // if (i < count - 1) {
                await ut.wait(0.2, this)
            // }
            if (i < count - 1) { //最后一击交给后续逻辑
                this.playHitAnim(target)
                await ut.wait(times[i], this)
            }
        }
    }
    //------------------------------------------------------------

    //-------------------- 黄大仙 臭鼬 -------------------------
    protected async showSkill_1011(role: cc.Node, mergeMap) {
        let sk = role.Child('body', sp.Skeleton)
        let showOne = async (receiver) => {
            let skill = this.createSkill(role.Data.id)
            if (!skill) return
            skill.parent = this.skillRoot
            let target = this.getRole(receiver)
            let startPos = ut.convertToNodeAR(this.getSkillMountPoint(role), this.skillRoot)
            let targetPos = ut.convertToNodeAR(target, skill.parent)
            skill.setPosition(startPos)

            if (target.Data.type == BattleRoleType.PASSENGER) {
                skill.scaleX = -1
            }
            let oriScale = skill.scaleX
            targetPos.y += target.Child('body').height * 0.6    //todo 取怪物面部的pos
            skill.scale = 0
            let speed = 1200
            let dis = Math.abs(target.x - skill.x)
            cc.tween(skill).to(0.3, { scaleX: oriScale, scaleY: 1 }).start()
            await cc.tween(skill).to(dis / speed, { x: targetPos.x, y: targetPos.y }).promise()
            cc.tween(skill).to(0.15, { opacity: 0 }).removeSelf().start()
            await this.handleEffect({ sender: role.Data.uid, receiver, next: mergeMap[receiver] })
        }
        let pList = []
        for (let receiver in mergeMap) {
            let p = showOne(receiver)
            pList.push(p)
        }
        await Promise.all(pList)
    }
    //--------------------------------------------------

    //-------------------- 牛仔汪 ---------------------------

    protected async playSkillReady_1017(role: cc.Node, onSkill) {
        let sk = role.Child('body', sp.Skeleton)
        onSkill()
        await sk.playAnimation(PassengerBattleAnimation.SKILL)
        this.resetAnim(role)
    }

    protected async showSkill_1017(role: cc.Node, mergeMap) {
        let showOne = async (rev) => {
            let target = this.getRole(rev)
            let actions = mergeMap[rev]
            let changePosAction = actions.find(a => a.type == BattleLogType.CHANGE_POSITION)
            let { data } = changePosAction
            let index = data.index - 1
            let mountPoint = this.getSkillMountPoint(role)
            let node = this.createSkill(role.Data.id)
            if (!node) return
            node.parent = mountPoint

            let startPos1 = new cc.Vec2(-6, -4)
            let startPos2 = new cc.Vec2(-6, -10)
            node.setPosition(startPos1)
            //技能的移动是移动技能预制体下的子节点，这样起点是(0,0),终点是targetPos
            let targetPos = ut.convertToNodeAR(target, node)
            //区分对小怪用S,大的用L
            let size = this.getSkillScale(target) > 1 ? 'L' : 'S'
            node.Swih(`one${size}`)
            let ropeNode = node.Child(`one${size}`)

            //处理层级问题-----------------
            let tempTarget = cc.instantiate2(target, node)
            tempTarget.Child("body").scaleX = -1
            let tmpSk = tempTarget.Child('body', sp.Skeleton)
            let orgSk = target.Child('body', sp.Skeleton)
            let skin = orgSk["curSkinName"]
            if (skin) {
                tmpSk.setSkin(skin)
            }
            tempTarget.setPosition(targetPos)
            tempTarget.zIndex = 1000
            node.Child('downs').zIndex = tempTarget.zIndex + 1
            target.opacity = 0
            //*----------------------------------

            //ropeNode.active = true
            let t1 = 0.6
            let t2 = 0.35
            let targetPos1 = new cc.Vec2(targetPos.x * 0.2, 200)
            let targetPos2 = new cc.Vec2(targetPos.x * 0.3, 200)
            let targetPos3 = new cc.Vec2(targetPos.x - target.Child('body').width * 0.6, targetPos.y + target.Child('body').height * 0.4)
            let typeArray = [targetPos1, targetPos2, targetPos3]
            var bezierTo1 = cc.bezierTo(t1, typeArray);
            //var act = bezierTo1.easing(cc.easeOut(t1));
            cc.tween(ropeNode).then(bezierTo1).promise()
            await ut.wait(t1, this)
            //ropeNode.active = false
            node.Swih(`double${size}`)
            tempTarget.active = true
            ropeNode = node.Child(`double${size}`)

            targetPos3.x += target.Child('body').width * 0.45
            ropeNode.setPosition(targetPos3)
            node.setPosition(startPos2)

            //get到target移动后的位置
            targetPos = ut.convertToNodeAR(target.parent, node, this.getPosByIndex(index, BattleRoleType.MONSTER))
            cc.tween(tempTarget).to(t2, { x: targetPos.x }).promise()
            cc.tween(ropeNode).to(t2, { x: targetPos.x - target.Child('body').width * 0.15, y: targetPos.y + target.Child('body').height * 0.4 })
                .call(() => {
                    target.opacity = 255
                    cc.tween(node).removeSelf().promise()
                }).promise()
            await this.changePos(target, index, t2)

            actions = actions.filter(a => a.type != BattleLogType.CHANGE_POSITION)
            await this.handleEffect({ sender: role.Data.uid, receiver: rev, next: actions })
        }

        for (let rev in mergeMap) {
            await showOne(rev)
        }
    }
    //-----------------------------------------------

    //--------------------- 幽灵喵 ----------------------------------
    protected async playSkillReady_1022(role: cc.Node, onSkill, mergeMap) {
        let sk = role.Child('body', sp.Skeleton)
        let scales = [1, 0.8, 0.8]
        //count与敌人数量取min
        let count = Math.min(role.Data.curSkill.objects[0].count, this.monsters.length)
        ut.wait(0.8, this).then(() => { //火焰出现时机
            for (let i = 1; i <= count; i++) {
                let mountPoint = sk.getAttachedNode(`guadian_fire${i}`)
                let node = this.createSkill(role.Data.id)
                node.parent = mountPoint
                let animIndexes = ut.randomArray(["", 2, 3, 4])
                let animName = `animation${animIndexes[0]}`
                node.Component(sp.Skeleton).playAnimation(animName, true)
                node.scale = 0
                // node.y = -50
                cc.tween(node).to(0.2, { scale: scales[i - 1], y: 0 }).start()
            }
        }).catch(err => console.error(err))
        await this.playSkillReadyCommon(role, onSkill)
    }

    protected async playSkill_1022(role: cc.Node, target: cc.Node, action: BattleLog) {
        if (action.type != BattleLogType.HIT) {
            return
        }

        let sk = role.Child('body', sp.Skeleton)
        let count = role.Data.curSkill.objects[0].count
        let node: cc.Node = null
        for (let i = 1; i <= count; i++) {
            let mountPoint = sk.getAttachedNode(`guadian_fire${i}`)
            node = mountPoint.children[0]
            if (!!node) {
                break
            }
        }
        if (!node) return
        let pos = ut.convertToNodeAR(node, this.skillRoot)
        node.parent = this.skillRoot
        node.setPosition(pos)
        if (target.Data.type == BattleRoleType.PASSENGER) {
            node.scaleX = -1
        }

        let targetPos = ut.convertToNodeAR(target, node.parent, this.getRoleCenter(target))
        let speed = 2400
        let dis = pos.sub(targetPos).len()
        await cc.tween(node).to(dis / speed, { x: targetPos.x, y: targetPos.y, scale: 1.5 }).removeSelf().promise()
    }
    //-------------------------------------------------------

    //-------------------------- 修女喵 -------------------------------------
    protected async playSkillReady_1023(role: cc.Node, onSkill, mergeMap) {
        let skill = this.createSkill(role.Data.id)
        skill.parent = role
        let sk = skill.Component(sp.Skeleton)
        let anim = "animation"
        sk.setEventListener(({ animation }, { data, time }) => {
            if (animation.name == anim && data.name == "effect") {
                sk.setEventListener(null)
                onSkill()
                onSkill = null
            }
        })
        await sk.playAnimation(anim)
    }
    //----------------------------------------------------------------------

    //-------------------------- 神灯喵 -------------------------------------
    protected async playAddBuff_1024(role: cc.Node, target: cc.Node, action) {
        let { type, times, skillId, skillType } = action.data
        let data = role.Data
        if (type != BattleSkillEffectType.IMMUNE_DAMAGE) {
            return
        }

        if (data.times) {
            data.times += times
            return
        }
        role.Data.times = times

        let body = role.Child('body')
        let skill = this.createSkill(role.Data.id)
        role.insertChild(skill, body.getSiblingIndex())
        skill.scaleX = body.scaleX

        this.moveAttachedNode(body, skill)

        await Promise.all([
            body.Component(sp.Skeleton).playAnimation(PassengerBattleAnimation.SKILL),
            skill.Component(sp.Skeleton).playAnimation(PassengerBattleAnimation.SKILL)
        ])

        role.clearCacheField()
        body.name = "skill"
        skill.name = "body"

        role.Child('skill').active = false

        this.resetAnim(role)
    }

    protected async playRemoveBuff_1024(role: cc.Node, action: BattleLog) {
        let { type, times, skillId, skillType } = action.data
        if (type != BattleSkillEffectType.IMMUNE_DAMAGE) {
            return
        }
        let data = role.Data

        data.times--
        if (data.times != 0) return

        let skill = role.Child('skill')
        let body = role.Child('body')
        let sk = body.Component(sp.Skeleton)
        skill.active = true

        this.moveAttachedNode(body, skill)

        role.clearCacheField()
        body.name = "skill"
        skill.name = "body"

        await Promise.all([
            body.Component(sp.Skeleton).playAnimation(PassengerBattleAnimation.SKILL + "2"),
            skill.Component(sp.Skeleton).playAnimation(PassengerBattleAnimation.SKILL + "2")
        ])

        role.getChildByName("skill").destroy()

        this.resetAnim(role)
    }
    //----------------------------------------------------------------------

    //-------------------------- 凤凰 -------------------------------------
    //死亡之后释放非召唤类的技能都可以走这个
    protected async playSkillReadyDeathSkillCommon(role: cc.Node, onSkill, mergeMap) {
        let body = role.Child('body')
        let sk = body.Component(sp.Skeleton)
        sk.paused = false
        onSkill()
        let dur = sk.getAnimationDuration(sk.animation)
        let pass = sk.getAnimationTime()
        let time = dur - pass
        await cc.tween(body).to(time, { opacity: 0 }).promise()
    }
    //----------------------------------------------------------------------

    //-------------------------- 机器喵 -------------------------------------
    protected async showSkill_1043(role: cc.Node, mergeMap) {
        let receiver = Object.keys(mergeMap)[0]
        let action = mergeMap[receiver][0]
        let fromUid = action.data.from
        let fromNode = this.getRole(fromUid)
        await this.playSkillCommon(fromNode, role, action, false)
        // role.Data.isGainBySelf = true
        // let skillReadyEnd
        // await new Promise((onSkill) => {
        //     skillReadyEnd = this.playSkillReadyCommon(role, onSkill)
        // })

        let sender = role.Data.uid
        await this.handleEffect({ fromUid, receiver, next: mergeMap[receiver] })
        // return skillReadyEnd
    }
    //----------------------------------------------------------------------

    //海盗蜜蜂
    protected async playSkill_2002(role: cc.Node, target: cc.Node) {
        let skill = this.createSkill(role.Data.id)
        if (!skill) return
        skill.parent = this.skillRoot
        if (target.Data.type == BattleRoleType.PASSENGER) {
            skill.scaleX = -1
        }
        let startPos = ut.convertToNodeAR(role, skill.parent)
        skill.setPosition(startPos)
        let targetPos = ut.convertToNodeAR(target, skill.parent)
        let dur = skill.Component(sp.Skeleton).getAnimationDuration("animation")
        await cc.tween(skill).to(dur, { x: targetPos.x }).promise()
        cc.tween(skill).to(0.15, { opacity: 0 }).removeSelf().promise()
    }


    //---------------------- 乌龟 凤凰 -----------------------
    protected async playAddBuff_2009(role: cc.Node, target: cc.Node, action) {
        let { data } = action
        let { type, times, skillId, skillType } = data
        if (type != BattleSkillEffectType.IMMUNE_DAMAGE) {
            return
        }

        let cfg = this.getSkillCfg(role)
        let mountPoint = cfg?.buffMountPoint || "guadian_wugui"
        let buffRoot = target.Child("body", sp.Skeleton).getAttachedNode(mountPoint)
        if (!buffRoot) {
            console.error("guadian_wugui not found ", target.Data.id)
            return
        }
        let skill = this._checkAddBuff(target, action, buffRoot)
        if (skill) {
            return
        }
        skill = this.createSkill(role.Data.id)
        if (!skill) return
        if (target.Data.type == BattleRoleType.MONSTER) {
            skill.scaleX = -1
        }
        skill.parent = buffRoot

        let sk = skill.Component(sp.Skeleton)
        await sk.playAnimation("animation")
        sk.playAnimation("animation2", true)
        skill.Data = { sender: role.Data.uid, times, type, skillId, skillType }

        skill.scale = this.getSkillScale(target)
    }

    protected async playRemoveBuff_2009(role: cc.Node, action, cfg) {
        let { data } = action
        let { type, times, skillId, skillType } = data
        if (type != BattleSkillEffectType.IMMUNE_DAMAGE) {
            return
        }

        let mountPoint = cfg?.buffMountPoint || "guadian_wugui"
        let buffRoot = role.Child("body", sp.Skeleton).getAttachedNode(mountPoint)
        if (!buffRoot) return
        let buff = this._checkRemoveBuff(role, action, buffRoot)
        if (!buff) return
        if (buff.Data.times == 0) {
            let sk = buff.Component(sp.Skeleton)
            let mountPointCmpt = buff.Component(MountPointCmpt)
            if (mountPointCmpt) {
                mountPointCmpt.getPoint('lizi')?.Component(cc.ParticleSystem)?.stopSystem()
                mountPointCmpt.getPoint('lizi2')?.Component(cc.ParticleSystem)?.stopSystem()
            }
            await sk.playAnimation("animation3")
            buff.parent = null
            buff.destroy()
        }
    }

    //-------------------------------------------------

    //-------------------- 猎豹 ------------------------
    protected async playSkill_2010(role: cc.Node, target: cc.Node) {
        let skill = this.createSkill(role.Data.id)
        if (!skill) return
        skill.parent = this.skillRoot
        skill.setPosition(ut.convertToNodeAR(target, this.skillRoot, this.getRoleCenter(target)))
        if (target.Data.type == BattleRoleType.PASSENGER) {
            skill.scaleX = -1
        }
        let sk = skill.Component(sp.Skeleton)
        let anim = "animation"
        let dur = sk.getAnimationDuration(anim) * 0.17
        sk.playAnimation(anim).then(() => {
            skill.destroy()
        })
        return ut.wait(dur, this)
    }
    //--------------------------------------------

    //----------------- 大猩猩 -----------------
    protected async playSkillReady_2012(role: cc.Node, onSkill) {
        let stage = role.Data.stage
        let anim
        if (stage) {
            anim = PassengerBattleAnimation.SKILL + "2"
        }
        await this.playSkillReadyCommon(role, onSkill, null, anim)
        if (!stage) {
            role.Data.stage = 1
            role.Child("body", sp.Skeleton).setSkin("plus")
        }
    }
    //---------------------------------------------------------------

    //----------------- 小鸡 -----------------
    protected async playSkillReady_2015(role: cc.Node, onSkill) {
        let stage = role.Data.stage
        if (!stage) {
            role.Data.stage = 2
        }
        await this.playSkillReadyCommon(role, onSkill)
    }
    //----------------------------------------------------

    //----------------- 青蛙 -----------------
    protected async playAddBuff_2018(role: cc.Node, target: cc.Node, action) {
        let { data } = action
        let { type, times, skillId, skillType } = data
        if (type != BattleSkillEffectType.IMMUNE_DAMAGE) {
            return
        }
        if (!role.Data.times) {
            role.Data.times = 0
        }
        if (role.Data.times == 0) {
            let sk = role.Child('body', sp.Skeleton)
            sk.mix(PassengerBattleAnimation.ATTACK_IDLE, PassengerBattleAnimation.ATTACK_READY, 0)
            sk.setSkin('tk')
        }

        role.Data.times += times
    }

    protected async playRemoveBuff_2018(role: cc.Node, action) {
        let { data } = action
        let { type, times, skillId, skillType } = data
        if (type != BattleSkillEffectType.IMMUNE_DAMAGE) {
            return
        }
        if (role.Data.times > 0) {
            role.Data.times--
        }
        if (role.Data.times <= 0) {
            let sk = role.Child('body', sp.Skeleton)
            sk.mix(PassengerBattleAnimation.ATTACK_IDLE, PassengerBattleAnimation.ATTACK_READY, 0.15)
            sk.setSkin('default')
        }
    }
    //----------------------------------------------------

    //----------------- 鹦鹉 -----------------
    protected async showSkill_2019(role: cc.Node, mergeMap) {
        let skill = this.createSkill(role.Data.id)
        if (!skill) return
        skill.parent = this.sendSkillRoot
        let skillSk = skill.Component(sp.Skeleton)
        skillSk.playAnimation("animation")

        let receiver = Object.keys(mergeMap)[0]
        let actions = mergeMap[receiver]
        let action = mergeMap[receiver][0]
        let fromUid = action.data.from
        let fromNode = this.getRole(fromUid)
        let startPos = ut.convertToNodeAR(fromNode, skill.parent, this.getRoleCenter(fromNode))
        skill.setPosition(startPos)
        let endPos = ut.convertToNodeAR(role, skill.parent, cc.v2(0, 500))

        let offsetX = 500, offsetY = 50
        if (endPos.x < startPos.x) {
            skill.scaleX = -1
            offsetX = -offsetX
        }

        let speed = 600
        let c2 = cc.v2(startPos.x, endPos.y)
        let time = Math.abs(startPos.x - endPos.x) / speed
        await cc.tween(skill).bezierTo(time, startPos, c2, endPos).promise()
        skillSk.playAnimation("animation2")
        let heart = skillSk.getAttachedNode("aixin").Child("heart")
        heart.active = true
        ut.convertParent(heart, skill.parent)

        let p1 = (async () => {
            let heartEndPos = ut.convertToNodeAR(role, skill.parent, this.getRoleCenter(role))
            await cc.tween(heart).to(0.25, { x: heartEndPos.x, y: heartEndPos.y }).removeSelf().promise()
            role.Data.isGainBySelf = true
            let skillReadyEnd
            await new Promise((onSkill) => {
                skillReadyEnd = this.playSkillReadyCommon(role, onSkill)
            })
            let sender = role.Data.uid
            await this.handleEffect({ sender, receiver, next: actions })
            return skillReadyEnd
        })()

        time = Math.abs(offsetX) / speed
        let p2 = ut.wait(time, this)
        cc.tween(skill).by(time, { x: offsetX, y: offsetY }).promise()
        cc.tween(skill).delay(time * 0.2).to(time * 0.8, { opacity: 0 }).removeSelf().promise()

        return Promise.all([p1, p2])
    }
    //----------------------------------------------------

    //-------------------- 蜘蛛 -------------------------
    protected async playSkillReady_2034(role: cc.Node, onSkill) {
        let sk = role.Child('body', sp.Skeleton)
        onSkill()
        await sk.playAnimation(PassengerBattleAnimation.SKILL)
        this.resetAnim(role)
    }

    protected async showSkill_2034(role: cc.Node, mergeMap) {
        let sk = role.Child('body', sp.Skeleton)

        let anim = PassengerBattleAnimation.SKILL
        let pullStartTime = sk.getEvent(anim, "effect")?.time || 0
        let pullEndTime = sk.getEvent(anim, "effect2")?.time || 0
        let sendDur = Math.max(0, pullStartTime - sk.getAnimationTime())

        let showOne = async (receiver) => {
            let mountPoint = this.getSkillMountPoint(role)
            let skill = this.createSkill(role.Data.id)
            skill.parent = mountPoint
            let si = skill.Child('si')
            let wang = skill.Child('wang')

            let target = this.getRole(receiver)
            let targetPos = ut.convertToNodeAR(target, mountPoint, this.getRoleCenter(target))
            let offset = cc.v2(30, 43)
            let vec = targetPos
            let width = vec.mag() + offset.x
            si.angle = vec.xAngle()
            si.width = 0

            await cc.tween(si).to(sendDur, { width }).promise()
            wang.active = true
            let anchorPos = ut.convertToNodeAR(si, wang.parent, cc.v2(width - offset.x, offset.y))
            wang.setPosition(anchorPos.sub(wang.Child("anchor").getPosition()))

            let pullDur = Math.max(sk.getAnimationDuration(anim) - sk.getAnimationTime(), pullEndTime - pullStartTime)
            let actions = mergeMap[receiver]
            let changePosAction = actions.find(a => a.type == BattleLogType.CHANGE_POSITION)
            let index = changePosAction.data.index - 1
            let pos = this.getPosByIndex(index, target.Data.type, target)
            let netxVec = ut.convertToNodeAR(target.parent, mountPoint, pos.add(this.getRoleCenter(target)))
            let disX = netxVec.mag() - vec.mag()
            cc.tween(si).to(pullDur, { width: width + disX, angle: netxVec.xAngle() }).call(() => {
                skill.destroy()
            }).promise()
            cc.tween(wang).progress(pullDur, () => {
                let anchorPos = ut.convertToNodeAR(si, wang.parent, cc.v2(si.width - offset.x, offset.y))
                wang.setPosition(anchorPos.sub(wang.Child("anchor").getPosition()))
            }).promise()
            // await this.handleChangePos(changePosAction, pullDur)
            await this.changePos(target, index, pullDur)

            actions = actions.filter(a => a.type != BattleLogType.CHANGE_POSITION)
            await this.handleEffect({ sender: role.Data.uid, receiver, next: actions })
            // let hitAction = actions.find(a => a.type == BattleLogType.HIT)
            // if (hitAction) {
            //     await this.handleHit(hitAction)
            // }
        }
        let pList = []
        for (let receiver in mergeMap) {
            let p = showOne(receiver)
            pList.push(p)
        }
        await Promise.all(pList)
    }
    //--------------------------------------------------

    //---------------------- 机械星Boss -------------------------
    protected async playSkillReady_2060(role: cc.Node, onSkill) {
        let stage = role.Data.stage || 1
        let anim = PassengerBattleAnimation.SKILL + stage
        await this.playSkillReadyCommon(role, onSkill, null, anim)
        role.Data.stage = stage + 1
        role.Child("body", sp.Skeleton).setSkin("skin_0" + role.Data.stage)
    }
    //----------------------------------------------------------

    //---------------------- 魔眼Boss -------------------------
    protected async showSkill_2061(role: cc.Node, mergeMap) {
        let body = role.Child("body")
        let sk = body.Component(sp.Skeleton)
        let anim = PassengerBattleAnimation.SKILL
        let dur = sk.getAnimationDuration(anim)
        let time = sk.getEvent(anim).time
        let animEnd = sk.playAnimation(anim)
        await ut.wait(time, this)

        let roleData = role.Data

        let receiver = Object.keys(mergeMap)[0]
        let action = mergeMap[receiver][0]
        let copyId = action.data.copyId
        let copyRole = this.createRole({ id: copyId, uid: roleData.uid, hp: roleData.hp, attack: roleData.attack, lv: roleData.lv, starLv: roleData.starLv, type: roleData.type })
        let copyNode = this.createRoleNode(copyRole)
        let copyBody = copyNode.Child("body")
        let copyUI = copyNode.Child("ui")

        let initCopyNode = () => {
            copyUI.active = false
            let copyBody = copyNode.Child("body")
            let anchorY = 400 / copyBody.height
            let anchorX = (copyBody.width + 100 * copyBody.scaleX) / copyBody.width
            copyNode.anchorY = anchorY
            copyNode.anchorX = anchorX
            let offsetY = copyNode.anchorY * copyBody.height
            let offsetX = (copyNode.anchorX - 0.5) * copyBody.width
            copyNode.x = role.x + offsetX
            copyNode.y = role.y + offsetY
            copyBody.y = -offsetY
            copyBody.x = -offsetX
            copyNode.zIndex = role.zIndex + 1
        }
        initCopyNode()

        copyNode.scale = 0
        copyNode.opacity = 0
        await cc.tween(copyNode).to(dur - time, { scale: 1, opacity: 255 }).promise()

        copyBody.y = 0
        copyBody.x = 0
        copyNode.anchorX = 0
        copyNode.anchorY = 0
        copyNode.setPosition(role.getPosition())
        copyNode.Data.isCopy = true
        copyUI.active = true

        role.active = false

        // role.clearCacheField()
        // body.parent = null
        // copyBody.name = "body"
        // role.Data = copyRole
        // role.Data.isCopy = true
        // copyNode.active = false

        this.replaceRoleNode(role, copyNode)

        if (action.data.isDeath) {
            await this.playDeath(copyNode, BattleDeathType.DEATH)
        }
        await this.handleUseSkill(action.next[0])

        await animEnd

        role.active = true
        roleData.hp = copyRole.hp
        roleData.attack = copyRole.attack
        this.changeHp(role)
        this.changeAttack(role)
        this.replaceRoleNode(copyNode, role)
        role.x = copyNode.x

        // role.Data = roleData
        // role.clearCacheField()
        // role.insertChild(body, copyBody.getSiblingIndex())

        let p = sk.playBackAnimation(anim)

        if (!action.data.isDeath) {
            initCopyNode()
            await cc.tween(copyNode).to(dur - time, { scale: 0, opacity: 0 }).promise()
        }
        copyNode.destroy()

        await p
        this.resetAnim(role)
    }
    //----------------------------------------------------------

    //---------------------- 雪星Boss -------------------------
    protected async showSkill_2063(role: cc.Node, mergeMap) {
        let cfg = this.getSkillCfg(role)
        let skill = this.createSkill(role.Data.id)
        if (!skill) return
        let mountPoint = this.getSkillMountPoint(role)
        if (!mountPoint) return
        skill.parent = this.skillRoot
        skill.setPosition(ut.convertToNodeAR(mountPoint, this.skillRoot))
        let receiver = Object.keys(mergeMap)[0]
        let target = this.getRole(receiver)
        let targetPos = ut.convertToNodeAR(target, skill.parent)
        let speed = cfg.skill?.speed || 1200
        let dirX = targetPos.x - skill.x
        skill.scaleX = role.scaleX
        if (dirX < 0) {
            skill.scaleX = -skill.scaleX
        }
        let targetX = cc.winSize.width * 0.5 * ut.normalizeNumber(skill.scaleX)
        let dis = Math.abs(targetX - skill.x)
        let pList = []
        let p = cc.tween(skill).to(dis / speed, { x: targetX }).removeSelf().promise()
        pList.push(p)
        let handleEffect = async (receiver) => {
            let target = this.getRole(receiver)
            let targetPos = ut.convertToNodeAR(target, skill.parent, this.getRoleCenter(target))
            let dis = Math.abs(targetPos.x - skill.x)
            let time = dis / speed
            await ut.wait(time, this)
            return this.handleEffect({ sender: role.Data.uid, receiver, next: mergeMap[receiver] })
        }
        for (let receiver in mergeMap) {
            pList.push(handleEffect(receiver))
        }

        return Promise.all(pList)
    }
    //----------------------------------------------------------

    //---------------------- 宝石星Boss -------------------------
    protected async showSkill_2064(role: cc.Node, mergeMap) {
        let cfg = this.getSkillCfg(role)
        let receiver = Object.keys(mergeMap)[0]
        let target = this.getRole(receiver)
        let body = role.Child("body")
        let sk = body.Component(sp.Skeleton)
        let skillId = 1
        await sk.playAnimation(`${PassengerBattleAnimation.SKILL}_${skillId}_start`)
        sk.playAnimation(`${PassengerBattleAnimation.SKILL}_${skillId}_loop`, true)

        let targetPos = ut.convertToNodeAR(target, body.parent)
        let offsetX = body.width / 2
        if (targetPos.x > body.x) {
            targetPos.x -= offsetX
        }
        else {
            targetPos.x += offsetX
        }
        let dis = Math.abs(targetPos.x - body.x)
        let speed = 1600
        let time = dis / speed
        await cc.tween(body).to(time, { x: targetPos.x }).promise()
        body.x = 0

        let p1 = this.handleEffect({ sender: role.Data.uid, receiver, next: mergeMap[receiver] })
        let p2 = sk.playAnimation(`${PassengerBattleAnimation.SKILL}_${skillId}_end`)
        p2.then(()=>{
            this.resetAnim(role)
        })

        return Promise.all([p1, p2])
    }
    //----------------------------------------------------------

    //---------------------- 甜甜圈Boss -------------------------
    protected async playSkillReady_2066(role: cc.Node, onSkill) {
        let curSkill = role.Data.curSkill
        let skill = this.createSkill(curSkill.getId(), curSkill.getType())
        role.Data.isGainBySelf = true
        let body = role.Child("body")

        let orgSk = body.Component(sp.Skeleton)
        let time = orgSk.getEvent(PassengerBattleAnimation.SKILL)?.time || 0
        orgSk.playAnimation(PassengerBattleAnimation.SKILL)
        await ut.wait(time, this)  

        //大骨骼出现
        skill.active = true
        role.insertChild(skill, body.getSiblingIndex())
        skill.scaleX = body.scaleX
        this.moveAttachedNode(body, skill)
        await skill.Component(sp.Skeleton).playAnimation(PassengerBattleAnimation.SKILL)

        role.clearCacheField()
        body.name = "skill"
        skill.name = "body"
        role.getChildByName('skill').active = false

        this.resetAnim(role)

        onSkill()
    }

    protected async playRemoveBuff_2066(role: cc.Node, action) {
        let skill = role.Child('skill')
        let body = role.Child('body')
        let skillSk = skill.Component(sp.Skeleton)

        let anim = PassengerBattleAnimation.SKILL
        let orgSk = body.Component(sp.Skeleton)
        let time = orgSk.getAnimationDuration(anim) - (skillSk.getAnimationDuration(anim) - (skillSk.getEvent(anim)?.time || 0))
        orgSk.playBackAnimation(PassengerBattleAnimation.SKILL)
        await ut.wait(time, this)  

        //小骨骼出现
        skill.active = true
        skill.scaleX = body.scaleX
        this.moveAttachedNode(body, skill)
        role.clearCacheField()
        body.name = "skill"
        skill.name = "body"
        await skill.Component(sp.Skeleton).playBackAnimation(anim)

        role.getChildByName("skill").destroy()

        this.resetAnim(role)
    }
    //----------------------------------------------------------


     //------------- 狂月boss -----------------
     protected async playAddBuff_2068_1(role: cc.Node, target: cc.Node, action) {
        let { data } = action
        let { type, times, skillId, skillType } = data

        let buffRoot: cc.Node = this.getSkillMountPoint(target)
        if (!buffRoot) {
            console.error("playAddBuff_2068 not found ", target.Data.id)
            return
        }
        let skillNode = this.createSkill(role.Data.id)
        if (!skillNode) return
       
        let add = (parent, anim)=>{
            let skill = cc.instantiate2(skillNode.Swih("2")[0], parent)
            skill.name = skillNode.name
            let sk = skill.Component(sp.Skeleton)
            sk.playAnimation(anim, true)
            skill.Data = { sender: role.Data.uid, times, type, skillId, skillType }

            let pos = cc.v2()
            let zero = cc.v2()
            sk.scheduleUpdate(()=>{
                ut.convertToNodeAR(buffRoot, skill.parent, zero, pos)
                skill.setPosition(pos)
            })
        }

        add(target.Child("effect_back"), "animation_hou")
        add(target.Child("effect_front"), "animation_qian")

        skillNode.destroy()
    }

    protected async playRemoveBuff_2068_1(role: cc.Node, action, cfg) {
        let { data } = action

        let remove = (parent)=>{
            let buffNode = parent.Child("skill_2068")
            let data = buffNode.Data
            data.times--
            if (data.times <= 0) {
                buffNode.param = null
                buffNode.destroy()
            }
        }

        remove(role.Child("effect_back"))
        remove(role.Child("effect_front"))
    }

    protected async playAddBuff_2068(role: cc.Node, target: cc.Node, action) {
        let { data } = action
        let { type, times, skillId, skillType } = data

        let cfg = this.getSkillCfg(role)
        let mountPoint = cfg?.buffMountPoint || "buff"
        let _sk = target.Child("body", sp.Skeleton)
        let buffRoot: cc.Node = _sk.getAttachedNode(mountPoint) || _sk.getAttachedNode("zhixin")
        if (!buffRoot) {
            console.error("playAddBuff_2068 not found ", target.Data.id)
            return
        }
        let skill = this._checkAddBuff(target, action, buffRoot)
        if (skill) return
        let skillNode = this.createSkill(role.Data.id)
        if (!skillNode) return
        skill = skillNode.Swih("1")[0]
        skill.parent = buffRoot
        skillNode.destroy()
        this.fixedMountPointAngle(skill, buffRoot, target)

        let scale = this.getSkillScale(target)
        skill.scaleX *= scale
        skill.scaleY *= scale

        let sk = skill.Component(sp.Skeleton)
        sk.playAnimation("jingzhi", true)
        skill.Data = { sender: role.Data.uid, times, type, skillId, skillType }
    }

    protected async playBuffSkillReady_2068(role: cc.Node, cfg: BattleSkillViewCfg, onSkill, mergeMap) {
        let mountPoint = "buff"
        let _sk = role.Child("body", sp.Skeleton)
        let buffRoot: cc.Node = _sk.getAttachedNode(mountPoint) || _sk.getAttachedNode("zhixin")
        if (!buffRoot) {
            console.error("playAddBuff_2068 not found ", role.Data.id)
            return
        }
        let buffNode: cc.Node = buffRoot.children.find(s => s.Data?.skillId == 2068 && s.Data?.skillType == SkillType.BATTLE)
        if (!buffNode) return

        let sk = buffNode.Component(sp.Skeleton)
        let animName = "animation"
        ut.wait(sk.getEventTime(animName), this).then(()=>{
            onSkill()
        })
        await sk.playAnimation(animName)
        buffNode.destroy()
    }
    //---------------------------------------

    //---------- 神灵遗迹小怪2
    protected async showSkill_2080(role: cc.Node, mergeMap) {
        let body = role.Child("body")
        let sk = body.Component(sp.Skeleton)

        let opacityTime = 0.3
        let show = async(target: cc.Node)=>{
            body.opacity = 255
            await cc.tween(body).to(opacityTime, {opacity: 0}).delay(0.1).promise()

            let pos = ut.convertToNodeAR(target, body.parent)
            body.setPosition(pos)

            cc.tween(body).to(opacityTime, {opacity: 255}).start()
        }

        let hide = async()=>{
            await cc.tween(body).to(opacityTime, {opacity: 0}).promise()

            body.setPosition(cc.v2())

            cc.tween(body).to(opacityTime, {opacity: 255}).start()
        }

        let showOne = async (receiver) => {
            let target = this.getRole(receiver)
            await show(target)

            let anim = PassengerBattleAnimation.SKILL
            let time = sk.getEventTime(anim)
            sk.playAnimation(anim).then(()=>{
                this.resetAnim(role)
            })
            await ut.wait(time, this)

            let actions = mergeMap[receiver]
            let rebirthAction = actions.find(a => a.type == BattleLogType.REBIRTH)
            let p1 = this.handleRebirth(rebirthAction)

            actions = actions.filter(a => a != rebirthAction)
            let p2 = this.handleEffect({ sender: role.Data.uid, receiver, next: actions })

            await Promise.all([
                p1, p2, hide()
            ])
        }
        let pList = []
        for (let receiver in mergeMap) {
            let p = showOne(receiver)
            pList.push(p)
        }
        await Promise.all(pList)
    }
    //---------------------------------

    //----------------------------- 副本 ------------------------------------------
    protected async playSkill_Instance(role: cc.Node, target: cc.Node) {
        let cfg = this.getSkillCfg(role)
        let curSkill = role.Data.curSkill
        let skill = this.createSkill(curSkill.getId(), curSkill.getType())
        if (!skill) return

        let mountPoint = "tou"
        let buffRoot: cc.Node = target.Child("body", sp.Skeleton).getAttachedNode(mountPoint)
        skill.parent = this.effectRoot

        let sk = skill.Component(sp.Skeleton)
        let y = 250
        let pos = ut.convertToNodeAR(buffRoot, this.effectRoot)
        pos.y += y
        skill.setPosition(pos)
        sk.setSkin(cfg.skill.skin)
        await sk.playAnimation("animation")
        skill.destroy()
    }
    //---------------------------------------------------------------------------

    //----------------------------- 装备 ------------------------------------------
    protected playSKill_EquipDamage(role: cc.Node, target: cc.Node) {
        let cfg = this.getSkillCfg(role)
        let curSkill = role.Data.curSkill
        let skill = this.createSkill(curSkill.getId(), curSkill.getType())
        if (!skill) return
        let mountPoint = eventCenter.get(NodeType.GUIDE_BATTLE_EQUIP)
        skill.parent = this.skillRoot
        skill.setPosition(ut.convertToNodeAR(mountPoint, this.skillRoot))
        return this.playStraightLineAnim(skill, target, cfg)
    }

    protected playSKill_EquipGain(role: cc.Node, target: cc.Node) {
        let cfg = this.getSkillCfg(role)
        let curSkill = role.Data.curSkill
        let skill = this.createSkill(curSkill.getId(), curSkill.getType())
        if (!skill) return
        let mountPoint = eventCenter.get(NodeType.GUIDE_BATTLE_EQUIP)
        skill.parent = this.skillRoot
        skill.setPosition(ut.convertToNodeAR(mountPoint, this.skillRoot))
        return this.playStraightLineAnim(skill, target, cfg)
    }

    protected async showSKill_Equip1012(role: cc.Node, mergeMap, action?: BattleLog) {
        let {skillId, skillType} = action.data
        let mountPoint = eventCenter.get(NodeType.GUIDE_BATTLE_EQUIP)

        let showOne = async (receiver) => {
            let skill = this.createSkill(skillId, skillType)
            skill.parent = this.skillRoot
            skill.setPosition(ut.convertToNodeAR(mountPoint, this.skillRoot))
            let si = skill.Child('si')
            let wang = skill.Child('wang')

            let target = this.getRole(receiver)
            let targetPos = ut.convertToNodeAR(target, skill.parent, this.getRoleCenter(target))
            let vec = targetPos.sub(skill.getPosition())
            let width = vec.mag()
            si.angle = vec.xAngle()
            si.width = 0
            let speed = 2400
            let sendDur = width / speed

            await cc.tween(si).to(sendDur, { width }).promise()
            wang.active = true
            let anchorPos = ut.convertToNodeAR(si, wang.parent, cc.v2(width, 0))
            wang.setPosition(anchorPos.sub(wang.Child("anchor").getPosition()))

            let actions = mergeMap[receiver]
            let changePosAction = actions.find(a => a.type == BattleLogType.CHANGE_POSITION)
            let index = changePosAction.data.index - 1
            let pos = this.getPosByIndex(index, target.Data.type, target)
            let netxVec = ut.convertToNodeAR(target.parent, skill.parent, pos.add(this.getRoleCenter(target))).sub(skill.getPosition())
            let disX = netxVec.mag() - vec.mag()
            let pullDur = Math.abs(disX) / 1000
            cc.tween(si).to(pullDur, { width: width + disX, angle: netxVec.xAngle() }).call(() => {
                skill.destroy()
            }).promise()
            cc.tween(wang).progress(pullDur, () => {
                let anchorPos = ut.convertToNodeAR(si, wang.parent, cc.v2(si.width, 0))
                wang.setPosition(anchorPos.sub(wang.Child("anchor").getPosition()))
            }).promise()
            await this.changePos(target, index, pullDur)

            actions = actions.filter(a => a.type != BattleLogType.CHANGE_POSITION)
            await this.handleEffect({ sender: role.Data.uid, receiver, next: actions })
        }
        let pList = []
        for (let receiver in mergeMap) {
            let p = showOne(receiver)
            pList.push(p)
        }
        await Promise.all(pList)
    }

    protected async playAddBuff_Equip(role: cc.Node, target: cc.Node, action: BattleLog) {
        let { type, times, skillId, value, skillType } = action.data
        let buffsRoot = target.Child('body', sp.Skeleton).getAttachedNode('zhixin')
        let buffNode = this._checkAddBuff(target, action, buffsRoot)
        if (buffNode) return
        buffNode = this.createSkill(skillId, skillType)
        buffNode.parent = buffsRoot
        buffNode.Data = { times, type, skillId, skillType }
    }
    //----------------------------------------------------------------------------

    //----------------------- 列车 -----------------------------------------------
    protected playSKill_TrainDamage(role: cc.Node, target: cc.Node) {
        let cfg = this.getSkillCfg(role)
        let curSkill = role.Data.curSkill
        let skill = this.createSkill(curSkill.getId(), curSkill.getType())
        if (!skill) return
        let node = this.getRole(role.Data.uid)
        let mountPoint = node.Child("body/train_head_theme_1", MountPointCmpt).getPoint("fish")
        skill.parent = this.skillRoot
        skill.setPosition(ut.convertToNodeAR(mountPoint, this.skillRoot))
        return this.playStraightLineAnim(skill, target, cfg)
    }
    //---------------------------------------------------------------------------

    private async loadSkill(id: number | string, type: SkillType = SkillType.BATTLE) {
        let tag = this.getTag()
        let url = "battle/skill/"
        if (type == SkillType.BATTLE) {
            url += `role/skill_${id}`
        }
        else if (type == SkillType.BLACKHOLE_EQUIP) {
            url += `tech/tech_skill_${id}`
        }
        else if (type == SkillType.EQUIP) {
            url += `equip/equip_skill_${id}`
        }
        else if (type == SkillType.INSTANCE) {
            url += `instance/instance_skill`
        }
        else if (type == SkillType.TRAIN) {
            url += `train/train_skill_${id}`
        }
        let pfb = await assetsMgr.loadTempRes(url, cc.Prefab, tag)
        if (!cc.isValid(this)) {
            return assetsMgr.releaseTempRes(url, tag)
        }
        return pfb
    }

    private createSkill(id: number, type: SkillType = SkillType.BATTLE, childName?: string): cc.Node {
        let key = `${id}-${type}`
        let node = this.skillMap[key]
        if (!node) {
            console.error("createSkill not found", key)
            return
        }
        if (node instanceof cc.Prefab) {
            node = cc.instantiate(node)
            this.skillMap[key] = node
        }
        if (childName) {
            let child = node.getChildByName(childName)
            if (child) {
                return cc.instantiate(child)
            }
        }
        return cc.instantiate(node)
    }

    //丢技能
    private async playSkillCommon(role: cc.Node, target: cc.Node, action: BattleLog, useMountPoint: boolean = true) {
        let type = action.type
        let skillType = 0
        if (this.isBehind(role, target)) {
            skillType = 1
        }

        let skill = this.getSkill(role, skillType, useMountPoint)
        let bg = skill.Child('bg')
        let iconName

        if (type == BattleLogType.HIT) {
            iconName = 'hit'
            bg.Swih(iconName)
        }
        else {
            iconName = 'props'
            let skin
            if (type == BattleLogType.CHANGE_HP) {
                skin = 'hp'
            }
            else if (type == BattleLogType.CHANGE_ATTACK) {
                skin = 'atk'
            }
            else if (type == BattleLogType.CHANGE_HP_ATTACK) {
                skin = 'all'
            }
            else {
                skin = 'all'
            }
            let node = bg.Swih(iconName)[0]
            let sk = node.Component(sp.Skeleton)
            sk.setSkin(skin)
            sk.playAnimation("animation").then(() => {
                sk.playAnimation("jingzhi")
            })
        }

        //技能图片飞
        return this.playCurveSkillAnim(skill, target, skillType)
    }

    //抛物线类技能
    private async playCurveSkillAnim(skill: cc.Node, target: cc.Node, skillType = 0, isPool = true) {
        let targetPos = this.getRevSkillPos(target)
        let curPos = skill.getPosition()
        let time = 0.8
        let base = 1
        // let p1 = cc.tween(skill).to(time * 0.5, { scale: 1 * base }).to(time * 0.5, { scale: 1 * base }).promise()
        let leftBound = this.getPosByIndex(5, BattleRoleType.PASSENGER).x
        let rightBound = this.getPosByIndex(5, BattleRoleType.MONSTER).x
        let midDis = this.getPosByIndex(0, BattleRoleType.MONSTER).x - this.getPosByIndex(0, BattleRoleType.PASSENGER).x
        let maxDis = rightBound - leftBound
        let roleDis = this.getPosByIndex(0, BattleRoleType.PASSENGER).x - this.getPosByIndex(1, BattleRoleType.PASSENGER).x

        let baseH = 180 + STD_SKILL_SIZE.height
        let dis = Math.abs(targetPos.x - curPos.x)
        let ratio
        let maxY

        if (skillType == 1) { //如果是往后丢
            let minD = 0
            let maxD = (maxDis - midDis) * 0.5
            maxY = curPos.y + baseH
            ratio = (dis - minD) / (maxD - minD)
            time *= cc.misc.lerp(0.7, 0.9, cc.misc.clamp01(ratio))
        }
        else {
            let maxD = maxDis
            let minD = midDis
            ratio = (dis - minD) / (maxD - minD)
            let maxX = (curPos.x + targetPos.x) * 0.5 //极值点的x坐标
            let angle = 20 - ratio * 5
            let dy = Math.abs((maxX - curPos.x) * ut.tan(angle))
            maxY = curPos.y + dy
            // console.log("~~~~", dy)
            // console.log("rrr", ratio, angle, maxY)
            time *= cc.misc.lerp(0.4, 1, cc.misc.clamp01(ratio))
        }
        // console.log("cccc", maxY)

        let dy = targetPos.y - curPos.y
        let dx = targetPos.x - curPos.x
        let b = (8 * dy + 16 * curPos.y - 16 * maxY)
        let a = 16
        let c = dy * dy
        let h = (-b + Math.sqrt(b * b - 4 * a * c)) / (2 * a)
        if (isNaN(h)) {
            h = 100
        }
        // console.log("aaa", a, b, c)
        // console.log("bbb", time, ratio, h)
        let tween = cc.jumpTo(time, targetPos, h, 1)
        let removeSkill = () => {
            if (isPool) {
                this.putSkill(skill)
            }
        }
        let p2 = cc.tween(skill).then(tween).call(removeSkill).promise()

        let rotateByTrack = !isPool

        if (rotateByTrack) {
            let targetAngle = 0
            let preElapse = -1
            if (dx < 0) {
                skill.scaleX = -1
            }
            let update = (elapse) => {
                if (dx == 0) {
                    return
                }
                let dt = elapse - preElapse
                preElapse = elapse
                let x = cc.misc.lerp(curPos.x, targetPos.x, elapse)
                let tan = (-8 * h * x / (dx * dx)) + (8 * h * curPos.x / (dx * dx)) + ((4 * h + dy) / dx)
                let angle = cc.misc.radiansToDegrees(Math.atan(tan))
                targetAngle = angle
                skill.angle = cc.misc.lerp(skill.angle, targetAngle, cc.misc.clamp01(10 * dt))
            }
            update(0)
            cc.tween(skill).progress(time, (elapse) => {
                update(elapse)
            }).start()
        }

        await Promise.all([p2])
    }

    private getCurveSkillAnimTime(skill: cc.Node, target: cc.Node, skillType = 0) {
        let time = 0.8
        let targetPos = this.getRevSkillPos(target)
        let curPos = skill.getPosition()
        let leftBound = this.getPosByIndex(5, BattleRoleType.PASSENGER).x
        let rightBound = this.getPosByIndex(5, BattleRoleType.MONSTER).x
        let midDis = this.getPosByIndex(0, BattleRoleType.MONSTER).x - this.getPosByIndex(0, BattleRoleType.PASSENGER).x
        let maxDis = rightBound - leftBound

        let baseH = 180 + STD_SKILL_SIZE.height
        let dis = Math.abs(targetPos.x - curPos.x)
        let ratio
        let maxY

        if (skillType == 1) { //如果是往后丢
            let minD = 0
            let maxD = (maxDis - midDis) * 0.5
            maxY = curPos.y + baseH
            ratio = (dis - minD) / (maxD - minD)
            time *= cc.misc.lerp(0.7, 0.9, cc.misc.clamp01(ratio))
        }
        else {
            let maxD = maxDis
            let minD = midDis
            ratio = (dis - minD) / (maxD - minD)
            let maxX = (curPos.x + targetPos.x) * 0.5 //极值点的x坐标
            let angle = 20 - ratio * 5
            let dy = Math.abs((maxX - curPos.x) * ut.tan(angle))
            maxY = curPos.y + dy
            // console.log("~~~~", dy)
            // console.log("rrr", ratio, angle, maxY)
            time *= cc.misc.lerp(0.4, 1, cc.misc.clamp01(ratio))
        }
        return time
    }

    private fixedMountPointAngle(node: cc.Node, mountPoint: cc.Node, target: cc.Node) {
        let mat4 = cc.mat4(), quat = cc.quat(), vec3 = cc.v3()
        mountPoint.getWorldMatrix(mat4)
        cc.Mat4.toRTS(mat4, quat, vec3, cc.v3())
        cc.Quat.toEuler(vec3, quat)
        let angle = vec3.z
        let scaleX = ut.normalizeNumber(target.Child('body').scaleX)
        node.angle = -angle * scaleX //抵消掉bone本身自带的旋转
    }

    private getSkillMountPoint(role: cc.Node, name: string = "guadian_jineng") {
        let sk = role.Child("body")?.Component(sp.Skeleton)
        if (!sk) return
        let node = sk.getAttachedNode(name)
        if (!node) {
            console.error("getSkillMountPoint not found", role.Data.id, name)
        }
        return node
    }

    private async showSkillTips(role: cc.Node, skills: BattleSkill[]) {
        if (role.Data.id == 1006) {
            eventCenter.emit(EventType.GUIDE_BATTLE_SHOW_SKILL_TIPS)
            await this.waitPauseOver()
        }
        let pos = ut.convertToNodeAR(role, this.tipsRoot, cc.v2(0, STD_ROLE_BOX.height + 20))
        this.tipsRoot.active = true
        let data: BattleRole = role.Data
        viewHelper.showSkillTips(this.tipsRoot, pos, { id: data.id, lv: data.lv, skills }, this.getTag())
        let skillTime = 1.5 * skills.length
        await cc.tween(this.tipsRoot).delay(skillTime).promise()
        this.tipsRoot.active = false
    }

    private getSkill(role: cc.Node, type: number = 0, useMountPoint: boolean = true) {
        let node = this.skillPool.get() || cc.instantiate(this.skillTemplate)
        node.parent = this.skillRoot
        node.active = true
        let pos = this.getSendSkillPos(role, type, useMountPoint)
        node.setPosition(pos)
        return node
    }

    private getSendSkillPos(role: cc.Node, type: number = 0, useMountPoint: boolean = true) {
        let pos
        let body = role.Child('body')
        if (body && useMountPoint) {
            let mountPoint = body.Component(sp.Skeleton).getAttachedNode("guadian_jineng")
            if (mountPoint) {
                return ut.convertToNodeAR(mountPoint, this.skillRoot)
            }
        }

        let width = body?.width || STD_ROLE_BOX.width
        let size = STD_SKILL_SIZE
        let offsetY = this.getRoleCenter(role).y + size.height * 0.5
        let offsetX = (width - size.width) * 0.5
        if (type == 1) {
            offsetX = 0
        }
        if (role.Data.deathSkill) { //亡语状态取固定高度
            offsetY = 100
        }
        if (role.Data.type == BattleRoleType.MONSTER) {
            offsetX = -offsetX
        }
        pos = cc.v2(offsetX, offsetY)
        pos = ut.convertToNodeAR(role, this.skillRoot, pos).clone()
        return pos
    }

    private getRevSkillPos(role, icon?: cc.Node) {
        let pos
        let size = STD_SKILL_SIZE
        if (icon) {
            size = icon.getContentSize()
        }
        let offsetX = 0
        let offsetY = this.getRoleCenter(role).y + size.height * 0.5
        pos = cc.v2(offsetX, offsetY)
        pos = ut.convertToNodeAR(role, this.skillRoot, pos).clone()
        return pos
    }

    private putSkill(node) {
        node.active = false
        this.skillPool.put(node)
    }

    private moveQueue(roles, time: number = 0.3) {
        let pList = []
        for (let i = 0; i < roles.length; i++) {
            let role = roles[i]
            let pos = this.getPosByIndex(i, role.Data.type, role)
            if (role.x != pos.x || role.y != pos.y) {
                let p = cc.tween(role).to(time, { x: pos.x, y: pos.y }).promise()
                pList.push(p)
            }
            this.posMap[role.Data.type][i] = role.Data
        }
        return Promise.all(pList)
    }

    public getPosByIndex(index: number, type: BattleRoleType, role?: cc.Node) {
        let startPos = 222
        let roles = this.getRolesByType(type)

        let summonRatio = roles.length > 5 ? 0.8 : 1
        let oneWidth = 222
        let orgWidth = oneWidth
        let offsetX = 32

        if (roles.length > 5) {
            let summonCnt = 0
            for (let role of roles) {
                if (this.isSummon(role)) {
                    summonCnt++
                }
            }
            let width = 5 * oneWidth + offsetX
            let r = offsetX / oneWidth
            oneWidth = width / (r + summonRatio * summonCnt + (roles.length - summonCnt))
            offsetX = oneWidth * r
        }

        if (role) {
            let id = role.Data.id
            let cfg = cfgHelper.getCharacter(id)
            if (cfg?.battle?.startPosOffset) {
                startPos += cfg.battle.startPosOffset
            }
            let scale = oneWidth / orgWidth * summonRatio
            role.Child("ui").scale = scale
        }

        let x = startPos + oneWidth * 0.5

        for (let i = 1; i <= index; i++) {
            let role = roles[i - 1]
            let val = oneWidth
            if (i == 1) {
                x += offsetX
            }
            if (role && this.isSummon(role)) {
                x += val * summonRatio
            }
            else {
                x += val
            }
        }
        x -= oneWidth * 0.5
        if (type == BattleRoleType.PASSENGER) {
            x = -x
        }
        return cc.v2(x, 0)
    }

    public isSummon(role: cc.Node) {
        return role.Data.id > BattleSummonID.BASE
    }

    private getRolesByType(type: BattleRoleType) {
        return type == BattleRoleType.PASSENGER ? this.passengers : this.monsters
    }

    private getRole(uid: string) {
        let node = this.passengers.concat(this.monsters, this.buffs).find(r => r.Data.uid == uid)
        if (!node) {
            console.error(`${uid} not found`)
        }
        return node
    }

    private getRoleIndex(uid: string) {
        let index = this.passengers.findIndex(r => r.Data.uid == uid)
        if (index < 0) {
            index = this.monsters.findIndex(r => r.Data.uid == uid)
        }
        return index
    }

    private getRoleCenter(role: cc.Node) {
        let body = role.Child('body')
        if (body) {
            return cc.v2(0, role.Child('body').height * 0.5)
        }
        else {
            return cc.v2(0, STD_ROLE_BOX.height * 0.5)
        }
    }

    private removeRole(role) {
        let data = role.Data
        if (data.isCopy) return
        let roles = this.getRolesByType(data.type)
        roles.for((r, i) => {
            if (r.Data.uid == data.uid) {
                roles.splice(i, 1)
            }
        })
    }

    //判断role2是不是在role1后面，相同位置也返回true
    private isBehind(role1, role2) {
        if (!role2) return false
        if (role1.x == role2.x) return true
        if (role1.Data.type == role2.Data.type) {
            let isBehind = role2.x - role1.x >= 0
            if (role1.Data.type == BattleRoleType.PASSENGER) {
                isBehind = !isBehind
            }
            return isBehind
        }
        else { //不同阵营肯定在前面
            return false
        }
    }

    private isBoss(role) {
        return gameHelper.isBoss(role.Data?.id)
    }

    private getSkillScale(role) {
        if (!this.isBoss(role)) return 1
        let id = role.Data.id
        if (id == 2008) return 2
        return 1
    }

    private async shake(factor = 1) {
        let node = this.node.parent.parent
        await viewHelper.shake(node, 0.2, factor)
    }

    private resetAnim(role: cc.Node) {
        let sk = role.Child('body', sp.Skeleton)
        let anim = this.getAnim(role)
        sk.tryPlayAnimations([anim?.idle, PassengerBattleAnimation.ATTACK_IDLE, PassengerLifeAnimation.IDLE], true)
    }

    private getAnim(role: cc.Node) {
        if (!role.Data) return
        let id = role.Data.id
        let stage = role.Data.stage || 1
        let animMap = SP_ANIM[id]
        if (animMap) {
            return animMap[stage]
        }
    }

    private changeBossStage(role: cc.Node) {
        if (!this.isBoss(role)) return
        let id = role.Data.id
        let stage = role.Data.stage || 1
        if (SP_ANIM[id] && SP_ANIM[id][stage + 1]) {
            role.Data.stage = stage + 1
        }
    }

    private moveAttachedNode(node1: cc.Node, node2: cc.Node) {
        let prefix = "ATTACHED_NODE:"
        let rootName = "ATTACHED_NODE_TREE"
        let root = node1.getChildByName(rootName)
        if (!root) return

        let handle = (node: cc.Node) => {
            if (!node.name.startsWith(prefix)) {
                let parentName = node.parent.name.split(prefix)[1]
                let parentNode = node2.Component(sp.Skeleton).getAttachedNode(parentName)
                if (!parentNode) {
                    console.error("copyAttachedNode same node not found", parentName, node.parent.name, node.name)
                }
                node.parent = parentNode
            }
        }

        let walk = (node: cc.Node) => {
            for (let i = node.children.length - 1; i >= 0; i--) {
                let child = node.children[i]
                handle(child)
                if (child.parent == node) {
                    walk(child)
                }
            }
        }

        walk(root)
    }

    private replaceRoleNode(orgNode, newNode) {
        let orgData = orgNode.Data
        let newData = newNode.Data
        let roles = this.getRolesByType(orgData.type)
        let index = roles.findIndex(r => r == orgNode)
        roles[index] = newNode
        for (let index in this.posMap[orgData.type]) {
            if (this.posMap[orgData.type][index] == orgData) {
                this.posMap[orgData.type][index] = newData
            }
        }
    }

    private getSkillCfg(role: cc.Node): BattleSkillViewCfg {
        let skill = role.Data.curSkill
        if (!skill) return
        return skill.viewCfg
    }

    private getSkillCfgByHash(id: number, hash): BattleSkillViewCfg {
        let role = this.roles.find(r => r.id == id)
        if (!role) return
        let skill = role.skills.find(s => s.getHash() == hash)
        if (!skill) return
        return skill.viewCfg
    }

    //-----------------------------------------------------------

    private isClear() {
        return !cc.isValid(this) || this._isClear
    }

    private playReward(node: cc.Node) {
        if (node.Data.id == 2300) {
            for (let i = 0; i < 3; i++) {
                let cond = new ConditionObj().init(ConditionType.DIAMOND, -1, 1)
                let root = node.Child("ui/di")
                let pos = ut.convertToNodeAR(node, root)
                this.showReward(cond, pos, root)
            }
        }
    }

    private showReward(reward: ConditionObj, pos: cc.Vec2, root?) {
        let rewardNode = this.Child('template/nodeReward')
        root = root || this.Child("reward")
        let center = pos.add(cc.v2(100, 0))
        let gen = (reward) => {
            rewardNode.active = true
            let node = cc.instantiate2(rewardNode, root)
            let cmpt = node.Component(PlanetNodeRewardCmpt)
            cmpt.init(reward)
            animHelper.playPlanetRewardJump(node, center, this.rewardPosAry, { maxa: 200 }).then(()=>{
                cmpt.onDropEnd()
            })
            return node
        }
        gen(reward)
    }
}