import EventType from "../../../common/event/EventType";
import { game<PERSON>elper } from "../../../common/helper/GameHelper";
import { resHelper } from "../../../common/helper/ResHelper";
import HeroModel from "../../../model/hero/HeroModel";
import PlanetCheckPointModel from "../../../model/planet/PlanetCheckPointModel";
import PlanetNodeModel from "../../../model/planet/PlanetNodeModel";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetNodeCmpt extends mc.BaseCmptCtrl  {

    public model: PlanetNodeModel = null

    public size: cc.Size = null

    @property(cc.Node)
    protected touchNode: cc.Node = null

    protected isShow: boolean = false

    public loadedPromise: Promise<any> = null

    public orgViewPos: cc.Vec2 = null
    public orgViewAngle: number = 0

    public init(model: PlanetNodeModel, ...args) {
        this.model = model
        this.loadedPromise = this.initView()
        this.loadedPromise.then(()=>{
            this.initClick()
        })
    }

    protected async initView() {
        if (this.model.checkShow()) {
            this.setShow(true)
        }
        else {
            this.setShow(false)
        }
        this.initPos()
    }

    protected initPos() {
        this.node.setPosition(this.model.position)
    }

    protected initClick() {
        if (this.touchNode) {
            this.touchNode.off("click", this.onClick, this)
            this.touchNode.on("click", this.onClick, this)
            if (this.size) {
                this.touchNode.setContentSize(this.size)
            }
        }
    }

    update(dt?) {
        if (!this.model) return

        if (!this.isShow) {
            if (this.model.checkShow()) {
                this.show()
            }
        }

        if (this.model.dead) {
            this.onDeath()
        }
    }

    protected setShow(show) {
        this.isShow = show
        if (!show) {
            this.node.opacity = 0
        }
    }

    protected show() {
        this.isShow = true
        this.node.opacity = 255
    }

    protected onDeath() {
        this.model = null
        this.node.parent = null
        this.node.destroy()
    }

    public onClick() {
        if (!this.model) return
        this.model.onClick()
    }
}