import { NpcCfg } from "../../../../common/constant/DataType";
import { BattleLevelType, PassengerLifeAnimation } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { resHelper } from "../../../../common/helper/ResHelper";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetModel from "../../../../model/planet/PlanetModel";
import MountPointCmpt from "../../common/MountPointCmpt";
import MulColorCmpt from "../../common/MulColorCmpt";
import RoleSpeakCmpt from "../../role/RoleSpeakCmpt";


const { ccclass, property } = cc._decorator;

const MOVE_SPEED = 300

@ccclass
export default class PlanetEntryCmpt extends mc.BaseCmptCtrl {
    protected model: PlanetModel = null

    public listenEventMaps() {
        return [
        ]
    }

    public preload(model: PlanetModel) {
        this.model = model
    }

    public init(model: PlanetModel) {
        this.model = model

        let role = this.Child("role")
        if (role) {
            role.active = true
            let sk = role.Component(sp.Skeleton)
            sk.playAnimation(PassengerLifeAnimation.IDLE, true)
            let speakCmpt = role.Component(RoleSpeakCmpt)
            if (speakCmpt) {
                speakCmpt.init(1005)
            }
            else {
                twlog.error("PlanetEntryCmpt", "speakCmpt is null", this.model.getId())
            }
        }

        this.initView()
        this.checkNpc()
        this.checkMonster()

        if (this.model.needLandAnim()) {
            this.initFirstLand()
        }
    }

    protected initView() {
    }

    protected setLockColor(node: cc.Node, type) {
        let unlock = unlockHelper.isUnlockFunction(type)
        node.Component(MulColorCmpt).show(!unlock)
        node.children.forEach(n => {
            if (n.name.includes("sk") && !unlock) {
                let sk = n.Component(sp.Skeleton)
                if (sk.findAnimation("jingzhi")) {
                    sk.playAnimation("jingzhi")
                }
                else {
                    // sk.paused = true
                }
            }
        })
    }

    protected getPos(name: string) {
        return this.Component(MountPointCmpt).getPoint("pos").getChildByName(name)?.getPosition()
    }

    protected async roleMoveTo(role: cc.Node, targetPos: cc.Vec2, filpX = -1) {
        let vec = targetPos.sub(role.getPosition())
        let dis = vec.mag()
        let time = dis / MOVE_SPEED
        let sk = role.getComponent(sp.Skeleton) || role.Child("body", sp.Skeleton)
        sk.tryPlayAnimations(["walk", PassengerLifeAnimation.WALK], true)
        let orgScale = Math.abs(role.scale)
        role.scaleX = (vec.x > 0 ? 1 : -1) * orgScale
        await cc.tween(role).to(time, { x: targetPos.x, y: targetPos.y }).promise()
        role.scaleX = filpX * orgScale
        sk.tryPlayAnimations(["daiji", PassengerLifeAnimation.IDLE], true)
    }

    protected async initFirstLand() {
        let role = this.getRole()
        let orgPos = role.getPosition()
        let startPos = cc.v2(orgPos.x - MOVE_SPEED * 4, orgPos.y)
        role.setPosition(startPos)
        let sk = role.getComponent(sp.Skeleton)
        sk.playAnimation(PassengerLifeAnimation.IDLE, true)
        await ut.wait(0.6, this)
        await this.roleMoveTo(role, orgPos, 1)
    }

    public getRole() {
        return this.Child("role")
    }

    setReddot(role: cc.Node, active: boolean) {
        let reddot = role.Child("reddot")
        if (active) {
            let speakCmpt = role.Component(RoleSpeakCmpt) || role.Child("body", RoleSpeakCmpt)
            if (speakCmpt) {
                reddot.active = true
                reddot.parent = speakCmpt.getTalkNode()
                reddot.setPosition(cc.v2(0, 0))
                return
            }
        }
        reddot.active = false
    }

    protected checkNpc() {
        let npcCfgs = assetsMgr.getJson<NpcCfg>("Npc").datas
        npcCfgs = npcCfgs.filter(n => n.planet == this.model.getId())
        if (!npcCfgs?.length) return
        for (const npcCfg of npcCfgs) {
            if (npcCfg.taskDialog != 1) continue
            const roleNode = this.Component(MountPointCmpt).getPoint(npcCfg.id + "")
            if (!roleNode) {
                console.error("npc 节点不存在", npcCfg.id)
                continue
            }
            const hasDialogTask = gameHelper.dailyTask.hasDialogTask(npcCfg.id)
            roleNode.active = true
            const isLocalTest = npcCfg.spine.startsWith("/")
            let urlPrefix = isLocalTest ? "passenger" : "npc/"
            const body = roleNode.Child("body")
            // 暂时使用了乘客的spine  没有talk挂点
            if (!isLocalTest) {
                this.setReddot(roleNode, !!hasDialogTask)
            }
            else {
                const reddot = roleNode.Child("reddot")
                reddot.active = !!hasDialogTask
            }
            if (body) {
                if (isLocalTest) {
                    let sk = body.Component(sp.Skeleton)
                    resHelper.loadSkeletonData(sk, { url: `${urlPrefix}${npcCfg.spine}`, tag: this.getTag() }).then(()=>{
                        sk.playAnimation(PassengerLifeAnimation.IDLE, true)
                    })
                }
            }
            if (roleNode.hasEventListener("click")) {
                continue
            }
            roleNode.on("click", () => this.checkClickTaskDialogNpc(roleNode))
        }
    }

    protected checkClickTaskDialogNpc(roleNode: cc.Node): boolean {
        const id = Number(roleNode.name)
        if (id <= 0) return false
        const hasDialogTask = gameHelper.dailyTask.hasDialogTask(id)
        if (!hasDialogTask) return false
        eventCenter.on(EventType.DAILY_TASK_DIALOG_SUBMIT, this.checkNpc, this)
        viewHelper.showPnl("daily_task/DailyTaskDialogPnl", id, () => {
            eventCenter.off(EventType.DAILY_TASK_DIALOG_SUBMIT, this.checkNpc, this)
        })
        return true
    }

    protected checkMonster() {
        const node = this.Component(MountPointCmpt).getPoint("monster")
        if (!node) return
        node.active = false
        node.off("click")
        const hasBattleTask = gameHelper.dailyTask.hasBattleTask(this.model.getId())
        if (!hasBattleTask) return
        if (!hasBattleTask.battleInfo) return
        node.active = true
        const firstMonster = hasBattleTask.battleInfo[0]
        resHelper.loadMonsterSp(firstMonster.id, node.Child("body", sp.Skeleton), this.getTag())
        node.on("click", async () => {
            eventCenter.on(EventType.DAILY_TASK_BATTLE_SUBMIT, this.checkMonster, this)
            await gameHelper.dailyTask.processBattleTask(hasBattleTask)
            eventCenter.off(EventType.DAILY_TASK_BATTLE_SUBMIT, this.checkMonster, this)
        })
    }
}