import { GuideStepMark, HeroAnimation, LangCfgName, MarkNewType, NPC_ID, PassengerLifeAnimation, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { cfgHelper } from "../../../../common/helper/CfgHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import MountPointCmpt from "../../common/MountPointCmpt";
import RoleSpeakCmpt from "../../role/RoleSpeakCmpt";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";
import PlanetEntryCmpt from "./PlanetEntryCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetEntry1007Cmpt extends PlanetEntryCmpt {

    public listenEventMaps() {
        return [
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onUnlockFunction },
        ]
    }

    protected initView() {
        this.initBranch()
        this.initTransport()
        this.initManager()
    }

    private initBranch() {
        let type = UIFunctionType.PLAY_SPACE_STONE
        let branch = this.Component(MountPointCmpt).getPoint("branch")
        branch.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        branch.active = false
        let bg = branch.getChildByName("bg")
        bg.active = false
        if (unlockHelper.isUnlockFunction(type)) {
            branch.active = true
            bg.active = true
        }
        else if (gameHelper.guide.isStepEnd(GuideStepMark.START_SPACE_STONE)) {
            branch.active = true
            bg.active = true
        }
        branch.off("click")
        branch.on("click", () => {
            if (unlockHelper.isUnlockFunction(type)) {
                viewHelper.gotoPlanetBranch(this.model)
            }
            else {
                eventCenter.emit(EventType.GUIDE_UNLOCK_SPACE_STONE)
            }
        })
    }

    private initTransport() {
        let type = UIFunctionType.PLAY_TRANSPORT
        let transport = this.Component(MountPointCmpt).getPoint("transport")
        transport.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        transport.off("click")
        transport.on("click", () => {
            this.onClickTransport()
        })
    }

    private initManager() {
        let role = this.getManager()
        let speakCmpt = role.Child("body", RoleSpeakCmpt)
        speakCmpt.init(NPC_ID.TRANSFER_MGR)
        role.setPosition(this.getPos("normal"))
        this.setReddot(role, false)
        if (!unlockHelper.isUnlockFunction(UIFunctionType.PLAY_TRANSPORT)) {
            role.setPosition(this.getPos("door"))
            if (unlockHelper.isUnlockFuncByMisc(UIFunctionType.PLAY_TRANSPORT)) {
                this.setReddot(role, true)
            }
        }

        role.off("click")
        role.on("click", () => {
            this.onClickTransport()
        })
    }

    private onClickTransport() {
        let role = this.getManager()
        let speakCmpt = role.Child("body", RoleSpeakCmpt)
        if (!unlockHelper.isUnlockFuncByMisc(UIFunctionType.PLAY_TRANSPORT)) {
            let key = cfgHelper.getPlotId("guide_bubble_4006_1")
            let talkNode = speakCmpt.getTalkNode()
            if (talkNode) {
                viewHelper.showSpeechBubble(talkNode.convertToWorldSpaceAR(cc.v2(0, 0)), key)
            }
        }
        else if (!unlockHelper.isUnlockFunction(UIFunctionType.PLAY_TRANSPORT)) {
            eventCenter.emit(EventType.GUIDE_UNLOCK_TRANSPORT)
            this.setReddot(role, false)
        }
        else {
            if (this.checkClickTaskDialogNpc(role)) return
            viewHelper.showPnl('transport/TransportPnl')
        }
    }

    private onUnlockFunction(type: UIFunctionType) {
        if (type == UIFunctionType.PLAY_TRANSPORT) {
            this.initTransport()
            this.openDoor()
        }
        else if (type == UIFunctionType.PLAY_SPACE_STONE) {
            this.initBranch()
        }
    }

    private async openDoor() {
        mc.lockTouch(true)
        let role = this.getManager()
        await this.roleMoveTo(role, this.getPos("normal"))
        mc.lockTouch(false)
    }

    private getManager() {
        return this.Component(MountPointCmpt).getPoint(NPC_ID.TRANSFER_MGR)
    }
}