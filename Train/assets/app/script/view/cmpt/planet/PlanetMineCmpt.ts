import NodePool from "../../../../core/utils/NodePool";
import { PlanetMineType } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import PlanetMineModel from "../../../model/planet/PlanetMineModel";
import MountPointCmpt from "../common/MountPointCmpt";
import PlanetNodeCmpt from "./PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetMineCmpt extends PlanetNodeCmpt {

    public model: PlanetMineModel = null

    protected body: cc.Sprite = null

    protected hp: cc.Sprite = null

    protected hpBar: cc.Node = null

    protected infoLayout: cc.Layout = null

    protected icon: cc.Sprite = null

    protected info: cc.Label = null

    protected actionMap: any = {}

    protected curHp: number = 0

    protected ui: cc.Node = null

    protected selected: cc.Node = null

    protected shadow: cc.Node = null

    protected collectEffectNode: cc.Node = null

    protected nodePool: NodePool = new NodePool()

    private showQTETips: boolean = false

    public listenEventMaps() {
        return [
            { [EventType.ENTER_COLLECT]: this.onEnterCollect },
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
            { [EventType.REACH_PLANET_NODE]: this.onReach },
            { [EventType.PLANET_HIDE_CUR_MINE_UI]: this.onHideUI },
            { [EventType.PLANET_MINE_HP_CHANGE]: this.onChangHp },
        ]
    }

    public onCreate() {
        this.ui = this.Child('ui')
        this.hpBar = this.ui.Child('hpBar')
        this.hp = this.hpBar.Child('hp', cc.Sprite)
        this.infoLayout = this.ui.Child('infoLayout', cc.Layout)
        this.icon = this.infoLayout.Child('icon', cc.Sprite)
        this.info = this.infoLayout.Child('info', cc.Label)
        this.selected = this.ui.Child('selected')
        this.body = this.Child('body', cc.Sprite)
        this.shadow = this.Child('shadow')
        this.collectEffectNode = this.Child('collectEffect')
    }

    onClean() {
        if (this.showQTETips) {
            viewHelper.hidePlanetTips()
        }
    }

    public init(model: PlanetMineModel) {
        super.init(model)
        this.curHp = this.model.hp
    }

    protected async initView() {
        await super.initView()

        this.initScale()

        this.updateHpView(1)
        await this.initIcon()
        if (!cc.isValid(this)) return

        this.initSize()
        this.node.zIndex = this.model.getZIndex()

        this.initName()
        this.initUI()
        this.initCollectEffect()

        if (gameHelper.hero.isTarget(this.model)) {
            this.onTarget(this.model)
            if (gameHelper.hero.isCollect()) {
                this.onEnterCollect(this.model)
            }
        }
    }

    protected initScale() {
        if (this.model.scale < 0) {
            this.body.node.scaleX = -1
        }
        let scale = Math.abs(this.model.scale)
        this.node.scale = scale
        this.ui.scale = 1 / scale
    }

    protected initName() {
        this.icon.Component(cc.MultiFrame).setFrame(this.model.type - 1)
        this.info.setLocaleKey("common_guiText_11", this.model.lv)
    }

    protected initSize() {
        let size = this.body.node.getContentSize()
        this.model.setSize(size)
    }

    protected async initIcon() {
        let tag = this.getTag()
        let icon = this.model.icon
        let spf = await assetsMgr.loadTempRes(`planet/mine/${icon}`, cc.SpriteFrame, tag)
        if (!cc.isValid(this)) {
            assetsMgr.releaseTempResByTag(tag)
            return
        }
        this.body.spriteFrame = spf
    }

    protected initUI() {
        this.ui.y = this.model.size.height + (40 / this.node.scale)
        this.ui.x = this.model.centerOffset.x
        this.ui.active = false
    }

    protected initCollectEffect() {
        this.collectEffectNode.x = this.ui.x
        this.collectEffectNode.y = this.model.size.height * 0.5
    }

    update(dt) {

        if (this.ui.active) {
            this.updateHpView(dt)
        }

        if (!this.model) return

        if (this.touchNode) {
            this.touchNode.Component(cc.Button).interactable = false
        }

        super.update()
    }

    protected onChangHp(model: PlanetMineModel, hp: number, damageMul: number = 1, isAuto = false) {
        if (this.model != model) return
        let damage = -hp
        if (damage > 0) {
            this.onHit()
        }
        eventCenter.emit(EventType.PLANET_SHOW_HIT_TIPS, damage, damageMul, this.model, this.node, isAuto)
    }

    protected updateHpView(dt) {
        let lerpRatio = 10 * dt
        let targetRatio = 0
        if (this.model) {
            targetRatio = this.model.hp / this.model.maxHp
            // this.hpBar.Child('lb').Component(cc.Label).string = `${this.model.hp}/${this.model.maxHp}`
        }
        let shadow = this.hpBar.Child('shadow').Component(cc.Sprite)
        if (Math.abs(targetRatio - shadow.fillRange) < 0.01) {
            lerpRatio = 1
        }
        shadow.fillRange = cc.misc.lerp(shadow.fillRange, targetRatio, cc.misc.clamp01(lerpRatio))

        this.hp.fillRange = targetRatio
    }

    protected onHit() {
        if (!this.model) return

        this.ui.active = true

        let node = this.body.node
        this.doScaleAction(node, (scale) => {
            return cc.tween(node).to(0.1, { scaleY: scale.y * 1.2 }).to(0.1
                , { scaleY: scale.y * 0.8 }).to(0.1, { scaleY: scale.y })
        })

        if (this.shadow) {
            let shadowNode = this.shadow
            this.doScaleAction(shadowNode, (scale) => {
                return cc.tween(shadowNode).to(0.1, { scaleY: scale.y * 1.2 }).to(0.1
                    , { scaleY: scale.y * 0.8 }).to(0.1, { scaleY: scale.y })
            })
        }

        this.playCollectEffect()
    }

    private playCollectEffect() {
        if (this.collectEffectNode && this.model.collectEffect) {
            let node = this.nodePool.get(this.collectEffectNode, this.node)
            let sk = node.Component(sp.Skeleton)
            sk.setSkin(this.model.collectEffect)
            let name
            if (this.model.type == PlanetMineType.SEED) {
                name = "animation4_zhongzhi" + ["", 2, 3].random()
            }
            else {
                if (gameHelper.hero.isRageMode()) {
                    name = "animation_baozou"
                }
                else {
                    name = "animation" + [1, 2, 3].random()
                }
            }
            let p = sk.playAnimation(name)
            p.then(() => {
                this.nodePool.put(this.collectEffectNode, node)
            })
            return p
        }
    }

    private doScaleAction(node, callback) {
        let key = node.uuid
        let info = this.actionMap[key]
        let scale
        if (!info) {
            info = { node }
            this.actionMap[key] = info
        }
        if (info.orgScale) {
            scale = info.orgScale;
        }
        else {
            scale = cc.v2(node.scaleX, node.scaleY);
        }
        info.orgScale = scale;

        if (info.scaleAction) {
            info.scaleAction.stop();
        }
        info.scaleAction = callback(scale).call(() => {
            info.orgAnimScale = null;
            info.scaleAction = null;
        }).start()
    }


    protected stopScaleActions() {
        for (let key in this.actionMap) {
            let { node } = this.actionMap[key]
            node.stopAllActions()
        }
    }

    protected onSelect() {
        this.selected.active = true
    }

    private onHideUI(model: any) {
        if (this.model != model) return
        this.ui.active = false
    }

    public onTarget(model: PlanetMineModel) {
        if (this.model != model) return
        this.ui.active = true
        this.onSelect()
    }

    private onReach(model: PlanetMineModel) {
        if (this.model != model) return
        if (this.model.qteId && gameHelper.planet.isFirstQTE()) {
            viewHelper.showPlanetTips("explore_guiText_19")
            this.showQTETips = true
        }
    }

    protected onEnterCollect(model) {
        if (this.model != model) return
        this.hpBar.active = true
        this.infoLayout.node.active = true

        this.selected.active = false
    }

    protected async onDeath() {
        let waitEffect = null
        if (gameHelper.hero.isRageMode()) {
            waitEffect = this.playCollectEffect()
        }
        this.model = null
        this.stopScaleActions()

        let toOpacity = (node) => {
            cc.tween(node).to(0.2, { opacity: 0 }).start()
        }
        toOpacity(this.body.node)
        toOpacity(this.ui)
        toOpacity(this.shadow)

        await Promise.all([
            waitEffect,
            this.playDeadAnim()
        ])

        this.node.parent = null
        this.node.destroy()
    }

    protected playDeadAnim() {
        let deadAni = this.Child('deadAni')
        deadAni.active = true
        deadAni.SetColor("#B2B2B278") //todo区分星球
        return deadAni.Component(sp.Skeleton).playAnimation("animation")
    }
}