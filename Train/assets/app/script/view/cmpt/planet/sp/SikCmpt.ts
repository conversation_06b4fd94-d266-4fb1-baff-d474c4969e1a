import { HeroAction } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class SikCmpt extends PlanetNodeCmpt {

    private actionTree: ActionTree = null
    private endCb: Function = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public init(model, planetCtrl) {
        super.init(model, planetCtrl)
        this.actionTree = new ActionTree().init(this)
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    update(dt: number) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model) {
        if (this.model != model) return

        let hero = gameHelper.hero
        const waitEnd = new Promise((r) => {
            this.endCb = r
        })

        await this.actionTree.start(async (action: ActionNode) => {
            let pos = this.model.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        await waitEnd

        await this.model.die()
        this.model.end()
    }

    public async onClick() {
        cc.Tween.stopAllByTarget(this.touchNode)
        await cc.tween(this.touchNode).to(.2, { opacity: 0 }).start().promise()
        this.endCb?.()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }

}