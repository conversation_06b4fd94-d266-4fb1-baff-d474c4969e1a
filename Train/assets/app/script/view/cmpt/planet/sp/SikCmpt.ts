import { ConditionType, HeroAction, HeroAnimation, PassengerAction, PassengerAnimation, PassengerLifeAnimation, PlanetMineType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import NodeType from "../../../../common/event/NodeType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetRandomBox from "../../../../model/planet/sp/PlanetRandomBox";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;


enum State {
    CLICK,
    END,
}

@ccclass
export default class SikCmpt extends PlanetNodeCmpt {

    private state: State = State.CLICK

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public init(model, planetCtrl) {
        super.init(model, planetCtrl)
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    private async onTarget(model) {
        console.log(this.model.reachOffset)
    }

}