import { ConditionType, HeroAction, HeroAnimation, PassengerAction, PassengerAnimation, PassengerLifeAnimation, PlanetMineType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetMineModel from "../../../../model/planet/PlanetMineModel";
import PlanetRandomBox from "../../../../model/planet/sp/PlanetRandomBox";
import PlanetMineCmpt from "../PlanetMineCmpt";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;


class Mine extends PlanetMineModel {

    public hp: number = 0
    public maxHp: number = 0
    public type: PlanetMineType = PlanetMineType.ORE
    public hitCb: Function = null
    public reachOffset: cc.Vec2 = cc.v2(-220, 0)

    public isLast() { return false }

    public checkShow() { return true }
    public isPassContorl() { return true }

    public hit(damage, damageMul: number = 1) {
        super.hit(damage, damageMul)
        this.hitCb && this.hitCb()
    }

    public async die() {
        if (this.dead) return true
        this.dead = true
        return true
    }
}

class MineCmpt extends PlanetMineCmpt {

    protected body: any = null
    private isDead: boolean = false

    public cb: Function = null

    public init(model: PlanetMineModel) {
        super.init(model)

        if (model.dead) {
            this.playDeath()
        }
    }

    protected initPos() {
        //do nothing
    }

    protected async initIcon() {
        //do nothing
    }

    protected async onDeath() {
        if (this.isDead) return
        this.playDeath()
    }

    private async playDeath() {
        this.isDead = true
        this.ui.active = false
        this.cb && this.cb()
    }
}

@ccclass
export default class PuzzleBoxCmpt extends PlanetNodeCmpt {

    public model: PlanetRandomBox = null
    private actionTree: ActionTree = null
    private mine: Mine = null
    private mineDeadEnd: Promise<any> = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    init(model, planetCtrl) {
        super.init(model)
        this.actionTree = new ActionTree().init(this)
        this.initMine()
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    private initMine() {
        this.mine = new Mine()
        let mine = this.mine
        let json = this.model.json
        mine.setJson(json)
        mine.maxHp = json.hp
        mine.fromDB(this.model.toDB())
        mine.setPosition(this.model.position)
        this.mine.hitCb = () => {
            this.onHit()
        }
        if (mine.maxHp <= this.model.progress) {
            mine.die()
        }

        let mineNode = this.Child("mine")
        let cmpt = mineNode.addComponent(MineCmpt)
        this.mineDeadEnd = new Promise((r) => {
            cmpt.cb = r
        })
        cmpt.init(this.mine)
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model) {
        if (this.model != model) return

        let hero = gameHelper.hero

        await this.actionTree.start(async (action: ActionNode) => {
            let pos = this.mine.reachPosition
            await action.run(hero.moveToPos, pos, hero)
            await action.run(hero.collectAction, this.mine, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        await this.mineDeadEnd
        
        await new Promise((r) => viewHelper.showPnl("planet/GearPlayPnl", r))

        this.onEnd()
    }

    private onHit() {
        this.model.progress = this.mine.progress
    }

    private async onEnd() {
        let model = this.model
        await Promise.all([
            model.die(),
            ut.wait(1),
        ])
        eventCenter.emit(EventType.CLAIM_MINE_REWARD_START, model)
        model.end()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }
}