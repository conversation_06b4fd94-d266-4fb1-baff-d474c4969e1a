import { ChapterPlanetMonsterCfg } from "../../../../common/constant/DataType";
import { ConditionType, HeroAction, HeroAnimation, PassengerAction, PassengerAnimation, PassengerLifeAnimation, PlanetMineType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { cfgHelper } from "../../../../common/helper/CfgHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import { localConfig } from "../../../../common/LocalConfig";
import ActionTree, { ActionNode } from "../../../../model/passenger/ActionTree";
import PlanetCheckPointModel from "../../../../model/planet/PlanetCheckPointModel";
import PlanetEmptyNode from "../../../../model/planet/PlanetEmptyNode";
import PlanetMineModel from "../../../../model/planet/PlanetMineModel";
import PlanetMonsterBox from "../../../../model/planet/sp/PlanetMonsterBox";
import EternalGardenWindCtrl from "../../../eternalGarden/EternalGardenWindCtrl";
import HeroCmpt from "../../hero/HeroCmpt";
import PlanetMineCmpt from "../PlanetMineCmpt";
import PlanetNodeCmpt from "../PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;


const HP = 2
class Mine extends PlanetMineModel {

    public hp: number = HP
    public maxHp: number = HP
    public type: PlanetMineType = PlanetMineType.ORE
    public hitCb: Function = null

    public isLast() { return false }

    public checkShow() { return true }
    public isPassContorl() { return true }

    public hit(damage, damageMul: number = 1) {
        super.hit(1, damageMul)
        this.hitCb && this.hitCb()
    }

    public async die() {
        if (this.dead) return true
        this.dead = true
        return true
    }
}

class MineCmpt extends PlanetMineCmpt {

    protected body: any = null

    public cb: Function = null

    public onCreate(): void {
        super.onCreate()
        this.body = this.Child('body', sp.Skeleton)
    }

    public init(model: PlanetMineModel) {
        super.init(model)

        if (model.dead) {
            this.playClose(false)
        }
        else {
            this.body.playAnimation("on_idle", true)
        }
    }

    protected initPos() {
        //do nothing
    }

    protected async initIcon() {
        //do nothing
    }

    protected async onDeath() {
        //do nothing
    }

    protected onChangHp(model: PlanetMineModel, hp: number, damageMul: number = 1, isAuto = false) {
        if (this.model != model) return
        this.onHit()
    }

    protected onEnterCollect(model) {
        if (this.model != model) return
        this.selected.active = false
    }

    public async playClose(showAnim) {
        if (showAnim) {
            await this.body.playAnimation("off")
        }
        this.body.playAnimation("off_idle", true)
    }
}

class CheckPoint extends PlanetCheckPointModel {

    public onSyncDie: Function = null
    public onDie: Function = null

    get isWin() { return true }

    protected initJson() {
        //do nothing
    }

    public setJson(json: any) {
        this.json = json
    }

    public async die() {
        if (this.dead) return true
        this.dead = true
        this.onDie && this.onDie()
        return true
    }

    public async syncDie(res) {
        return this.onSyncDie(res)
    }

    public getLevelIndex() {
        return 1
    }
}

@ccclass
export default class MonsterBoxCmpt extends PlanetNodeCmpt {

    public model: PlanetMonsterBox = null
    private actionTree: ActionTree = null
    private mine: Mine = null
    private checkPoint: CheckPoint = null

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    init(model, planetCtrl) {
        super.init(model)
        this.actionTree = new ActionTree().init(this)
        this.initMine()
        this.initCheckPoint()
        if (gameHelper.hero.getTargetModel() == this.model) {
            this.onTarget(this.model)
        }
    }

    private initMine() {
        this.mine = new Mine()
        let mine = this.mine
        mine.fromDB(this.model.toDB())
        mine.setPosition(this.model.position)
        mine.reachOffset = this.model.reachOffset
        this.mine.hitCb = () => {
            this.onHit()
        }
        if (mine.maxHp <= this.model.progress) {
            mine.die()
        }

        let mineNode = this.Child("mine")
        let cmpt = mineNode.addComponent(MineCmpt)
        cmpt.init(mine)
    }

    private initCheckPoint() {
        this.checkPoint = new CheckPoint()
        let checkPoint = this.checkPoint
        checkPoint.setPosition(this.model.position)
        checkPoint.reachOffset = this.model.reachOffset
        let lv = this.model.json.lv
        checkPoint.setJson({
            monster: [
                {
                    id: 2300,
                    lv: lv,
                    starLv: cfgHelper.getMonsterStarLvByLevel(lv)
                }
            ]
        })
        checkPoint.init("")
        checkPoint.onSyncDie = async(res) => {
            let succ = await this.model.syncDie(res)
            this.checkPoint.rewards = this.model.diyRewards
            return succ
        }
        checkPoint.onDie = () => {
            this.onEnd()
        }
    }

    update(dt) {
        super.update(dt)
        this.actionTree && this.actionTree.update(dt)
    }

    private async onTarget(model) {
        if (this.model != model) return

        let hero = gameHelper.hero

        await this.actionTree.start(async (action: ActionNode) => {
            let startPos = hero.getPosition()
            let pos = this.mine.reachPosition
            let vec = pos.sub(startPos)
            let minDis = 200
            if (!this.mine.dead && vec.mag() > minDis) {
                let closePos = startPos.add(vec.normalize().mul(minDis))
                await action.run(hero.moveToPos, closePos, hero)
                this.Child("mine").Component(MineCmpt).playClose(true)
            }
            else {
                this.Child("mine").Component(MineCmpt).playClose(false)
            }
            await action.run(hero.moveToPos, pos, hero)
            await action.run(hero.collectAction, this.mine, hero)
            hero.setAction(HeroAction.IDLE)
            action.ok()
        })

        await this.actionTree.start(async (action: ActionNode) => {
            await hero.startBattle(action, this.checkPoint)
            action.ok()
        })

        this.onEnd()
    }

    private onHit() {
        this.model.progress = this.mine.progress
    }

    private async onEnd() {
        let model = this.model
        await model.die(),
        model.end()
    }

    onRemove() {
        super.onRemove()
        this.actionTree && this.actionTree.terminate()
    }
}