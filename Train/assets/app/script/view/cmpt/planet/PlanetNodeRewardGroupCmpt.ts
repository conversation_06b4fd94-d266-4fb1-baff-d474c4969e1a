import { MAX_VALUE, MAX_ZINDEX, TILE_SIZE } from "../../../common/constant/Constant";
import { ConditionType } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import NodeType from "../../../common/event/NodeType";
import { anim<PERSON>elper } from "../../../common/helper/AnimHelper";
import { gameHelper } from "../../../common/helper/GameHelper";
import ConditionObj from "../../../model/common/ConditionObj";
import PlanetNodeModel from "../../../model/planet/PlanetNodeModel";
import PlanetNodeRewardCmpt from "./PlanetNodeRewardCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetNodeRewardGroupCmpt extends mc.BaseCmptCtrl {

    private items: cc.Node[] = []

    private isFly: boolean = false

    private cb: Function = null
    private aniKey: number = 0

    private model: PlanetNodeModel = null

    public listenEventMaps() {
        return [
            { [EventType.CLAIM_MINE_REWARD_START]: this.fly },
        ]
    }

    public init(items: cc.Node[], cb?, model?) {
        this.items = items
        this.aniKey = animHelper.planetGenStart()
        this.cb = cb
        this.model = model
        return this
    }

    public async fly(model?) {
        if (this.model != model) return
        if (this.isFly) return
        this.isFly = true
        let targetMap = new Map()

        for (let item of this.items) {
            let reward = item.getComponent(PlanetNodeRewardCmpt).reward
            let target = null
            if (reward.type == ConditionType.STAR_DUST || reward.type == ConditionType.HEART || reward.type == ConditionType.DIAMOND) {
                target = eventCenter.get(NodeType.PLANET_UI_CURRENCY_ICON, reward.type)
            }
            else {
                target = eventCenter.get(NodeType.UI_BAG)
            }

            let info = targetMap.get(target)
            if (!info) {
                let commonEnd = (rewards: ConditionObj[])=>{
                    if (model) {
                        let delayRewardMap = model.delayRewardMap
                        for (let reward of rewards) {
                            let id = delayRewardMap[reward.type]
                            let num = gameHelper.currency.getDelayCurrency(reward.type)
                            gameHelper.currency.removeDelayCurrency(id)

                            switch(reward.type) {
                                case ConditionType.STAR_DUST:
                                    eventCenter.emit(EventType.UPDATE_STARDUST, num)
                                    break
                                case ConditionType.HEART:
                                    eventCenter.emit(EventType.UPDATE_HEART, num)
                                    break
                                case ConditionType.DIAMOND: 
                                    eventCenter.emit(EventType.UPDATE_DIAMOND, num)
                            }
                        }
                    }
                }

                let startFunc, endFunc
                if (reward.type == ConditionType.STAR_DUST || reward.type == ConditionType.HEART || reward.type == ConditionType.DIAMOND) {
                    startFunc = ()=>{
                        eventCenter.emit(EventType.SHOW_CURRENCY_UI, reward.type, true)
                        let num = gameHelper.currency.getDelayCurrency(reward.type)
                        reward.num = num
                        eventCenter.emit(EventType.SHOW_FLUTTER_MONEY, reward, item.convertToWorldSpaceAR(cc.v2()))
                    }
                    endFunc = async(rewards: ConditionObj[])=>{
                        commonEnd(rewards)
                        await ut.wait(1)
                        eventCenter.emit(EventType.SHOW_CURRENCY_UI, reward.type, false)
                    }
                }
                else {
                    let key = this.aniKey
                    startFunc = ()=>{
                        eventCenter.emit(EventType.PLANET_GEN_BAG_ASSETS1)
                    }
                    endFunc = async(rewards: ConditionObj[])=>{
                        eventCenter.emit(EventType.PLANET_GEN_BAG_ASSETS2, rewards, key)
                        commonEnd(rewards)
                    } 
                }
                info = {
                    items: [],
                    startFunc: startFunc,
                    endFunc: endFunc,
                }
                targetMap.set(target, info)
            }
            info.items.push(item)
        }

        let go = async(target: cc.Node, items: cc.Node[])=>{
            let tempVec = cc.v2()
            let min = MAX_VALUE, max = 0
            let arr = []
            for (let item of items) {
                item.stopAllActions()
                let end = target
                const endPos = end.getPosition()
                const dis = item.getPosition().sub(endPos, tempVec).magSqr();
                (dis < min) && (min = dis);
                (dis > max) && (max = dis);
                arr.push({ item, dis })
            }

            arr.sort((a, b) => a.dis - b.dis)
            const maxDis = max - min

            await ut.promiseMap(arr, async ({ item, dis }: { item: cc.Node, dis: number }) => {
                await ut.wait(maxDis <= 0 ? 0 : (dis - min) / maxDis * 0.06)
                if (!item.isValid || !item.parent.isValid) {
                    return
                }
                let end = target
                let pos = ut.convertToNodeAR(item.parent, end.parent, item.getPosition(), null, true)
                let worldScale = item.getWorldScale(cc.v2())
                item.parent = end.parent
                item.setPosition(pos)
                item.scale = worldScale.x
                // 起飞
                await new Promise(r => {
                    if (!item.isValid) {
                        return r(null)
                    }
                    item.Component(PlanetNodeRewardCmpt).fly(end, r)
                })
            })
        }
        let cb = this.cb
        let targets = Array.from(targetMap.entries())
        if (targets.length > 0) {
            await ut.promiseMap(targets, async ([target, { items, startFunc, endFunc }]) => {
                let rewards = items.map(item => item.getComponent(PlanetNodeRewardCmpt).reward)
                rewards = gameHelper.mergeCondition(rewards)
                startFunc && startFunc()
                await go(target, items)
                endFunc && endFunc(rewards)
                eventCenter.emit(EventType.CLAIM_MINE_REWARD_END, target)
            })
        }
        else {
            for (let item of this.items) {
                item.destroy()
            }
        }
  
        //走到下面逻辑的时候，当前脚本已被销毁
        cb && cb()
    }

    update() {
        if (animHelper.isPlanetGenStop(this.aniKey)) {
            this.cb && this.cb()
            for (let item of this.items) {
                if (!cc.isValid(item)) continue
                item.destroy()
            }
        }
    }
}