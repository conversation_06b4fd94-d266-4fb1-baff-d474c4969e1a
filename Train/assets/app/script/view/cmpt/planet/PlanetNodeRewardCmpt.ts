import { MAX_ZINDEX } from "../../../common/constant/Constant";
import { ItemCfg } from "../../../common/constant/DataType";
import { CfgName, ConditionType } from "../../../common/constant/Enums";
import { resHelper } from "../../../common/helper/ResHelper";
import ConditionObj from "../../../model/common/ConditionObj";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetNodeRewardCmpt extends mc.BaseCmptCtrl {

    @property(cc.Node)
    private icon: cc.Node = null

    public reward: ConditionObj = null

    private target: cc.Node = null

    public isDropEnd: boolean = false

    private cb: Function = null

    public init(reward: ConditionObj) {
        this.reward = reward
        this.initView()
    }

    protected async initView() {
        this.icon = this.icon || this.node
        let spr = this.icon.Child("val", cc.Sprite) || this.icon.Component(cc.Sprite)
        if (spr) {
            let reward = this.reward
            let type = reward.type
            if (type == ConditionType.PROP) {
                let json = assetsMgr.getJsonData<ItemCfg>(CfgName.PROP, this.reward.id)
                let useInfo = json?.planetUse
                if (useInfo) {
                    this.icon.scale = useInfo.scale || 1
                    this.icon.angle = -useInfo.rotate || 0
                }
                await resHelper.loadIconByCondInfo(this.reward, this.icon, this.getTag())
            } else {
                await resHelper.loadIconByCondInfo(this.reward, this.icon, this.getTag())
            }
        }
        if (!cc.isValid(this)) return
        this.updateZIndex()
    }

    private updateZIndex() {
        let val = this.icon.Child('val') || this.icon
        let size = val.getContentSize();
        let pos = ut.convertToNodeAR(val, this.node, cc.v2(0, size.height * 0.5))
        let height = pos.y
        this.node.zIndex = MAX_ZINDEX - (this.node.y - height * 0.5)
    }

    public onDropEnd() {
        this.updateZIndex()
    }

    public onClick() {
    }

    public fly(target: cc.Node, cb) {
        if (this.target) return
        this.target = target
        this.cb = cb
        let t = ut.getRandomNum(0.35, 0.6, true, 3)
        let pos = this.node.getPosition()
        let targetPos = this.target.getPosition()
        let vec = targetPos.sub(pos)
        let targetPos1 = new cc.Vec2(pos.x + vec.x * 0.4, pos.y + vec.y * 0.9)
        let targetPos2 = new cc.Vec2(pos.x + vec.x * 0.8, pos.y + vec.y)
        cc.tween(this.node).to(t, { scale: 0.8 * 0.65 }, { easing: cc.easing.sineIn }).promise()
        let typeArray = [targetPos1, targetPos2, targetPos]
        var bezierTo1 = cc.bezierTo(t, typeArray);
        var act = bezierTo1.easing(cc.easeOut(t));
        cc.tween(this.node).then(act).start()
    }
    //x - drtX = y^2 - drtY
    update(dt) {
        if (this.target) {
            let targetPos = this.target.getPosition()
            let pos = this.node.getPosition()
            if (pos.fuzzyEquals(targetPos, 0.01)) {
                this.cb && this.cb()
                this.node.destroy()
            }
        }
    }
}