import { PassengerLifeAnimation, ConditionType, BUILD_MOUNT_POINT, RoleDir, CarriageUsePosType, CarriageID } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import PassengerModel from "../../model/passenger/PassengerModel";
import { resHelper } from "../../common/helper/ResHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { MAX_ZINDEX } from "../../common/constant/Constant";
import StateObj from "../../model/passenger/StateObj";
import { StateType } from "../../model/passenger/StateEnum";
import { animHelper } from "../../common/helper/AnimHelper";
import BuildObj from "../../model/train/common/BuildObj";
import BuildCmpt from "../cmpt/build/BuildCmpt";
import { TimeStateData } from "../../model/passenger/StateDataType";
import { util } from "../../../core/utils/Utils";
import RoleSpeakCmpt from "../cmpt/role/RoleSpeakCmpt";
import { viewHelper } from "../../common/helper/ViewHelper";
import ClickTouchCmpt from "../cmpt/common/ClickTouchCmpt";
import MountPointCmpt from "../cmpt/common/MountPointCmpt";
import { PlotStep } from "../../common/constant/DataType";
import ConditionObj from "../../model/common/ConditionObj";
import { ActType } from "../../model/passenger/themeActions/ActionCfg";
import RoleChessCmpt from "../cmpt/role/RoleChessCmpt";
import FixedNodeTransCmpt from "../cmpt/common/FixedNodeTransCmpt";
import { dropItemHelper } from "../../common/helper/DropItemHelper";
import PassengerDialog from "./PassengerDialog";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PassengerView extends mc.BaseCmptCtrl {

    @property(sp.Skeleton)
    body: sp.Skeleton = null

    @property(cc.Node)
    ui: cc.Node = null

    @property(cc.Node)
    heart: cc.Node = null

    @property(cc.Node)
    plot: cc.Node = null

    @property(cc.Node)
    dialog: cc.Node = null

    @property(cc.Node)
    touch: cc.Node = null

    private prePos: cc.Vec2 = null

    private model: PassengerModel = null

    private state: StateObj<StateType> = null

    private orgParent: cc.Node = null

    private tableWareEmpty: boolean = false

    private convertPosFunc: Function = null
    private convertScaleFunc: Function = null

    private dialogCmpt: PassengerDialog = null

    private refNodes: cc.Node[] = [] //引用节点，当前节点销毁时，引用节点也销毁

    public listenEventMaps() {
        return [
            { [EventType.PASSENGER_HEART_CHANGE]: this.checkAndUpdateTalkUI },
            { [EventType.PASSENGER_FLIP]: this.onSetDir },
            { [EventType.UPDATE_PASSENGER_POS]: this.onUpdatePos },
            { [EventType.CHANGE_PLOT]: this.checkAndUpdateTalkUI },
            { [EventType.END_PLOT]: this.checkAndUpdateTalkUI },
            { [EventType.PASSENGER_PLOT_CHANGE]: this.checkAndUpdateTalkUI },
            { [EventType.PASSENGER_SKIN_CHANGE]: this.onSkinChange },
            { [EventType.PASSENGER_DIALOG_START]: this.onRoleDialogShow },
            { [EventType.PASSENGER_DIALOG_END]: this.onRoleDialogHide },
            { [EventType.PASSENGER_STATE_CHANGE]: this.checkAndUpdateTalkUI },
        ]
    }

    init(model: PassengerModel) {
        this.model = model
        this.model.view = this.node
        this.initView()
        this.Component(RoleSpeakCmpt).init(model.id)
    }

    onCreate() {
        this.orgParent = this.node.parent
    }

    getId() {
        return this.model?.id
    }

    onClean() {
        for (let node of this.refNodes) {
            if (cc.isValid(node)) {
                node.destroy()
            }
        }
    }

    //------------------------- 更新状态
    private async playIdleAnim(data?, type?) {
        let anim = data?.anim
        let sk = this.body
        let build = data?.build
        let mountPoint = data?.mountPoint
        if (!anim) {
            for (let state of this.model.actionAgent.getStates()) {
                anim = this.model.actionAgent.getIdleAnim(state.type)
                if (anim) {
                    break
                }
            }
        }
        if (!anim) {
            anim = PassengerLifeAnimation.IDLE
        }
        anim = this.getSpAnim(anim)
        let elapsed = data?.timeData?.elapsed || 0

        if (sk.animation == anim) return
        let loop = true
        if (data?.loop === false) {
            loop = false
        }
        // console.log("playIdleAnim", this.model.id, anim, elapsed, loop)
        viewHelper.playAnimation(sk, anim, elapsed, loop)

        if (build && mountPoint) {
            let buildNode = this.getBuildNode(build.id) || await this.getBuildNodeAysnc(build.id, type) //直接走异步视图上会闪一下
            if (!buildNode) return
            this.setMountPoint(buildNode, mountPoint)
        }
    }

    private playMoveAnim(data) {
        let anim = data?.anim
        let sk = this.body
        if (!anim) {
            for (let state of this.model.actionAgent.getStates()) {
                anim = this.model.actionAgent.getMoveAnim(state.type)
                if (anim) {
                    break
                }
            }
        }
        if (!anim) {
            anim = PassengerLifeAnimation.WALK
        }
        anim = this.getSpAnim(anim)
        if (sk.animation == anim) return
        if (!sk.findAnimation(anim)) { //兼容没有脚的
            anim = PassengerLifeAnimation.IDLE
        }
        sk.playAnimation(anim, true)
    }

    private playMoveStepAnim(data) {
        let anim = data?.anim || PassengerLifeAnimation.WALK
        let sk = this.body
        let timeData = data.timeData
        sk.playAnimation(anim, timeData.elapsed)
    }

    private getBuildNode(id): cc.Node {
        let root = this.orgParent
        for (let child of root.children) {
            let cmpt = child.getComponent(BuildCmpt)
            if (cmpt && cmpt.model.id == id && cmpt.isActive()) {
                return child
            }
        }
    }

    private async getBuildNodeAysnc(id: string, type: StateType) {
        while (true) {
            let build = this.getBuildNode(id)
            if (build) return build
            await ut.wait(0.1, this)
            if (!cc.isValid(this)) return
            if (!this.model.actionAgent.getState(type)) return
        }
    }

    private async playEnterLeftBedByStair(data) {
        let timeData: TimeStateData = data.timeData
        let build = data.build
        let elapsed = timeData.elapsed
        this.setDir(RoleDir.LEFT)
        let sk = this.body
        sk.setEventListener(({ animation }, { data }) => {
            if (animation.name == PassengerLifeAnimation.TO_LEFT_BED && data.name == "effect") {
                let buildNode = this.getBuildNode(build.id)
                if (!buildNode) {
                    return
                }
                let buildSk = buildNode.Child('body', sp.Skeleton)
                this.setMountPoint(buildNode, BUILD_MOUNT_POINT.SLEEP, this.node.getPosition())
                buildSk.playAnimation("aniSit")
                let body2Node = this.getMountPoint(buildNode, BUILD_MOUNT_POINT.BODY2)
                body2Node.active = true
                sk.setEventListener(null)
            }
        })
        this.playAnimation(sk, PassengerLifeAnimation.TO_LEFT_BED, elapsed)
    }

    private async playEnterBed(data) {
        let timeData: TimeStateData = data.timeData
        let buildNode = this.getBuildNode(data.build.id)
        if (!buildNode) {
            timeData.complete()
            return
        }
        let sk = this.body
        let elapsed = timeData.elapsed

        let jumpTime = data.jumpTime
        let point = this.getMountPoint(buildNode, BUILD_MOUNT_POINT.SLEEP)

        this.setDir(RoleDir.RIGHT)
        if (jumpTime) {
            let anim = PassengerLifeAnimation.STAND_TO_SIT
            anim = this.getSpAnim(anim)
            elapsed = await this.playJump(jumpTime, point, elapsed)
            elapsed = await this.playAnimation(sk, anim, elapsed)
        }

        let body2Node = this.getMountPoint(buildNode, BUILD_MOUNT_POINT.BODY2)
        body2Node.active = false
        this.setMountPoint(buildNode, BUILD_MOUNT_POINT.SLEEP) //换到挂点上

        if (jumpTime) {
            //弹一弹
            let cmpt = buildNode.Component(BuildCmpt)
            let func = cmpt["playOnJumpEnter"]
            if (func) {
                func.call(cmpt)
            }
            let anim = PassengerLifeAnimation.SIT_DAP
            anim = this.getSpAnim(anim)
            elapsed = await this.playAnimation(sk, anim, elapsed)
        }

        if (data.getDown) {
            let anim = PassengerLifeAnimation.GET_DOWN
            anim = this.getSpAnim(anim)
            sk.setEventListener(({ animation }, { data }) => {
                if (animation.name == anim && data.name == "effect") {
                    body2Node.active = true
                    sk.setEventListener(null)
                }
            })
            elapsed = await this.playAnimation(sk, anim, elapsed)
        }
    }

    private async playEnterRightBedByBookcase(data) {
        let timeData: TimeStateData = data.timeData
        let build = data.build
        let elapsed = timeData.elapsed
        this.setDir(RoleDir.LEFT)
        let sk = this.body
        sk.setEventListener(({ animation }, { data }) => {
            if (animation.name == PassengerLifeAnimation.TO_RIGHT_BED && data.name == "effect") {
                let buildNode = this.getBuildNode(build.id)
                if (!buildNode) {
                    return
                }
                let buildSk = buildNode.Child('body', sp.Skeleton)
                this.setMountPoint(buildNode, BUILD_MOUNT_POINT.SLEEP, this.node.getPosition())
                buildSk.playAnimation("aniSit")
                let body2Node = this.getMountPoint(buildNode, BUILD_MOUNT_POINT.BODY2)
                body2Node.active = true
                sk.setEventListener(null)
            }
        })
        this.playAnimation(sk, PassengerLifeAnimation.TO_RIGHT_BED, elapsed)
    }

    private setMountPoint(node: cc.Node, pointName: string = "guadian", pos?) {
        let point = this.getMountPoint(node, pointName)
        if (!point) {
            console.warn("mount_point not found", node.getPath(), pointName)
        }
        this.node.setParent(point)
        this.node.zIndex = 0
        if (pos) {
            this.node.setPosition(ut.convertToNodeAR(this.orgParent, point, pos))
        }
        else {
            this.node.setPosition(0, 0)
        }
    }

    private getMountPoint(node: cc.Node, pointName: string = "guadian") {
        let cmpt = node.Component(MountPointCmpt)
        if (!cmpt) return
        return cmpt.getPoint(pointName)
    }

    private async playExitBed(data) {
        let timeData: TimeStateData = data.timeData
        let pos = data.pos
        let buildNode = this.getBuildNode(data.build.id)
        if (!buildNode) {
            timeData.complete()
            return
        }
        let body2Node = this.getMountPoint(buildNode, BUILD_MOUNT_POINT.BODY2)
        body2Node.active = true
        this.setMountPoint(buildNode, BUILD_MOUNT_POINT.SLEEP)
        let downTime = data.downTime
        let sk = this.body
        let elapsed = timeData.elapsed

        let exitMountPoint = () => {
            body2Node.active = false
            let pos = ut.convertToNodeAR(this.node, this.orgParent)
            this.node.setParent(this.orgParent)
            this.node.setPosition(pos)
            this.updateZIndex()
        }

        if (data.getUp) {
            sk.setEventListener(({ animation }, { data, time }) => {
                if (animation.name == PassengerLifeAnimation.GET_UP && data.name == "effect") {
                    exitMountPoint()
                    sk.setEventListener(null)
                }
            })
            elapsed = await this.playAnimation(sk, PassengerLifeAnimation.GET_UP, elapsed)
        }
        else {
            exitMountPoint()
        }

        if (downTime) {
            elapsed = await this.playDown(downTime, pos, elapsed)
        }
        else {
            this.node.setPosition(pos)
        }
    }

    private async playSit(data) {
        let build = data.build as BuildObj

        let anim = data?.anim || PassengerLifeAnimation.SIT
        if (this.body.animation != anim) {
            this.body.playAnimation(anim, true)
        }
        this.setDir(RoleDir.RIGHT)

        let buildNode = this.getBuildNode(build.id) || await this.getBuildNodeAysnc(build.id, StateType.SIT) //直接走异步视图上会闪一下
        if (!buildNode) return
        let pointStr = data?.mountPoint || BUILD_MOUNT_POINT.SIT
        this.setMountPoint(buildNode, pointStr)
        let pos = build.getSitPos(this.model.id)
        if (pos) this.node.setPosition(pos)

        let cmpt = buildNode.Component(BuildCmpt)
        let func = cmpt["playOnSit"]
        if (func) {
            func.call(cmpt, pointStr)
        }
        else {
            let body2Node = this.getMountPoint(buildNode, BUILD_MOUNT_POINT.BODY2)
            if (body2Node) {
                body2Node.active = true
            }
        }
    }

    private async playAnimWithSit() {
        let actionAgent = this.model.actionAgent
        let sit = actionAgent.getState(StateType.SIT)
        this.playSit(sit.data)

        let cfgs = [
            { func: this.playEat, type: StateType.EAT },
            { func: this.playDrink, type: StateType.DRINK },
            { func: this.playSleep, type: StateType.SLEEP },
            { func: this.playAct, type: StateType.PLAY_ACT },
            { func: this.playEngineComputer, type: StateType.ENGINE_COMPUTER },
        ]

        for (let { type, func } of cfgs) {
            let state = actionAgent.getState(type)
            if (state) {
                func.call(this, state.data)
                break
            }
        }
    }

    private async playSleep(data) {
        let build = data.build
        let anim = data.anim || PassengerLifeAnimation.SLEEP
        this.body.playAnimation(anim, true)

        if (build) {
            this.setDir(RoleDir.RIGHT)
            let buildNode = this.getBuildNode(data.build.id) || await this.getBuildNodeAysnc(data.build.id, StateType.SLEEP)
            if (!buildNode) return
            let mountPoint = data?.mountPoint || BUILD_MOUNT_POINT.SLEEP
            this.setMountPoint(buildNode, mountPoint)
            let body2Node = this.getMountPoint(buildNode, BUILD_MOUNT_POINT.BODY2)
            if (body2Node) {
                body2Node.active = true
            }
            this.updateSleepEffect()
        }
    }

    private async playEat(data) {
        let sk = this.body
        let eatAnim = data.anim || PassengerLifeAnimation.SIT_EAT
        let eatDur = sk.getAnimationDuration(eatAnim)
        let munchAnim = data.animMunch || PassengerLifeAnimation.SIT_MUNCH
        let munchCount = data.munchCount || 0
        let munchDur = sk.getAnimationDuration(munchAnim)
        let oneLoopTime = eatDur + munchDur * munchCount
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        if (oneLoopTime <= 0) {
            timeData.complete()
            return
        }
        elapsed = elapsed % (oneLoopTime)
        let actionAgent = this.model.actionAgent

        let anim = sk.findAnimation(eatAnim)
        if (!anim) {
            return twlog.error("playEat eatAnim not found", this.model.id, eatAnim)
        }
        anim = sk.findAnimation(munchAnim)
        if (!anim) {
            return twlog.error("playEat munchAnim not found", this.model.id, munchAnim)
        }
        this.tableWareEmpty = false
        sk.setEventListener(({ animation }, { data, time }) => { //食物到嘴边了，需要做一些操作，如把勺子变成空
            if (animation.name == eatAnim && data.name == "effect") {
                this.tableWareEmpty = true
                this.updateTableWare()
            }
        })
        let count = 1000
        while (count--) {
            elapsed = await this.playAnimation(sk, eatAnim, elapsed)
            if (!actionAgent.getState(StateType.EAT)) return
            this.tableWareEmpty = false
            this.updateTableWare()

            for (let i = 0; i < munchCount; i++) {
                elapsed = await this.playAnimation(sk, munchAnim, elapsed)
                if (!actionAgent.getState(StateType.EAT)) return
            }
        }
    }

    private async playDrink(data) {
        let sk = this.body
        let drinkAnim = data.anim || PassengerLifeAnimation.SIT_DRINK
        let drinkDur = sk.getAnimationDuration(drinkAnim)
        let tasteAnim = data.animTaste || PassengerLifeAnimation.SIT_TASTE
        let tasteCount = data.tasteCount || 0
        let tasteDur = sk.getAnimationDuration(tasteAnim)
        let oneLoopTime = drinkDur + tasteDur * tasteCount
        let timeData = data.timeData
        if (oneLoopTime <= 0) {
            timeData.complete()
            return
        }
        let elapsed = timeData.elapsed
        elapsed = elapsed % (oneLoopTime)
        let actionAgent = this.model.actionAgent
        let state = StateType.DRINK

        let anim = sk.findAnimation(drinkAnim)
        if (!anim) {
            return twlog.error("playDrink eatAnim not found", this.model.id, drinkAnim)
        }
        anim = sk.findAnimation(tasteAnim)
        if (!anim) {
            return twlog.error("playEat munchAnim not found", this.model.id, tasteAnim)
        }

        let count = 1000
        while (count--) {
            elapsed = await this.playAnimation(sk, drinkAnim, elapsed)
            if (!actionAgent.getState(state)) return
            for (let i = 0; i < tasteCount; i++) {
                elapsed = await this.playAnimation(sk, tasteAnim, elapsed)
                if (!actionAgent.getState(state)) return
            }
        }
    }

    private async playAct(data) {
        let anim = data.anim
        if (data.type) {
            return this.playAct2(data)
        }
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        this.body.playAnimation(anim, data.loop, elapsed)
    }

    private async playAct2(data, stateType: StateType = StateType.PLAY_ACT) {
        let sk = this.body
        let actionAgent = this.model.actionAgent
        let { timeData, type, loopInfos, cfg } = data
        let elapsed = timeData.elapsed
        let startAnim = cfg.start, endAnims = cfg.end, loopAnim = cfg.loop?.anim, loopEndAnims = cfg.loopEndAnims || []
        let preElapsed, need

        let handle = async (anim) => {
            preElapsed = timeData.elapsed
            need = sk.getAnimationDuration(anim) - elapsed
            elapsed = await this.playAnimation(sk, anim, elapsed)
            let de = Math.max(0, timeData.elapsed - preElapsed - need)
            // console.log("handle ", anim, timeData.elapsed - preElapsed, need, de)
            if (elapsed < 0) elapsed = 0
            elapsed += de
        }

        if (startAnim) {
            await handle(startAnim)
            if (!actionAgent.getState(stateType)) return
        }

        for (let info of loopInfos) {
            for (let i = 0; i < info.count; i++) {
                await handle(loopAnim)
                if (!actionAgent.getState(stateType)) return
            }
            if (info.needEnd) {
                for (let anim of loopEndAnims) {
                    await handle(anim)
                    if (!actionAgent.getState(stateType)) return
                }
            }
        }

        if (endAnims) {
            if (!Array.isArray(endAnims)) {
                endAnims = [endAnims]
            }
            for (let anim of endAnims) {
                await handle(anim)
                if (!actionAgent.getState(stateType)) return
            }
        }

        if (loopAnim) {
            this.playAnimation(sk, loopAnim, 0, true)
        }
    }

    private async playJump(jumpTime, point, elapsed) {
        let sk = this.body
        let orgJumpTime = jumpTime
        let anim = PassengerLifeAnimation.JUMP
        anim = this.getSpAnim(anim)
        jumpTime -= cc.misc.clampf(elapsed, 0, jumpTime)
        sk.setEventListener(({ animation }, { data, time }) => {
            if (animation.name == anim && data.name == "effect") {
                let jumpPos = ut.convertToNodeAR(point, this.node.parent)
                let dur = Math.max(0, jumpTime - time)
                cc.tween(this.node).then(cc.jumpTo(dur, jumpPos, 100, 1)).start()
                sk.setEventListener(null)
            }
        })
        this.setDir(RoleDir.RIGHT)

        this.playAnimation(sk, anim, elapsed)

        await ut.wait(jumpTime, this)
        elapsed -= orgJumpTime
        return elapsed
    }

    private async playDown(downTime, pos, elapsed) {
        let sk = this.body
        let orgDownTime = downTime
        downTime -= cc.misc.clampf(elapsed, 0, downTime)
        sk.setEventListener(({ animation }, { data, time }) => {
            if (animation.name == PassengerLifeAnimation.DOWN && data.name == "effect") {
                let dur = Math.max(downTime - time)
                cc.tween(this.node).then(cc.jumpTo(dur, pos, 0, 1)).start()
                sk.setEventListener(null)
            }
        })
        this.playAnimation(sk, PassengerLifeAnimation.DOWN, elapsed)
        await ut.wait(downTime, this)
        elapsed -= orgDownTime

        return await this.playAnimation(sk, PassengerLifeAnimation.SIT_TO_STAND, elapsed)
    }

    private async playEnterStair(data) {
        let buildNode = this.getBuildNode(data.build.id)
        let timeData = data.timeData
        if (!buildNode) {
            timeData.complete()
            return
        }
        let sk = this.body
        let elapsed = data.timeData.elapsed

        let jumpTime = data.jumpTime
        let point = this.getMountPoint(buildNode, BUILD_MOUNT_POINT.SIT)
        this.setDir(RoleDir.RIGHT)
        elapsed = await this.playJump(jumpTime, point, elapsed)
        elapsed = await this.playAnimation(sk, PassengerLifeAnimation.STAND_TO_SIT, elapsed)
    }

    private async playExitStair(data) {
        let downTime = data.downTime
        let pos = data.pos
        let buildNode = this.getBuildNode(data.build.id)
        let timeData = data.timeData
        if (!buildNode) {
            timeData.complete()
            return
        }
        let point = this.getMountPoint(buildNode, BUILD_MOUNT_POINT.SIT)
        let elapsed = data.timeData.elapsed
        this.setDir(RoleDir.RIGHT)
        this.node.setPosition(ut.convertToNodeAR(point, this.node.parent))
        elapsed = await this.playDown(downTime, pos, elapsed)
    }

    private playWatchTV(data) {
        let sk = this.body
        let elapsed = data.timeData.elapsed
        this.setDir(data.index ? RoleDir.LEFT : RoleDir.RIGHT)
        this.playAnimation(sk, PassengerLifeAnimation.IDLE, elapsed, true)
    }

    private playCook(data) {
        let sk = this.body
        let anim = data?.anim || PassengerLifeAnimation.COOK
        this.setDir(RoleDir.RIGHT)
        this.playAnimation(sk, anim, 0, true)
    }

    private async playFish(data) {
        let sk = this.body
        let timeData = data.timeData
        let waitTime = data.waitTime
        let elapsed = timeData.elapsed
        let root = this.node.parent.parent.Child("root")
        if (!root) {
            return timeData.complete()
        }
        ut.convertParent(this.node, root)
        elapsed = await this.playAnimation(sk, PassengerLifeAnimation.FISH_START, elapsed)
        this.playAnimation(sk, PassengerLifeAnimation.FISH, elapsed, true)
        elapsed -= waitTime
        await ut.wait(waitTime, this)
        elapsed = this.playAnimation(sk, PassengerLifeAnimation.FISH_END, elapsed)
    }

    private playStargaze(data) {
        let sk = this.body
        let anim = data.anim
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        let root = this.orgParent?.parent?.Component("TrainHeadCmpt")?.getBody()
        if (!root) {
            return timeData.complete()
        }
        let node = root.Component(sp.Skeleton).getAttachedNode("guadian_xiaoniao")
        this.node.parent = node
        this.node.setPosition(0, 0)
        this.playAnimation(sk, anim, elapsed, true)
    }

    private playWalkTreadmill(data) {
        let sk = this.body
        let anim = data.anim
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        let build = data.build
        let buildNode = this.getBuildNode(build.id)
        if (!buildNode) {
            return timeData.complete()
        }
        this.setMountPoint(buildNode, BUILD_MOUNT_POINT.WALK)
        this.setDir(RoleDir.LEFT)
        this.playAnimation(sk, anim, elapsed, true)
    }

    private async playPull(data) {
        let build = data.build
        let timeData: TimeStateData = data.timeData
        let anim = data.anim
        let elapsed = timeData.elapsed
        let buildNode = this.getBuildNode(build.id)
        if (!buildNode) {
            return timeData.complete()
        }
        this.setDir(RoleDir.RIGHT)
        this.setMountPoint(buildNode)
        this.playAnimation(this.body, anim, elapsed, true)
    }

    private async playEngineComputer(data) {
        let timeData = data.timeData
        let elapsed = timeData.elapsed
        let sk = this.body
        this.setDir(RoleDir.RIGHT)
        this.playAnimation(sk, data.anim, elapsed, true)
    }

    private async playEngineAddFuel(data) {
        let fuelType = data?.fuelType
        if (fuelType < 3) {
            this.playThrow(data, node => {
                node.Component(cc.MultiFrame).setFrame(fuelType)
            })
        }
        else if (fuelType == 3) {
            this.playThrow(data, null, async(root)=>{
                await cc.tween(root).then(cc.jumpTo(data.throwTime, data.targetPos, 200, 1)).promise()
                let water = root.Child("water")
                if (water) {
                    let effect: cc.Node = water.Swih("effect")[0]
                    await effect.Component(sp.Skeleton).playAnimation("exit")
                }
                root.removeAndDestroy()
            })
        }
    }

    private async playThrow(data, initAttachment?, flyAnim?) {
        let type = StateType.ENGINE_ADD_FUEL
        let attachNode = this.body.getAttachedNode(data.mountPoint)
        if (!attachNode) {
            twlog.error("playEngineAddFuel attachNode not found", data.mountPoint)
        }
        let root = new cc.Node("engine_fuel")
        root.parent = attachNode
        root.active = false
        let nodeName = data.nodeName || "engine_fuel"
        this.updateAttachment(root, nodeName, type).then(node => {
            if (!node) return
            initAttachment && initAttachment(node)
        })
        let sk = this.body
        let elapsed = data.elapsed
        let anim = data.anim
        let handleThrow = () => {
            if (!root.parent) return
            ut.convertParent(root, this.orgParent)
            root.zIndex = MAX_ZINDEX
            if (flyAnim) {
                flyAnim(root)
            }
            else {
                cc.tween(root).then(cc.jumpTo(data.throwTime, data.targetPos, 200, 1)).to(0.1, { opacity: 0, scale: 0.5 }).removeSelf().start()
            }
        }
        if (anim == PassengerLifeAnimation.ENGINE_THROW) {
            sk.setEventListener(({ animation }, { data }) => {
                if (animation.name != anim) return
                if (data.name == "effect") {
                    root.active = true
                }
                else if (data.name == "effect2") {
                    root.active = true
                    handleThrow()
                    sk.setEventListener(null)
                }
            })
        }
        else {
            handleThrow()
        }
        await this.playAnimation(sk, anim, elapsed)
        this.playAnimation(sk, PassengerLifeAnimation.IDLE)
    }

    private playDiningFoodPull({ timeData }) {
        this.setDir(RoleDir.LEFT)
        this.playAnimation(this.body, PassengerLifeAnimation.DINING_WORK, timeData.elapsed)
    }

    private playDiningFoodQueue(data) {
        this.setDir(RoleDir.LEFT)
        this.playIdleAnim()
    }

    private playDiningDrinkPull({ timeData }) {
        this.setDir(RoleDir.RIGHT)
        this.playAnimation(this.body, PassengerLifeAnimation.DINING_WORK, timeData.elapsed)
    }

    private playDiningDrinkQueue(data) {
        this.setDir(RoleDir.RIGHT)
        this.playIdleAnim()
    }

    private playWCQueue(data) {
        this.setDir(RoleDir.LEFT)
        this.playIdleAnim()
    }

    private async playTakeBath(data) {
        let anim = data?.anim || PassengerLifeAnimation.ANI_BATH
        let buildNode = this.getBuildNode(data.buildId) || await this.getBuildNodeAysnc(data.buildId, StateType.TAKE_BATH)
        if (!buildNode) {
            return
        }
        this.setDir(RoleDir.RIGHT)
        this.body.playAnimation(anim, true)
        this.setMountPoint(buildNode, `bath${data.index}`)
    }

    private playDancing(data) {
        let anim = data?.anim || PassengerLifeAnimation.ANI_DANCE
        let _dir = data.dir
        this.setDir(_dir)
        this.body.playAnimation(anim, true)
    }

    private getSpAnim(anim) {
        let newAnim
        if (this.model.id == 1006 && gameHelper.guide.needForbidGoGarden2()) {
            let [_, name] = anim.split("/")
            newAnim = `sp/${name}`
        }
        if (newAnim) {
            if (this.body.findAnimation(newAnim)) {
                return newAnim
            }
            twlog.error("getSpAnim not found", newAnim, anim)
        }
        return anim
    }

    @util.addLock
    private async updateSuitcase() {
        let type = StateType.CHECK_IN
        let node = await this.updateMountPointAttachment(type, "guadian_xinglixiang", "checkIn_xinglixiang")
        if (!node) return
        let actionAgent = this.model.actionAgent
        node.scale = this.model.suitcaseScale
        let data = actionAgent.getState(StateType.CHECK_IN_TIDY)?.data
        let anim = data?.anim
        if (!anim) {
            if (this.model.isMoving()) {
                anim = actionAgent.getMoveAnim(type)
            }
            else {
                anim = actionAgent.getIdleAnim(type)
            }
        }
        let elapsed = data?.timeData?.elapsed || 0
        let sk = node.Component(sp.Skeleton)

        anim = anim.split("/")[1]
        if (anim == PassengerLifeAnimation.CHECK_IN_TIDY) {
            sk.playAnimation(anim, false, elapsed)
        }
        else {
            sk.playAnimation(anim, true)
        }
    }

    @util.addLock
    private async updateSleepEffect() {
        let type = StateType.SLEEP
        let node = await this.updateMountPointAttachment(type, "shuijiao")
        if (!node) return
        let state = this.model.actionAgent.getState(type)
        if (state?.data?.hideEffect) {
            node.active = false
        }
        node.Component(sp.Skeleton).timeScale = 0.5
        node.Component(FixedNodeTransCmpt)?.updateFlipX()
    }

    @util.addLock
    private async updateMusicEffect() {
        let type = StateType.PLAY_ACT
        let node = await this.updateMountPointAttachment(type, "guadian_yinfu", "yinfu")
        if (!node) return

        let state = this.model.actionAgent.getState(type)
        let actionTypes = [ActType.PLAY_GUITAR, ActType.PLAY_BASS]
        node.active = actionTypes.includes(state.data.type)
        node.Component(FixedNodeTransCmpt)?.updateFlipX()
    }

    private async updateMountPointAttachment(type, mountPointName, attachmentName?) {
        let actionAgent = this.model.actionAgent
        let attachNode = this.body.getAttachedNode(mountPointName)
        if (!attachNode) {
            let state = actionAgent.getState(type)
            if (state) {
                console.warn("attachNode not found", this.model.id, mountPointName)
            }
            return
        }
        attachmentName = attachmentName || mountPointName
        return this.updateAttachment(attachNode, attachmentName, () => {
            return actionAgent.getState(type)
        })
    }

    private async updateAttachment(root: cc.Node, attachmentName: string, check: Function | StateType, nodeName: string = attachmentName) {
        if (typeof check == "number") {
            const stateType = check as StateType;
            check = () => {
                return this.model.actionAgent.getState(stateType)
            }
        }
        if (!check()) {
            return this.clearAttachment(root, attachmentName, nodeName)
        }
        let url = `role/attachment/${attachmentName}`
        let node: cc.Node = root.getChildByName(nodeName)
        if (!node) {
            let prefab = await assetsMgr.loadTempRes(url, cc.Prefab, this.getTag())
            if (!prefab || !cc.isValid(root) || !check()) {
                if (prefab) {
                    assetsMgr.releaseTempRes(url, this.getTag())
                }
                return
            }
            node = root.getChildByName(nodeName)
            if (!node) {
                node = cc.instantiate2(prefab, root)
                node.name = nodeName
            }
        }
        return node
    }

    private clearMountPointAttachment(mountPointName, attachmentName?) {
        let attachNode = this.body.getAttachedNode(mountPointName)
        if (!attachNode) return
        attachmentName = attachmentName || mountPointName
        this.clearAttachment(attachNode, attachmentName)
    }

    private clearAttachment(root: cc.Node, attachmentName, nodeName: string = attachmentName) {
        let node: cc.Node = root.getChildByName(nodeName)
        let url = `role/attachment/${attachmentName}`
        if (node) {
            assetsMgr.releaseTempRes(url, this.getTag())
            node.removeAndDestroy()
        }
    }

    private async updateFood() {
        let actionAgent = this.model.actionAgent
        let type = StateType.HOLD_FOOD
        let state = actionAgent.getState(type)
        let slot = state?.data?.slot || "food"
        let food = state?.data?.food
        let slots = ["food", "handsFood", "panzi", "beizi"]
        let url = `food/${food}`
        this.switchSlotAttachment(slot, url, type, slots)
    }

    private async updateTableWare() {
        let type = StateType.HOLD_TABLEWARE
        let state = this.model.actionAgent.getState(type)
        let cutlery = state?.data || 'spoon'

        let url = 'food/' + cutlery
        if (this.tableWareEmpty) {
            url += '2'
        }
        this.updateSlotAttachment("shaozi", url, type, null, false)
    }

    private updateBathPaopao() {
        let state = this.model.actionAgent.getState(StateType.TAKE_BATH)
        let icon = state?.data?.paoIcon
        let name = "guadian_paozao"
        let point = this.body.getAttachedNode(name)
        if (!point) return
        let sp: cc.Sprite = point.Child(name, cc.Sprite)
        if (!sp) {
            let node = new cc.Node(name)
            node.parent = point
            sp = node.addComponent(cc.Sprite)
        }
        if (icon) {
            resHelper.loadTmpIcon(`food/${icon}`, sp, this.getTag())
        } else {
            resHelper.releaseSpriteSpf(sp)
        }
        this.heartMustUpPaozao()
    }

    private getPaozaoUI(): cc.Node {
        let name = "guadian_paozao"
        let point = this.body.getAttachedNode(name)
        if (!point) return
        let sp: cc.Sprite = point.Child(name, cc.Sprite)
        if (sp && resHelper.haveRefSpf(sp)) {
            return sp.node
        }
    }

    //--------------列车每日任务--------------------------------
    private handlePatrol(type) {
        let sk = this.body.Component(sp.Skeleton)
        if (sk.findSlot("mojing")) {
            this.updateSlotAttachmentByOne("mojing", "passenger/attachment/dz_xunluo2", type)
        }
        else {
            this.updateMountPointAttachment(type, "guadian_mojing", "dz_xunluo2");
        }
        if (sk.findSlot("guadian_phone")) {
            this.updateSlotAttachmentByOne("guadian_phone", "passenger/attachment/dz_xunluo1", type)
        }
        else {
            this.updateMountPointAttachment(type, "guadian_phone", "dz_xunluo1");
        }
    }
    private handleOnRepair(type) {
        let sk = this.body.Component(sp.Skeleton)
        if (sk.findSlot("banshou")) {
            this.updateSlotAttachmentByOne("banshou", "passenger/attachment/dz_xiuli1", type)
        }
        else {
            this.updateMountPointAttachment(type, "guadian_banshou", "dz_xiuli1");
        }
        if (sk.findSlot("gongjuxiang")) {
            this.updateSlotAttachmentByOne("gongjuxiang", "passenger/attachment/dz_xiuli2", type)
        }
        else {
            this.updateMountPointAttachment(type, "guadian_gongjuxiang", "dz_xiuli2");
        }
    }
    private handleLostSearch(type) {
        this.updateMountPointAttachment(type, "guadian_phone", "dz_fangdajing");
    }
    private async handleOverTime(type) {
        this.updateSlotAttachmentByOne("guadian_phone", "passenger/attachment/dz_diannao", type)
        let node = await this.updateAttachment(this.Child("back"), "work_effect", type)
        if (!node) return
        // node.scale = this.body.node.height / 391
    }
    private handleOnClean(type) {
        let actionAgent = this.model.actionAgent
        let state = actionAgent.getState(type)
        let cleanType = state?.data?.cleanType
        let url = "passenger/attachment/dz_dasao"
        this.updateSlotAttachmentByOne("xichenqi", url, ()=>{
            return cleanType == CarriageUsePosType.CLEAN_FLOOR
        })
    }
    private handleClean(type) {
        let actionAgent = this.model.actionAgent
        let state = actionAgent.getState(StateType.ON_CLEAN)
        let cleanType = state?.data?.cleanType
        let url = "passenger/attachment/jimaodanzi"
        this.updateSlotAttachmentByOne("jimaodanzi", url, ()=>{
            return cleanType == CarriageUsePosType.CLEAN && actionAgent.getState(StateType.CLEAN)
        })
    }
    private async handleRepair(data) {
        let type = StateType.REPAIR
        let root = this.Child("front")
        let node = await this.updateAttachment(root, "repair_effect", type)
        if (!node) return

        let mountPoint = this.body.getAttachedNode("guadian_banshou")
        node.setPosition(ut.convertToNodeAR(mountPoint, node.parent, cc.v2(91, 106)))
    }
    private async handleStand(type) {
        let root = this.orgParent
        let name = "dz_zhangang"
        let node: cc.Node | void
        let state = this.model.actionAgent.getState(type)
        let nodeName = `${name}_${this.model.id}`
        node = root.getChildByName(nodeName)
        if (!node) {
            node = await this.updateAttachment(root, name, type, nodeName)
            if (node) {
                this.refNodes.push(node)
            }
        }
        if (!node) {
            return
        }
        node.zIndex = 0
        node.setPosition(this.model.getPosition())

        let data = state?.data
        let front = node.Child("front", sp.Skeleton)
        let back = node.Child("back")
        if (!data) return
        if (data.type == 1) {
            back.active = false
            front.playAnimation("loop1", true)
        }
        else {
            back.active = true
            front.playAnimation("loop2_2", true)
        }

        this.node.parent = node.Child("role")
        this.node.setPosition(0, 0)
    }
    private handleInspect(type) {
        this.updateMountPointAttachment(type, "guadian_phone", "dz_chaqin");
    }
    private async handleClothesClean(type) {
        let root = this.Child("front")
        let node = await this.updateAttachment(root, "dz_wash", type)
        if (!node) return
        node.scaleX = this.model.getDir() == RoleDir.LEFT ? -1 : 1
    }
    private async handleSafetyPromotion(type) {
        let node: cc.Node | void = await this.updateMountPointAttachment(type, "guadian_phone", "dz_laba");
        if (!node) return
        let effect = node.Child("effect")
        cc.Tween.stopAllByTarget(effect)
        let time = 0.15
        cc.tween(effect)
        .call(() => { effect.opacity = 255 })
        .delay(time)
        .call(() => { effect.opacity = 0 })
        .delay(time)
        .call(() => { effect.opacity = 255 })
        .delay(time)
        .call(() => { effect.opacity = 0 })
        .delay(1)
        .union()
        .repeatForever()
        .start();
    }
    private handleLifeGoods(type) {
        this.updateSlotAttachmentByOne("xichenqi", "passenger/attachment/dz_shenghuotuiche", type)
    }
    private updateTrainDailyTask() {
        let cfgs = [
            { type: StateType.PATROL, func: this.handlePatrol },
            { type: StateType.ON_REPAIR, func: this.handleOnRepair },
            { type: StateType.REPAIR, func: this.handleRepair },
            { type: StateType.OVERTIME, func: this.handleOverTime },
            { type: StateType.ON_CLEAN, func: this.handleOnClean },
            { type: StateType.CLEAN, func: this.handleClean },
            { type: StateType.STAND, func: this.handleStand },
            { type: StateType.LOST_SEARCH, func: this.handleLostSearch },
            { type: StateType.INSPECT, func: this.handleInspect },
            { type: StateType.CLOTHES_CLEAN, func: this.handleClothesClean },
            { type: StateType.SAFETY_PROMOTION, func: this.handleSafetyPromotion },
            { type: StateType.LIFE_GOODS, func: this.handleLifeGoods },
        ];
        for (const cfg of cfgs) {
            cfg.func.call(this, cfg.type);
        }
    }
    //-------------------------------------------

    //--------------列车突发任务--------------------------------
    private updateTrainBurstTask() {
        let cfgs = [
        ]
        for (const cfg of cfgs) {
            cfg.func.call(this, cfg.type);
        }
    }
    //-------------------------------------------

    private async switchSlotAttachment(slotName: string, url: string, check: Function | StateType, slots: string[], setFunc?: Function, needRelease: boolean = true) {
        let actionAgent = this.model.actionAgent
        return resHelper.switchSlotAttachment(this.body, slotName, url, this.getTag(), () => {
            if (typeof check == "function") return check()
            else return actionAgent.getState(check)
        }, slots, setFunc, needRelease)
    }

    private async updateSlotAttachment(slotName: string, url: string, check?: Function | StateType, setFunc?: Function, needRelease: boolean = true, clearFilter?: (url: any) => boolean) {
        let actionAgent = this.model.actionAgent
        return resHelper.updateSlotAttachment(this.body, slotName, url, this.getTag(), () => {
            if (typeof check == "function") return check()
            else return actionAgent.getState(check)
        }, setFunc, needRelease, clearFilter)
    }

    private async updateSlotAttachmentByOne(slotName: string, url: string, check?: Function | StateType, setFunc?: Function, needRelease: boolean = true, clearFilter?: (url: any) => boolean) {
        return this.updateSlotAttachment(slotName, url, check, setFunc, needRelease, (info) => {
            return url == info.url
        })
    }

    public resetParent() {
        this.node.setParent(this.orgParent)
        this.prePos = null
        this.updatePosition()
    }

    private updateState() {
        if (!this.body.skeletonData) return

        let actionAgent = this.model.actionAgent
        let state = actionAgent?.state
        if (this.state == state) return
        this.state = state

        let states = actionAgent.getStates()
        let types = []
        for (let key in StateType) {
            let val: any = StateType[key]
            if (states.find(s => s.type == val)) {
                types.push(key)
            }
        }
        // twlog.info("state change", this.model.id, types)

        if (this.node.parent !== this.orgParent) {
            this.resetParent()
        }

        this.updatePosition()

        let type = state?.type
        let data = state?.data

        if (!state) {
            this.playIdleAnim()
        }
        else if (type == StateType.IDLE) {
            this.playIdleAnim(data)
        }
        else if (actionAgent.getState(StateType.MOVE)) {
            if (state.type == StateType.MOVE_STEP) {
                this.playMoveStepAnim(data)
            }
            else {
                this.playMoveAnim(data)
            }
        }
        else if (actionAgent.getState(StateType.SIT)) {
            this.playAnimWithSit()
        }
        else if (type == StateType.ENTER_BED) {
            this.playEnterBed(data)
        }
        else if (type == StateType.ENTER_LEFT_BED_BY_STAIR) {
            this.playEnterLeftBedByStair(data)
        }
        else if (type == StateType.ENTER_RIGHT_BED_BY_BOOKCASE) {
            this.playEnterRightBedByBookcase(data)
        }
        else if (type == StateType.SLEEP) {
            this.playSleep(data)
        }
        else if (type == StateType.EXIT_BED) {
            this.playExitBed(data)
        }
        else if (type == StateType.ENTER_STAIR) {
            this.playEnterStair(data)
        }
        else if (type == StateType.EXIT_STAIR) {
            this.playExitStair(data)
        }
        else if (type == StateType.PLAY_ACT) {
            this.playAct(data)
        }
        else if (type == StateType.WATCH_TV) {
            this.playWatchTV(data)
        }
        else if (type == StateType.COOK) {
            this.playCook(data)
        }
        else if (type == StateType.EAT) {
            this.playEat(data)
        }
        else if (type == StateType.DRINK) {
            this.playDrink(data)
        }
        else if (type == StateType.FISH) {
            this.playFish(data)
        }
        else if (type == StateType.STARGAZE) {
            this.playStargaze(data)
        }
        else if (type == StateType.WALK_TREADMILL) {
            this.playWalkTreadmill(data)
        }
        else if (type == StateType.ENGINE_PULL) {
            this.playPull(data)
        }
        else if (type == StateType.ENGINE_COMPUTER) {
            this.playEngineComputer(data)
        }
        else if (type == StateType.ENGINE_ADD_FUEL) {
            this.playEngineAddFuel(data)
        }
        else if (type == StateType.DINING_FOOD_PULL) {
            this.playDiningFoodPull(data)
        }
        else if (type == StateType.DINING_FOOD_QUEUE) {
            this.playDiningFoodQueue(data)
        }
        else if (type == StateType.DINING_DRINK_PULL) {
            this.playDiningDrinkPull(data)
        }
        else if (type == StateType.DINING_DRINK_QUEUE) {
            this.playDiningDrinkQueue(data)
        }
        else if (type == StateType.WC_QUEUE) {
            this.playWCQueue(data)
        }
        else if (type == StateType.TAKE_BATH) {
            this.playTakeBath(data)
        }
        else if (type == StateType.DANCING) {
            this.playDancing(data)
        }
        else {
            this.playIdleAnim(data, type)
        }

        this.updateSuitcase()
        this.updateFood()
        this.updateTableWare()
        this.updateBathPaopao()
        this.updateSleepEffect()
        this.updateMusicEffect()
        this.updateTrainDailyTask()
        this.updateTrainBurstTask()
        this.checkRefNodes()
    }
    //-------------------------------------------

    private async playAnimation(sk, animName, elapsed = 0, loop = false) {
        return viewHelper.playAnimation(sk, animName, elapsed, loop)
    }

    private async showHeart(isInit) {
        let heart = this.heart
        if (heart.active) return
        heart.active = true
        let name = "heart"
        let point = this.body.getAttachedNode("guadian_talk")
        let ui = point.Child(name) || new cc.Node(name)
        ui.parent = point
        heart.parent = ui
        heart.setPosition(cc.v2(0, -30)) //爱心位置参数
        if (!isInit) {
            animHelper.showBubbleAction(heart)
        }
        animHelper.stopTrailingParticle(this.heart.Child('aixin'))
        this.heartMustUpPaozao()
    }

    private getHeartUI(): cc.Node {
        let name = "heart"
        let point = this.body.getAttachedNode("guadian_talk")
        if (!point) return
        return point.Child(name)
    }

    private async heartMustUpPaozao() {
        await ut.waitNextFrame(1, this)
        let heart = this.getHeartUI()
        if (!heart) return
        heart.y = 0
        let paozao = this.getPaozaoUI()
        if (!paozao) return
        let pos1 = heart.convertToWorldSpaceAR(cc.v2())
        let pos2 = paozao.convertToWorldSpaceAR(cc.v2())
        let offY = pos1.y - pos2.y
        let minY = 160
        if (offY >= minY) return
        heart.y = minY - offY
    }

    @util.addLock
    private async claimHeart() {
        let drops = [this.model.getDropByHeart()]
        let carriage = this.model.carriage
        if (carriage && carriage.isCollectAll()) {
            drops = dropItemHelper.getAllDropsByCarriage(carriage)
        }

        if (!cc.isValid(this)) return

        let node = this.heart.Child('aixin')
        dropItemHelper.collect(drops, true, node.convertToWorldSpaceAR(cc.v2()))
    }

    public async playClaimHeartAnim(count: number, showFlutter: boolean = true) {
        let icon = this.heart.Child('aixin')
        let type = ConditionType.HEART

        if (showFlutter) {
            let worldPos = icon.convertToWorldSpaceAR(cc.v2())
            let cond = new ConditionObj().init(type, -1, count)
            eventCenter.emit(EventType.SHOW_FLUTTER_MONEY, cond, worldPos)
        }


        const target: cc.Node = eventCenter.get(EventType.GET_UI_MONEY_ICON, type)
        let it = cc.instantiate(icon)
        it.active = true
        it.removeComponent("SpeedUpTimeScaleCmpt")
        it.getComponent("TimeScaleCmpt")?.setScale(1)

        let cameraCtrl = cc.Camera.findCamera(icon)
        let pos = cc.v2()
        icon.convertToWorldSpaceAR(pos, pos)
        cameraCtrl.getWorldToScreenPoint(pos, pos)
        if (pos.x < 0 || pos.x > cc.winSize.width) {  //屏幕外的直接消失
            it.destroy()
            return
        }
        const startPos = target.parent.convertToNodeSpaceAR(pos)
        it.parent = target.parent
        it.setPosition(startPos)
        it.scale *= cameraCtrl.zoomRatio
        let endPos = target.getPosition(cc.v2())
        animHelper.playTrailingParticle(it)
        await animHelper.flyPassengerHeart(it, endPos)
    }

    private async showPlotBtn(isInit) {
        let plot = this.plot
        if (plot.active) return
        plot.active = true
        let name = "plot"
        let point = this.body.getAttachedNode("guadian_talk")
        let ui = point.Child(name) || new cc.Node(name)
        ui.parent = point
        plot.parent = ui
        plot.setPosition(cc.v2(0, -30)) //爱心位置参数
        plot.Component(FixedNodeTransCmpt).updateFlipX()
    }

    @util.addLock
    public onClickPlot() {
        this.model.startPlot()
    }

    private async initView() {
        let sk = this.body
        let windTag = mc.currWind.getTag()
        await resHelper.loadOwnRoleSp(this.model.getID(), sk, this.getTag())
        if (!cc.isValid(this)) return

        this.touch.width = sk.node.width >> 1
        this.touch.height = sk.node.height
        this.touch.Component(ClickTouchCmpt).on(this.onRoleTouch, this)
        this.touch.anchorY = 0

        const url = 'passenger/' + sk.skeletonData.name
        assetsMgr.releaseTempRes(url, windTag) //清掉预加载时的引用

        this.updatePosition()
        this.initMix()
        this.state = null
        this.heart.Child('touch').Component(ClickTouchCmpt).on(this.claimHeart, this)
        this.plot.Child("bg/touch", ClickTouchCmpt).on(this.onClickPlot, this)
        this.updateTalkUI(true)
        this.setDir(this.model.getDir())
    }

    private initMix() {
        let sk = this.body
        sk.mix2(PassengerLifeAnimation.IDLE, PassengerLifeAnimation.WALK, 0.15)
        sk.mix2(PassengerLifeAnimation.IDLE, PassengerLifeAnimation.STAND_ACT, 0.15)

        // sk.mix(PassengerLifeAnimation.IDLE, PassengerLifeAnimation.SIT, 0.4)
        // sk.mix(PassengerLifeAnimation.IDLE, PassengerLifeAnimation.SIT_SLEEP, 0.4)

        // sk.mix(PassengerLifeAnimation.SIT_DRINK, PassengerLifeAnimation.IDLE, 0.4)
        sk.mix(PassengerLifeAnimation.SIT_DRINK, PassengerLifeAnimation.DOWN, 0.5)
        sk.mix(PassengerLifeAnimation.SIT_DRINK, PassengerLifeAnimation.SIT, 0.3)
        // sk.mix(PassengerLifeAnimation.SIT_TASTE, PassengerLifeAnimation.IDLE, 0.4)
        sk.mix(PassengerLifeAnimation.SIT_TASTE, PassengerLifeAnimation.SIT, 0.3)

        // sk.mix(PassengerLifeAnimation.SIT_EAT, PassengerLifeAnimation.IDLE, 0.4)
        sk.mix(PassengerLifeAnimation.SIT_EAT, PassengerLifeAnimation.DOWN, 0.5)
        sk.mix(PassengerLifeAnimation.SIT_EAT, PassengerLifeAnimation.SIT, 0.3)
        // sk.mix(PassengerLifeAnimation.SIT_MUNCH, PassengerLifeAnimation.IDLE, 0.4)
        sk.mix(PassengerLifeAnimation.SIT_MUNCH, PassengerLifeAnimation.SIT, 0.3)

        sk.mix(PassengerLifeAnimation.SIT, PassengerLifeAnimation.DOWN, 0.5)
        // sk.mix(PassengerLifeAnimation.SIT, PassengerLifeAnimation.IDLE, 0.4)
        sk.mix(PassengerLifeAnimation.SIT, PassengerLifeAnimation.SIT_DRINK, 0.6)
        sk.mix(PassengerLifeAnimation.SIT, PassengerLifeAnimation.SIT_EAT, 0.6)

        sk.mix(PassengerLifeAnimation.STAND_TO_SIT, PassengerLifeAnimation.SIT, 0.3)

        sk.mix(PassengerLifeAnimation.SIT_DAP, PassengerLifeAnimation.SIT, 0.15)
        // sk.mix(PassengerLifeAnimation.SIT_DAP, PassengerLifeAnimation.SIT_DRINK, 0.15)
        // sk.mix(PassengerLifeAnimation.SIT_DAP, PassengerLifeAnimation.SIT_EAT, 0.15)

        sk.mix(PassengerLifeAnimation.SIT_ACT, PassengerLifeAnimation.DOWN, 0.5)
        sk.mix(PassengerLifeAnimation.SIT, PassengerLifeAnimation.SIT_ACT, 0.4)
    }

    updateZIndex() {
        this.node.zIndex = MAX_ZINDEX - this.getModelPos().y
    }

    update(dt: number) {
        if (!this.model) return

        if (!this.prePos) {
            this.prePos = this.getModelPos().clone();
        }
        if (this.model.isMoving()) {
            this.updatePosition()
        }

        if (this.orgParent != this.node.parent) {
            this.body.node.walk((node) => {
                node.Component(FixedNodeTransCmpt)?.updateFlipX()
            }, null)
        }

        this.updateState();

        this.node.opacity = this.model.isHide ? 0 : 255
    }

    private updatePosition() {
        let pos = this.getModelPos()
        this.node.setPosition(pos)
        this.updateZIndex()
        this.updateScale()
        this.updateDir()

        if (this.prePos) {
            this.prePos.set(this.node.getPosition())
        }
    }

    private getModelPos() {
        let pos = this.model.getPosition()
        if (this.convertPosFunc) {
            pos = this.convertPosFunc(pos, this.model)
        }
        return pos
    }

    public setConvertPosFunc(func) {
        this.convertPosFunc = func
    }

    public setConvertScaleFunc(func) {
        this.convertScaleFunc = func
    }

    private updateDir() {
        if (!this.prePos) return;
        let _dir = this.model.getDir()
        if (this.model.isMoving()) {
            this.setDir(_dir)
            return
        }

        let vec = this.getModelPos().sub(this.prePos)
        if (vec.x != 0) { //防止垂直移动的时候转向
            let dir = vec.x < 0 ? RoleDir.LEFT : RoleDir.RIGHT
            this.setDir(dir)
        }
    }

    private onSetDir(role: PassengerModel) {
        if (this.model != role) return
        this.setDir(role.getDir())
    }

    public setDir(dir: RoleDir) {
        let curScale = this.body.node.scale //面朝右边scale > 0
        let scale = dir == RoleDir.LEFT ? -Math.abs(curScale) : Math.abs(curScale)
        if (curScale != scale) {
            this.body.node.scaleX = scale
            this.body.node.walk((node) => {
                node.Component(FixedNodeTransCmpt)?.updateFlipX()
            }, null)
        }
    }

    public onUpdatePos(model: PassengerModel) {
        if (this.model != model) return
        this.prePos = cc.v2(this.getModelPos())
        this.updatePosition()
    }

    private updateScale() {
        if (!this.model?.actionAgent) return
        let scale = this.model.actionAgent.getScale()
        if (this.convertScaleFunc) {
            scale = this.convertScaleFunc(scale, this.model)
        }
        this.node.scale = scale
    }

    private onSkinChange(role: PassengerModel) {
        if (this.model != role) return
        if (!this.body.skeletonData) return
        resHelper.loadOwnRoleSp(this.model.getID(), this.body, this.getTag())
    }

    //--------------------ui操作

    private updateTalkUI(isInit?: boolean) {
        let id = this.model.getID()
        if (!this.body.skeletonData) return
        let root = this.body.getAttachedNode("guadian_talk")
        let cfg = [
            { nodeName: "ui_duihuakuang", check: () => this.Component(RoleSpeakCmpt).isShow() },
            { nodeName: "plot", check: () => this.model.isShowPlot(), update: () => this.showPlotBtn(isInit) },
            { nodeName: "heart", check: () => this.model.hasHeart(), update: () => this.showHeart(isInit) },
        ]
        root.Swih("")
        for (let { nodeName, check, update } of cfg) {
            if (check()) {
                // twlog.info(this.model.id, nodeName)
                if (update) update()
                root.Swih(nodeName)
                return
            }
        }
        // twlog.info(this.model.id, "null")
    }

    public checkAndUpdateTalkUI(role: PassengerModel | PlotStep, extra?: boolean) {
        if (role instanceof PassengerModel) {
            if (this.model != role) return
        }
        this.updateTalkUI(extra)
    }

    private onRoleTouch() {
        if (this.model.isShowPlot()) {
            this.model.startPlot()
        }
        else if (this.model.getHeart()) {
            this.claimHeart()
        }
        else {
            this.model.checkDialog(true)
        }
    }

    private createDialogCmpt() {
        if (this.dialogCmpt) return true

        const mainUi = mc.getPnl("UIPnl")
        if (!cc.isValid(mainUi)) return
        let contaner = mainUi.node.Child("dialog_container")
        if (!contaner) {
            contaner = new cc.Node("dialog_container")
            mainUi.node.addChild(contaner, -1)
        }

        let dialogN = contaner.Child("dialog_" + this.model.getID())
        if (!dialogN) {
            const prefab = assetsMgr.getPrefab("dialog")
            if (!prefab) return false
            dialogN = cc.instantiate2(prefab, contaner)
            // 默认很低
            dialogN.setSiblingIndex(-1024)
        }
        this.dialogCmpt = dialogN.Component(PassengerDialog)
        this.dialogCmpt.init({
            model: this.model,
        })
        return true
    }

    private onRoleDialogShow(role: PassengerModel, fromClick: boolean) {
        if (role != this.model) return

        let cameraCtrl = cc.Camera.findCamera(this.node)
        let pos = cc.v2()
        this.node.convertToWorldSpaceAR(pos, pos)
        cameraCtrl.getWorldToScreenPoint(pos, pos)
        if (pos.x < 0 || pos.x > cc.winSize.width) {
            return
        }

        if (!this.createDialogCmpt()) return
        this.model.actionAgent.stopMove()
        this.dialogCmpt.show()

        // 被点击的乘客 对话框在最上面
        this.dialogCmpt.node.setSiblingIndex(!!fromClick ? -1 : -1024)
    }

    private onRoleDialogHide(role: PassengerModel) {
        if (role != this.model) return
        this.model.actionAgent.restartMove()
        if (!this.dialogCmpt) return
        this.dialogCmpt.active = false
    }

    private checkRefNodes() {
        if (this.refNodes.length < 10) return
        this.refNodes = this.refNodes.filter((node) => cc.isValid(node))
    }
}
