import { util } from "../../../core/utils/Utils";
import { DIR_POINTS_4, MAX_ZINDEX } from "../../common/constant/Constant";
import { ChapterPlanetMineCfg, CollectMapCfg, PlanetMineCfg } from "../../common/constant/DataType";
import { ConditionType, DailyTaskState, DailyTaskType, RuleType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { animHelper } from "../../common/helper/AnimHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { mapHelper } from "../../common/helper/MapHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import CollectMineModel from "../../model/collect/CollectMineModel";
import CollectModel from "../../model/collect/CollectModel";
import ConditionObj from "../../model/common/ConditionObj";
import { DailyTask } from "../../model/daily_task/DailyTaskModel";
import CollectMineCmpt from "../cmpt/collect/CollectMineCmpt";
import RoleCollectCmpt from "../cmpt/collect/RoleCollectCmpt";
import ClickTouchCmpt from "../cmpt/common/ClickTouchCmpt";
import PlanetNodeRewardCmpt from "../cmpt/planet/PlanetNodeRewardCmpt";
import PlanetNodeRewardGroupCmpt from "../cmpt/planet/PlanetNodeRewardGroupCmpt";

const { ccclass } = cc._decorator;
@ccclass
export default class CollectWindCtrl extends mc.BaseWindCtrl {

    //@autocode property begin
    protected touchNode_: cc.Node = null // path://touch_n
    protected mapLandNode_: cc.Node = null // path://mapLand_n
    protected lightNode_: cc.Node = null // path://light_n
    protected mapBgNode_: cc.Node = null // path://mapBg_n
    protected cameraNode_: cc.Node = null // path://camera_n
    protected mineNode_: cc.Node = null // path://mine_n
    protected nodeRewardNode_: cc.Node = null // path://nodeReward_n
    protected mapNode_: cc.Node = null // path://map_n
    protected roleNode_: cc.Node = null // path://map_n/role_n
    protected arrowNode_: cc.Node = null // path://map_n/arrow_n
    protected frontNode_: cc.Node = null // path://front_n
    protected hitTipsNode_: cc.Node = null // path://hitTips_n
    protected backNode_: cc.Node = null // path://ui/back_be_n
    protected controlNode_: cc.Node = null // path://ui/bottom/control_n
    protected taskNode_: cc.Node = null // path://ui/bottom/task_n
    protected uiCommonNode_: cc.Node = null // path://ui/ui_common_wg_n
    //@end

    private model: CollectModel = null

    private viewNodes: cc.Node[] = []

    public listenEventMaps() {
        return [
            { [EventType.PLANET_GEN_REWARD]: this.genRewardNodes },
            { [EventType.PLANET_SHOW_HIT_TIPS]: this.showHitTips },
            { [EventType.REACH_PLANET_NODE]: this.onReach },
            { [EventType.CHANGE_NUM_PROP]: this.initDailyTask },
            { [EventType.DAILY_TASK_FINISH]: this.initDailyTask },
        ]
    }

    public async onCreate(data) {
        this.model = gameHelper.collect
        this.touchNode_.setContentSize(this.model.size)
        this.initMapBg()
        this.initLight()
    }

    public onEnter(data: any) {
        this.initRole()
        this.initMines()
        this.initTouch()
        this.updateCamera()
        this.initControl()
        this.initDailyTask()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://ui/back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        viewHelper.gotoPlanetEntry()
    }

    // path://ui/top/rule_be
    onClickRule(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('battle/EquipLvRules', RuleType.COLLECT)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private initRole() {
        let role = gameHelper.collect.getRole()
        role.reset()
        this.roleNode_.Component(RoleCollectCmpt).init()
    }

    private async initMines() {
        this.mapNode_.children.forEach(it => {
            if (it.name == this.mineNode_.name) it.destroy()
        })
        let mines = this.model.getMines()

        for (let mine of mines) {
            let node = this.addMine(mine, false)
        }
    }

    private addMine(mine: CollectMineModel, anim: boolean = true) {
        const role = gameHelper.collect.getRole()
        let node = cc.instantiate2(this.mineNode_, this.mapNode_)
        node.active = true
        let cmpt = node.Component(CollectMineCmpt)
        cmpt.init(mine)
        node.setPosition(mine.getPosition())
        let body = node.Child("body")
        let onClick = ()=>{
            this.arrowNode_.active = false
            role.onClickTarget(mine)
        }
        if (mine.canCollect()) {
            body.on("click", onClick)
        }
        else {
            body.removeComponent(cc.ButtonEx)
            body.removeComponent(cc.Button)
            let cmpt = body.addComponent(ClickTouchCmpt)
            cmpt.on(onClick)
        }
        node.zIndex = MAX_ZINDEX - node.y
        this.viewNodes.push(node)

        if (anim) {
            animHelper.showBubbleAction(node)
        }
        return node
    }

    private initTouch() {
        let node = this.touchNode_
        node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
    }

    private initMapBg() {
        let size = this.model.size
        let landSize = this.mapLandNode_.getContentSize()

        this.mapBgNode_.width = size.width
        const numX = Math.ceil(Number(size.width) / landSize.width)
        const numY = Math.ceil(Number(size.height) / landSize.height)

        for (let y = 0; y < numY; y++) {
            for (let x = 0; x < numX; x++) {
                let it = cc.instantiate2(this.mapLandNode_, this.mapBgNode_)
                it.active = true
                it.setPosition(-size.width * 0.5 + (x + 0.5) * landSize.width, size.height * 0.5 - (y + 0.5) * landSize.height)
                let index = (x * y) % 4
                it.Component(cc.MultiFrame).setFrame(index)
            }
        }
    }

    private initLight() {
        const screenSize = cc.winSize
        let size = this.model.size
        const numX = Math.ceil(Number(size.width) / screenSize.width)
        const numY = Math.ceil(Number(size.height) / screenSize.height)

        for (let i = 0; i < numY; i++) {
            for (let j = 0; j < numX; j++) {
                let it = cc.instantiate2(this.lightNode_, this.frontNode_)
                it.active = true
                
                // 计算位置
                it.setPosition(-size.width * 0.5 + (j + 0.5) * screenSize.width, size.height * 0.5 - (i + 0.5) * screenSize.height)
            }
        }
    }

    private onTouchEnd(event: cc.Event.EventTouch) {
        let role = gameHelper.collect.getRole()
        if (role.errorTarget) {
            eventCenter.emit(EventType.COLLECT_TASK_TARGET_ERROR, role.errorTarget)
            role.errorTarget = null
            return
        }

        let camera = this.cameraNode_.Component(cc.Camera)
        const location = event.getLocation()
        let worldPos = cc.v2()
        camera.getScreenToWorldPoint(location, worldPos)
        let pos = this.roleNode_.parent.convertToNodeSpaceAR(worldPos)

        role.setTarget(null)
        role.setTargetPos(pos)

        this.arrowNode_.active = true
        this.arrowNode_.Component(sp.Skeleton).playAnimation("dianji_caiji")
        this.arrowNode_.setPosition(pos)
        this.arrowNode_.zIndex = MAX_ZINDEX - this.arrowNode_.y
    }

    private onReach() {
        this.arrowNode_.active = false
    }

    update() {
        let role = gameHelper.collect.getRole()
        if (role.isMoving()) {
            this.updateCamera()
        }
    }

    private updateCamera() {
        let role = gameHelper.collect.getRole()
        let pos = role.getPosition()
        let size = gameHelper.collect.size
        pos.x = cc.misc.clampf(pos.x, -size.width * 0.5 + cc.winSize.width * 0.5, size.width * 0.5 - cc.winSize.width * 0.5)
        pos.y = cc.misc.clampf(pos.y, -size.height * 0.5 + cc.winSize.height * 0.5, size.height * 0.5 - cc.winSize.height * 0.5)
        this.cameraNode_.setPosition(pos)
    }

    private showHitTips(damage, damageMul: number, model: CollectMineModel, node?, isAuto = false) {
        let hitTips = this.hitTipsNode_
        let prefab
        let nodes = this.viewNodes
        let mineNode = nodes.find(node => cc.isValid(node) && node.Component(CollectMineCmpt)?.model == model)
        if (!mineNode) mineNode = node
        if (!mineNode) return
        hitTips.active = true
        let isQte = !!model.qteId
        if (damageMul == 0) {
            prefab = hitTips.Child('miss')
        }
        else if (!isQte) {
            prefab = hitTips.Child('lb')
        }
        else { //qte
            if (damageMul == 1) {
                prefab = hitTips.Child('lbS')
            }
            else if (damageMul == 2) {
                prefab = hitTips.Child('lbM')
            }
            else {
                prefab = hitTips.Child('lbL')
            }
        }

        hitTips.setPosition(ut.convertToNodeAR(mineNode.Child('ui'), hitTips.parent))

        let tipNode = cc.instantiate2(prefab, hitTips);

        const randomOffsetX = 30
        tipNode.x = ut.randomRange(-randomOffsetX, randomOffsetX)
        tipNode.y = 90
        if (isAuto) {
            let p = ut.convertToNodeAR(mineNode, tipNode.parent, cc.v2(0, model.size.height * 0.5))
            tipNode.y = p.y
        }
        tipNode.name = 'tip'
        tipNode.active = true

        let isCirt = false
        if (damageMul) {
            if (damage >= 0) {
                tipNode.Component(cc.Label).string = "-" + damage
                if (damageMul > 1 && !isQte) {
                    isCirt = true
                    tipNode.SetColor('#F7A221')
                }
            }
            else if (damage < 0) {
                tipNode.Component(cc.Label).string = "+" + Math.abs(damage)
                tipNode.SetColor('41c82f')
                tipNode.y = -45
            }
        }

        let startY = tipNode.y

        if (isCirt) {
            let scale = 1.3
            tipNode.scale = scale
            tipNode.y += 5

            cc.tween(tipNode)
                .to(0.12, { scale: 3 }, { easing: cc.easing.sineOut })
                .to(0.18, { scale: 0.85 * scale }, { easing: cc.easing.sineOut })
                .to(0.1, { scale: 1.1 * scale }, { easing: cc.easing.sineIn })
                .start()

            cc.tween(tipNode)
                .to(0.4, { y: startY + 35, opacity: 255 }, { easing: cc.easing.sineOut })
                .to(0.6, { y: startY + 55, opacity: 0 }, { easing: cc.easing.sineOut })
                .call(() => {
                    tipNode.destroy()
                }).start()
        }
        else {
            cc.tween(tipNode)
                .to(0.4, { y: startY + 20, opacity: 255 }, { easing: cc.easing.sineOut })
                .to(0.1, { y: startY + 25, opacity: 0 }, { easing: cc.easing.sineOut })
                .call(() => {
                    tipNode.destroy()
                }).start()
        }
    }

    private async genRewardNodes(rewards: ConditionObj[], center?: cc.Vec2) {
        if (rewards.length <= 0) return

        let recordPosAry = []
        let gen = (reward) => {
            let node = cc.instantiate2(this.nodeRewardNode_, this.mapNode_)
            node.active = true
            let cmpt = node.Component(PlanetNodeRewardCmpt)
            cmpt.init(reward)

            animHelper.playPlanetRewardJump(node, center, recordPosAry).then(()=>{
                cmpt.onDropEnd()
            })
            return node
        }

        let key = animHelper.planetGenStart()
        let items: cc.Node[] = []
        for (let reward of rewards) {
            let iconNum = reward.extra?.iconNum || reward.num
            for (let i = 0; i < iconNum; i++) {
                let cond = new ConditionObj().init(reward)
                cond.num = 1
                let node = gen(cond)
                await ut.waitNextFrame(1, this)
                items.push(node)
            }
        }

        if (items.length <= 0) {
            return
        }

        let cmpt = items[0].addComponent(PlanetNodeRewardGroupCmpt)
        cmpt.init(items)
        await ut.wait(0.5, this)
        eventCenter.emit(EventType.CLAIM_MINE_REWARD_START)
    }

    private initControl() {
        this.controlNode_.on(cc.Node.EventType.TOUCH_START, (event: cc.Event.EventTouch) => {
            if (!this.controlNode_.Component(cc.Button).interactable) {
                return
            }
            this.updateControlBtnPress(true)
        }, this)

        this.controlNode_.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            let role = gameHelper.collect.getRole()
            let target = role.getTargetModel()
            if (!target || target.dead) {
                viewHelper.showAlert("collect_tips_1")
                return
            }
            role.onClickTarget()
            this.updateControlBtnPress(false)
        }, this)

        this.controlNode_.on(cc.Node.EventType.TOUCH_CANCEL, (event: cc.Event.EventTouch) => {
            this.updateControlBtnPress(false)
        }, this)
    }

    private updateControlBtnPress(down?: boolean) {
        let controlNode = this.controlNode_
        let press = controlNode.Child("press")
        press.y = 27
        if (down) {
            press.y -= 10
        }
    }

    private initDailyTask() {
        let tasks = gameHelper.dailyTask.getCollectTasks().filter(t => t.state == DailyTaskState.TAKE)
        this.taskNode_.active = tasks.length > 0
        if (tasks.length <= 0) return

        let needsNode = this.taskNode_.Child("needs")
        let targets = tasks.map(t => t.getTarget()).flat()
        targets = gameHelper.mergeCondition(targets)
        needsNode.Items(targets, (it, data) => {
            resHelper.loadIconByCondInfo(data, it.Child('icon'), this.getTag())
            it.Child('num', cc.Label).string = `${gameHelper.getNumByCondition(data)}/${data.num}`
            it.Child('num', cc.MultiColor).setColor(gameHelper.getNumByCondition(data) < data.num)
            uiHelper.regClickPropBubble(it, data)
        })
    }

    public debugHideInterference(hide: boolean = true) {
        let nodes = this.mapNode_.children.filter(c => c.name == this.mineNode_.name)
        for (let node of nodes) {
            let cmpt = node.Component(CollectMineCmpt)
            if (!cmpt.model.canCollect()) {
                node.active = !hide
            }
        }
    }

}
