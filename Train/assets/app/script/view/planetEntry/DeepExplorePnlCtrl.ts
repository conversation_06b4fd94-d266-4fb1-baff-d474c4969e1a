import PlanetModel from "../../model/planet/PlanetModel";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { timeHelper } from "../../common/helper/TimeHelper";
import { ExploreInfo } from "../../model/planet/DeepExploreModel";
import EventType from "../../common/event/EventType";
import { GuideStepMark } from "../../common/constant/Enums";
import { GuideStep } from "../../common/constant/DataType";

const { ccclass } = cc._decorator;

// 滚动速度
const scrollSpeed = 300

@ccclass
export default class DeepExplorePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected bgNode_: cc.Node = null // path://bg_n
    protected shipNode_: cc.Node = null // path://ship_n
    protected c1Node_: cc.Node = null // path://ship_n/sk_1/ATTACHED_NODE_TREE/ATTACHED_NODE:root/ATTACHED_NODE:All/ATTACHED_NODE:che1/ATTACHED_NODE:guadian_jiashishi/c1_n
    protected c3Node_: cc.Node = null // path://ship_n/sk_1/ATTACHED_NODE_TREE/ATTACHED_NODE:root/ATTACHED_NODE:All/ATTACHED_NODE:che1/ATTACHED_NODE:guadian_zuowei1/c3_n
    protected c2Node_: cc.Node = null // path://ship_n/sk_1/ATTACHED_NODE_TREE/ATTACHED_NODE:root/ATTACHED_NODE:All/ATTACHED_NODE:che1/ATTACHED_NODE:guadian_zuowei1/c2_n
    protected c4Node_: cc.Node = null // path://ship_n/sk_1/ATTACHED_NODE_TREE/ATTACHED_NODE:root/ATTACHED_NODE:All/ATTACHED_NODE:che1/ATTACHED_NODE:guadian_zuowei2/c4_n
    protected c5Node_: cc.Node = null // path://ship_n/sk_1/ATTACHED_NODE_TREE/ATTACHED_NODE:root/ATTACHED_NODE:All/ATTACHED_NODE:che1/ATTACHED_NODE:guadian_zuowei2/c5_n
    protected rewardNode_: cc.Node = null // path://ship_n/reward_n_be
    protected timeRt_: cc.RichText = null // path://title/PlanetTitle/progress/time_rt
    protected nameLbl_: cc.Label = null // path://title/PlanetTitle/name_l
    protected tipsNode_: cc.Node = null // path://tips_n
    //@end
    private model: PlanetModel = null
    private exploreInfo: ExploreInfo = null
    private _area: number
    private _bg: cc.Node = null
    _ary: cc.Node[] = null

    public listenEventMaps() {
        return [
            { [EventType.GUIDE_STEP_END]: this.onGuideEnd }
        ]
    }

    private isGuide: boolean = false

    public async onCreate() {
        this.setParam({ isAct: false, isMask: false })
        this.node.opacity = 0
        this._ary = [this.c1Node_, this.c2Node_, this.c3Node_, this.c4Node_, this.c5Node_]
        this.model = gameHelper.planet.getCurPlanet()
        this.exploreInfo = gameHelper.deepExplore.getExploreInfo(this.model.getId())
        this._area = await gameHelper.deepExplore.getArea(this.model.getId())
        this.timeRt_.node.active = true
        await this.loadBg()
    }

    public onEnter(data: any) {
        if (!gameHelper.guide.isStepEnd(GuideStepMark.DEEP_EXPLORE_START)) {
            this.isGuide = true
        }
        this.initView()
        cc.tween(this.node)
            .to(0.15, { opacity: 255 })
            .call(() => data?.())
            .start()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://ship_n/reward_n_be
    onClickReward(event: cc.Event.EventTouch, data: string) {
        eventCenter.once(EventType.REWARDPNL_CLOSE, () => {
            if (cc.isValid(this)) {
                this.exploreInfo = null
                this.rewardNode_.active = false
                eventCenter.emit(EventType.PLANET_DEEP_EXPLORE_END)
                cc.tween(this.node)
                    .to(.2, { opacity: 0 })
                    .call(() => this.close())
                    .start()
            }
        })
        gameHelper.deepExplore.claimExplore(this.model.getId())
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    async loadBg() {
        const area = await gameHelper.deepExplore.getArea(this.model.getId())
        await resHelper.loadPlanetAreaBg(this.model.getId(), area, this.bgNode_, this.getTag())
    }

    initView() {
        this.initTitle()
        this._bg = this.exploreInfo.isFinish() ? null : this.bgNode_.children[0]
        this.setReward()
        this.initShip()
        this.initRoles()
    }

    async initTitle() {
        let planet = gameHelper.planet.getCurPlanet()
        let curArea = await gameHelper.deepExplore.getArea(this.model.getId())
        let area = planet.getAreas().find(area => area.id == `${planet.getId()}-${curArea}`)
        this.nameLbl_.setLocaleKey(area.name)
    }

    initRoles() {
        const info = gameHelper.deepExplore.getExploreInfo(this.model.getId())
        info.roles.forEach((role, index) => {
            const it = this._ary[index]
            resHelper.loadRoleSp(role, it, this.getTag()).then(() => it.Component(sp.Skeleton).playAnimation("life/sit_idle"))
        })
    }

    initShip() {
        const anim = this._bg ? "daiji_cover" : "jingzhi"
        this.shipNode_.y = this._bg ? 400 : 180
        this.shipNode_.Child("sk_1", sp.Skeleton).playAnimation(anim, true)
        this.shipNode_.Child("sk_2", sp.Skeleton).playAnimation(anim, true)
    }

    setReward() {
        this.rewardNode_.active = this.exploreInfo.isFinish()
    }

    update(dt: number) {
        if (this.exploreInfo) {
            if (this.exploreInfo.isFinish()) {
                this._bg = null
                this.setReward()
                this.initShip()
                this.timeRt_.setLocaleKey("ui_deep_explore_done")
            } else {
                this.timeRt_.setLocaleUpdate(() => {
                    return `<img src='ty_shijian'/><color=#79ee73> ${ut.millisecondFormat(this.exploreInfo.getSurplusTime(), `hh:mm:ss`)}</color>`
                })
            }
        } else {
            this.timeRt_.node.active = false
        }
        if (this.isGuide) return
        if (!this._bg) return
        this.updateBg(dt)
    }

    private updateBg(dt: number) {
        if (!this._bg) return
        this.updateGround(this._bg, dt)
    }

    private updateGround(bgNode, dt) {
        let groundNode = bgNode.Child("ground") || bgNode
        this.implementInfiniteScroll(groundNode, dt)
        let loopNodes = bgNode.Child('loopNodes')
        if (loopNodes) {
            for (let node of loopNodes.children) {
                this.implementInfiniteScroll(node, dt)
            }
        }
        let sky = bgNode.Child('sky')
        if (sky) {
            sky.active = true
            sky.width = cc.winSize.width
            sky.x = 0
        }
        let cloud = bgNode.Child("cloud")
        if (cloud && cloud.activeInHierarchy) {
            this.moveCloud(cloud, dt)
        }
    }

    private implementInfiniteScroll(node, dt) {
        let item = node.Child('group')
        if (!item) return

        let loopLayout: cc.Layout = node.Component(cc.Layout)
        if (loopLayout) {
            loopLayout.enabled = false
        }

        let itemLayout = item.Component(cc.Layout)
        if (itemLayout) {
            itemLayout.updateLayout()
            itemLayout.enabled = false
        }
        if (!node._initialized) {
            node._initialized = true
            node._itemWidth = item.width
            node._spacingX = loopLayout ? loopLayout.spacingX : 0
            node._viewportWidth = cc.winSize.width
            node._requiredCount = Math.ceil((node._viewportWidth * 2) / (node._itemWidth + node._spacingX)) + 1
            while (node.children.length < node._requiredCount) {
                let newNode = cc.instantiate2(item, node)
                if (node.children.length > 1) {
                    let lastNode = node.children[node.children.length - 2]
                    newNode.x = lastNode.x + node._itemWidth + node._spacingX
                }
            }
            node._totalWidth = node._requiredCount * (node._itemWidth + node._spacingX)
            node._scrollOffset = 0
        }
        node._scrollOffset = (node._scrollOffset + Math.round(scrollSpeed * dt)) % node._totalWidth
        for (let i = 0; i < node.children.length; i++) {
            let basePosition = i * (node._itemWidth + node._spacingX) - node._scrollOffset
            if (basePosition < -node._itemWidth - node._spacingX) {
                basePosition += node._totalWidth
            }
            node.children[i].x = Math.round(basePosition)
        }
    }

    protected moveCloud(cloud: cc.Node, dt) {
        let loopNode = cloud.Child('loop')
        if (cloud.children.length < 2) {
            let node = cc.instantiate2(loopNode, cloud)
            node.x += loopNode.width
        }
        cloud.x -= dt * 10
        if (cloud.x < -loopNode.width) {
            cloud.x += loopNode.width
        }
    }

    private onGuideEnd(stepInfo: GuideStep) {
        if (stepInfo.mark == GuideStepMark.DEEP_EXPLORE_START) {
            this.isGuide = false
        }
    }
}
