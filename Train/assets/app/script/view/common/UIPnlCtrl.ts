import { viewHelper } from "../../common/helper/ViewHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { animHelper } from "../../common/helper/AnimHelper";
import { localConfig } from "../../common/LocalConfig";
import { unlockHelper } from "../../common/helper/UnlockHelper";
import { WeakGuideObj } from "../../model/guide/WeakGuideModel";
import { DATE_LANG, PNL_ZINDEX } from "../../common/constant/Constant";
import { ConditionType, ItemID, SpeedUpType, UIFunctionType, WeakGuideType } from "../../common/constant/Enums";
import NodeType from "../../common/event/NodeType";
import EventType from "../../common/event/EventType";
import ConditionObj from "../../model/common/ConditionObj";
import CurrencyUICmpt from "../cmpt/ui/CurrencyUICmpt";
import { cfgHelper } from "../../common/helper/CfgHelper";

const REDLIGHT_TIME = 1 //红光循环时间

const { ccclass } = cc._decorator;

@ccclass
export default class UIPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected uiCommonNode_: cc.Node = null // path://ui_common_wg_n
    protected shijiankuangNode_: cc.Node = null // path://top/shijiankuang_be_n
    protected worldTimeLbl_: cc.Label = null // path://top/shijiankuang_be_n/worldTime_l
    protected worldDateLbl_: cc.Label = null // path://top/shijiankuang_be_n/worldDate_l
    protected wordDayNode_: cc.Node = null // path://top/shijiankuang_be_n/wordDay_n
    protected outputNode_: cc.Node = null // path://top/output_n_be
    protected btnViewingOpenNode_: cc.Node = null // path://btnViewingOpen_n_be
    protected taskNode_: cc.Node = null // path://task_wg_n
    protected roleNode_: cc.Node = null // path://bottom/role_be_n
    protected trainNode_: cc.Node = null // path://bottom/train_be_n
    protected jackpotNode_: cc.Node = null // path://bottom/jackpot_be_n
    protected exploreNode_: cc.Node = null // path://bottom/explore_wg_n
    protected speedUpNode_: cc.Node = null // path://bottom/speedUp_wg_n
    protected gamesNode_: cc.Node = null // path://bottom/games_be_n
    protected profileNode_: cc.Node = null // path://bottom/profile_be_n
    protected transportNode_: cc.Node = null // path://transport_n
    protected debugNode_: cc.Node = null // path://debug_n
    protected currencyLayoutNode_: cc.Node = null // path://currency_layout_n
    protected btnViewingCloseNode_: cc.Node = null // path://btnViewingClose_n_be
    protected flutterNode_: cc.Node = null // path://flutter_n
    protected tipActivityNode_: cc.Node = null // path://tip_activity_n
    protected tipTopNode_: cc.Node = null // path://tip_top_n
    //@end

    private timeShow: number = 0;//单位秒
    private stateViewing: number = 0
    private transportBattleIconShow: boolean = false

    public listenEventMaps() {
        return [
            { [EventType.GET_UI_FUNCTION_NODE]: this.getFunctionNode },
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onFunctionUnlock },
            { [EventType.SHOW_FLUTTER_MONEY]: this.onShowFlutterMoney },
            { [EventType.GET_UI_MONEY_ICON]: this.onGetUIMoneyIcon },
            { [EventType.INIT_SHOW_TIME]: this.delayInitShowTime },
            { [EventType.SHOW_WEAK_GUIDE]: this.onWeakGuide },
            { [EventType.DAY_NIGHT_EXCHANGE]: this.setWorldDayOrNight },
            { [EventType.TRANSPORT_MONSTER_ICON_HIDE]: this.hideTransportBattleIcon },
            { [EventType.TRANSPORT_MONSTER_ICON_SHOW]: this.showTransportBattleIcon },
            { [EventType.TRAIN_ACTIVITY_STATE_CHANGE]: this.setTrainActivity },
            { [EventType.UI_TIP_TRAIN_ACTIVITY]: this.showTip },

            //guide node
            { [NodeType.GUIDE_BUTTON_CHARACTER]: () => { return this.getFunctionNode(UIFunctionType.CHARACTER) } },
            { [NodeType.GUIDE_BUTTON_LOTTERY]: () => { return this.getFunctionNode(UIFunctionType.LOTTERY) } },
            { [NodeType.GUIDE_BUTTON_SPEED_UP]: () => { return this.getFunctionNode(UIFunctionType.SPEED_UP) } },
            { [NodeType.GUIDE_BUTTON_TRAIN]: () => { return this.getFunctionNode(UIFunctionType.TRAIN) } },
            { [NodeType.GUIDE_STAR_MAP]: () => { return this.getFunctionNode(UIFunctionType.STAR_MAP) } },
        ]
    }

    public async onCreate() {
        this.setParam({ isMask: false, isAct: false, Index: PNL_ZINDEX.UI })
    }

    public onEnter(data: any) {
        this.tipActivityNode_.active = false
        this.initDebug();
        this.initView()
        this.initBtnViewing()
        this.bindTransportBtnEvt()
    }

    private initView() {
        this.initFunc()
        this.setWorldDayOrNight()
        this.setTrainActivity()
    }

    private initFunc() {
        unlockHelper.initFunctionNode(this.getFunctionNode.bind(this))
    }

    private initDebug() {
        if (localConfig.debug == false) {
            this.debugNode_.active = false
        }
    }

    private initShowTime() {
        if (!unlockHelper.useRealTime()) {
            this.timeShow = gameHelper.guide.getGuideTime()
        }
    }

    private delayInitShowTime() {
        this.timeShow = 0
    }

    private addShowTime(dt) {
        if (this.timeShow == 0) {
            this.initShowTime()
        }
        let world = gameHelper.world
        this.timeShow += world.transDT(dt, SpeedUpType.S3)
        return world.changeTime(this.timeShow)
    }

    update(dt: number) {
        let world = gameHelper.world
        let time = unlockHelper.useRealTime() ? world.getTime() : this.addShowTime(dt)
        let dayTime = time % ut.Time.Day
        this.worldTimeLbl_.string = ut.millisecondFormat(dayTime, "hh:mm")
        this.worldDateLbl_.setLocaleUpdate(() => `${world.getMonth(time)}${assetsMgr.lang(DATE_LANG.m)} ${world.getDay(time)}${assetsMgr.lang(DATE_LANG.d)}`)

        // this.transportNode_.active = gameHelper.planet.isMeetMonsters()
        if (this.transportNode_.active) {
            this.transportNode_.Child("light").opacity = 255 * Math.abs((gameHelper.now() % (REDLIGHT_TIME * ut.Time.Second * 2)) / (REDLIGHT_TIME * ut.Time.Second) - 1)
        }

        this.updateTransport()
        this.updateOutputClockN()
        this.setTrainActivity()
        if (this.debugNode_.active) {
            this.debugNode_.Child("time", cc.Label).string = ut.dateFormat("yyyy-MM-dd hh:mm:ss", gameHelper.now())
        }
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://bottom/role_be_n
    onClickRole(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('role/RolePnl');
    }

    // path://bottom/train_be_n
    onClickTrain(event: cc.Event.EventTouch, data: string) {
        // viewHelper.showPnl('train/TrainInformationPnl')
        viewHelper.showPnl('train/TrainConsolePnl')
    }

    // path://bottom/jackpot_be_n
    onClickJackpot(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('jackpot/JackPotPnl');
    }

    // path://btnViewingOpen_n_be
    onClickBtnViewingOpen(event: cc.Event.EventTouch, data: string) {
        this.hideViewing()
    }

    // path://btnViewingClose_n_be
    onClickBtnViewingClose(event: cc.Event.EventTouch, data: string) {
        this.showViewing()
    }

    // path://bottom/games_be_n
    onClickGames(event: cc.Event.EventTouch, data: string) {
        //viewHelper.showPnl('games/GamesPnl')
        if (gameHelper.transport.isTransportDone()) {
            return gameHelper.transport.getTransportReward()
        }
        viewHelper.showPnl('planet/PlanetChoose')
    }

    // path://top/output_n_be
    onClickOutput(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('train/OutputTimeInfoPnl')
    }

    // path://bottom/profile_be_n
    onClickProfile(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('profile/ProfilePnl')
    }

    // path://top/shijiankuang_be_n
    onClickShijiankuang(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('trainActivity/TrainActivityArrangePnl')
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    public getFunctionNode(type: UIFunctionType) {
        switch (type) {
            case UIFunctionType.EXPLORE: return this.exploreNode_
            case UIFunctionType.CHARACTER: return this.roleNode_
            case UIFunctionType.LOTTERY: return this.jackpotNode_
            case UIFunctionType.SPEED_UP: return this.speedUpNode_
            case UIFunctionType.TRAIN: return this.trainNode_
            case UIFunctionType.VIEWING_MODE: return this.btnViewingOpenNode_
            case UIFunctionType.STAR_MAP: return this.gamesNode_
            case UIFunctionType.PROFILE: return this.profileNode_
            case UIFunctionType.OUTPUT: return this.outputNode_
        }
    }

    private onFunctionUnlock(type: UIFunctionType) {
        unlockHelper.unlockFunction(this.getFunctionNode.bind(this), type)
    }

    private onShowFlutterMoney(cond: ConditionObj, worldPos: cc.Vec2, offsetY = 0) {
        let node = this.flutterNode_
        let mainCamera = cc.find("Canvas/Main Camera").getComponent(cc.Camera)
        let _temp_vec2 = cc.v2()
        let targetCamera = cc.Camera.findCamera(node)
        mainCamera.getWorldToScreenPoint(worldPos, _temp_vec2)
        targetCamera.getScreenToWorldPoint(_temp_vec2, _temp_vec2);
        let location = node.convertToNodeSpaceAR(_temp_vec2, _temp_vec2)
        location.y += offsetY
        animHelper.playFlutterMoney(cond, node, location, this.getTag())
    }

    private onGetUIMoneyIcon(type: ConditionType) {
        let node = this.currencyLayoutNode_.children.find(node => node.Component(CurrencyUICmpt).type == type)
        return node.Child('icon')
    }

    private initBtnViewing() {
        let btnClose = this.btnViewingCloseNode_
        btnClose.getComponent(cc.Widget).updateAlignment()
        btnClose.Data = btnClose.y
        btnClose.active = false
    }

    private async hideViewing() {
        let curState = this.stateViewing
        if (curState != 0) return
        this.stateViewing = 2
        let time = 0.5
        let timeBtn = 0.4
        let newState = 1
        let top1 = this.node.Child('top')
        let top2 = this.currencyLayoutNode_
        let left1 = this.taskNode_
        let left2 = this.debugNode_
        let right1 = this.uiCommonNode_
        let bottom1 = this.node.Child('bottom')
        let btnOpen = this.btnViewingOpenNode_
        let btnClose = this.btnViewingCloseNode_
        let easing = cc.easing.expoIn
        cc.tween(top1).by(time, { y: 200 }, { easing: easing }).hide().start()
        cc.tween(top2).by(time, { y: 200 }, { easing: easing }).set({ active: false }).start()
        cc.tween(left1).to(time, { x: -1100 }, { easing: easing }).hide().start()
        cc.tween(left2).to(time, { x: -700 }, { easing: easing }).hide().start()
        cc.tween(right1).to(time, { x: 600 }, { easing: easing }).hide().start()
        cc.tween(bottom1).by(time, { y: -400 }, { easing: easing }).hide().start()
        cc.tween(btnOpen).by(time, { y: 200 }, { easing: easing }).set({ active: false }).start()
        top1.removeComponent(cc.Widget)
        top2.removeComponent(cc.Widget)
        left1.removeComponent(cc.Widget)
        left2.removeComponent(cc.Widget)
        right1.removeComponent(cc.Widget)
        bottom1.removeComponent(cc.Widget)
        btnOpen.removeComponent(cc.Widget)
        btnClose.removeComponent(cc.Widget)
        let y = btnClose.Data
        cc.tween(btnClose).delay(timeBtn).set({ active: true, y: y + 200 }).to(time, { y }, { easing: cc.easing.expoOut }).start()
        eventCenter.emit(EventType.SHOW_TRAIN_BTNS, false)
        mc.lockTouch(true)
        await ut.wait(time, this)
        mc.lockTouch(false)
        await ut.wait(timeBtn, this)
        this.stateViewing = newState
    }

    private async showViewing() {
        let curState = this.stateViewing
        if (curState != 1) return
        this.stateViewing = 2
        let time = 0.5
        let timeBtn = 0.4
        let newState = 0
        let top1 = this.node.Child('top')
        let top2 = this.currencyLayoutNode_
        let left1 = this.taskNode_
        let left2 = this.debugNode_
        let right1 = this.uiCommonNode_
        let bottom1 = this.node.Child('bottom')
        let btnOpen = this.btnViewingOpenNode_
        let btnClose = this.btnViewingCloseNode_
        cc.tween(btnClose).to(time, { y: btnClose.Data + 200 }, { easing: cc.easing.expoIn }).set({ active: false }).start()
        await ut.wait(timeBtn, this)
        let easing = cc.easing.expoOut
        cc.tween(top1).show().by(time, { y: -200 }, { easing: easing }).start()
        cc.tween(top2).set({ active: true }).by(time, { y: -200 }, { easing: easing }).start()
        cc.tween(left1).show().to(time, { x: 0 }, { easing: easing }).start()
        cc.tween(left2).show().to(time, { x: 0 }, { easing: easing }).start()
        cc.tween(right1).show().to(time, { x: 0 }, { easing: easing }).start()
        cc.tween(bottom1).show().by(time, { y: 400 }, { easing: easing }).start()
        cc.tween(btnOpen).set({ active: true }).by(time, { y: -200 }, { easing: easing }).start()
        eventCenter.emit(EventType.SHOW_TRAIN_BTNS, true)
        mc.lockTouch(true)
        await ut.wait(time, this)
        mc.lockTouch(false)
        this.stateViewing = newState
    }

    private onWeakGuide(guide: WeakGuideObj) {
        if (guide.id == WeakGuideType.GOTO_BUY_TRAIN_1) {
            let targetNode = this.trainNode_
            animHelper.showWeakGuideFinger(targetNode.parent, guide.fingerGuide, targetNode.getPosition())
            this.checkOverGotoBuyTrain1()
        } else if (guide.id == WeakGuideType.GO_PLANET_1) {
            let targetNode = eventCenter.get(NodeType.GUIDE_STAR_MAP)
            animHelper.showWeakGuideFinger(targetNode, guide.fingerGuide)
            this.checkOverGotoGoPlanet1()
        } else if (guide.id == WeakGuideType.EXPLORE_1) {
            let targetNode = eventCenter.get(NodeType.GUIDE_BUTTON_EXPLORE)
            animHelper.showWeakGuideFinger(targetNode, guide.fingerGuide)
            this.checkOverGotoExplore1()
        } else if (guide.id == WeakGuideType.CHARACTER_1) {
            let targetNode = this.roleNode_
            animHelper.showWeakGuideFinger(targetNode.parent, guide.fingerGuide, targetNode.getPosition())
            this.checkOverGotoCharacter1()
        } else if (guide.id == WeakGuideType.JACKPOT_1) {
            let targetNode = this.jackpotNode_
            animHelper.showWeakGuideFinger(targetNode.parent, guide.fingerGuide, targetNode.getPosition())
            this.checkOverGotoJackpot1()
        }
    }
    private async checkOverGotoBuyTrain1() {
        let event = await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.buyTrainStep = event.target == this.trainNode_ ? 3 : 0
    }
    private async checkOverGotoGoPlanet1() {
        let event = await viewHelper.waitAnyClickEnd()
        if (gameHelper.weakGuide.goPlanetData == 0) {
            gameHelper.weakGuide.goPlanetStep = 0
            return
        }
        let targetNode = eventCenter.get(NodeType.GUIDE_BUTTON_EXPLORE)
        gameHelper.weakGuide.goPlanetStep = event.target == targetNode ? 2 : 0
    }
    private async checkOverGotoCharacter1() {
        await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.characterStep = 0
    }
    private async checkOverGotoJackpot1() {
        await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.jackpotStep = 0
    }
    private async checkOverGotoExplore1() {
        await viewHelper.waitAnyClickEnd()
        gameHelper.weakGuide.exploreStep = 0
    }

    private async updateTransport() {
        let preActive = this.transportNode_.active
        this.transportNode_.active = gameHelper.planet.isMeetMonsters()
        if (!preActive && this.transportNode_.active) {
            this.transportBattleIconShow = true
            // let btn = this.transportNode_.Child("anim/btn")
            // btn.off("click")
            // let sk = this.transportNode_.Child("anim", sp.Skeleton)
            // await sk.playAnimation("animation1")
            // sk.playAnimation("animation2", true)

            // btn.on("click", () => {
            //     gameHelper.transport.battle()
            // })
        }
    }

    private setWorldDayOrNight() {
        this.wordDayNode_.Component(cc.MultiFrame).setFrame(gameHelper.world.isNight())
    }

    private bindTransportBtnEvt() {
        const it = this.transportNode_.Child("pos")
        it.off("click")
        it.on("click", () => {
            eventCenter.emit(EventType.TRANSPORT_FOCUS_MONSTER)
        })
    }

    private showTransportBattleIcon() {
        if (this.transportBattleIconShow == true) return
        this.transportBattleIconShow = true
        const it = this.transportNode_.Child("pos")
        it.active = true
        cc.Tween.stopAllByTarget(it)
        cc.tween(it).to(.2, { opacity: 255 }).start()
    }

    private hideTransportBattleIcon() {
        if (this.transportBattleIconShow == false) return
        this.transportBattleIconShow = false
        const it = this.transportNode_.Child("pos")
        it.active = true
        cc.Tween.stopAllByTarget(it)
        cc.tween(it).to(.2, { opacity: 0 }).call(() => {
            it.active = false
        }).start()
    }

    private updateOutputClockN() {
        const time = gameHelper.world.getOutputTime()
        const max = gameHelper.world.getMaxOfflineTime()
        const rate = Math.min(time / max, 1)
        this.outputNode_.Child("progress", cc.Sprite).fillRange = rate
        this.outputNode_.Child("pointer").active = rate >= 1
        this.outputNode_.Child("warn").active = rate >= 1
    }

    private setTrainActivity() { this.shijiankuangNode_.Component(cc.MultiFrame).setFrame(!!gameHelper.trainActivity.currentActivity) }

    private showTip(content: string) {
        this.tipActivityNode_.Child("lb").setLocaleUpdate(() => content)
        this.moveIn(this.tipActivityNode_)
        this.scheduleOnce(() => {
            this.moveOut(this.tipActivityNode_)
        }, 2)
    }

    private moveIn(it: cc.Node, time: number = 0.3) {
        if (it.Data == null) it.Data = it.y
        let y = it.Data
        animHelper.moveYIn(it, time, y, -y)
    }
    private async moveOut(it: cc.Node, time: number = 0.3) {
        let y = it.Data
        await animHelper.moveYOut(it, time, y, -y)
    }

}
