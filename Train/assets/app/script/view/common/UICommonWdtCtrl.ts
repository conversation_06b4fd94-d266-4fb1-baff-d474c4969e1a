import { Condition } from "../../common/constant/DataType";
import { ConditionType } from "../../common/constant/Enums";
import { UIFunctionType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import NodeType from "../../common/event/NodeType";
import { animHelper } from "../../common/helper/AnimHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { unlockHelper } from "../../common/helper/UnlockHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../../model/common/ConditionObj";
import BuildObj from "../../model/train/common/BuildObj";
import CarriageModel from "../../model/train/common/CarriageModel";

const { ccclass } = cc._decorator;

@ccclass
export default class UICommonWdtCtrl extends mc.BaseWdtCtrl {

    //@autocode property begin
    protected touchNode_: cc.Node = null // path://touch_be_n
    protected residentNode_: cc.Node = null // path://resident_n
    protected bagPosNode_: cc.Node = null // path://resident_n/bag_pos_n
    protected bagNode_: cc.Node = null // path://resident_n/bag_be_n
    protected bagRewardsNode_: cc.Node = null // path://resident_n/bag_rewards_n
    protected shopNode_: cc.Node = null // path://resident_n/shop_be_n
    protected dailyTaskNode_: cc.Node = null // path://resident_n/daily_task_be_n
    protected menuNode_: cc.Node = null // path://menu_n
    protected menuContentNode_: cc.Node = null // path://menu_n/mask/menu_content_n
    protected settingNode_: cc.Node = null // path://menu_n/mask/menu_content_n/setting_be_n
    protected mailNode_: cc.Node = null // path://menu_n/mask/menu_content_n/mail_be_n
    protected personalInfoNode_: cc.Node = null // path://menu_n/mask/menu_content_n/personalInfo_be_n
    protected menuCtrlNode_: cc.Node = null // path://menu_n/menu_ctrl_be_n
    protected mainFunctionNode_: cc.Node = null // path://mainFunction_n
    //@end

    private bagGenRef: number = 0
    private bagGenAct: boolean = false
    private isShowMenu: boolean = false
    private bagRewardColor: cc.Color[] = null

    public listenEventMaps() {
        return [
            { [NodeType.UI_BAG]: () => this.bagNode_ },
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onFunctionUnlock },
            { [EventType.HIDE_MENU]: this.hideMenu },
            { [EventType.PLANET_GEN_BAG_ASSETS1]: this.bagIn },
            { [EventType.PLANET_GEN_BAG_ASSETS2]: this.showPlanetGen },
            { [EventType.SHOW_BUILDCOST_TIPS]: this.showBuildCostTips },
            { [EventType.SHOW_CARRIAGECOST_TIPS]: this.onCreateCarriage },
            { [EventType.GET_UI_FUNCTION_NODE]: this.getCrossFunctionNode },

            { [NodeType.GUIDE_BUTTON_ACHIEVEMENT]: () => { return this.getFunctionNode(UIFunctionType.ACHIEVEMENT) } },
        ]
    }

    public async onCreate() {
        this.initFunc()
        this.initCross()
        this.initBagReward()
    }

    protected start(): void {
    }

    public setBagNeedAct(bol: boolean) {
        this.bagGenAct = bol
        this.bagNode_.x = bol ? this.getBagNodeHideX() : 0
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://resident_n/achieve_be_n
    onClickAchieve(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('achieve/AchievePnl')
    }

    // path://resident_n/bag_be_n
    onClickBag(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('train/BagPnl')
    }

    // path://resident_n/shop_be_n
    onClickShop(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('shop/ShopPnl')
    }

    // path://menu_n/menu_ctrl_be_n
    onClickMenuCtrl(event: cc.Event.EventTouch, data: string) {
        let show = !this.isShowMenu
        this.updateMenu(show)
    }

    // path://touch_be_n
    onClickTouch(event: cc.Event.EventTouch, data: string) {
        this.updateMenu(false)
    }

    // path://menu_n/mask/menu_content_n/setting_be_n
    onClickSetting(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('train/SettingPnl')
    }

    // path://menu_n/mask/menu_content_n/mail_be_n
    onClickMail(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('train/MailPnl')
    }

    // path://menu_n/mask/menu_content_n/personalInfo_be_n
    onClickPersonalInfo(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('train/PersonalInfoPnl')
    }

    // path://resident_n/daily_task_be_n
    onClickDailyTask(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl('daily_task/DailyTaskPnl')
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    private initCross() {
        let root = this.mainFunctionNode_
        root.active = false
        root.children.forEach(n => n.setPosition(0, 0))
    }
    private getCrossFunctionNode(type: UIFunctionType) {
        let it = this.getFunctionNode(type)
        if (it) return it
        // 跨场景的功能解锁入口放这里
        if (mc.currWindName == "main") return
        let root = this.mainFunctionNode_
        switch (type) {
            case UIFunctionType.WORK_WATER: return root.Child('water')
            case UIFunctionType.WORK_ENGINE: return root.Child('engine')
        }
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initFunc() {
        unlockHelper.initFunctionNode(this.getFunctionNode.bind(this))
    }

    public getFunctionNode(type: UIFunctionType) {
        switch (type) {
            case UIFunctionType.BAG: return this.bagNode_
            case UIFunctionType.SET: return this.settingNode_
            case UIFunctionType.SHOP: return this.shopNode_
            case UIFunctionType.ACHIEVEMENT: return this.dailyTaskNode_
        }
    }

    private onFunctionUnlock(type: UIFunctionType) {
        unlockHelper.unlockFunction(this.getFunctionNode.bind(this), type)
    }

    private updateMenu(show) {
        show = !!show
        this.isShowMenu = show
        this.touchNode_.active = show
        this.menuCtrlNode_.Component(cc.MultiFrame).setFrame(show ? 1 : 0)
        this.menuNode_.Child('redDotMask').active = !show

        cc.Tween.stopAllByTarget(this.menuContentNode_)
        cc.Tween.stopAllByTarget(this.residentNode_)
        let showX = 1119, hideX = 1400, showY = 35, hideY = 300
        let targetX, targetY
        this.residentNode_.active = !show
        if (show) {
            targetX = hideX, targetY = showY
        }
        else {
            targetX = showX, targetY = hideY
            this.residentNode_.x = hideX
            cc.tween(this.residentNode_).to(0.2, { x: targetX }).start()
        }
        cc.tween(this.menuContentNode_).to(0.2, { y: targetY }).start()
    }

    private hideMenu() {
        this.menuNode_.active = false
    }
    private getBagNodeHideX() {
        let node = this.bagPosNode_
        if (node.Data == null) {
            let pos = ut.convertToNodeAR(this.node, this.bagNode_, cc.v2(this.node.width * 0.5, 0))
            node.Data = pos.x + this.bagNode_.width * 0.5
        }
        return node.Data
    }
    private async bagIn() {
        if (!this.bagGenAct) return
        this.bagGenRef++
        if (this.bagGenRef == 1) {
            await ut.wait(0.2, this)
            cc.Tween.stopAllByTarget(this.bagNode_)
            cc.tween(this.bagNode_).to(0.5, { x: 0 }).start()
        }
    }
    private bagOut() {
        if (!this.bagGenAct) return
        this.bagGenRef--
        if (this.bagGenRef == 0) {
            cc.tween(this.bagNode_).to(0.5, { x: this.getBagNodeHideX() }).start()
        }
    }
    private initBagReward() {
        let parent = this.bagRewardsNode_
        let lb = parent.Child('item/count')
        this.bagRewardColor = [lb.color, lb.Component(cc.LabelOutline).color]
        parent.active = false
    }
    private sizeBagReward() {
        let parent = this.bagRewardsNode_
        let max = 0
        let ary = parent.children
        ary.forEach(it => {
            if (it.active) {
                max = Math.max(max, it.width)
            }
        })
        if (max > 0) {
            parent.width = max
            ary.forEach(it => { it.x = -max })
        }
    }

    private actShowGen(callEnd?: Function) {
        let node = this.bagRewardsNode_
        cc.Tween.stopAllByTarget(node)
        cc.tween(node)
            .set({ y: this.bagPosNode_.y - 30, opacity: 0, active: true })
            .parallel(
                cc.tween(node).by(0.5, { y: 30 }, { easing: cc.easing.sineOut }),
                cc.tween(node)
                    .by(0.2, { opacity: 255 }, { easing: cc.easing.sineOut })
                    .delay(0.8)
                    .to(0.2, { opacity: 0 }, { easing: cc.easing.sineIn })
                    .call(this.bagOut.bind(this))
                    .set({ active: false })
                    .call(callEnd || function () { })
            )
            .start()
    }

    private resetBagRewardNode() {
        let node = this.bagRewardsNode_
        node.active = false
        cc.Tween.stopAllByTarget(node)
    }

    private loadPlanetGen(rewards: ConditionObj[], isCost: boolean = false) {
        let list = gameHelper.mergeChestCondition(rewards)
        let maxWidth = 0
        this.bagRewardsNode_.Items(list, (it: cc.Node, data: ConditionObj) => {
            if (data.num == 0) {
                it.active = false
                return
            }
            let lb = it.Child('count')
            lb.Component(cc.Label).string = `${isCost ? '-' : '+'}${ut.simplifyMoney(data.num)}`
            // lb.Component(cc.Label).string = ut.simplifyMoney(gameHelper.getNumByCondition(data))
            lb.color = this.bagRewardColor[isCost ? 1 : 0]
            lb.Component(cc.LabelOutline).color = this.bagRewardColor[isCost ? 0 : 1]
            lb.Component(cc.Label)._forceUpdateRenderData()

            resHelper.updateAssetsByCondInfo(data, it.Child('icon'), this.getTag())

            let width = it.Component(cc.Layout).spacingX
            for (let child of it.children) {
                width += child.width
            }
            maxWidth = Math.max(width, maxWidth)
        })

        this.bagRewardsNode_.children.forEach(n => {
            n.x = -maxWidth
        })
    }

    private showPlanetGen(rewards: ConditionObj[], key: number) {
        if (animHelper.isPlanetGenStop(key)) {
            return this.completeGenAct()
        }
        let check = () => {
            if (animHelper.isPlanetGenStop(key)) {
                this.unschedule(check)
                this.completeGenAct()
            }
        }
        this.scheduleUpdate(check)
        this.loadPlanetGen(rewards)
        this.actShowGen(() => {
            animHelper.planetGenEnd(key)
            this.unschedule(check)
        })
    }

    private completeGenAct() {
        if (this.bagGenAct) {
            this.bagGenRef = 0
            this.bagNode_.x = this.getBagNodeHideX()
        }
        cc.Tween.stopAllByTarget(this.bagNode_)
        this.resetBagRewardNode()
    }

    private showCostProp(buyCost: Condition[]) {
        let ary = buyCost.filter(con => con.type == ConditionType.PROP)
        if (ary.length > 0) {
            this.loadPlanetGen(gameHelper.toConditions(ary), true)
            this.actShowGen()
        }
    }

    private showCostCurrency(buyCost: Condition[]) {
        let ary = buyCost.filter(con => con.type < ConditionType.PASSENGER)
        if (ary.length > 0) {
            ary.forEach(con => {
                this.emit(EventType.UPDATE_ONE_CURRENCY, con.type, con.num)
            })
        }
    }

    private showBuildCostTips(build: BuildObj) {
        let cfg = cfgHelper.getBuildLvCfg(build.carriageId, build.order, build.lv)
        if (!cfg) return
        let ary = cfg.buyCost
        if (ary) {
            this.showCostProp(ary)
            this.showCostCurrency(ary)
        }
    }

    private onCreateCarriage(model: CarriageModel) {
        let ary = model.cfg.buyCost
        if (ary) {
            this.showCostProp(ary)
            this.showCostCurrency(ary)
        }
    }
}
