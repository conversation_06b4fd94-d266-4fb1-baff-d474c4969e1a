import { Msg } from "../../../proto/msg-define";
import { Condition } from "../../common/constant/DataType";
import EventType from "../../common/event/EventType";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import PlanetModel from "../../model/planet/PlanetModel";

const { ccclass } = cc._decorator;

type ArgType = {
    id: number,
    x: number,
    y: number,
    bodyX: number,
    bodyY: number,
    scaleX: number,
    scaleY: number,
    nameX: number,
    nameY: number,
    redDotX: number,
    redDotY: number,
    timeY: number,
}

@ccclass
export default class PublicityPlayPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected blockNode_: cc.Node = null // path://block_n
    protected mapSv_: cc.ScrollView = null // path://map_sv
    protected planetsNode_: cc.Node = null // path://map_sv/view/content/planets_n
    protected backNode_: cc.Node = null // path://back_be_n
    protected oneKeyGetNode_: cc.Node = null // path://one_key_get_n_be
    //@end

    private _showArgs: ArgType[] = []

    public listenEventMaps() {
        return [
            { [EventType.PUBLICITY_ROLE_NUM_CHANGE]: this.onPublicityRoleNumChange },
            { [EventType.PUBLICITY_DURATION_TIME_CHANGE]: this.onPublicityDurationTimeChange },
            { [EventType.PUBLICITY_UPGRADE]: this.onPublicityUpgrade },
        ]
    }

    public async onCreate() {
        this.setParam({ isAct: false, isMask: false })
    }

    public onEnter(data: ArgType[]) {
        this._showArgs = data
        this.initView()
    }

    public onRemove() {
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://one_key_get_n_be
    onClickOneKeyGet(event: cc.Event.EventTouch, data: string) {
        this.handleGetReward()
    }

    // path://bag_n_be
    onClickBag(event: cc.Event.EventTouch, data: string) {
        cc.log('onClickBag', data)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    onPublicityRoleNumChange(id: number) {
        const node = this.planetsNode_.children.find(it => it.Data == id)
        if (node) {
            this.updateOne(node, this._showArgs.find(it => it.id == id))
        }
    }

    onPublicityDurationTimeChange(id: number) {
        const node = this.planetsNode_.children.find(it => it.Data == id)
        if (node) {
            this.updateOne(node, this._showArgs.find(it => it.id == id))
        }
    }

    async onPublicityUpgrade(id: number) {
        const node = this.planetsNode_.children.find(it => it.Data == id)
        if (!node) return
        await ut.waitNextFrame(1)
        const planet = gameHelper.planet.getPlanet(id)
        const rewardCond = planet.getPublicityInfo()?.reward
        const it = node.Child("ui/upgrade")
        cc.Tween.stopAllByTarget(it)
        resHelper.loadZjmIconByCondInfo(rewardCond, it.Child("icon"), this.getTag())
        const cur = planet.getPublicityInfo()
        const last = planet.getPublicityLastInfo()
        it.Child("lbl").setLocaleUpdate(() => `${last.rate}<color=#8dfd5c>+${cur.rate - last.rate}/${assetsMgr.lang("common_timeUnitShort_hour")}</color>`)
        it.active = true
        it.Component(cc.Layout).updateLayout()
        it.y = -205
        it.opacity = 100
        cc.tween(it)
            .to(.2, {
                y: -105,
                opacity: 255
            })
            .start()
            .delay(1)
            .to(.2, {
                opacity: 100
            })
            .call(() => it.active = false)
            .start()

    }

    // ----------------------------------------- custom function ----------------------------------------------------

    initView() {
        this.initContnet()
        this.initPlanets()
    }

    initContnet() {
        let content = this.mapSv_.content
        let width = 4014, height = 1675
        let sx = cc.winSize.width / width
        let sy = cc.winSize.height / height
        let scale = Math.max(sx, sy)
        if (scale > 1) {
            content.scale = scale
            content.width = scale * width
            content.height = scale * height
        }
    }

    initPlanets() {
        let planetsNode_ = this.planetsNode_
        planetsNode_.Items(this._showArgs, (it: cc.Node, arg: ArgType) => this.updateOne(it, arg))
    }

    updateOne(it: cc.Node, arg: ArgType) {
        it.Data = arg.id
        let planet = gameHelper.planet.getPlanet(arg.id)
        it.setPosition(arg.x, arg.y)
        const body = it.Child('body')
        const ui = it.Child('ui')
        if (!planet || !planet.isDone()) {
            body.active = false
            ui.active = false
            return
        }
        body.active = true
        ui.active = true
        body.setPosition(arg.bodyX, arg.bodyY)
        body.scaleX = arg.scaleX
        body.scaleY = arg.scaleY
        const icon = body.Child('icon')
        this.updateName(it, planet, arg)
        this.updateicon(icon.Component(cc.Sprite), planet)
        this.updateNum(ui.Child("num"), planet, arg)
        this.updateReward(ui.Child("num/reward"), planet)
        let redDot = it.Child('ui/redDot')
        redDot.active = false
    }

    updateicon(icon: cc.Sprite, planet: PlanetModel) {
        if (planet.isHide()) {
            return void resHelper.loadPlanetIconHide(planet.getId(), icon, this.getTag())
        }
        resHelper.loadPlanetIcon(planet.getId(), icon, this.getTag())
        icon.node.off("click")
        icon.node.on("click", () => {
            viewHelper.showPnl("planet/PublicityDo", planet.getId())
        })
    }

    updateName(node: cc.Node, planet: PlanetModel, arg: ArgType) {
        let nameNode: cc.Node = node.Child('ui/name')
        nameNode.setPosition(arg.nameX, arg.nameY)
        nameNode.Component(cc.Label).setLocaleUpdate(() => {
            return planet.getShowName()
        })
        nameNode.Component(cc.MultiColor).setColor(planet.getRoleNum() > 0)
    }

    updateNum(node: cc.Node, planet: PlanetModel, arg: ArgType) {
        node.y = arg.timeY
        node.Child("lb", cc.Label).string = planet.getRoleNum() + ""
        node.Child("sp", cc.MultiFrame).setFrame(planet.getRoleNum() > 0)
    }

    updateReward(node: cc.Node, planet: PlanetModel) {
        node.off("click")
        node.on("click", () => this.handleGetReward(planet.getId()))
        let percent = planet.getPublicityTimePercent()
        if (planet.getRoleNum() <= 0) {
            percent = 0
        }
        let reward = planet.getPublicityInfo()?.reward
        if (!reward) {
            reward = planet.getPublicityNextInfo()?.reward
        }
        node.Items([reward], (it: cc.Node, cond: Condition) => {
            resHelper.loadZjmIconByCondInfo(cond, it.Child("icon"), this.getTag())
            const bg1 = it.Child("bg1")
            const bg2 = it.Child("bg2")
            const bg3 = it.Child("bg3")
            bg1.active = percent == 0
            bg2.active = percent > 0
            bg3.active = percent > 0
            if (percent > 0) {
                bg3.Component(cc.Sprite).fillRange = percent
            }
        })
    }

    @ut.addLock
    async handleGetReward(planetId: number = -1) {
        if (planetId != -1) {
            const planet = gameHelper.planet.getPlanet(planetId)
            if (planet.getRoleNum() == 0) {
                return void viewHelper.showAlert("planet_publicity_tips_1")
            }
            if (planet.getPublicityUnGetTime() < ut.Time.Hour) {
                return void viewHelper.showAlert("planet_publicity_tips_1")
            }
        }
        else {
            const match = gameHelper.planet.getPlanets().filter(p => p.isDone() && p.getRoleNum() > 0 && p.getPublicityUnGetTime() >= ut.Time.Hour)
            if (!match.length) return void viewHelper.showAlert("planet_publicity_tips_1")
        }
        const r = await gameHelper.net.requestWithDataWait(Msg.C2S_GetPublicityRewardMessage, { planetId })
        if (r.code != 0) return void viewHelper.showNetError(r.code)
        const rewards = gameHelper.toConditions(r.rewards)
        await viewHelper.showGeneralReward(rewards)
        gameHelper.grantRewards(rewards)
        for (const key in r.timeMap) {
            const planetId = Number(key)
            const v = r.timeMap[key]
            const planet = gameHelper.planet.getPlanet(planetId)
            planet.setPublicityUnGetTime(v)
        }
    }
}
