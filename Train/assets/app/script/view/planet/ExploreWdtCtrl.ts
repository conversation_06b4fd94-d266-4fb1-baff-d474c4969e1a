import { util } from "../../../core/utils/Utils";
import EventType from "../../common/event/EventType";
import NodeType from "../../common/event/NodeType";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import PlanetModel from "../../model/planet/PlanetModel";

const { ccclass } = cc._decorator;

enum State {
    Normal,
    Moving,
    AutoPick,
}

const PlanetScalePos = {
    cfg: {
        [1001]: { scale: 0.36, pos: cc.v2(13, 10.5) },
        [1002]: { scale: 0.2821, pos: cc.v2(13, 0) },
        [1005]: { scale: 0.32, pos: cc.v2(28, -3) },
        [1006]: { scale: 0.36, pos: cc.v2(17.5, 8.8) },
        [1007]: { scale: 0.36, pos: cc.v2(13, 10.5) },
    },
    getDic(id: number) {
        let dic = PlanetScalePos.cfg[id]
        // if (dic == null) cc.error("get planet dic. no id:", id)
        return dic
    },
    getPos(id: number): cc.Vec2 {
        let dic = PlanetScalePos.getDic(id)
        if (dic) return dic.pos
        return cc.v2(0, 0)
    },
}

@ccclass
export default class ExploreWdtCtrl extends mc.BaseWdtCtrl {

    //@autocode property begin
    protected btn1Node_: cc.Node = null // path://btn1_be_n
    protected rewardNode_: cc.Node = null // path://reward_n
    protected cancelNode_: cc.Node = null // path://cancel_be_n
    //@end

    private myState: State = null
    private waitAnimation: boolean = false
    private showId: number = null

    public listenEventMaps() {
        return [
            { [EventType.TRAIN_MOVING_PLANET]: this.initView, 'tag': 'create' },

            { [NodeType.GUIDE_BUTTON_EXPLORE]: () => this.btn1Node_ },
        ]
    }

    public onCreate() {
        this.initView()
    }

    private initView() {
        this.initState()
        this.initExploreTimer()
    }

    public onDisable(): void {
        this.rewardNode_.children.forEach(node => node.active && node.destroy())
    }

    update() {
        if (this.waitAnimation) return
        this.setExploreTimer1()
        this.node.Child('warning').active = gameHelper.planet.isMeetMonsters()
        this.node.Child('transportReward').active = gameHelper.transport.isTransportDone()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://btn1_be_n
    onClickBtn1(event: cc.Event.EventTouch, data: string) {
        this.clickBtn()
    }

    // path://btn2_be_n
    onClickBtn2(event: cc.Event.EventTouch, data: string) {
        this.clickBtn()
    }

    // path://cancel_be_n
    onClickCancel(event: cc.Event.EventTouch, data: string) {
        this.onCancelMove()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
  
    // ----------------------------------------- custom function ----------------------------------------------------
    private initState() {
        this.myState = State.Normal
        if (gameHelper.planet.isMoving()) {
            this.myState = State.Moving
        }
        this.updateCancelNode()
        this.initBtns()
    }
    private initBtns() {
        this.setBtn1()
        this.setTransportRewardBtn()
    }

    private updateCancelNode() {
        this.cancelNode_.active = (this.myState == State.Moving)
    }

    @util.addLock
    private async clickBtn() {
        if (this.waitAnimation) return
        // if (gameHelper.guide.needForbidGoGarden()) {
        //     return viewHelper.showAlert("explore_tips_7")
        // }
        if (gameHelper.guide.needForbidGoGarden2()) {
            return viewHelper.showAlert("explore_tips_8")
        }
        if (gameHelper.transport.isTransportDone()) {
            return gameHelper.transport.getTransportReward()
        }

        let planet = gameHelper.planet.getCurPlanet()
        if (planet && !gameHelper.planet.isMoving()) {
            viewHelper.gotoPlanet()
        } else {
            if (gameHelper.planet.isMoving()) {
                if (gameHelper.planet.isMeetMonsters()) {
                    await gameHelper.transport.battle()
                }
                else {
                    this.onCancelMove()
                }
            } else {
                viewHelper.showPnl("planet/PlanetChoose")
            }
        }
    }

    @util.addLock
    private async cancelMove() {
        let succ = await gameHelper.planet.cancelMove()
        if (succ) {
            this.initState()
            viewHelper.showTips("planet_cancel_tips_3")
        }
    }

    private setIcon(planet: PlanetModel) {
        this.showId = planet.getId()
    }
    private set1Icon(isMov: boolean) {
        let planet = null
        if (isMov) {
            planet = gameHelper.planet.moveTarget
        } else {
            planet = gameHelper.planet.getCurPlanet()
        }
        this.setIcon(planet)
    }
    private setBtn1() {
        let btn = this.btn1Node_
        btn.active = true
        let mov = this.myState == State.Moving
        let pre = mov ? '2' : '1'
        btn.Child('time').active = mov
        this.set1Icon(mov)
        let planet = gameHelper.planet.getPlanet(this.showId)
        let icon = btn.Child('icon')
        let node1 = icon.Child('1')
        let node2 = icon.Child('2')
        let loadFunc1, loadFunc2
        icon.Swih(pre)
        let maskNode = node2.Child('mask')
        maskNode.Component(cc.Mask).spriteFrame = null

        if (planet.isHide()) {
            loadFunc1 = resHelper.loadPlanetIconHide(this.showId, node1.Component(cc.Sprite), this.getTag())
            loadFunc2 = resHelper.loadPlanetIconHide(this.showId, node2.Component(cc.Sprite), this.getTag())
            maskNode.active = false
        }
        else {
            maskNode.active = true
            loadFunc1 = resHelper.loadPlanetIcon(this.showId, node1.Component(cc.Sprite), this.getTag())
            loadFunc2 = resHelper.loadPlanetIcon(this.showId, node2.Component(cc.Sprite), this.getTag())
        }
        loadFunc1.then(() => {
            if (!cc.isValid(this)) return
            node1.setPosition(PlanetScalePos.getPos(this.showId))
        })
        loadFunc2.then(() => {
            if (!cc.isValid(this)) return
            let maskNode = node2.Child('mask')
            maskNode.Component(cc.Mask).spriteFrame = node2.Component(cc.Sprite).spriteFrame
            maskNode.width = node2.width
            maskNode.height = node2.height
        })
        return icon
    }

    private setTransportRewardBtn() {
        this.node.Child('transportReward').off('click')
        this.node.Child('transportReward').on('click', () => {
            if (gameHelper.transport.isTransportDone()) {
                gameHelper.transport.getTransportReward()
            }
        })
    }
    private initExploreTimer() {
        let timer1 = this.btn1Node_.Child('time', cc.LabelTimer)
        timer1.setFormat(gameHelper.updateCountDown)
        this.setExploreTimer1()
    }
    private getTime1() {
        return gameHelper.planet.getMoveSurplusTime()
    }

    private setExploreTimer1() {
        if (this.myState != State.Moving) return
        let node = this.btn1Node_.Child('time')
        let time = this.getTime1()
        node.Component(cc.LabelTimer).run(time)
        if (time < 1000) {
            this.movingToNormal()
        }
    }

    private async movingToNormal() {
        this.waitAnimation = true
        await ut.wait(0.5, this)
        let btn = this.btn1Node_
        let node1 = btn.Child('icon/1')
        let node2 = btn.Child('icon/2')
        let temp1 = { x: node1.x, y: node1.y, scale: node1.scale, opacity: node1.opacity }
        let temp2 = { x: node2.x, y: node2.y, scale: node2.scale, opacity: node2.opacity }
        node1.active = true
        node2.active = false
        btn.Child('time').active = false
        await cc.tween(node1).set(temp2).to(0.8, temp1).promise()
        this.myState = State.Normal
        this.initState()
        this.waitAnimation = false
    }

    private onCancelMove() {
        viewHelper.showMessageBox("planet_cancel_guiText_2", ()=>{
            if (gameHelper.planet.isMeetMonsters()) {
                viewHelper.showAlert("planet_cancel_tips_1")
            }
            else if (!gameHelper.planet.isMoving()) {
                viewHelper.showAlert("planet_cancel_tips_2")
            }
            else {
                this.cancelMove()
            }
        }, null, {title: "planet_cancel_guiText_1", mask: true})
    }
}
