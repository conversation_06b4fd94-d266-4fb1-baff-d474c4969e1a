import { RuleType } from "../../common/constant/Enums";
import { gameHelper } from "../../common/helper/GameHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import PlanetTitleCmpt from "../cmpt/planet/PlanetTitleCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class BlackHoleLevellPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected backNode_: cc.Node = null // path://back_be_n
    protected PlanetTitleNode_: cc.Node = null // path://PlanetTitle_n
    protected levelsNode_: cc.Node = null // path://root/levels_n
    //@end

    private select = null

    public listenEventMaps() {
        return []
    }

    public onEnter(data: any) {
        this.initView()
    }


    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.close()
    }

    // path://root/confirm_be
    onClickConfirm(event: cc.Event.EventTouch, data: string) {
        let level = this.select.id
        viewHelper.showPnl("blackHole/BlackHoleReady", {level})
        this.close()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    
    // ----------------------------------------- custom function ----------------------------------------------------
    

    private initView() {
        // this.PlanetTitleNode_.Component(PlanetTitleCmpt).init('blackHole_guiText_1', '', RuleType.BLACKHOLE)

        // let levels = assetsMgr.getJson<any>("BlackHoleLevel").datas
        // let unlockLv = gameHelper.blackHole.getUnlockLevel()

        // if (!this.select) {
        //     this.select = levels.find(d => d.id == unlockLv)
        // }

        // this.levelsNode_.Items(levels, (it, data) => {
        //     let level = data.id
        //     let isUnlock = level <= unlockLv

        //     it.Data = data
        //     it.setGray(!isUnlock)

        //     let isSelect = this.select == data

        //     let icon = it.Child('icon')
        //     icon.scale = isSelect ? 1.4 : 1
        //     icon.Component(cc.MultiFrame).setFrame(level - 1)

        //     let name = it.Child("name")
        //     name.Child("1").setLocaleKey(data.name)
        //     name.Child("2").setLocaleKey(data.name)
        //     name.Swih(isSelect ? "2" : "1")

        //     icon.off("click")
        //     icon.on("click", ()=>{
        //         if (this.select == data) return
        //         if (!isUnlock) {
        //             viewHelper.showAlert("blackHole_tips_2")
        //             return
        //         }
        //         if (level < unlockLv) {
        //             viewHelper.showAlert("blackHole_tips_3")
        //             return
        //         }
        //         this.selectLv(data)
        //     })
        // })
    }

    private async selectLv(data) {
        this.select = data
        this.initView()
    }

    protected update(dt: number): void {
        let time = ut.millisecondFormat(gameHelper.world.getNextDaySurpluTime(), "hh:mm:ss")
        this.node.Child("resttime/time", cc.RichText).setLocaleKey("blackHole_guiText_3", `<color=79ee73>${time}</c>`)
    }
}
