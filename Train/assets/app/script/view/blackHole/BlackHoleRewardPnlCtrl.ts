import MultiMaterial from "../../../core/component/MultiMaterial";
import { CONDITION_NEEDPLAY, UNLOCK_FADEIN_TIME, UNLOCK_TIME_SHORT } from "../../common/constant/Constant";
import { anim<PERSON>elper } from "../../common/helper/AnimHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import ConditionObj from "../../model/common/ConditionObj";
import FrameIconNum from "../prefab/FrameIconNum";

const { ccclass } = cc._decorator;

@ccclass
export default class BlackHoleRewardPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected backNode_: cc.Node = null // path://back_be_n
    protected iconNode_: cc.Node = null // path://content/title/icon_n
    protected rewardSv_: cc.ScrollView = null // path://content/reward_sv
    protected rewardLyNode_: cc.Node = null // path://content/reward_sv/view/rewardLy_n
    protected bottomTipsNode_: cc.Node = null // path://content/bottomTips_n
    //@end
    private _resolve: Function = null

    public listenEventMaps() {
        return []
    }

    public async onCreate() {
        this.setParam({ isAct: false, maskOpacity: 0.9 });
    }

    public onEnter(data: { rewards: ConditionObj[], done: boolean, resolve: Function }) {
        this.hideContinue()
        this.node.opacity = 0
        let act = cc.fadeIn(UNLOCK_FADEIN_TIME)
        cc.tween(this.node).then(act).start()
        this.bottomTipsNode_.setLocaleKey(data.done ? "blackHole_guiText_27" : "blackHole_guiText_26")
        this._resolve = data.resolve
        this.initView(data.rewards, true);
    }

    public onRemove() {
        this._resolve?.()
    }

    public onClean() {
        super.onClean()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        let diamondPosList: cc.Vec2[] = []
        let list: ConditionObj[] = []
        this.rewardLyNode_.children.forEach((it) => {
            let cond = it.Data
            if (CONDITION_NEEDPLAY.has(cond.type)) {
                diamondPosList.push(ut.convertToNodeAR(it, this.rewardSv_.node.parent))
                list.push(cond)
            }
        })
        if (diamondPosList.length > 0 && !!diamondPosList[0]) {
            this.hideContinue()
            this.showDiamond(diamondPosList, list)
        }
        else {
            this.close()
        }
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private async showDiamond(diamondPosList, list) {
        cc.tween(this.rewardLyNode_).to(0.2, { opacity: 0 }).call(() => {
            viewHelper.showPnl('common/DiamondPnl', diamondPosList, list, () => this.close())
        }).start()

    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView(data: ConditionObj[], needMerge) {
        ut.wait(UNLOCK_TIME_SHORT, this).then(() => {
            this.showContinue()
        })
        if (needMerge) data = gameHelper.mergeCondition(data)
        let content = this.rewardLyNode_
        let layout = content.Component(cc.Layout)
        let oneLine = 7
        if (data.length > oneLine) {
            layout.type = cc.Layout.Type.GRID
            content.width = content.children[0].width * oneLine + (oneLine - 1) * layout.spacingX + layout.paddingLeft + layout.paddingRight
            this.rewardSv_.node.width = content.width + layout.spacingY * 2
            this.rewardSv_.node.height = Math.min(630, (content.children[0].height + layout.spacingY) * Math.ceil(data.length / oneLine) + layout.spacingY)
        } else {
            layout.type = cc.Layout.Type.HORIZONTAL
            this.rewardSv_.node.width = (content.children[0].width * data.length + (data.length - 1) * layout.spacingX + layout.paddingLeft + layout.paddingRight) + layout.spacingY * 2
            this.rewardSv_.node.height = content.children[0].height + 120
        }
        this.initRewards(data)
    }

    private initRewards(rewards: ConditionObj[]) {
        this.rewardLyNode_.Items(rewards, (it, data) => {
            data.isHide = false
            it.Component(FrameIconNum).init(data, this.getTag())
            it.Data = data
            uiHelper.regClickPropBubble(it, data)
        })
        this.rewardSv_.node.Child('mask', MultiMaterial).setMaterial(1, { color: this.rewardSv_.node.Child('mask').color })
    }

    private hideContinue() {
        this.backNode_.active = false
        this.bottomTipsNode_.active = false

    }

    private showContinue() {
        this.backNode_.active = true
        this.bottomTipsNode_.active = true
        animHelper.playContinueBlink(this.bottomTipsNode_)
    }

}
