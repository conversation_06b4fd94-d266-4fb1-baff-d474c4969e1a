import MultiMaterial from "../../../core/component/MultiMaterial";
import CoreEventType from "../../../core/event/CoreEventType";
import { util } from "../../../core/utils/Utils";
import { localConfig } from "../../common/LocalConfig";
import { LongPress, RuleType, UIFunctionType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import NodeType from "../../common/event/NodeType";
import { animHelper } from "../../common/helper/AnimHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { unlockHelper } from "../../common/helper/UnlockHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import Battle from "../../model/battle/Battle";
import { BattleRoleType } from "../../model/battle/BattleEnum";
import { BattleTeam } from "../../model/battle/BattleMgr";
import BattleRole from "../../model/battle/BattleRole";
import Monster from "../../model/battle/Monster";
import PassengerModel from "../../model/passenger/PassengerModel";
import PlanetCheckPointModel from "../../model/planet/PlanetCheckPointModel";
import { DragLongEvent } from "../cmpt/common/DragLongPressCmpt";
import PlanetTitleCmpt from "../cmpt/planet/PlanetTitleCmpt";

const { ccclass } = cc._decorator;

@ccclass
export default class BattleReadyPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected bgNode_: cc.Node = null // path://bg_n
    protected bottomNode_: cc.Node = null // path://bottom_n
    protected selectRolesSv_: cc.ScrollView = null // path://bottom_n/selectRoles_sv
    protected battleRolesNode_: cc.Node = null // path://up/battleRoles_n
    protected battleNode_: cc.Node = null // path://battle_be_n
    protected descLbl_: cc.Label = null // path://top/desc_l
    protected tipsNode_: cc.Node = null // path://tips_n
    //@end

    private passengers: (PassengerModel | BattleRole)[] = []
    private selectedRoles: (PassengerModel | BattleRole)[] = null

    private callback: Function = null

    private battlePos: cc.Vec2 = null

    private isCommBg: boolean = true //是不是走通用背景

    private team: BattleTeam = null

    private isPreview: boolean = false

    private onStartBattle: Function = null

    private level: number = 0

    private cfg = null

    public listenEventMaps() {
        return [
        ]
    }

    public async onCreate(data: any = {}) {
        this.setParam({ isAct: false, isMask: false })
        this.level = data?.level || 1
        this.callback = data?.callback
        this.passengers = data?.passengers || gameHelper.passenger.getPassengers()
        this.passengers = this.passengers.map(p => {
            if (p instanceof PassengerModel) {
                return p.toResonance()
            }
            return p
        })
        this.cfg = assetsMgr.getJsonData("BlackHoleLevel", this.level)
        let people = this.cfg.people
        this.selectedRoles = this.team?.getRoles().map(uid => this.passengers.find(p => p.uid == uid)) || []
        if (this.selectedRoles.length < people) {
            this.selectedRoles.length = people
        }
        this.onStartBattle = data?.onStartBattle

        let pList = []

        let roles = this.getSelectRoles().slice(0, 5)
        pList.push(
            ut.promiseMap(roles, async ({ id }) => {
                await resHelper.preloadRoleSk(id, this.getTag())
            }),
        )

        await Promise.all(pList)
    }

    public onEnter(data: any = {}) {
        this.battlePos = data.battlePos
        this.hideSkillInfo()
        this.initView()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://battle_be_n
    onClickBattle(event: cc.Event.EventTouch, data: string) {
        this.onBattle()
    }

    // path://back_be
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.callback && this.callback()
        this.saveTeam()
        this.close()
    }

    // path://top/preview_be
    onClickPreview(event: cc.Event.EventTouch, data: string) {
        this.onPreviewBoss()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    private saveTeam() {
        let uids = this.selectedRoles.filter(m => !!m).map(r => r.uid)
        return this.team?.setRolesBySever(uids)
    }

    @util.addLock
    private async onBattle() {
        let selectedRoles = this.selectedRoles.filter(m => !!m)
        if (selectedRoles.length <= 0) {
            viewHelper.showAlert("chapterEmbattle_tips_2")
            return
        }

        if (this.onStartBattle) {
            let succ = await this.onStartBattle()
            if (!succ) return
        }

        await this.saveTeam()
        if (!cc.isValid(this)) return

        let succ = await gameHelper.blackHole.start(this.level, selectedRoles.map(r => r.id))
        if (!succ) return

        viewHelper.showPnl("blackHole/BlackHolePnl")

        this.callback && this.callback()
        this.waitClose()
        // this.close()
    }

    private async waitClose() {
        this.battleNode_.Component(cc.Button).interactable = false
        await eventCenter.wait(CoreEventType.PNL_ENTER)
        this.close()
    }
    private registerRoleLong(it: cc.Node, data: any) {
        let drag = it.Child('drag')
        drag.off(DragLongEvent.LP_START)
        drag.on(DragLongEvent.LP_START, () => { this.showSkillInfo(it, data) }, this)
        drag.off(DragLongEvent.LP_CANCEL)
        drag.on(DragLongEvent.LP_CANCEL, this.hideSkillInfo, this)

    }
    private registerRoleDown(it: cc.Node, data: any) {
        this.registerRoleLong(it, data)
        let drag = it.Child('drag')
        drag.off(DragLongEvent.DL_CLICK)
        drag.on(DragLongEvent.DL_CLICK, () => {
            this.onSelectRole(data)
            eventCenter.emit(EventType.GUIDE_INTO_TEAM)
        }, this)
        drag.off(DragLongEvent.DRAG_MOVE)
        drag.on(DragLongEvent.DRAG_MOVE, () => { this.dragMoveDown(it, drag) }, this)
        drag.off(DragLongEvent.DRAG_END)
        drag.on(DragLongEvent.DRAG_END, () => {
            let swapNode = this.checkSwap(it, drag)
            if (swapNode) {
                let index = this.battleRolesNode_.children.findIndex(r => r == swapNode)
                this.onSelectRole(data, index)
            }
        }, this)
    }

    private registerRoleUp(it: cc.Node, data: any) {
        this.registerRoleLong(it, data)
        let drag = it.Child('drag')
        drag.off(DragLongEvent.DL_CLICK)
        drag.on(DragLongEvent.DL_CLICK, () => { this.onUnSelectRole(data) }, this)
        drag.off(DragLongEvent.DRAG_START)
        drag.on(DragLongEvent.DRAG_START, () => {
        }, this)
        drag.off(DragLongEvent.DRAG_MOVE)
        drag.on(DragLongEvent.DRAG_MOVE, () => {
            let { type } = this.dragMoveUp(it, drag)
        }, this)
        drag.off(DragLongEvent.DRAG_END)
        drag.on(DragLongEvent.DRAG_END, () => {
            let { type, node } = this.checkUpSwap(it, drag)
            if (type == 1) this.onUnSelectRole(data)
            else if (type == 2) this.swapRole(it, node)
        }, this)
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        this.battleNode_.active = !this.isPreview
        this.initRoleSelectView()
        this.initBattleRoles()
        this.initBg()

        this.descLbl_.setLocaleKey("blackHole_guiText_17", this.cfg.people)
    }

    private async initBg() {
        let winHeight = cc.winSize.height
        let scaleBg = 1
        if (!this.isCommBg) {
            scaleBg = viewHelper.getPlanetWindScale()
        }
        else {
            scaleBg = this.bgNode_.children[0].scale
        }
        let upNode = this.Child('up')
        let minY = (upNode.y + 1242 * 0.5) - winHeight * 0.5 //upNode的最低位置
        let targetY = -280 * scaleBg //upNode要变到这个位置才能和背景对应上
        if (this.battlePos && !this.isCommBg) {
            targetY = this.battlePos.y
        }
        if (minY > targetY) { //如果需要对应的位置比最低位置还低，把背景往上挪
            upNode.y = minY
            this.bgNode_.y = minY - targetY
        }
        else {
            upNode.y = targetY
        }

        if (!this.isCommBg) { //走截图的方式
            await ut.waitNextFrame(1, this) //等只剩背景再截图
            this.bgNode_.Component(cc.Sprite).spriteFrame = viewHelper.getSpriteFrameByCapture(viewHelper.getPlanetCamera())
        }
    }

    private setUIAtkHp(role: cc.Node, hp, atk) {
        let ui = role.Child('BattleUIAtkHp') || role.Child("ui/BattleUIAtkHp")
        ui.Child("hp/count", cc.Label).string = hp
        ui.Child("hp/bar", cc.Sprite).fillRange = 1
        ui.Child("attack/count", cc.Label).string = atk
    }
    //下面待选择的乘客视图
    private initRoleSelectView() {
        this.setDownBgSelect(false)
        let roles = this.getSelectRoles()
        let length = roles.length
        if (length == 1) {
            // 为了ignoreSelectRoleNode能生效 需要立即把item创建出来
            this.selectRolesSv_.Items(roles, (it, data) => {
                this.setOneRole(it, data)
            })
        } else {
            this.selectRolesSv_.List(length, (it, i) => {
                let data = roles[i]
                it.Data = data
                this.setOneRole(it, data)
            })
        }
    }
    private setOneRole(it: cc.Node, data: PassengerModel | BattleRole) {
        this.setUIRole(it.Child('drag/role'), data, false)
        this.registerRoleDown(it, data)
    }
    private setUIRole(role: cc.Node, data: PassengerModel | BattleRole, playOnLoad: boolean) {
        let body = role.Child('body')
        let starLv = data.getStarLvWithQuality()
        let level = data.getLevel()
        let quality = data.quality
        let lvs = ut.newArray(starLv, 1)
        role.Child('starLv').Items(lvs, (it, data) => {
            it.active = true
        })
        this.setUILv(role, level)
        role.Child('lv', cc.MultiColor).setColor(quality - 1)
        this.setUISk(body.Child('sp'), data.id, playOnLoad)
        this.setUIAtkHp(role, data.getHp(), data.getAttack())
        let battleTypeNode = role.Child("battle_type")
        resHelper.loadBattleTypeIcon(data.battleType, battleTypeNode.Child('icon'), this.getTag())
        let animalTypeNode = role.Child("animal_type")
        resHelper.loadAnimalTypeIcon(data.animalType, animalTypeNode.Child('icon'), this.getTag())
        return body
    }

    private setUILv(role: cc.Node, val: number) {
        role.Child('lv', cc.Label).setLocaleKey('common_guiText_11', val)
    }
    private setUISk(skNode: cc.Node, id: number, playOnLoad: boolean) {
        let dic = skNode.Data
        if (dic && dic.id == id && dic.bol == playOnLoad) {
            return
        }
        skNode.Data = { id, bol: playOnLoad }
        let sk = skNode.Component(sp.Skeleton)
        resHelper.loadOwnRoleSp(id, sk, this.getTag(), playOnLoad).then(() => {
            if (playOnLoad) {
                uiHelper.setRoleDragSize(skNode, 360, 67)
            } else {
                uiHelper.setRoleDragSize(skNode, 300, 22)
            }
        })
    }

    //上阵的乘客视图
    private initBattleRoles() {
        let startX = -110, startY = -76
        let offseX = -240, offsetY = -90
        // let count = Math.floor((10 - this.cfg.people) / 2)
        // startX += count * offseX

        this.battleRolesNode_.Items(this.selectedRoles, (it, data, i) => {
            let posX = startX + i * offseX
            let posY = startY + (i % 2 ? 0 : offsetY)
            it.setPosition(posX, posY)
            let drag = it.Child('drag')
            let bol = !!data
            drag.active = bol
            this.setBattleRoleSelect(it, false)
            if (bol) {
                this.setUIRole(drag.Child('role'), data, true)
                this.registerRoleUp(it, data)
            }
        })
    }
    private setBattleRoleSelect(it: cc.Node, bol: boolean) {
        it.Child('drag/role').setDark(bol ? 0.5 : 0, true)
    }
    private setDownBgSelect(bol: boolean) {
        this.bottomNode_.Child('zdbz_bg1', MultiMaterial).setMaterial(bol)
        this.bottomNode_.Child('zdbz_bg2', MultiMaterial).setMaterial(bol)
    }

    private onSelectRole(role: PassengerModel, giveIndex?: number) {
        //让浪客喵上阵的提示，玩家操作浪客喵上阵后，滑出
        let index = giveIndex != null ? giveIndex : this.selectedRoles.findIndex(r => r == null)
        this.selectedRoles[index] = role
        this.updateRoles()
    }
    private onUnSelectRole(role: PassengerModel) {
        let index = this.selectedRoles.findIndex(r => r == role)
        this.selectedRoles[index] = null
        this.updateRoles()
    }
    private updateRoles() {
        this.initRoleSelectView()
        this.initBattleRoles()
    }

    private dragMoveDown(it: cc.Node, drag: cc.Node) {
        this.hideSkillInfo()
        let swapNode = this.checkSwap(it, drag)
        for (let item of this.battleRolesNode_.children) {
            this.setBattleRoleSelect(item, item == swapNode)
        }
        return swapNode
    }
    private dragMoveUp(it: cc.Node, drag: cc.Node) {
        this.hideSkillInfo()
        let info = this.checkUpSwap(it, drag)
        let { type, node } = info
        this.setDownBgSelect(type == 1)
        for (let item of this.battleRolesNode_.children) {
            this.setBattleRoleSelect(item, type == 2 && item == node)
        }
        return info
    }
    private checkUpSwap(curNode: cc.Node, body: cc.Node) {
        let type = 0
        let node = this.checkDownBg(body)
        if (node) {
            type = 1
        } else {
            node = this.checkSwap(curNode, body)
            if (node) type = 2
        }
        return { type, node }
    }
    private checkDownBg(body: cc.Node) {
        let bg = this.bottomNode_.Child('zdbz_bg1')
        let pos = ut.convertToNodeAR(body, bg)
        let checkY = pos.y < bg.height * 0.6
        if (checkY) return bg
    }
    private checkSwap(curNode: cc.Node, body: cc.Node) {
        let spacX = 240
        for (let node of this.battleRolesNode_.children) {
            if (node == curNode) continue
            let pos = ut.convertToNodeAR(body, node)
            let width = (node.width + spacX) * 0.5
            let checkX = -width <= pos.x && pos.x <= width
            let checkY = pos.y > -body.height * 0.5
            let isIntersects = checkX && checkY
            if (isIntersects) {
                return node
            }
        }
    }
    private swapRole(roleNode1: cc.Node, roleNode2: cc.Node) {
        let children = this.battleRolesNode_.children
        let index1 = children.findIndex(r => r == roleNode1)
        let index2 = children.findIndex(r => r == roleNode2)
        let ary = this.selectedRoles
        let t = ary[index1]
        ary[index1] = ary[index2]
        ary[index2] = t
        this.initBattleRoles()
    }

    private showSkillInfo(it: cc.Node, data: PassengerModel | BattleRole) {
        this.tipsNode_.active = true
        let info = { id: data.id, skills: data.getSkills() }
        viewHelper.showBubble("SkillBubble", it, info)
    }

    private hideSkillInfo() {
        viewHelper.hideBubble()
        this.tipsNode_.active = false
    }

    private getDicSelected() {
        let dic = new Set()
        this.selectedRoles.forEach(r => { if (r) dic.add(r) })
        return dic
    }
    private getSelectRoles() {
        let dic = this.getDicSelected()
        let ary = this.passengers
        let roles = ary.filter(r => !dic.has(r))
        roles.sort(gameHelper.battlePassengersCmp)
        return roles
    }

    private async onPreviewBoss() {
        viewHelper.showPnl("blackHole/BlackHolePreview")
    }
}
