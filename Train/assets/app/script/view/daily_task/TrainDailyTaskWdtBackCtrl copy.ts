import { PASSENGER_QUALITY_NAME } from "../../common/constant/Constant";
import { WantedConditionType, WantedState } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { timeHelper } from "../../common/helper/TimeHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import { Wanted } from "../../model/wanted/WantedModel";
import FrameIconNum from "../prefab/FrameIconNum";

const { ccclass } = cc._decorator;

const CONDITION_INDEXES = ["quality", "animal", "battle"]
const progress_max = 356
const progress_min = 46

@ccclass
export default class TrainDailyTaskWdtCtrl extends mc.BaseWdtCtrl {

    //@autocode property begin
    protected tasksSv_: cc.ScrollView = null // path://tasks_sv
    protected detailNode_: cc.Node = null // path://detail_n
    protected oneKeyNode_: cc.Node = null // path://detail_n/bottom/0/one_key_be_n
    protected doNode_: cc.Node = null // path://detail_n/bottom/0/do_be_n
    protected bottomMaskNode_: cc.Node = null // path://detail_n/bottom_mask_n
    //@end

    rootNode: cc.Node = null
    rolesNode: cc.Node = null
    _selected: number = -1
    _isSelectorOpened: boolean = false
    _roles: number[] = []

    _globalReg: Function[] = []
    _selectReg: Function[] = []
    _selectHeadItem: cc.Node = null

    set selectHeadItem(value: cc.Node) {
        if (this._selectHeadItem) {
            this._selectHeadItem.Child("state").Swih("default")
        }
        this._selectHeadItem = value
        if (!this._selectHeadItem) return
        this._selectHeadItem.Child("state").Swih("select")
    }

    public listenEventMaps() {
        return [
            { [EventType.WANTED_UPDATE]: this.onWantedUpdate },
            { [EventType.DAILY_REFRESH]: this.init }
        ]
    }

    public async onCreate() {
        gameHelper.wanted.pnlOpenRed = true
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://detail_n/bottom/0/one_key_be_n
    onClickOneKey(event: cc.Event.EventTouch, data: string) {
        const wanted = gameHelper.wanted.getWanted(this._selected)
        const roles = gameHelper.wanted.oneKeyPlan(wanted)

        if (!roles || roles.length == 0) {
            // 完全没有找到满足条件的乘客组合 一个都没有！
            viewHelper.showAlert("wanted_tips_3")
            return
        }
        const realLen = roles.length
        const canUse = this.getCanUseRoles(wanted, roles)
        if (canUse.length) {
            for (let i = 0; i < wanted.getPeople() - realLen; i++) {
                roles.push(canUse.pop().id)
            }
        }

        // 更新选中的乘客
        this._roles = roles
        // 更新界面
        this.updateDetailTop()
        this.updateDetailMiddle()
        // 显示提示信息
        // viewHelper.showAlert("wanted_tips_4", { params: [realLen] })
        // this.onClickSure(event, data)
        this.showRoleSelector()
    }

    // path://detail_n/bottom/0/do_be_n
    onClickDo(event: cc.Event.EventTouch, data: string) {
        if (!this._isSelectorOpened) {
            this.showRoleSelector()
            if (!this.selectHeadItem) {
                const list = this.detailNode_.Child("middle/list")
                const node = list.children.find(node => !node.Data)
                if (node) {
                    this.selectHeadItem = node
                }
            }
            return
        }
        this.hideRoleSelector()
    }

    // path://detail_n/bottom/2/get_be
    onClickGet(event: cc.Event.EventTouch, data: string) {
        this.claimReward(this._selected)
    }

    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    update() {
        let wantedNodes = this.tasksSv_.content.children
        for (let node of wantedNodes) {
            let data = node.Data
            if (node.activeInHierarchy) {
                if (data?.getState() == WantedState.START) {
                }
            }
        }
        for (const reg of this._globalReg) {
            reg && reg()
        }
        for (const reg of this._selectReg) {
            reg && reg()
        }
        // this.resetTimeNode_.Child("lbl").setLocaleKey("wanted_guiText_5", timeHelper.getTimeText(Math.floor(gameHelper.world.getNextDaySurpluTime() / ut.Time.Second)))
    }

    public async init(rootNode: cc.Node, rolesNode: cc.Node) {
        rootNode && (this.rootNode = rootNode)
        rolesNode && (this.rolesNode = rolesNode)

        await gameHelper.wanted.checkSync()
        let wanteds = gameHelper.wanted.getWanteds()
        this.tasksSv_.Items(wanteds, (it, data, index: number) => this.updateWanted(it, data, index))
        this.setSelected(0)
        this.bottomMaskNode_.active = false
        this.rolesNode.active = false
        //this.rolesNode.Child("mask").off("click")
        // this.rolesNode.Child("mask").on("click", () => this.hideRoleSelector())

        this.rolesNode.Child("sure").off("click")
        this.rolesNode.Child("sure").on("click", () => this.onClickSure(null))
    }


    updateWanted(it: cc.Node, data: Wanted, index: number) {
        it.Data = data

        it.Child("selected").active = index == this._selected
        const body = it.Child("body")
        body.Child("bg", cc.MultiFrame).setFrame(data.type)

        body.Child('name').setLocaleKey(data.getName())
        let levelNodes = body.Child("level")
        levelNodes.Items(ut.newArray(data.getLv()))

        resHelper.loadIcon(body.Child("icon"), 'passenger/icon_circle', `character_${data.publisher}_icon_circle`, this.getTag())

        it.off("click")
        it.on("click", () => this.setSelected(data.index))
        const state = data.getState()
        const node = body.Child("state").Swih(state)[0]
        this.setNodeOpacity(body, 255)
        switch (data.getState()) {
            case WantedState.DEFAULT:
                break
            case WantedState.START:
                this._globalReg.push(() => {
                    if (!cc.isValid(node)) return
                    node.Child("time", cc.Label).string = timeHelper.getTimeText(data.getSurplusTime() / ut.Time.Second)
                })
                break
            case WantedState.END:
                break
            case WantedState.COMPLETE:
                this.setNodeOpacity(body, 102)
                break
        }
    }
    setNodeOpacity(node: cc.Node, opacity: number) {
        node.children.forEach(it => it.name != "state" && (it.opacity = opacity))
    }
    async claimReward(index: number) {
        await gameHelper.wanted.claimWantedReward(index)
    }
    onWantedUpdate(wanted: Wanted) {
        let wantedNodes = this.tasksSv_.content.children
        for (let i = 0; i < wantedNodes.length; i++) {
            let node = wantedNodes[i]
            if (node.activeInHierarchy && node.Data?.index == wanted.index) {
                this.updateWanted(node, wanted, i)
                if (this._selected == wanted.index) {
                    this.updateDetail()
                    this.updateRoleSv()
                }
            }
        }
    }

    setSelected(index: number) {
        if (index == this._selected) return
        const prev = gameHelper.wanted.getWanted(this._selected)
        const prevNode = this.tasksSv_.content.children.find(it => it && it.Data && it.Data == prev)
        if (prevNode) {
            prevNode.Child("selected").active = false
        }
        const next = gameHelper.wanted.getWanted(index)
        const nextNode = this.tasksSv_.content.children.find(it => it && it.Data && it.Data == next)
        if (nextNode) {
            nextNode.Child("selected").active = true
        }
        this._selectReg.length = 0
        this._selected = index
        this._roles = next.getRoles().map(r => r)
        this.updateDetail()
        if (next.getState() != WantedState.DEFAULT) {
            return void this.hideRoleSelector()
        }
        this.updateRoleSv()
    }

    updateDetail() {
        this.selectHeadItem = null
        this.updateDetailTop()
        this.updateDetailMiddle()
        this.updateDetailBottom()
        // 奖励
        const wanted = gameHelper.wanted.getWanted(this._selected)
        const rewardsNode = this.detailNode_.Child("rewards")
        rewardsNode.Items(wanted.getRewards(), (it, data) => {
            it.Component(FrameIconNum).init(data, this.getTag())
            uiHelper.regClickPropBubble(it, data)
            it.scale = .8
        })
    }

    updateDetailTop() {
        const wanted = gameHelper.wanted.getWanted(this._selected)
        const matchResult = wanted.calculateConditions(this._roles)

        const top = this.detailNode_.Child("top")

        resHelper.loadTmpIcon(`work/${wanted.bg}`, top.Child("bg/1", cc.Sprite), this.getTag())
        resHelper.loadTmpIcon(`work/${wanted.bgArg}`, top.Child("bg/2", cc.Sprite), this.getTag())
        resHelper.loadTmpIcon(`passenger/profile/tiezhi_character_icon_${wanted.publisher}`, top.Child("role", cc.Sprite), this.getTag())

        const req = top.Child("req/list")
        req.Items(wanted.getConditions(), top.Child("req/prefab"), (it, data) => {
            const real = CONDITION_INDEXES[data.type - 1]
            it.Swih(real)
            it.Child(real + "/icon", cc.MultiFrame).setFrame(data.value - 1)
            const btn = it.Child(real)
            btn.off("click")
            btn.on("click", () => {
                let info = ""
                switch (data.type) {
                    case WantedConditionType.QUALITY:
                        info = assetsMgr.lang("wanted_req_bubble_quality", assetsMgr.lang(PASSENGER_QUALITY_NAME[data.value]))
                        break;
                    case WantedConditionType.ANIMAL_TYPE:
                        info = assetsMgr.lang(cfgHelper.getAnimalTypeName(data.value))
                        break
                    case WantedConditionType.BATTLE_TYPE:
                        info = assetsMgr.lang(cfgHelper.getBattleTypeName(data.value))
                        break
                }
                viewHelper.showBubble('CommonBubble', btn, info)
            })
            const lbl = it.Child(real + "/lbl")
            let matchNum = matchResult[data.type]?.[data.value] || 0
            lbl.Component(cc.MultiColor).setColor(matchNum >= data.num)
            lbl.Component(cc.Label).string = `${matchNum}/${data.num}`
        })
    }

    updateDetailMiddle() {
        const wanted = gameHelper.wanted.getWanted(this._selected)
        const middle = this.detailNode_.Child("middle")
        middle.Child("time/lbl").setLocaleUpdate(() => {
            return timeHelper.getTimeText(wanted.cfg.costTime * ut.Time.Minute / ut.Time.Second)
        })
        middle.Child("list").Items(this._roles, (it, data) => {
            it.off("click")
            it.on("click", () => {
                if (wanted.getState() != WantedState.DEFAULT) return
                this.selectHeadItem = it
                if (data == null) {
                    if (!this._isSelectorOpened) {
                        return void this.showRoleSelector()
                    }
                }

                const next = this._roles.findIndex(r => r == data)
                if (data && next != -1) {
                    this._roles[next] = null
                    this.updateRoleSv()
                    this.updateDetailTop()
                    this.updateDetailMiddle()
                }
            })
            if (data == null) {
                return void it.Swih("state")
            }
            const role = gameHelper.passenger.getPassenger(data)
            const roleNode = it.Swih("role")[0]
            roleNode.Child("quality", cc.MultiFrame).setFrame(role.quality - 1)
            roleNode.Child("animal/icon", cc.MultiFrame).setFrame(role.animalType - 1)
            roleNode.Child("battle/icon", cc.MultiFrame).setFrame(role.battleType - 1)
            resHelper.loadIcon(roleNode.Child("4"), 'passenger/icon_circle', `character_${role.id}_icon_circle`, this.getTag())
        })
        if (wanted.getState() == WantedState.DEFAULT && this.selectHeadItem == null && this._isSelectorOpened) {
            const first = middle.Child("list").children[0]
            if (!first.Data) {
                this.selectHeadItem = first
            }
        }
    }

    updateDetailBottom() {
        const wanted = gameHelper.wanted.getWanted(this._selected)
        const bottom = this.detailNode_.Child("bottom")
        const state = wanted.getState()
        const node = bottom.Swih(state)[0]
        switch (state) {
            case WantedState.DEFAULT:
                break
            case WantedState.START:
                this._selectReg.push(() => {
                    if (!cc.isValid(node)) return
                    node.Child("lbl").setLocaleKey("common_guiText_2", timeHelper.getTimeText(Math.floor(wanted.getSurplusTime() / ut.Time.Second)))
                    node.Child("progress/1").width = progress_min + (progress_max - progress_min) * (1 - wanted.getSurplusTime() / (wanted.cfg.costTime * ut.Time.Minute))
                })
                break
            case WantedState.END:
                break
            case WantedState.COMPLETE:
                break
        }
    }

    showRoleSelector() {
        const wanted = gameHelper.wanted.getWanted(this._selected)
        if (wanted.getState() != WantedState.DEFAULT) return
        if (this._isSelectorOpened) return
        this._isSelectorOpened = true

        cc.Tween.stopAllByTarget(this.rolesNode)
        this.rolesNode.active = true
        this.updateRoleSv()
        cc.tween(this.rolesNode).by(.3, { y: 630 })
            .call(() => this.rolesNode.Child("mask").active = this.bottomMaskNode_.active = true)
            .start()
        cc.tween(this.rootNode).by(.3, { y: 40 }).start()
    }

    hideRoleSelector() {
        if (!this._isSelectorOpened) return false
        this._isSelectorOpened = false
        this.selectHeadItem = null
        this.rolesNode.Child("mask").active = false
        cc.Tween.stopAllByTarget(this.rolesNode)
        cc.tween(this.rolesNode).by(.3, { y: -630 })
            .call(() => this.rolesNode.active = this.bottomMaskNode_.active = false)
            .start()
        cc.tween(this.rootNode).by(.3, { y: -40 }).start()
        return true
    }

    updateRoleSv() {
        if (!this._isSelectorOpened) return
        const wanted = gameHelper.wanted.getWanted(this._selected)
        const roles = this.getCanUseRoles(null)
        // 满足条件的优先前面
        // 但是如果任务条件已经全部满足，但是还差人数，就反过来
        const pass = this.isConditionPass()
        const ary = this._roles.filter(r => r != null)

        const numMap = {}
        roles.sort((a, b) => {
            let matchNumA = numMap[a.id]
            if (!matchNumA) {
                matchNumA = wanted.getConditionMatchNum(a)
                numMap[a.id] = matchNumA
            }
            let matchNumB = numMap[b.id]
            if (!matchNumB) {
                matchNumB = wanted.getConditionMatchNum(b)
                numMap[b.id] = matchNumB
            }
            if (pass && wanted.getPeople() != ary.length) {
                return matchNumA - matchNumB
            }
            return matchNumB - matchNumA
        })

        this.rolesNode.Child("list", cc.ScrollView).List(roles.length, (it, index) => {
            const role = roles[index]
            if (it.Data && it.Data == role) return
            it.Data = role
            it.Child("quality", cc.MultiFrame).setFrame(role.quality - 1)
            it.Child("animal/icon", cc.MultiFrame).setFrame(role.animalType - 1)
            it.Child("battle/icon", cc.MultiFrame).setFrame(role.battleType - 1)

            resHelper.loadOwnRoleSp(role.id, it.Child('btn/body', sp.Skeleton), this.getTag(), true).then(() => {
                if (!cc.isValid(it)) return
                let body = it.Child('btn/body')
                uiHelper.setRoleButtonSize(it, body, { scale: body.scaleY, widthLimit: it.width, btnHeightDown: 13 })
            })

            const darkVal = !!numMap[role.id] ? 0 : .4
            it.Child("btn/body").setDark(darkVal)
            it.Child("quality").setDark(darkVal)
            it.Child("animal").setDark(darkVal, true)
            it.Child("battle").setDark(darkVal, true)
            it.Child("btn").off("click")
            it.Child("btn").on("click", () => {
                const list = this.detailNode_.Child("middle/list")
                const next = list.children.findIndex(node => node == this._selectHeadItem)
                if (next == -1) {
                    return void viewHelper.showAlert("wanted_tips_1")
                }
                this._roles[next] = role.id
                const firstEmpty = this._roles.findIndex(id => id == null)
                if (firstEmpty != -1) {
                    this.selectHeadItem = list.children[firstEmpty]
                }
                this.updateRoleSv()
                this.updateDetailTop()
                this.updateDetailMiddle()
            })
        })
    }

    getCanUseRoles(wanted: Wanted, exceptRoles: number[] = []) {
        const wantedAry = gameHelper.wanted.getWanteds()
        const roles = gameHelper.passenger.getPassengers().filter(r => {
            if (exceptRoles.findIndex(id => id == r.id) != -1) return false
            if (this._roles.findIndex(id => id == r.id) != -1) return false
            // 安排在其他未完成的工作中
            const exists = wantedAry.find(w => w != wanted && w.getState() == WantedState.START && !!w.getRoles().find(id => id == r.id))
            if (exists) return false
            if (!wanted) return true
            const conds = wanted.getConditions()
            return !!conds.filter(cond => {
                switch (cond.type) {
                    case WantedConditionType.QUALITY:
                        return r.quality >= cond.value
                    case WantedConditionType.ANIMAL_TYPE:
                        return r.animalType == cond.value
                    case WantedConditionType.BATTLE_TYPE:
                        return r.battleType == cond.value
                }
            }).length
        })
        gameHelper.sortPassengers(roles)
        return roles
    }

    // 当前选中的任务是否满足条件  
    isConditionPass() {
        const wanted = gameHelper.wanted.getWanted(this._selected)
        const result = wanted.calculateConditions(this._roles)
        let bol = true
        const condAry = wanted.getConditions()
        for (const typeKey in result) {
            const type = result[typeKey]
            for (const valueKey in type) {
                const value = type[valueKey]
                const num = condAry.filter(c => c.type == +typeKey && c.value == +valueKey).reduce((a, b) => a + b.num, 0)
                if (value < num) {
                    bol = false
                    break
                }
            }
        }
        return bol
    }

    onClickSure(event: cc.Event.EventTouch) {
        if (!this.isConditionPass()) {
            return void viewHelper.showAlert("wanted_tips_2")
        }
        const wanted = gameHelper.wanted.getWanted(this._selected)
        const ary = this._roles.filter(r => r != null)
        if (wanted.getPeople() != ary.length) {
            return void viewHelper.showAlert("wanted_tips_5")
        }
        gameHelper.wanted.startWanted(wanted.index, ary).then(r => {
            if (r) {
                if (!cc.isValid(this)) return
                this.hideRoleSelector()
                viewHelper.showAlert("wanted_tips_4")
            }
        })
    }
}
