import { res<PERSON><PERSON><PERSON> } from "../../common/helper/ResHelper";
import { ui<PERSON>el<PERSON> } from "../../common/helper/UIHelper";
import NodeType from "../../common/event/NodeType";
import ConditionObj from "../../model/common/ConditionObj";
import { BattlePreviewPnlType } from "../../common/constant/Enums";
import FrameIconNum from "../prefab/FrameIconNum";
import { gameHelper } from "../../common/helper/GameHelper";

const { ccclass } = cc._decorator;

@ccclass
export default class BattlePreviewPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected rootNode_: cc.Node = null // path://root_n
    protected bgNode_: cc.Node = null // path://root_n/bg_n
    protected monsterNode_: cc.Node = null // path://root_n/monster_n
    protected titleLbl_: cc.Label = null // path://root_n/monster_n/title/title_l
    protected enemiesNode_: cc.Node = null // path://root_n/enemies_n
    protected descNode_: cc.Node = null // path://root_n/desc_n
    protected rewardsSv_: cc.ScrollView = null // path://root_n/rewards_sv
    protected battleNode_: cc.Node = null // path://root_n/battle_be_n
    //@end

    private callback: Function = null

    private type: BattlePreviewPnlType
    private enemies: { id: number, lv: number }[] = []
    private rewards: ConditionObj[] = []
    private descObj: { lbl: string, args: any[] }

    public listenEventMaps() {
        return [
            { [NodeType.GUIDE_BUTTON_CHALLENGE]: () => this.battleNode_ }
        ]
    }

    public async onCreate(data) {
        this.setParam({ isMask: !data.noMask })
        let pList = []
        if (data.bg) {
            pList.push(resHelper.loadIcon(this.bgNode_, "battlePreviewBg", data.bg, this.getTag()))
        }
        let iconId = data.iconId || (data.enemies?.length > 0 && data.enemies[0].id)
        if (iconId) {
            let isPassenger = gameHelper.checkPassengerById(iconId)
            let p
            if (isPassenger) {
                p = resHelper.loadRoleBigIcon(iconId, this.monsterNode_.Child("icon", cc.Sprite), this.getTag())
            }
            else {
                p = resHelper.loadMonsterBigIcon(iconId, this.monsterNode_.Child("icon"), this.getTag())
            }
            pList.push(p)
        }
        await Promise.all(pList)
    }

    public onEnter(data: any) {
        this.callback = data.callback
        this.rewards = data.rewards || []

        this.titleLbl_.setLocaleKey(data.title?.key, ...(data.title?.params || []))
        this.battleNode_.Child('lb', cc.Label).setLocaleKey(data.isPreview ? 'blackHole_buttonName_2' : 'chapterInformation_buttonName_1')

        this.type = data.type || BattlePreviewPnlType.NORMAL

        switch (this.type) {
            case BattlePreviewPnlType.NORMAL:
                this.enemies = data.enemies || []
                this.initEnemies()
                break
            case BattlePreviewPnlType.INSTANCE:
                this.descObj = data.descObj || {}
                this.initDesc()
                break
        }
        this.initPosition()
        this.initRewards()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://close_be
    onClickClose(event: cc.Event.EventTouch, data: string) {
        this.close()
        this.callback && (this.callback(false))
    }

    // path://root_n/battle_be_n
    onClickBattle(event: cc.Event.EventTouch, data: string) {
        this.close()
        this.callback && (this.callback(true))
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------
    private initPosition() {
        if (this.type == BattlePreviewPnlType.NORMAL) {
            this.rootNode_.Child("decorate").y = -130
            this.rewardsSv_.node.y = -196
            this.battleNode_.y = -370
        } else if (this.type == BattlePreviewPnlType.INSTANCE) {
            this.rootNode_.Child("decorate").y = -83
            this.rewardsSv_.node.y = -149
            this.battleNode_.y = -380
        }
    }

    private initDesc() {
        this.enemiesNode_.active = false
        this.descNode_.active = true
        const lbl = this.descNode_.Child("lbl", cc.Label)
        lbl.setLocaleKey(this.descObj.lbl, ...this.descObj.args)
    }

    private initEnemies() {
        this.enemiesNode_.active = true
        this.descNode_.active = false
        this.enemiesNode_.Items(this.enemies, (it, data) => {
            let id = data.id
            let isPassenger = gameHelper.checkPassengerById(id)
            if (isPassenger) {
                resHelper.loadRoleCircleIcon(id, it.Child("icon/val", cc.Sprite), this.getTag())
            }
            else {
                resHelper.loadMonsterCircleIcon(id, it.Child("icon"), this.getTag())
            }
            it.Child('lv', cc.Label).setLocaleKey('common_guiText_11', data.lv)
        })
    }

    private initRewards() {
        this.rewardsSv_.enabled = this.rewards.length > 5
        this.rewardsSv_.Items(this.rewards, (it, data) => {
            it.Component(FrameIconNum).init(data, this.getTag())
            uiHelper.regClickPropBubble(it, data)
        })
    }
}
