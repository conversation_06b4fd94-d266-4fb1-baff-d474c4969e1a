import { viewHelper } from "../../common/helper/ViewHelper";
import Battle from "../../model/battle/Battle";
import { BattleRoleType } from "../../model/battle/BattleEnum";
import BattleRole from "../../model/battle/BattleRole";
import BattleTest from "../../model/battle/BattleTest";
import <PERSON> from "../../model/battle/Monster";
import PlanetCheckPointModel from "../../model/planet/PlanetCheckPointModel";
import BattleReplayCmpt from "../cmpt/battle/BattleReplayCmpt";
import { gameHelper } from "../../common/helper/GameHelper";
import EventType from "../../common/event/EventType";
import NodeType from "../../common/event/NodeType";
import { BattleLevelType, LangCfgName, LongPress, OreMakePnlType, RuleType, TalentAttrType, UIFunctionType } from "../../common/constant/Enums";
import { unlockHelper } from "../../common/helper/UnlockHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { animHelper } from "../../common/helper/AnimHelper";
import TimeScaleCmpt from "../cmpt/common/TimeScaleCmpt";
import PlanetTitleCmpt from "../cmpt/planet/PlanetTitleCmpt";
import { InstanceLevelCfg } from "../../common/constant/DataType";
import { uiHelper } from "../../common/helper/UIHelper";
import { localConfig } from "../../common/LocalConfig";
import NotEvent from "../../common/event/NotEvent";
import { util } from "../../../core/utils/Utils";
import { taHelper } from "../../common/helper/TaHelper";
import PassengerModel from "../../model/passenger/PassengerModel";
import { TaEvent } from "../../common/event/TaEvent";

const { ccclass } = cc._decorator;

@ccclass
export default class BattlePnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected battleNode_: cc.Node = null // path://battle_n
    protected bgNode_: cc.Node = null // path://battle_n/bg_n
    protected uiNode_: cc.Node = null // path://ui_n
    protected nextRoundNode_: cc.Node = null // path://ui_n/nextRound_n
    protected nextRoundBtnNode_: cc.Node = null // path://ui_n/nextRound_n/nextRoundBtn_be_n
    protected titleNode_: cc.Node = null // path://ui_n/title_n
    protected autoSwitchNode_: cc.Node = null // path://ui_n/autoSwitch_be_n
    protected autoNode_: cc.Node = null // path://ui_n/autoSwitch_be_n/auto_n
    protected speedUpNode_: cc.Node = null // path://ui_n/speedUp_be_n
    protected stopNode_: cc.Node = null // path://ui_n/stop_be_n
    protected guideNode_: cc.Node = null // path://ui_n/guide_be_n
    protected skipNode_: cc.Node = null // path://ui_n/skip_be_n
    protected weatherNode_: cc.Node = null // path://ui_n/weather_n
    protected descNode_: cc.Node = null // path://ui_n/desc_n
    protected techNode_: cc.Node = null // path://ui_n/tech_be_n
    protected roundsLbl_: cc.Label = null // path://ui_n/rounds_l
    protected exitNode_: cc.Node = null // path://exit_n
    protected restartNode_: cc.Node = null // path://exit_n/root/restart_be_n
    //@end

    private battlePos: cc.Vec2 = null;
    private passengers: BattleRole[] = []

    private battleReplayCmpt: BattleReplayCmpt = null

    private callback: Function = null

    private checkPoint: PlanetCheckPointModel = null

    private isShowNextBtn: boolean = false

    protected waitTalkPromise: Promise<any> = null

    private isPause: boolean = false

    private monsters: (Monster | BattleRole)[] = []

    private isCommBg: boolean = true

    private battleResult: { isWin: boolean, roles: BattleRole[] } = null

    private onWin: Function = null

    private title: string = null
    private progress: string = null
    private instanceData: InstanceLevelCfg = null
    private data

    private isTransport:boolean = false

    private buff: BattleRole = null
    private desc: string = ""

    private levelType: BattleLevelType = BattleLevelType.NONE
    private levelId: string = ""

    public listenEventMaps() {
        return [
            { [EventType.GUIDE_ON_COLLIDE]: this.onCollideGuide },
            { [EventType.GUIDE_SHOW_BATTLE_GUIDE]: this.showGuideNode },
            { [EventType.GUIDE_SHOW_BATTLE_TALK]: this.showSpeechBubble },
            { [EventType.BATTLE_SKIP]: this.skip },
            { [EventType.ON_ROUND_START]: this.updateRound },
            // { [NotEvent.ON_HIDE_BUBBLE]: () => this.battleReplayCmpt.resume() },

            { [NodeType.GUIDE_BUTTON_START_ROUND]: () => this.nextRoundBtnNode_ },
        ]
    }

    public async onCreate(data) {
        this.setParam({ isAct: false, isMask: false })

        this.checkPoint = data.checkPoint
        this.passengers = (data.battleRoles || []).filter(p => !!p)
        this.monsters = data.monsters || this.checkPoint?.monsters
        this.battlePos = data.battlePos
        this.onWin = data.onWin
        this.title = data.title?.name
        this.progress = data.title?.progress
        this.instanceData = data?.instanceData
        this.buff = data.buff
        this.data = data
        this.desc = data.desc
        this.isTransport = data.isTransport
        if (data.levelType) {
            this.levelType = data.levelType
        }
        this.levelId = data.levelId || ""
        
        let pList = []
        if (data.battleBg) {
            pList.push(
                resHelper.loadPrefabByUrl(data.battleBg, this.bgNode_, this.getTag())
            )
        }
        else if (this.checkPoint?.battleScene?.mapId) {
            pList.push(
                resHelper.loadPlanetBattleBg(this.checkPoint.planet.getId(), this.bgNode_, this.getTag(), this.checkPoint?.battleScene?.mapId)
            )
        }
        else {
            this.isCommBg = false
        }
    }

    public onEnter(data: any = {}) {
        this.callback = data.callback
        let node = this.battleNode_.Child("BattleReplay")
        node.active = true
        this.battleReplayCmpt = node.Component(BattleReplayCmpt)
        this.battleNode_.Component(cc.Widget).updateAlignment()

        this.battleResult = null

        this.initReplayCmpt()
        this.initSpeedUpBtn()
        this.initNextBtn()
        this.setAutoSwitch(gameHelper.battle.isAuto)
        this.exitNode_.active = false
        this.initGuide()
        this.initBg()
        this.initSkip(data.skip)
        this.onChangeMap()
        this.initTech()
        this.updateRound()

        this.restartNode_.active = !data.noAgain

        if (data.debug) {
            this.test()
        }
        else {
            this.replay()
        }
    }

    public onRemove() {

    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://ui_n/autoSwitch_be_n
    onClickAutoSwitch(event: cc.Event.EventTouch, data: string) {
        let isAuto = !gameHelper.battle.isAuto
        gameHelper.battle.isAuto = isAuto

        this.battleReplayCmpt.setAuto(isAuto)
        this.setAutoSwitch(isAuto)
    }

    // path://ui_n/speedUp_be_n
    onClickSpeedUp(event: cc.Event.EventTouch, data: string) {
        let speed = gameHelper.battle.speed
        if (speed == 1) speed = 2
        else speed = 1
        gameHelper.battle.setSpeed(speed)
        this.updateSpeed(speed)
        viewHelper.showAlert(`chapterBattle_tips_${speed}`, { cd: 0.01 })
    }

    // path://ui_n/stop_be_n
    onClickStop(event: cc.Event.EventTouch, data: string) {
        this.showExitDlg()
    }

    // path://exit_n/root/exit_be
    onClickExit(event: cc.Event.EventTouch, data: string) {
        this.close()
        this.callback && this.callback(null)
    }

    // path://exit_n/root/continue_be
    onClickContinue(event: cc.Event.EventTouch, data: string) {
        this.hideExitDlg()
    }

    // path://ui_n/nextRound_n/nextRoundBtn_be_n
    onClickNextRoundBtn(event: cc.Event.EventTouch, data: string) {
        this.nextStep()
    }

    // path://ui_n/guide_be_n
    onClickGuide(event: cc.Event.EventTouch, data: string) {
        this.hideGuideNode()
    }

    // path://ui_n/skip_be_n
    onClickSkip(event: cc.Event.EventTouch, data: string) {
        this.skip()
    }

    // path://ui_n/tech_be_n
    onClickTech(event: cc.Event.EventTouch, data: string) {
        let key = "blackHole/BlackHoleEquip"
        if (this.techNode_.active) {
            this.battleReplayCmpt.stop()
            viewHelper.showPnl(key)
            viewHelper.waitCloseUI(key).then(() => {
                if (!cc.isValid(this.battleReplayCmpt)) return
                this.battleReplayCmpt.resume()
            })
        }
    }

    // path://exit_n/root/restart_be_n
    onClickRestart(event: cc.Event.EventTouch, data: string) {
        this.restart()
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    // ----------------------------------------- custom function ----------------------------------------------------

    private initReplayCmpt() {
        if (this.battlePos && !this.isCommBg) {
            this.battleReplayCmpt.node.y = this.battlePos.y;
        }
        else {
            let scaleBg = 1
            if (!this.isCommBg) {
                scaleBg = viewHelper.getPlanetWindScale()
            }
            else {
                scaleBg = this.bgNode_.children[0]?.scale || 1
            }
            this.battleReplayCmpt.node.y = -280 * scaleBg
        }
        this.battleReplayCmpt.node.y *= this.bgNode_.scale
    }

    private initSpeedUpBtn() {
        let speed = gameHelper.battle.speed
        this.updateSpeed(speed)
    }

    private initNextBtn() {
        this.isShowNextBtn = false
        this.nextRoundNode_.active = false
    }

    private initGuide() {
        unlockHelper.initFunctionNode(this.getFunctionNode.bind(this))
    }

    private initBg() {

        if (!this.isCommBg) {
            this.bgNode_.Component(cc.Sprite).spriteFrame = viewHelper.getSpriteFrameByCapture(viewHelper.getPlanetCamera())
        }

        uiHelper.checkInstanceBattleBg(this.instanceData, this.bgNode_)
        uiHelper.checkInstanceBattleWeather(this.instanceData, this.weatherNode_, this.buff)

        if (this.isTransport) {
            uiHelper.updateTransportBattleBg(this.bgNode_, gameHelper.world.isNight())
         }
        this.initDesc()
    }

    private initDesc() {
        this.descNode_.active = !!this.desc
        if (this.descNode_.active) {
            this.descNode_.Child("content").setLocaleUpdate(() => {
                return this.desc
            })
        }
    }

    private initSkip(bol: boolean) {
        this.skipNode_.active = false
        if (!bol) return
        // 等可以跳过的时候 按钮才出现
        let cb = () => {
            bol = this.battleReplayCmpt.canSkip()
            if (bol) {
                this.unschedule(cb)
                this.skipNode_.active = true
            }
        }
        this.scheduleUpdate(cb)
    }

    private onChangeMap() {
        if (!!this.title && !!this.progress) {
            this.titleNode_.active = true
            this.titleNode_.Child('PlanetTitle').Component(PlanetTitleCmpt).init(this.title, this.progress, RuleType.BATTLE)
        }
        else {
            this.titleNode_.active = false
        }
    }

    private initTech() {
        this.techNode_.active = this.data.isBlackHole
    }

    private async nextStep() {
        eventCenter.emit(EventType.BATTLE_ROUND_START)
        this.nextRoundBtnNode_.Component(cc.Button).interactable = false
        let sk = this.nextRoundNode_.Component(sp.Skeleton)
        await sk.playAnimation("animation2")
        this.nextRoundBtnNode_.Component(cc.Button).interactable = true
        this.battleReplayCmpt.nextStep()
    }

    private async replay() {
        let cmpt = this.battleReplayCmpt

        let passengers = this.passengers.map(m => {
            return new BattleRole().initData(m)
        })
        let monsters = this.monsters.map(m => {
            if (m instanceof BattleRole) {
                return new BattleRole().initData(m)
            }
            else {
                return new BattleRole().initData({ id: m.id, hp: m.hp, attack: m.attack, skills: m.getSkills(), type: BattleRoleType.MONSTER, role: m })
            }
        })

        let passengers1 = passengers.map(m => new BattleRole().initData(m))
        let monsters1 = monsters.map(m => new BattleRole().initData(m))
        let battle = new Battle()
        let buffs = []
        if (this.buff) {
            buffs.push(this.buff)
        }
        let isWin = battle.init(passengers, monsters, buffs)

        if (this.data.isWin) {
            isWin = true
        }

        passengers = passengers.filter(r => r && !r.isDeath())
        monsters = monsters.filter(r => r && !r.isDeath())
        let roles = isWin ? passengers : monsters
        let result = {
            isWin, 
            roles,
            allMonsters: battle["allMonsters"],
            passengers,
            monsters,
            runAwayCnt: battle.getRecord("runAwayCnt"),
            monsterHitCnt: battle.getRecord("monsterHitCnt"),
            round: battle.getRound(),
        }
        cmpt.setResult(result)

        this.handleTa(result)

        let actions = battle.getLogs()
        if (CC_DEV) {
            console.log(actions)
        }
        for (let buff of buffs) buff.reset()
        await cmpt.init(actions, passengers1, monsters1, buffs)
        if (!cc.isValid(this)) return

        this.battleResult = result

        if (isWin && this.onWin) {
            await this.onWin(this.battleResult)
        }
        this.exitBattle()
    }

    private async test() {
        let cmpt = this.battleReplayCmpt
        let test = new BattleTest()
        let battle = test.init()
        let actions = battle.getLogs()
        console.log(actions)
        await cmpt.init(actions, test.passengers, test.monsters, test.buffs)
    }

    private setAutoSwitch(isAuto) {
        let sk = this.autoNode_.Component(sp.Skeleton)
        if (isAuto) {
            sk.playAnimation("auto", true)
        }
        else {
            sk.playAnimation("manual")
        }
    }

    private showExitDlg() {
        this.exitNode_.active = true
        this.battleReplayCmpt.stop()
    }

    private hideExitDlg() {
        this.exitNode_.active = false
        this.battleReplayCmpt.resume()
    }

    // 结束战斗
    public async exitBattle() {
        this.uiNode_.active = false
        if (this.waitTalkPromise) {
            await this.waitTalkPromise
        }
        if (!cc.isValid(this)) return
        this.callback && this.callback(this.battleResult)
    }

    private updateSpeed(speed: number) {
        let str = `x${speed}`
        if (Math.floor(speed) == speed) {
            str += '.0'
        }
        this.speedUpNode_.Child('lb', cc.Label).string = str
        // this.battleReplayCmpt.Component(TimeScaleCmpt).setScale(speed)
    }

    private showNextBtn() {
        if (this.isShowNextBtn) return
        this.isShowNextBtn = true
        this.nextRoundNode_.active = true
        let sk = this.nextRoundNode_.Component(sp.Skeleton)
        this.nextRoundBtnNode_.Component(cc.Button).interactable = false
        sk.playAnimation("animation").then(() => {
            this.nextRoundBtnNode_.Component(cc.Button).interactable = true
            eventCenter.emit(EventType.BATTLE_SHOW_START_ROUND)
        })
    }

    private hideNextBtn() {
        if (!this.isShowNextBtn) return
        this.isShowNextBtn = false
        this.nextRoundNode_.active = false
    }

    public getFunctionNode(type: UIFunctionType) {
        switch (type) {
            case UIFunctionType.BATTLE_AUTO: return this.autoSwitchNode_
            case UIFunctionType.BATTLE_SPEED: return this.speedUpNode_
            case UIFunctionType.BATTLE_BACK: return this.stopNode_
        }
    }

    update() {
        if (this.battleReplayCmpt) {
            if (this.battleReplayCmpt.canNextStep()) {
                this.showNextBtn()
            }
            else {
                this.hideNextBtn()
            }
        }
    }

    private async onCollideGuide(plotKey: string) {
        let node = this.guideNode_
        let lb = node.Child("lb", cc.Label)
        lb.node.active = true
        lb.setLocaleKey(`${LangCfgName.PLOT}.${plotKey}_1`)

        this.showGuideNode()
    }

    private async showGuideNode() {
        let mask = this.guideNode_
        mask.active = true
        let continueLb = mask.Child('continue')
        continueLb.active = false

        let timeScaleCmpt = this.battleReplayCmpt.Component(TimeScaleCmpt)
        timeScaleCmpt.setScale(0)

        await ut.wait(2, this)
        continueLb.active = true
        animHelper.playContinueBlink(continueLb)

        mask.Component(cc.Button).interactable = true
    }

    private hideGuideNode() {
        this.guideNode_.active = false
        let timeScaleCmpt = this.battleReplayCmpt.Component(TimeScaleCmpt)
        timeScaleCmpt.setScale(1)
        eventCenter.emit(EventType.GUIDE_HIDE_BATTLE_GUIDE)
    }

    private async showSpeechBubble(key: string, flip: boolean = true) {
        let cb
        this.waitTalkPromise = new Promise(r => {
            cb = r
        })

        let name = "__speech_bubble"
        let battleReplayCmpt = this.battleReplayCmpt
        let uiNode = battleReplayCmpt.tipsRoot
        let node = uiNode.Child(name)
        if (!node) {
            node = new cc.Node(name)
            node.parent = uiNode
        }
        else {
            node.active = true
        }
        let bubble: cc.Node = await resHelper.loadSpeechBubble(node, this.getTag())
        if (!cc.isValid(this) || !bubble) return

        let scaleX = flip ? -1 : 1
        let pos = battleReplayCmpt.getPosByIndex(0, BattleRoleType.PASSENGER)
        pos.addSelf(cc.v2(80, 333))
        pos = ut.convertToNodeAR(battleReplayCmpt.roleRoot, uiNode, pos)
        node.setPosition(pos)
        let lb = bubble.Child('lb', cc.Label)
        lb.setLocaleKey(key)
        lb.node.scaleX = scaleX
        bubble.scale = 0
        cc.Tween.stopAllByTarget(bubble)
        cc.tween(bubble).to(0.1, { scaleX, scaleY: 1 }).start()
        await ut.wait(2, this)
        bubble.destroy()
        cb()
    }

    private skip() {
        this.battleReplayCmpt.skip()
    }

    @util.addLock
    private async restart() {
        await viewHelper.preloadPnl("battle/BattleReadyPnl", [this.data])
        if (!cc.isValid(this)) return
        this.close()
        this.callback({
            again: true
        })
    }

    private handleTa(result) {
        let roles: PassengerModel[] = this.passengers.map(p => p.role)
        let heros = roles.map(p => {
            let equips = p.getEquips()
            let weapon = equips.find(e => e.index == OreMakePnlType.Weapon)
            let armor = equips.find(e => e.index == OreMakePnlType.Armor)
            let resonance_type = 2
            if (p.isTmp) {
                resonance_type = 3
            }
            else if (gameHelper.resonance.isResonance(p.id)) {
                resonance_type = 0
            }
            else if (gameHelper.resonance.isBeResonanced(p.id)) {
                resonance_type = 1
            }
            return {
                hero_id: p.id,
                hero_lv: p.getLevel(),
                hero_star: p.getStarLv(),
                weapon_id: weapon?.quality || 0,
                weapon_lv: weapon?.getEffectsLevel() || 0,
                armor_id: armor?.quality || 0,
                armor_lv: armor?.getEffectsLevel() || 0,
                resonance_type,
                talent_1_lv: p.getTalentLevel(1),
                talent_2_lv: p.getTalentLevel(2),
                talent_3_lv: p.getTalentLevel(3),
            }
        })
        let ret = {
            level_type: this.levelType,
            level_id: this.levelId,
            heros,
            isWin: result.isWin,
            remain: result.passengers.length,
            round: result.round,
        }
        taHelper.track(TaEvent.TA_BATTLE_RET, ret)
    }

    private updateRound() {
        this.roundsLbl_.node.active = localConfig.debug
        this.roundsLbl_.string = `ROUND: ${this.battleReplayCmpt.round}`
    }
}
