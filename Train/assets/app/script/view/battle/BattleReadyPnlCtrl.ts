import MultiMaterial from "../../../core/component/MultiMaterial";
import CoreEventType from "../../../core/event/CoreEventType";
import { util } from "../../../core/utils/Utils";
import { localConfig } from "../../common/LocalConfig";
import { InstanceWeather, LongPress, RuleType, UIFunctionType } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import NodeType from "../../common/event/NodeType";
import { animHelper } from "../../common/helper/AnimHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import { resHelper } from "../../common/helper/ResHelper";
import { uiHelper } from "../../common/helper/UIHelper";
import { unlockHelper } from "../../common/helper/UnlockHelper";
import { viewHelper } from "../../common/helper/ViewHelper";
import Battle from "../../model/battle/Battle";
import { BattleRoleType } from "../../model/battle/BattleEnum";
import { BattleTeam } from "../../model/battle/BattleMgr";
import BattleRole from "../../model/battle/BattleRole";
import Monster from "../../model/battle/Monster";
import PassengerModel from "../../model/passenger/PassengerModel";
import PlanetCheckPointModel from "../../model/planet/PlanetCheckPointModel";
import { DragLongEvent } from "../cmpt/common/DragLongPressCmpt";
import PlanetTitleCmpt from "../cmpt/planet/PlanetTitleCmpt";
import { InstanceLevelCfg } from "../../common/constant/DataType";
import { Equip } from "../../model/equip/EquipModel";
import { MAX_VALUE } from "../../common/constant/Constant";

const { ccclass } = cc._decorator;

@ccclass
export default class BattleReadyPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected bgNode_: cc.Node = null // path://bg_n
    protected bottomNode_: cc.Node = null // path://bottom_n
    protected selectRolesSv_: cc.ScrollView = null // path://bottom_n/selectRoles_sv
    protected battleRolesNode_: cc.Node = null // path://up/battleRoles_n
    protected battleMonstersNode_: cc.Node = null // path://up/battleMonsters_n
    protected battleNode_: cc.Node = null // path://battle_be_n
    protected equipNode_: cc.Node = null // path://equip_be_n
    protected techNode_: cc.Node = null // path://tech_be_n
    protected titleNode_: cc.Node = null // path://title_n
    protected tipLongSkillNode_: cc.Node = null // path://top/tipLongSkill_n
    protected tipEnterTeamNode_: cc.Node = null // path://top/tipEnterTeam_n
    protected solveNode_: cc.Node = null // path://top/solve_be_n
    protected solve2Node_: cc.Node = null // path://top/solve2_be_n
    protected weatherNode_: cc.Node = null // path://weather_n
    protected descNode_: cc.Node = null // path://desc_n
    protected tipsNode_: cc.Node = null // path://tips_n
    //@end

    private passengers: (PassengerModel | BattleRole)[] = []
    private selectedRoles: (PassengerModel | BattleRole)[] = null

    private callback: Function = null

    private checkPoint: PlanetCheckPointModel = null
    private monsters: (Monster | BattleRole)[] = null

    private battlePos: cc.Vec2 = null

    private isCommBg: boolean = true //是不是走通用背景

    private team: BattleTeam = null

    private isBlackHole: boolean = false
    private isPreview: boolean = false

    private title: string = null
    private progress: string = null

    private onStartBattle: Function = null

    private instanceData: InstanceLevelCfg = null

    private buff: BattleRole = null

    private desc: string = ""

    private isTransport: boolean = false

    public listenEventMaps() {
        return [
            { [NodeType.GUIDE_CHARACTER_BATTLE_1005]: () => this.getSelectRoleBodyNode(1005) },
            { [NodeType.GUIDE_CHARACTER_BATTLE_1006]: () => this.getSelectRoleBodyNode(1006) },
            { [NodeType.GUIDE_CHARACTER_BATTLE_1007]: () => this.getSelectRoleBodyNode(1007) },
            { [NodeType.GUIDE_BUTTON_START_BATTLE]: () => this.battleNode_ },
            { [EventType.GUIDE_IGNORE_SELECT_ROLENODE_2]: () => this.ignoreSelectRoleNode(0) },
            { [EventType.GUIDE_SHOW_SELECT_ROLENODE_2]: () => this.showSelectRoleNode(0) },
            { [EventType.BATTLE_EQUIP_CHANGE]: this.updateEquip }
        ]
    }

    public async onCreate(data) {
        this.setParam({ isAct: false, isMask: false })
        this.isTransport = data.isTransport
        this.team = data.team || gameHelper.battle.getTeam()
        this.checkPoint = data.checkPoint
        this.monsters = data.monsters || this.checkPoint?.monsters
        this.passengers = data.passengers || gameHelper.passenger.getPassengers()
        this.passengers = this.passengers.map(p => {
            if (p instanceof PassengerModel) {
                return p.toResonance()
            }
            return p
        })
        this.selectedRoles = this.team.getRoles().map(uid => this.passengers.find(p => p.uid == uid))
        if (this.selectedRoles.length < 5) {
            this.selectedRoles.length = 5
        }
        this.isBlackHole = data.isBlackHole
        this.isPreview = data.isPreview
        this.onStartBattle = data.onStartBattle
        this.title = data.title?.name
        this.progress = data.title?.progress

        this.instanceData = data?.instanceData
        this.buff = data.buff
        this.desc = data.desc

        let pList = []
        if (data.battleBg) {
            pList.push(
                resHelper.loadPrefabByUrl(data.battleBg, this.bgNode_, this.getTag())
            )
        }
        else if (this.checkPoint?.battleScene?.mapId) {
            pList.push(
                resHelper.loadPlanetBattleBg(this.checkPoint.planet.getId(), this.bgNode_, this.getTag(), this.checkPoint?.battleScene?.mapId)
            )
        }
        else {
            this.isCommBg = false
        }

        let roles = this.getSelectRoles().slice(0, 5)

        pList.push(
            ut.promiseMap(roles, async ({ id }) => {
                await resHelper.preloadRoleSk(id, this.getTag())
            }),
            ut.promiseMap(this.monsters, async ({ id }) => {
                if (gameHelper.checkPassengerById(id)) {
                    await resHelper.preloadRoleSk(id, this.getTag())
                }
                else {
                    await resHelper.preloadMonsterSk(id, this.getTag())
                }
            })
        )

        await Promise.all(pList)
    }

    public onEnter(data: any) {
        this.callback = data.callback
        this.battlePos = data.battlePos
        this.hideSkillInfo()
        this.initView()
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://battle_be_n
    onClickBattle(event: cc.Event.EventTouch, data: string) {
        this.onBattle()
    }

    // path://back_be
    onClickBack(event: cc.Event.EventTouch, data: string) {
        this.callback && this.callback()
        this.saveTeam()
        this.close()
    }

    // path://top/tipLongSkill_n/btnLongSkill_be
    onClickBtnLongSkill(event: cc.Event.EventTouch, data: string) {
        this.setTipLongSkill()
    }

    // path://top/solve_be_n
    onClickSolve(event: cc.Event.EventTouch, data: string) {
        let selectedRoles = this.passengers.filter(m => !!m)
        selectedRoles = selectedRoles.filter(r => r.getLevel() > 1)
        this.solve(selectedRoles)
    }

    // path://top/solve2_be_n
    onClickSolve2(event: cc.Event.EventTouch, data: string) {
        this.solve(this.selectedRoles)
    }

    // path://tech_be_n
    onClickTech(event: cc.Event.EventTouch, data: string) {
        viewHelper.showPnl("blackHole/BlackHoleEquip")
    }

    // path://equip_be_n
    onClickEquip(event: cc.Event.EventTouch, data: string) {
        cc.log('onClickEquip', data)
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private solve(selectedRoles: (PassengerModel | BattleRole)[]) {
        let passengers = selectedRoles.filter(m => !!m).map(role => {
            if (role instanceof PassengerModel) {
                return new BattleRole().initData({
                    uid: role.uid, id: role.getID(), skills: role.getSkills(), type: BattleRoleType.PASSENGER,
                    hp: role.getHp(), attack: role.getAttack(), lv: role.getLevel(), starLv: role.getStarLv()
                })
            }
            return role
        })
        let monsters = this.monsters.map(m => {
            if (m instanceof BattleRole) {
                return new BattleRole().initData(m)
            }
            else {
                return new BattleRole().initData({ id: m.id, hp: m.hp, attack: m.attack, skills: m.getSkills(), type: BattleRoleType.MONSTER, lv: m.lv, starLv: m.getStarLv() })
            }
        })
        let combs = ut.combination(passengers, Math.min(5, passengers.length))
        let wins = []
        let count = 0
        for (let comb of combs) {
            let teams = ut.permutation(comb)
            console.log("开始求解...")
            for (let team of teams) {
                count++
                let _passengers = team.map(role => role.reset())
                let _monsters = monsters.map(role => role.reset())
                let battle = new Battle()
                battle["openDebug"] = false
                let isWin = battle.init(_passengers, _monsters, [this.buff])
                if (isWin) {
                    wins.push(team.map(t => t.id))
                }
            }
        }

        console.log(wins.length / count)
        console.log(wins[0])
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------
    private saveTeam() {
        let uids = this.selectedRoles.filter(m => !!m).map(r => r.uid)
        this.team.setRolesBySever(uids)
    }

    @util.addLock
    private async onBattle() {
        let selectedRoles = this.selectedRoles.filter(m => !!m)
        if (selectedRoles.length <= 0) {
            viewHelper.showAlert("chapterEmbattle_tips_2")
            return
        }

        if (this.onStartBattle) {
            let succ = await this.onStartBattle()
            if (!succ) return
        }

        let uids = selectedRoles.map(r => r.uid)
        await this.team.setRolesBySever(uids)
        if (!cc.isValid(this)) return

        let battleRoles = selectedRoles.map(role => {
            if (role instanceof PassengerModel) {
                return new BattleRole().initData({
                    uid: role.uid, id: role.getID(), skills: role.getSkills(), type: BattleRoleType.PASSENGER,
                    hp: role.getHp(), attack: role.getAttack(), role
                })
            }
            return role
        })

        this.callback && this.callback(battleRoles)
        this.waitClose()
    }

    private async waitClose() {
        this.battleNode_.Component(cc.Button).interactable = false
        await eventCenter.wait(CoreEventType.PNL_ENTER)
        this.close()
    }
    private registerMonster(body: cc.Node, it: cc.Node, data: any) {
        body.off(LongPress.LPSTART)
        body.on(LongPress.LPSTART, () => { this.showSkillInfo(it, data) }, this)
        // body.off(LongPress.LPEND)
        // body.on(LongPress.LPEND, this.hideSkillInfo, this)
    }
    private registerRoleLong(it: cc.Node, data: any) {
        let drag = it.Child('drag')
        drag.off(DragLongEvent.LP_START)
        drag.on(DragLongEvent.LP_START, () => { this.showSkillInfo(it, data) }, this)
        drag.off(DragLongEvent.LP_CANCEL)
        drag.on(DragLongEvent.LP_CANCEL, this.hideSkillInfo, this)
    }
    private registerRoleDown(it: cc.Node, data: any) {
        this.registerRoleLong(it, data)
        let drag = it.Child('drag')
        drag.off(DragLongEvent.DL_CLICK)
        drag.on(DragLongEvent.DL_CLICK, () => {
            this.onSelectRole(data)
            eventCenter.emit(EventType.GUIDE_INTO_TEAM)
        }, this)
        drag.off(DragLongEvent.DRAG_MOVE)
        drag.on(DragLongEvent.DRAG_MOVE, () => { this.dragMoveDown(it, drag) }, this)
        drag.off(DragLongEvent.DRAG_END)
        drag.on(DragLongEvent.DRAG_END, () => {
            let swapNode = this.checkSwap(it, drag)
            if (swapNode) {
                let index = this.battleRolesNode_.children.findIndex(r => r == swapNode)
                this.onSelectRole(data, index)
            }
        }, this)
    }

    private registerRoleUp(it: cc.Node, data: any) {
        this.registerRoleLong(it, data)
        let drag = it.Child('drag')
        let empty = it.Child('empty')
        drag.off(DragLongEvent.DL_CLICK)
        drag.on(DragLongEvent.DL_CLICK, () => { this.onUnSelectRole(data) }, this)
        drag.off(DragLongEvent.DRAG_START)
        drag.on(DragLongEvent.DRAG_START, () => {
            empty.active = true
            empty.Swih('ok')
        }, this)
        drag.off(DragLongEvent.DRAG_MOVE)
        drag.on(DragLongEvent.DRAG_MOVE, () => {
            let { type } = this.dragMoveUp(it, drag)
            if (type == 0) empty.Swih('ok')
        }, this)
        drag.off(DragLongEvent.DRAG_END)
        drag.on(DragLongEvent.DRAG_END, () => {
            empty.active = false
            let { type, node } = this.checkUpSwap(it, drag)
            if (type == 1) this.onUnSelectRole(data)
            else if (type == 2) this.swapRole(it, node)
        }, this)
    }

    // ----------------------------------------- custom function ----------------------------------------------------

    private initView() {
        this.battleNode_.active = !this.isPreview
        this.initCheckPointView()
        this.initRoleSelectView()
        this.initBattleRoles()
        this.initTipEnterTeam()
        this.initBg()
        this.onChangeMap()
        this.updateEquip()
        this.updateTech()
        this.solveNode_.active = this.solve2Node_.active = localConfig.debug
    }

    private updateTech() {
        this.techNode_.active = this.isBlackHole
    }

    private updateEquip() {
        this.equipNode_.active = false
    }

    private async initBg() {
        let winHeight = cc.winSize.height
        let scaleBg = 1
        if (!this.isCommBg) {
            scaleBg = viewHelper.getPlanetWindScale()
        }
        else {
            scaleBg = this.bgNode_.children[0].scale
        }
        let upNode = this.Child('up')
        let minY = (upNode.y + 1242 * 0.5) - winHeight * 0.5 //upNode的最低位置
        let targetY = -280 * scaleBg //upNode要变到这个位置才能和背景对应上
        if (this.battlePos && !this.isCommBg) {
            targetY = this.battlePos.y
        }
        if (minY > targetY) { //如果需要对应的位置比最低位置还低，把背景往上挪
            upNode.y = minY
            this.bgNode_.y = minY - targetY
        }
        else {
            upNode.y = targetY
        }

        if (!this.isCommBg) { //走截图的方式
            ut.waitNextFrame(1, this).then(()=>{
                this.bgNode_.Component(cc.Sprite).spriteFrame = viewHelper.getSpriteFrameByCapture(viewHelper.getPlanetCamera())
            }) //等只剩背景再截图
        }

        uiHelper.checkInstanceBattleBg(this.instanceData, this.bgNode_)
        uiHelper.checkInstanceBattleWeather(this.instanceData, this.weatherNode_, this.buff)

        if (this.isTransport) {
           uiHelper.updateTransportBattleBg(this.bgNode_, gameHelper.world.isNight())
        }

        this.initDesc()
    }

    private initDesc() {
        this.descNode_.active = !!this.desc
        if (this.descNode_.active) {
            this.descNode_.Child("content").setLocaleUpdate(()=>{
                return this.desc
            })
        }
    }

    private onChangeMap() {
        if (!!this.title && !!this.progress) {
            this.titleNode_.active = true
            this.titleNode_.Child('PlanetTitle').Component(PlanetTitleCmpt).init(this.title, this.progress, RuleType.BATTLE)
        }
        else {
            this.titleNode_.active = false
        }
    }

    //关卡信息
    private initCheckPointView() {
        let ary = this.dealMonsters()
        this.battleMonstersNode_.Items(ary, async (it, data) => {
            let drag = it.Child('drag')
            drag.active = !!data
            if (!data) return
            let id = data.id
            let role = drag.Child('role')
            let body = role.Child('body')
            this.setUILv(role, data.lv)
            this.registerMonster(drag, it, data)
            this.setUIAtkHp(role, data.hp, data.attack)

            let sk = body.Child('sp', sp.Skeleton)
            if (gameHelper.checkPassengerById(id)) {
                await resHelper.loadRoleSp(data.id, sk, this.getTag())
            }
            else {
                await resHelper.loadMonsterSp(data.id, sk, this.getTag())
            }
            if (!cc.isValid(this)) return
            uiHelper.setRoleDragSize(body.Child('sp'), 360, 67)
        })
    }
    private dealMonsters() {
        let ary = this.monsters.slice()
        //加了别的boss后这里判断boss的方法以及boss的位置都需要改
        let boss = ary.find(m => m.id == 2008)
        if (!!boss) {
            ary.splice(0, 0, null, null)
        }
        return ary
    }
    private setUIAtkHp(role: cc.Node, hp, atk) {
        let ui = role.Child('BattleUIAtkHp')
        ui.Child("hp/count", cc.Label).string = viewHelper.getHpStr(hp)
        ui.Child("hp/bar", cc.Sprite).fillRange = 1
        ui.Child("attack/count", cc.Label).string = atk
    }
    //下面待选择的乘客视图
    private initRoleSelectView() {
        this.setDownBgSelect(false)
        let roles = this.getSelectRoles()
        let length = roles.length
        if (length == 1) {
            // 为了ignoreSelectRoleNode能生效 需要立即把item创建出来
            this.selectRolesSv_.Items(roles, (it, data) => {
                this.setOneRole(it, data)
            })
        } else {
            this.selectRolesSv_.List(length, (it, i) => {
                let data = roles[i]
                it.Data = data
                this.setOneRole(it, data)
            })
        }
    }
    private setOneRole(it: cc.Node, data: PassengerModel | BattleRole) {
        this.setUIRole(it.Child('drag/role'), data, false)
        let starLv = data.getStarLvWithQuality()
        it.Child('drag/role/starBg').active = starLv > 0
        this.registerRoleDown(it, data)
    }
    private setUIRole(role: cc.Node, data: PassengerModel | BattleRole, playOnLoad: boolean) {
        let body = role.Child('body')
        role.Child('isAid').active = !!gameHelper.blackHole.getAid(data.uid)
        role.Child('equip').active = false
        let battleTypeNode = role.Child("battle_type")
        battleTypeNode.active = this.isBlackHole
        if (battleTypeNode.active) {
            resHelper.loadBattleTypeIcon(data.battleType, battleTypeNode.Child('icon'), this.getTag())
        }
        let animalTypeNode = role.Child("animal_type")
        animalTypeNode.active = this.isBlackHole
        if (animalTypeNode.active) {
            resHelper.loadAnimalTypeIcon(data.animalType, animalTypeNode.Child('icon'), this.getTag())
        }
        let starLv = data.getStarLvWithQuality()
        let level = data.getLevel()
        let quality = data.quality
        let lvs = ut.newArray(starLv, 1)
        role.Child('starLv').Items(lvs, (it, data) => {
            it.active = true
        })
        this.setUILv(role, level)
        role.Child('lv', cc.MultiColor).setColor(quality - 1)
        this.setUISk(body.Child('sp'), data.id, playOnLoad)
        this.setUIAtkHp(role, data.getHp(), data.getAttack())
        return body
    }

    private setUILv(role: cc.Node, val: number) {
        role.Child('lv', cc.Label).setLocaleKey('common_guiText_11', val)
    }
    private setUISk(skNode: cc.Node, id: number, playOnLoad: boolean) {
        let dic = skNode.Data
        if (dic && dic.id == id && dic.bol == playOnLoad) {
            return
        }
        skNode.Data = { id, bol: playOnLoad }
        let sk = skNode.Component(sp.Skeleton)
        resHelper.loadOwnRoleSp(id, sk, this.getTag(), playOnLoad).then(() => {
            if (playOnLoad) {
                uiHelper.setRoleDragSize(skNode, 360, 67)
            } else {
                uiHelper.setRoleDragSize(skNode, 300, 22)
            }
        })
    }

    //上阵的乘客视图
    private initBattleRoles() {
        this.battleRolesNode_.Items(this.selectedRoles, (it, data, i) => {
            let drag = it.Child('drag')
            let empty = it.Child('empty')
            let bol = !!data
            drag.active = bol
            empty.active = !bol
            this.initEmpty(empty, i)
            this.setBattleRoleSelect(it, false)
            if (bol) {
                this.setUIRole(drag.Child('role'), data, true)
                this.registerRoleUp(it, data)
            }
        })
    }
    private setBattleRoleSelect(it: cc.Node, bol: boolean) {
        it.Child('empty').Swih(bol ? 'ok' : 'no')
        it.Child('drag/role').setDark(bol ? 0.5 : 0, true)
    }
    private setDownBgSelect(bol: boolean) {
        this.bottomNode_.Child('zdbz_bg1', MultiMaterial).setMaterial(bol)
        this.bottomNode_.Child('zdbz_bg2', MultiMaterial).setMaterial(bol)
    }
    private initEmpty(node: cc.Node, i: number) {
        let text = String(i + 1)
        node.Child('no/num', cc.Label).string = text
        node.Child('ok/num', cc.Label).string = text
    }

    private onSelectRole(role: PassengerModel, giveIndex?: number) {
        //让浪客喵上阵的提示，玩家操作浪客喵上阵后，滑出
        let index = giveIndex != null ? giveIndex : this.selectedRoles.findIndex(r => r == null)
        this.selectedRoles[index] = role
        this.setTipEnterTeamOver()
        this.updateRoles()
    }
    private onUnSelectRole(role: PassengerModel) {
        let index = this.selectedRoles.findIndex(r => r == role)
        this.selectedRoles[index] = null
        this.updateRoles()
    }
    private updateRoles() {
        this.initRoleSelectView()
        this.initBattleRoles()
    }

    private dragMoveDown(it: cc.Node, drag: cc.Node) {
        this.hideSkillInfo()
        let swapNode = this.checkSwap(it, drag)
        for (let item of this.battleRolesNode_.children) {
            this.setBattleRoleSelect(item, item == swapNode)
        }
        return swapNode
    }
    private dragMoveUp(it: cc.Node, drag: cc.Node) {
        this.hideSkillInfo()
        let info = this.checkUpSwap(it, drag)
        let { type, node } = info
        this.setDownBgSelect(type == 1)
        for (let item of this.battleRolesNode_.children) {
            this.setBattleRoleSelect(item, type == 2 && item == node)
        }
        return info
    }
    private checkUpSwap(curNode: cc.Node, body: cc.Node) {
        let type = 0
        let node = this.checkDownBg(body)
        if (node) {
            type = 1
        } else {
            node = this.checkSwap(curNode, body)
            if (node) type = 2
        }
        return { type, node }
    }
    private checkDownBg(body: cc.Node) {
        let bg = this.bottomNode_.Child('zdbz_bg1')
        let pos = ut.convertToNodeAR(body, bg)
        let checkY = pos.y < bg.height * 0.6
        if (checkY) return bg
    }
    private checkSwap(curNode: cc.Node, body: cc.Node) {
        let spacX = this.battleRolesNode_.Component(cc.Layout).spacingX
        for (let node of this.battleRolesNode_.children) {
            if (node == curNode) continue
            let pos = ut.convertToNodeAR(body, node)
            let width = (node.width + spacX) * 0.5
            let checkX = -width <= pos.x && pos.x <= width
            let checkY = pos.y > -body.height * 0.5
            let isIntersects = checkX && checkY
            if (isIntersects) {
                return node
            }
        }
    }
    private swapRole(roleNode1: cc.Node, roleNode2: cc.Node) {
        let children = this.battleRolesNode_.children
        let index1 = children.findIndex(r => r == roleNode1)
        let index2 = children.findIndex(r => r == roleNode2)
        let ary = this.selectedRoles
        let t = ary[index1]
        ary[index1] = ary[index2]
        ary[index2] = t
        this.initBattleRoles()
    }

    private showSkillInfo(it: cc.Node, data: PassengerModel | BattleRole) {
        this.tipsNode_.active = true
        let info = { id: data.id, skills: data.getSkills() }
        viewHelper.showBubble("SkillBubble", it, info)
    }

    private hideSkillInfo() {
        viewHelper.hideBubble()
        this.tipsNode_.active = false
    }

    private getDicSelected() {
        let dic = new Set()
        this.selectedRoles.forEach(r => { if (r) dic.add(r) })
        return dic
    }
    private getSelectRoles() {
        let dic = this.getDicSelected()
        let ary = this.passengers
        let roles = ary.filter(r => !dic.has(r))
        roles.sort(gameHelper.battlePassengersCmp)
        return roles
    }
    private getSelectRoleBodyNode(id: number) {
        let item = this.getSelectRoleNodeById(id)
        if (item) return item.Child('drag')
    }
    private getSelectRoleNodeById(id: number) {
        return this.selectRolesSv_.content.children.find(c => c.Data?.id == id)
    }
    private ignoreSelectRoleNode(index: number) {
        let item = this.selectRolesSv_.content.children[index]
        if (item) item.active = false
    }
    private async showSelectRoleNode(index) {
        let time1 = 0.35
        let time2 = 0.1
        let item = this.selectRolesSv_.content.children[index]
        item.scale = 0
        item.active = true
        await ut.wait(0.25)
        await cc.tween(item).to(time1, { scale: 1.5 }).to(time2, { scale: 1 }).promise()
        eventCenter.emit(EventType.GUIDE_SHOW_SELECT_END)
    }

    // 提示 长按看技能
    private initTipLongSkill() {
        let bol = !this.tipEnterTeamNode_.active && !gameHelper.battle.isRecorded('tipSkill')
        if (bol) {
            bol = !gameHelper.openGuide || gameHelper.planet.getSchoolPlanet().isDone()
        }
        this.tipLongSkillNode_.active = bol
        if (bol) this.moveIn(this.tipLongSkillNode_)
    }
    private setTipLongSkill() {
        if (gameHelper.battle.isRecorded('tipSkill')) return
        gameHelper.battle.setRecord('tipSkill')
        this.moveOut(this.tipLongSkillNode_)
    }
    // 提示 让浪客喵上阵
    private initTipEnterTeam() {
        let id = cfgHelper.getMiscData("guide").enterTeamTips
        let bol = this.checkPoint?.getId() == id
        if (bol && gameHelper.battle.isRecorded('tipRove')) {//浪客喵上阵战斗过一次 就不需要提示了
            bol = false
        }
        this.tipEnterTeamNode_.active = bol
        if (bol) {
            this.tipEnterTeamNode_.Child('lb', cc.RichText).setLocaleKey("explore_guiText_20")
            this.moveIn(this.tipEnterTeamNode_)
        }
        this.initTipLongSkill()
    }
    private setTipEnterTeamOver() {
        if (gameHelper.battle.isRecorded('tipRove')) return
        if (!this.tipEnterTeamNode_.active) return
        let id = 1007
        if (this.selectedRoles.find(m => m && m.id == id)) {
            gameHelper.battle.setRecord('tipRove')
            this.moveOut(this.tipEnterTeamNode_).then(this.initTipLongSkill.bind(this))
        }
    }
    private moveIn(it: cc.Node, time: number = 0.3) {
        if (it.Data == null) it.Data = it.y
        let y = it.Data
        animHelper.moveYIn(it, time, y, -y)
    }
    private async moveOut(it: cc.Node, time: number = 0.3) {
        let y = it.Data
        await animHelper.moveYOut(it, time, y, -y)
    }
}
