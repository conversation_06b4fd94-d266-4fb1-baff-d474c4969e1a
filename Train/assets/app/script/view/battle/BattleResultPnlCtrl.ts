import { ui<PERSON><PERSON><PERSON> } from "../../common/helper/UIHelper";
import { cfgHelper } from "../../common/helper/CfgHelper";
import { animHelper } from "../../common/helper/AnimHelper";
import { gameHelper } from "../../common/helper/GameHelper";
import NodeType from "../../common/event/NodeType";
import ConditionObj from "../../model/common/ConditionObj";
import CoreEventType from "../../../core/event/CoreEventType";
import PlanetCheckPointModel from "../../model/planet/PlanetCheckPointModel";
import FrameIconNum from "../prefab/FrameIconNum";
import { viewHelper } from "../../common/helper/ViewHelper";
import { ConditionType } from "../../common/constant/Enums";
import { CONDITION_NEEDPLAY, DEFAULT_IGNORES } from "../../common/constant/Constant";
import { util } from "../../../core/utils/Utils";

const { ccclass } = cc._decorator;

@ccclass
export default class BattleResultPnlCtrl extends mc.BasePnlCtrl {

    //@autocode property begin
    protected backNode_: cc.Node = null // path://back_be_n
    protected winNode_: cc.Node = null // path://win_n
    protected rewardsNode_: cc.Node = null // path://win_n/rewards_n
    protected winSp_: sp.Skeleton = null // path://win_n/win_sp
    protected winTipsNode_: cc.Node = null // path://win_n/winTips_n
    protected winPvpScoreNode_: cc.Node = null // path://win_n/win_pvp_score_n
    protected failNode_: cc.Node = null // path://fail_n
    protected failBgtiaoNode_: cc.Node = null // path://fail_n/bg/fail_bgtiao_n
    protected failQuanSp_: sp.Skeleton = null // path://fail_n/fail_quan_sp
    protected failSp_: sp.Skeleton = null // path://fail_n/fail_sp
    protected loseRewardsNode_: cc.Node = null // path://fail_n/loseRewards_n
    protected failBtnNode_: cc.Node = null // path://fail_n/failBtn_nbe_n
    protected failTipsNode_: cc.Node = null // path://fail_n/failTips_n
    protected losePvpScoreNode_: cc.Node = null // path://fail_n/lose_pvp_score_n
    protected continueNode_: cc.Node = null // path://continue_n
    //@end

    private isWin: boolean = false
    private rewards: ConditionObj[] = []
    private callback: Function = null
    private isGuide: boolean = false
    private checkPoint: PlanetCheckPointModel = null
    private isAgain: boolean = false
    private noContinue: boolean = false
    private isTransport: boolean = false
    private isTransoprtFail: boolean = false
    private loseRewards: ConditionObj[] = []
    private failLb: string = null
    private winLb: string = null
    private isBlackHole: boolean = false
    private isArrest: boolean = false
    private noAgain: boolean = false
    private pvpData: { score: number, rank: number } = null

    private data: any = {}

    public listenEventMaps() {
        return [
            { [NodeType.GUIDE_BUTTON_BACK_7]: this.guideFailGoto },
        ]
    }

    public async onCreate() {
        this.setParam({ isAct: false, isMask: false })
    }

    public onEnter(data: any) {
        let bol = data.isWin
        this.isWin = bol
        this.callback = data.callback
        this.checkPoint = data.checkPoint
        this.isBlackHole = data.isBlackHole
        this.isArrest = data.isArrest
        this.noAgain = data.noAgain
        this.data = data
        if (bol) {
            this.rewards = data.rewards || this.checkPoint?.rewards || []
        } else {
            let curNode = gameHelper.planet.getCurPlanet().getCurNode()
            if (curNode instanceof PlanetCheckPointModel && curNode.getId() == "1005-1-1") {
                this.isGuide = true
                this.noAgain = true
            }
        }
        this.rewards = this.rewards.filter(r => r.type != ConditionType.WORLD_TIME)
        this.failLb = data.failTip
        this.winLb = data.winTip
        this.isTransport = data.isTransport
        this.hideContinue()
        this.winNode_.active = bol
        this.failNode_.active = !bol
        this.pvpData = data.pvpData
        this.winPvpScoreNode_.active = false
        this.losePvpScoreNode_.active = false

        if (bol) {
            this.initWin()
        } else {
            this.initFail()
        }
    }

    public onRemove() {
        this.emit(CoreEventType.CLOSE_PNL, "battle/Battle")
        this.callback && this.callback(this.isAgain)
    }

    public isFail() {
        return !this.isWin
    }

    // ----------------------------------------- button listener function -------------------------------------------
    //@autocode button listener

    // path://back_be_n
    onClickBack(event: cc.Event.EventTouch, data: string) {
        if (this.noContinue) return

        if (!this.isWin) {
            this.back()
        }
        else {
            let diamondPosList: cc.Vec2[] = []
            let list: ConditionObj[] = []
            this.rewardsNode_.children.forEach((it) => {
                let cond = it.Data
                if (!cond) return
                if (CONDITION_NEEDPLAY.has(cond.type)) {
                    diamondPosList.push(ut.convertToNodeAR(it, this.rewardsNode_.parent))
                    list.push(cond)
                }
            })
            if (diamondPosList.length > 0 && !!diamondPosList[0]) {
                this.hideContinue()
                this.showDiamond(diamondPosList, list)
            }
            else {
                this.back()
            }
        }
    }

    // path://fail_n/failBtn_nbe_n
    onClickFailBtn(event: cc.Event.EventTouch, data: string) {
        const name = event.target.name
        if (name === 'jackpot') {
            this.jackpot()
        } else if (name === 'again') {
            this.again()
        } else if (name === 'goto') {
            this.goto()
        }
    }
    //@end
    // ----------------------------------------- event listener function --------------------------------------------

    private async showDiamond(diamondPosList, list) {
        cc.tween(this.rewardsNode_).to(0.2, { opacity: 0 }).call(() => {
            viewHelper.showPnl('common/DiamondPnl', diamondPosList, list, () => this.close())
        }).start()

    }

    // ----------------------------------------- custom function ----------------------------------------------------
    private hideContinue() {
        this.backNode_.active = false
        this.continueNode_.active = false
    }

    private showContinue() {
        if (this.noContinue) return
        this.backNode_.active = true
        this.continueNode_.active = true
        animHelper.playContinueBlink(this.continueNode_)
    }

    private async initWin() {
        this.rewardsNode_.active = false
        this.winTipsNode_.active = false
        this.initRewards()
        await this.skShowLoop(this.winSp_, true)
        await ut.wait(0.2, this)
        if (!!this.winLb) this.winTipsNode_.Child('lb', cc.RichText).setLocaleKey(this.winLb)
        this.winTipsNode_.active = !!this.winLb
        await this.playRewardAnim()
        this.initPvpScoreRank()
        
        this.showContinue()
    }

    private async initFail() {
        this.failBtnNode_.active = false
        this.failTipsNode_.active = false
        this.loseRewardsNode_.active = false
        this.losePvpScoreNode_.active = false
        this.skShowLoop(this.failQuanSp_, false, true)
        await this.skShowLoop(this.failSp_, false)
        if (!!this.failLb) this.failTipsNode_.Child('lb', cc.RichText).setLocaleKey(this.failLb)
        this.failTipsNode_.active = !this.isBlackHole
        if (!this.isTransoprtFail && (!!this.loseRewards && this.loseRewards.length > 0)) {
            this.initLoseRewards()
            this.playLoseRewardAnim()
        }
        else if (!this.isBlackHole && !(this.isArrest && !!this.failLb)) {
            this.showBtn()
        }
        this.initPvpScoreRank()

        this.showContinue()
    }

    private guideFailGoto() {
        this.noContinue = true
        this.hideContinue()
        return this.failBtnNode_.Child('goto')
    }

    private async skShowLoop(sk: sp.Skeleton, isWin: boolean, isQuan: boolean = false) {
        // await sk.playAnimation(isArrest ? "animation_hd1" : "animation")
        // sk.playAnimation(isArrest ? "animation_hd2" : "animation2", true)
        let anim = isWin ? "animation_sj1" : "animation_sjsb1"
        if (isQuan) {
            anim = "animation"
        }
        await sk.playAnimation(anim)

        anim = isWin ? "animation_sj2" : "animation_sjsb2"
        if (isQuan) {
            anim = "animation2"
        }
        sk.playAnimation(anim, true)
    }

    private initRewards() {
        this.rewardsNode_.Items(this.rewards, (it, data) => {
            it.Data = data
            data.isHide = false
            it.Component(FrameIconNum).init(data, this.getTag())
            uiHelper.regClickPropBubble(it, data)
        })
    }

    private async playRewardAnim() {
        let node = this.rewardsNode_
        let items = node.children
        node.active = true
        items.forEach(it => it.opacity = 0)
        for (let it of items) {
            it.opacity = 10
            it.scale = 0.1
            let btn = it.Component(cc.Button)
            btn.interactable = false
            cc.Tween.stopAllByTarget(it)
            cc.tween(it).to(0.2, { opacity: 255 }).start()
            cc.tween(it).to(0.2 * 1.1 / 1.3, { scale: 1.2 }).to(0.2 * 0.2 / 1.3, { scale: 1 }).promise().then(() => {
                btn.interactable = true
            })
            await ut.wait(0.25, this)
        }
    }

    private async showBtn(isGoTo: boolean = true, jackpot: boolean = true) {
        let node = this.failBtnNode_
        let items = node.children
        node.active = true
        if (this.isTransport) {
            // node.Child('goto/lb', cc.Label).setLocaleKey('transport_buttonName_3')
            // node.Child('back/lb', cc.Label).setLocaleKey('transport_buttonName_4')
            node.active = false
        }
        items.forEach(it => it.active = false)
        for (let it of items) {
            let bol = false
            switch (true) {
                case it.name == "goto":
                    bol = isGoTo
                    break
                case it.name == "again":
                    bol = !this.noAgain
                    break
                case it.name == "jackpot":
                    bol = jackpot
                    break
            }
            // let bol = isGoTo ? it.name == key : it.name != key
            // bol ||= (this.isTransport && it.name == 'back')
            if (!bol) continue
            it.scale = 0.1
            it.active = true
            cc.Tween.stopAllByTarget(it)
            cc.tween(it)
                .to(0.35 * 1.1 / 1.3, { scale: 1.2 })
                .to(0.35 * 0.2 / 1.3, { scale: 1 })
                .start()
        }
    }

    private initLoseRewards() {
        this.loseRewardsNode_.Items(this.loseRewards, (it, data) => {
            it.Data = data
            it.Component(FrameIconNum).init(data, this.getTag())
            uiHelper.regClickPropBubble(it, data)
        })
    }

    private async playLoseRewardAnim() {
        let node = this.loseRewardsNode_
        let items = node.children
        node.active = true
        items.forEach(it => it.opacity = 0)
        for (let it of items) {
            it.opacity = 10
            it.scale = 0.1
            let btn = it.Component(cc.Button)
            btn.interactable = false
            cc.Tween.stopAllByTarget(it)
            cc.tween(it).to(0.2, { opacity: 255 }).start()
            cc.tween(it).to(0.2 * 1.1 / 1.3, { scale: 1.2 }).to(0.2 * 0.2 / 1.3, { scale: 1 }).promise().then(() => {
                btn.interactable = true
            })
            await ut.wait(0.25, this)
        }
    }

    private back() { this.close() }

    @util.addLock
    private async again() {
        await viewHelper.preloadPnl("battle/BattleReadyPnl", [this.data])
        if (!cc.isValid(this)) return
        this.isAgain = true
        this.close()
    }

    private goto() {
        let data = null
        if (!this.isGuide) {
            data = { showPnl: { key: "role/RolePnl" } }
        }
        let windName = "main"
        if (mc.currWind.key == windName) { //先临时适配
            // viewHelper.showUI(true)
            // eventCenter.emit(CoreEventType.ACTIVE_WIND, true)
            eventCenter.emit(CoreEventType.CLOSE_ALL_PNL, null, DEFAULT_IGNORES.join("|"))
            if (data?.showPnl) {
                viewHelper.showPnl(data.showPnl.key)
            }
        }
        else {
            eventCenter.emit(mc.Event.GOTO_WIND, windName, data)
        }
    }

    private jackpot() {
        let data = null
        if (!this.isGuide) {
            data = { showPnl: { key: "jackpot/JackPotPnl" } }
        }
        let windName = "main"
        if (mc.currWind.key == windName) {
            eventCenter.emit(CoreEventType.CLOSE_ALL_PNL, null, DEFAULT_IGNORES.join("|"))
            if (data?.showPnl) {
                viewHelper.showPnl(data.showPnl.key)
            }
        }
        else {
            eventCenter.emit(mc.Event.GOTO_WIND, windName, data)
        }
    }

    private async backToTransport() {
        let succ = await gameHelper.transport.transportBack()
        if (succ && cc.isValid(this)) {
            this.close()
        }
    }

    private initPvpScoreRank() {
        if (!this.pvpData) return
        this.failBtnNode_.active = false
        this.failTipsNode_.active = false
        let actN = this.isWin ? this.winPvpScoreNode_ : this.losePvpScoreNode_
        actN.active = !!this.pvpData
        let char = "+"
        if (this.pvpData.score < 0) {
            char = "-"
        } else if (this.pvpData.score == 0) {
            char = this.isWin ? "+" : "-"
        }
        actN.Child("lbl", cc.Label).string = `${char}${Math.abs(this.pvpData.score)}`
    }

}
