import {Msg} from "./msg-define";
//@ts-ignore
const msgJs = require('msg');

export const SendMsgMap = {
    [Msg.C2S_ClaimAchievementRewardMessage]: proto.C2S_ClaimAchievementRewardMessage,
    [Msg.C2S_AcceptArrestMessage]: proto.C2S_AcceptArrestMessage,
    [Msg.C2S_SetArrestBattleResultMessage]: proto.C2S_SetArrestBattleResultMessage,
    [Msg.C2S_FinishArrestMessage]: proto.C2S_FinishArrestMessage,
    [Msg.C2S_DestroyArrestMessage]: proto.C2S_DestroyArrestMessage,
    [Msg.C2S_RefreshAllArrestMessage]: proto.C2S_RefreshAllArrestMessage,
    [Msg.C2S_ShowArrestResultMessage]: proto.C2S_ShowArrestResultMessage,
    [Msg.C2S_TicketMergeMessage]: proto.C2S_TicketMergeMessage,
    [Msg.C2S_DropItemMessage]: proto.C2S_DropItemMessage,
    [Msg.C2S_SpaceStoneLvUpMessage]: proto.C2S_SpaceStoneLvUpMessage,
    [Msg.C2S_UseSpaceStoneMessage]: proto.C2S_UseSpaceStoneMessage,
    [Msg.C2S_MarkSpaceStoneMessage]: proto.C2S_MarkSpaceStoneMessage,
    [Msg.C2S_SyncItemMessage]: proto.C2S_SyncItemMessage,
    [Msg.C2S_UseItemMessage]: proto.C2S_UseItemMessage,
    [Msg.C2S_ReadyStartBlackHoleMessage]: proto.C2S_ReadyStartBlackHoleMessage,
    [Msg.C2S_StartBlackHoleMessage]: proto.C2S_StartBlackHoleMessage,
    [Msg.C2S_SelectBlackHoleNodeMessage]: proto.C2S_SelectBlackHoleNodeMessage,
    [Msg.C2S_SyncBlackHoleMessage]: proto.C2S_SyncBlackHoleMessage,
    [Msg.C2S_UnlockBlackHoleMessage]: proto.C2S_UnlockBlackHoleMessage,
    [Msg.C2S_SyncAllBurstTaskMessage]: proto.C2S_SyncAllBurstTaskMessage,
    [Msg.C2S_StartBurstTaskMessage]: proto.C2S_StartBurstTaskMessage,
    [Msg.C2S_ClaimBurstTaskRewardMessage]: proto.C2S_ClaimBurstTaskRewardMessage,
    [Msg.C2S_CollectMapMineDoneMessage]: proto.C2S_CollectMapMineDoneMessage,
    [Msg.C2S_FinishDailyTaskMessage]: proto.C2S_FinishDailyTaskMessage,
    [Msg.C2S_SyncDailyTaskInfoMessage]: proto.C2S_SyncDailyTaskInfoMessage,
    [Msg.C2S_DialogTaskDoneMessage]: proto.C2S_DialogTaskDoneMessage,
    [Msg.C2S_BattleTaskDoneTestMessage]: proto.C2S_BattleTaskDoneTestMessage,
    [Msg.C2S_WearEquipMessage]: proto.C2S_WearEquipMessage,
    [Msg.C2S_UnWearEquipMessage]: proto.C2S_UnWearEquipMessage,
    [Msg.C2S_MakeEquipMessage]: proto.C2S_MakeEquipMessage,
    [Msg.C2S_BuyEquipMessage]: proto.C2S_BuyEquipMessage,
    [Msg.C2S_SellEquipMessage]: proto.C2S_SellEquipMessage,
    [Msg.C2S_SyncExploreMessage]: proto.C2S_SyncExploreMessage,
    [Msg.C2S_StartExploreMessage]: proto.C2S_StartExploreMessage,
    [Msg.C2S_ClaimExploreRewardMessage]: proto.C2S_ClaimExploreRewardMessage,
    [Msg.C2S_BuySpaceshipMessage]: proto.C2S_BuySpaceshipMessage,
    [Msg.C2S_GetExploreAreaMessage]: proto.C2S_GetExploreAreaMessage,
    [Msg.C2S_CeilOperationMessage]: proto.C2S_CeilOperationMessage,
    [Msg.C2S_CeilSyncMessage]: proto.C2S_CeilSyncMessage,
    [Msg.C2S_InstanceFightMessage]: proto.C2S_InstanceFightMessage,
    [Msg.C2S_SyncInstanceMessage]: proto.C2S_SyncInstanceMessage,
    [Msg.C2S_UnlockInstanceMessage]: proto.C2S_UnlockInstanceMessage,
    [Msg.C2S_CompleteInstancePuzzleMessage]: proto.C2S_CompleteInstancePuzzleMessage,
    [Msg.C2S_GetPlayerInfoMessage]: proto.C2S_GetPlayerInfoMessage,
    [Msg.C2S_GmExecuteMessage]: proto.C2S_GmExecuteMessage,
    [Msg.C2S_JackpotReqMessage]: proto.C2S_JackpotReqMessage,
    [Msg.C2S_CollectItemMessage]: proto.C2S_CollectItemMessage,
    [Msg.C2S_SpeedUpMessage]: proto.C2S_SpeedUpMessage,
    [Msg.C2S_StopSpeedUpMessage]: proto.C2S_StopSpeedUpMessage,
    [Msg.C2S_SyncSpeedUpMessage]: proto.C2S_SyncSpeedUpMessage,
    [Msg.C2S_RecoverEnergyMessage]: proto.C2S_RecoverEnergyMessage,
    [Msg.C2S_SyncMessage]: proto.C2S_SyncMessage,
    [Msg.C2S_RecordGuideStepMessage]: proto.C2S_RecordGuideStepMessage,
    [Msg.C2S_ClaimTaskRewardMessage]: proto.C2S_ClaimTaskRewardMessage,
    [Msg.C2S_SyncPlanetMessage]: proto.C2S_SyncPlanetMessage,
    [Msg.C2S_SyncDailyInfoMessage]: proto.C2S_SyncDailyInfoMessage,
    [Msg.C2S_MailDetailMessage]: proto.C2S_MailDetailMessage,
    [Msg.C2S_DeleteReadMailMessage]: proto.C2S_DeleteReadMailMessage,
    [Msg.C2S_AttachMailMessage]: proto.C2S_AttachMailMessage,
    [Msg.C2S_MailListMessage]: proto.C2S_MailListMessage,
    [Msg.C2S_CheckCdkMessage]: proto.C2S_CheckCdkMessage,
    [Msg.C2S_DiyPlayerInfoMessage]: proto.C2S_DiyPlayerInfoMessage,
    [Msg.C2S_SignOutMessage]: proto.C2S_SignOutMessage,
    [Msg.C2S_RemoveNewMarkMessage]: proto.C2S_RemoveNewMarkMessage,
    [Msg.C2S_BuyBatteryMessage]: proto.C2S_BuyBatteryMessage,
    [Msg.C2S_SetBattleTeamMessage]: proto.C2S_SetBattleTeamMessage,
    [Msg.C2S_JackpotPointsGetMessage]: proto.C2S_JackpotPointsGetMessage,
    [Msg.C2S_UnlockSpeedUpAutoMessage]: proto.C2S_UnlockSpeedUpAutoMessage,
    [Msg.C2S_SyncTimeStoneRecordDataMessage]: proto.C2S_SyncTimeStoneRecordDataMessage,
    [Msg.C2S_UseTimeStoneMessage]: proto.C2S_UseTimeStoneMessage,
    [Msg.C2S_GetAdRewardMessage]: proto.C2S_GetAdRewardMessage,
    [Msg.C2S_OreActionMessage]: proto.C2S_OreActionMessage,
    [Msg.C2S_OreSyncBreakItemTimeMessage]: proto.C2S_OreSyncBreakItemTimeMessage,
    [Msg.C2S_OreLevelFightMessage]: proto.C2S_OreLevelFightMessage,
    [Msg.C2S_GetOreLevelDataMessage]: proto.C2S_GetOreLevelDataMessage,
    [Msg.C2S_UnlockOreMessage]: proto.C2S_UnlockOreMessage,
    [Msg.C2S_ChangePassengerDormMessage]: proto.C2S_ChangePassengerDormMessage,
    [Msg.C2S_PassengerLevelUpMessage]: proto.C2S_PassengerLevelUpMessage,
    [Msg.C2S_ChangePassengerWorkMessage]: proto.C2S_ChangePassengerWorkMessage,
    [Msg.C2S_CompletePassengerPlotMessage]: proto.C2S_CompletePassengerPlotMessage,
    [Msg.C2S_UnlockSkinMessage]: proto.C2S_UnlockSkinMessage,
    [Msg.C2S_ChangeSkinMessage]: proto.C2S_ChangeSkinMessage,
    [Msg.C2S_TalentLevelUpMessage]: proto.C2S_TalentLevelUpMessage,
    [Msg.C2S_FragMergeMessage]: proto.C2S_FragMergeMessage,
    [Msg.C2S_PassengerUnlockProfileMessage]: proto.C2S_PassengerUnlockProfileMessage,
    [Msg.C2S_TransPassengerMessage]: proto.C2S_TransPassengerMessage,
    [Msg.C2S_PassengerProfileSortChangeMessage]: proto.C2S_PassengerProfileSortChangeMessage,
    [Msg.C2S_CreatePayOrderMessage]: proto.C2S_CreatePayOrderMessage,
    [Msg.C2S_VerifyPayOrderMessage]: proto.C2S_VerifyPayOrderMessage,
    [Msg.C2S_GetPayRewardsMessage]: proto.C2S_GetPayRewardsMessage,
    [Msg.C2S_TrainNavigationMessage]: proto.C2S_TrainNavigationMessage,
    [Msg.C2S_GetPlanetMoveSurplusTimeMessage]: proto.C2S_GetPlanetMoveSurplusTimeMessage,
    [Msg.C2S_LandPlanetMessage]: proto.C2S_LandPlanetMessage,
    [Msg.C2S_ChapterPassMessage]: proto.C2S_ChapterPassMessage,
    [Msg.C2S_PassBranchPlanetNodeMessage]: proto.C2S_PassBranchPlanetNodeMessage,
    [Msg.C2S_ClaimBranchPlanetNodeRewardMessage]: proto.C2S_ClaimBranchPlanetNodeRewardMessage,
    [Msg.C2S_CancelMoveToPlanetMessage]: proto.C2S_CancelMoveToPlanetMessage,
    [Msg.C2S_UnlockProfileMessage]: proto.C2S_UnlockProfileMessage,
    [Msg.C2S_ProfileCollectRewardMessage]: proto.C2S_ProfileCollectRewardMessage,
    [Msg.C2S_PlanetProfileSortChangeMessage]: proto.C2S_PlanetProfileSortChangeMessage,
    [Msg.C2S_ChapterPassRandomBoxMessage]: proto.C2S_ChapterPassRandomBoxMessage,
    [Msg.C2S_ChapterStartTimeLimitBoxMessage]: proto.C2S_ChapterStartTimeLimitBoxMessage,
    [Msg.C2S_ChapterSyncTimeLimitBoxMessage]: proto.C2S_ChapterSyncTimeLimitBoxMessage,
    [Msg.C2S_ChapterPassTimeLimitBoxMessage]: proto.C2S_ChapterPassTimeLimitBoxMessage,
    [Msg.C2S_ChapterPassMonsterBoxMessage]: proto.C2S_ChapterPassMonsterBoxMessage,
    [Msg.C2S_ChapterPassToolBlessMessage]: proto.C2S_ChapterPassToolBlessMessage,
    [Msg.C2S_ChapterPassRageModeMessage]: proto.C2S_ChapterPassRageModeMessage,
    [Msg.C2S_DoPublicityMessage]: proto.C2S_DoPublicityMessage,
    [Msg.C2S_GetPublicityRewardMessage]: proto.C2S_GetPublicityRewardMessage,
    [Msg.C2S_ProfileBranchPassNodeMessage]: proto.C2S_ProfileBranchPassNodeMessage,
    [Msg.C2S_ProfileBranchQuestionMessage]: proto.C2S_ProfileBranchQuestionMessage,
    [Msg.C2S_ProfileBranchSyncEnergyMessage]: proto.C2S_ProfileBranchSyncEnergyMessage,
    [Msg.C2S_ProfileBranchUnlockMessage]: proto.C2S_ProfileBranchUnlockMessage,
    [Msg.C2S_UpdateFormationMessage]: proto.C2S_UpdateFormationMessage,
    [Msg.C2S_GetRankListMessage]: proto.C2S_GetRankListMessage,
    [Msg.C2S_GetRivalMessage]: proto.C2S_GetRivalMessage,
    [Msg.C2S_PvpFightMessage]: proto.C2S_PvpFightMessage,
    [Msg.C2S_PvpBattleRecordListMessage]: proto.C2S_PvpBattleRecordListMessage,
    [Msg.C2S_PvpBattleReplayMessage]: proto.C2S_PvpBattleReplayMessage,
    [Msg.C2S_PvpModuleDataMessage]: proto.C2S_PvpModuleDataMessage,
    [Msg.C2S_SetResonanceRoleMessage]: proto.C2S_SetResonanceRoleMessage,
    [Msg.C2S_StoreRefreshMessage]: proto.C2S_StoreRefreshMessage,
    [Msg.C2S_StoreBuyMessage]: proto.C2S_StoreBuyMessage,
    [Msg.C2S_SyncStoreMessage]: proto.C2S_SyncStoreMessage,
    [Msg.C2S_ToolMakeMessage]: proto.C2S_ToolMakeMessage,
    [Msg.C2S_FurnaceUpgradeMessage]: proto.C2S_FurnaceUpgradeMessage,
    [Msg.C2S_TowerBattleMessage]: proto.C2S_TowerBattleMessage,
    [Msg.C2S_TowerBattleWinMessage]: proto.C2S_TowerBattleWinMessage,
    [Msg.C2S_BuyCarriageMessage]: proto.C2S_BuyCarriageMessage,
    [Msg.C2S_GetCarriageBuildInfoMessage]: proto.C2S_GetCarriageBuildInfoMessage,
    [Msg.C2S_OpenCarriageDoorMessage]: proto.C2S_OpenCarriageDoorMessage,
    [Msg.C2S_BuildLevelUpMessage]: proto.C2S_BuildLevelUpMessage,
    [Msg.C2S_ChangeBuildSkinMessage]: proto.C2S_ChangeBuildSkinMessage,
    [Msg.C2S_CarriageThemeLvUpMessage]: proto.C2S_CarriageThemeLvUpMessage,
    [Msg.C2S_UnlockGoodsMessage]: proto.C2S_UnlockGoodsMessage,
    [Msg.C2S_LevelUpGoodsMessage]: proto.C2S_LevelUpGoodsMessage,
    [Msg.C2S_GetTrainActivityMessage]: proto.C2S_GetTrainActivityMessage,
    [Msg.C2S_ArrangeTrainActivityMessage]: proto.C2S_ArrangeTrainActivityMessage,
    [Msg.C2S_GetTrainActivityRewardMessage]: proto.C2S_GetTrainActivityRewardMessage,
    [Msg.C2S_SyncAllTrainDailyTaskMessage]: proto.C2S_SyncAllTrainDailyTaskMessage,
    [Msg.C2S_StartTrainDailyTaskMessage]: proto.C2S_StartTrainDailyTaskMessage,
    [Msg.C2S_ClaimTrainDailyTaskRewardMessage]: proto.C2S_ClaimTrainDailyTaskRewardMessage,
    [Msg.C2S_TrainTechUpgradeMessage]: proto.C2S_TrainTechUpgradeMessage,
    [Msg.C2S_TransportStartMessage]: proto.C2S_TransportStartMessage,
    [Msg.C2S_TransportFightMessage]: proto.C2S_TransportFightMessage,
    [Msg.C2S_TransportRewardGetMessage]: proto.C2S_TransportRewardGetMessage,
    [Msg.C2S_TransportBackMessage]: proto.C2S_TransportBackMessage,
    [Msg.C2S_SyncTransportMessage]: proto.C2S_SyncTransportMessage,
    [Msg.C2S_SyncWantedMessage]: proto.C2S_SyncWantedMessage,
    [Msg.C2S_RefrehWantedMessage]: proto.C2S_RefrehWantedMessage,
    [Msg.C2S_StartWantedMessage]: proto.C2S_StartWantedMessage,
    [Msg.C2S_ClaimWantedRewardMessage]: proto.C2S_ClaimWantedRewardMessage,
    [Msg.C2S_SyncAllWantedMessage]: proto.C2S_SyncAllWantedMessage,
    [Msg.C2S_RegisterMessage]: proto.C2S_RegisterMessage,
    [Msg.C2S_LoginAccountMessage]: proto.C2S_LoginAccountMessage,
    [Msg.C2S_LoginByTokenMessage]: proto.C2S_LoginByTokenMessage,
    [Msg.C2S_LoginGuestMessage]: proto.C2S_LoginGuestMessage,
    [Msg.C2S_LoginFBMessage]: proto.C2S_LoginFBMessage,
    [Msg.C2S_BindFBMessage]: proto.C2S_BindFBMessage,
    [Msg.C2S_LoginAppleMessage]: proto.C2S_LoginAppleMessage,
    [Msg.C2S_BindAppleMessage]: proto.C2S_BindAppleMessage,
    [Msg.C2S_LoginGoogleMessage]: proto.C2S_LoginGoogleMessage,
    [Msg.C2S_BindGoogleMessage]: proto.C2S_BindGoogleMessage,
    [Msg.C2S_LoginWxMessage]: proto.C2S_LoginWxMessage,
    [Msg.C2S_LoginWxAppMessage]: proto.C2S_LoginWxAppMessage,
    [Msg.C2S_LoginTapTapMessage]: proto.C2S_LoginTapTapMessage,
    [Msg.C2S_CertificationMessage]: proto.C2S_CertificationMessage,
    [Msg.C2S_EnterGameServerMessage]: proto.C2S_EnterGameServerMessage,
    [Msg.C2S_CancelSignOutMessage]: proto.C2S_CancelSignOutMessage,

}
export const ReceiveMsgMap = {
    [Msg.C2S_ClaimAchievementRewardMessage]: proto.S2C_ClaimAchievementRewardMessage,
    [Msg.C2S_AcceptArrestMessage]: proto.S2C_AcceptArrestMessage,
    [Msg.C2S_SetArrestBattleResultMessage]: proto.S2C_SetArrestBattleResultMessage,
    [Msg.C2S_FinishArrestMessage]: proto.S2C_FinishArrestMessage,
    [Msg.C2S_DestroyArrestMessage]: proto.S2C_DestroyArrestMessage,
    [Msg.C2S_RefreshAllArrestMessage]: proto.S2C_RefreshAllArrestMessage,
    [Msg.C2S_ShowArrestResultMessage]: proto.S2C_ShowArrestResultMessage,
    [Msg.C2S_TicketMergeMessage]: proto.S2C_TicketMergeMessage,
    [Msg.C2S_DropItemMessage]: proto.S2C_DropItemMessage,
    [Msg.C2S_SpaceStoneLvUpMessage]: proto.S2C_SpaceStoneLvUpMessage,
    [Msg.C2S_UseSpaceStoneMessage]: proto.S2C_UseSpaceStoneMessage,
    [Msg.C2S_MarkSpaceStoneMessage]: proto.S2C_MarkSpaceStoneMessage,
    [Msg.C2S_SyncItemMessage]: proto.S2C_SyncItemMessage,
    [Msg.C2S_UseItemMessage]: proto.S2C_UseItemMessage,
    [Msg.C2S_ReadyStartBlackHoleMessage]: proto.S2C_ReadyStartBlackHoleMessage,
    [Msg.C2S_StartBlackHoleMessage]: proto.S2C_StartBlackHoleMessage,
    [Msg.C2S_SelectBlackHoleNodeMessage]: proto.S2C_SelectBlackHoleNodeMessage,
    [Msg.C2S_SyncBlackHoleMessage]: proto.S2C_SyncBlackHoleMessage,
    [Msg.C2S_UnlockBlackHoleMessage]: proto.S2C_UnlockBlackHoleMessage,
    [Msg.C2S_SyncAllBurstTaskMessage]: proto.S2C_SyncAllBurstTaskMessage,
    [Msg.C2S_StartBurstTaskMessage]: proto.S2C_StartBurstTaskMessage,
    [Msg.C2S_ClaimBurstTaskRewardMessage]: proto.S2C_ClaimBurstTaskRewardMessage,
    [Msg.C2S_CollectMapMineDoneMessage]: proto.S2C_CollectMapMineDoneMessage,
    [Msg.C2S_FinishDailyTaskMessage]: proto.S2C_FinishDailyTaskMessage,
    [Msg.C2S_SyncDailyTaskInfoMessage]: proto.S2C_SyncDailyTaskInfoMessage,
    [Msg.C2S_DialogTaskDoneMessage]: proto.S2C_DialogTaskDoneMessage,
    [Msg.C2S_BattleTaskDoneTestMessage]: proto.S2C_BattleTaskDoneTestMessage,
    [Msg.C2S_WearEquipMessage]: proto.S2C_WearEquipMessage,
    [Msg.C2S_UnWearEquipMessage]: proto.S2C_UnWearEquipMessage,
    [Msg.C2S_MakeEquipMessage]: proto.S2C_MakeEquipMessage,
    [Msg.C2S_BuyEquipMessage]: proto.S2C_BuyEquipMessage,
    [Msg.C2S_SellEquipMessage]: proto.S2C_SellEquipMessage,
    [Msg.C2S_SyncExploreMessage]: proto.S2C_SyncExploreMessage,
    [Msg.C2S_StartExploreMessage]: proto.S2C_StartExploreMessage,
    [Msg.C2S_ClaimExploreRewardMessage]: proto.S2C_ClaimExploreRewardMessage,
    [Msg.C2S_BuySpaceshipMessage]: proto.S2C_BuySpaceshipMessage,
    [Msg.C2S_GetExploreAreaMessage]: proto.S2C_GetExploreAreaMessage,
    [Msg.C2S_CeilOperationMessage]: proto.S2C_CeilOperationMessage,
    [Msg.C2S_CeilSyncMessage]: proto.S2C_CeilSyncMessage,
    [Msg.C2S_InstanceFightMessage]: proto.S2C_InstanceFightMessage,
    [Msg.C2S_SyncInstanceMessage]: proto.S2C_SyncInstanceMessage,
    [Msg.C2S_UnlockInstanceMessage]: proto.S2C_UnlockInstanceMessage,
    [Msg.C2S_CompleteInstancePuzzleMessage]: proto.S2C_CompleteInstancePuzzleMessage,
    [Msg.C2S_GetPlayerInfoMessage]: proto.S2C_GetPlayerInfoResMessage,
    [Msg.C2S_GmExecuteMessage]: proto.S2C_GmExecuteRspMessage,
    [Msg.C2S_JackpotReqMessage]: proto.S2C_JackpotRspMessage,
    [Msg.C2S_CollectItemMessage]: proto.S2C_CollectItemRespMessage,
    [Msg.C2S_SpeedUpMessage]: proto.S2C_SpeedUpMessage,
    [Msg.C2S_StopSpeedUpMessage]: proto.S2C_StopSpeedUpMessage,
    [Msg.C2S_SyncSpeedUpMessage]: proto.S2C_SyncSpeedUpMessage,
    [Msg.C2S_RecoverEnergyMessage]: proto.S2C_RecoverEnergyRespMessage,
    [Msg.C2S_SyncMessage]: proto.S2C_SyncItemMessage,
    [Msg.C2S_RecordGuideStepMessage]: proto.S2C_RecordGuideStepResultMessage,
    [Msg.C2S_ClaimTaskRewardMessage]: proto.S2C_ClaimTaskRewardResultMessage,
    [Msg.C2S_SyncPlanetMessage]: proto.S2C_SyncPlanetMessage,
    [Msg.C2S_SyncDailyInfoMessage]: proto.S2C_SyncDailyInfoRespMessage,
    [Msg.C2S_MailDetailMessage]: proto.S2C_MailDetailRespMessage,
    [Msg.C2S_DeleteReadMailMessage]: proto.S2C_DeleteReadMailRespMessage,
    [Msg.C2S_AttachMailMessage]: proto.S2C_AttachMailRespMessage,
    [Msg.C2S_MailListMessage]: proto.S2C_MailListRespMessage,
    [Msg.C2S_CheckCdkMessage]: proto.S2C_CheckCdkMessage,
    [Msg.C2S_DiyPlayerInfoMessage]: proto.S2C_DiyPlayerInfoRespMessage,
    [Msg.C2S_SignOutMessage]: proto.S2C_SignOutRespMessage,
    [Msg.C2S_RemoveNewMarkMessage]: proto.S2C_RemoveNewMarkMessage,
    [Msg.C2S_BuyBatteryMessage]: proto.S2C_BuyBatteryMessage,
    [Msg.C2S_SetBattleTeamMessage]: proto.S2C_SetBattleTeamMessage,
    [Msg.C2S_JackpotPointsGetMessage]: proto.S2C_JackpotPointsGetMessage,
    [Msg.C2S_UnlockSpeedUpAutoMessage]: proto.S2C_UnlockSpeedUpAutoMessage,
    [Msg.C2S_SyncTimeStoneRecordDataMessage]: proto.S2C_SyncTimeStoneRecordDataMessage,
    [Msg.C2S_UseTimeStoneMessage]: proto.S2C_UseTimeStoneMessage,
    [Msg.C2S_GetAdRewardMessage]: proto.S2C_GetAdRewardMessage,
    [Msg.C2S_OreActionMessage]: proto.S2C_OreActionMessage,
    [Msg.C2S_OreSyncBreakItemTimeMessage]: proto.S2C_OreSyncBreakItemTimeMessage,
    [Msg.C2S_OreLevelFightMessage]: proto.S2C_OreLevelFightMessage,
    [Msg.C2S_GetOreLevelDataMessage]: proto.S2C_GetOreLevelDataMessage,
    [Msg.C2S_UnlockOreMessage]: proto.S2C_UnlockOreMessage,
    [Msg.C2S_ChangePassengerDormMessage]: proto.S2C_ChangePassengerDormRespMessage,
    [Msg.C2S_PassengerLevelUpMessage]: proto.S2C_PassengerLevelUpResultMessage,
    [Msg.C2S_ChangePassengerWorkMessage]: proto.S2C_ChangePassengerWorkMessage,
    [Msg.C2S_CompletePassengerPlotMessage]: proto.S2C_CompletePassengerPlotMessage,
    [Msg.C2S_UnlockSkinMessage]: proto.S2C_UnlockSkinMessage,
    [Msg.C2S_ChangeSkinMessage]: proto.S2C_ChangeSkinMessage,
    [Msg.C2S_TalentLevelUpMessage]: proto.S2C_TalentLevelUpMessage,
    [Msg.C2S_FragMergeMessage]: proto.S2C_FragMergeMessage,
    [Msg.C2S_PassengerUnlockProfileMessage]: proto.S2C_PassengerUnlockProfileMessage,
    [Msg.C2S_TransPassengerMessage]: proto.S2C_TransPassengerMessage,
    [Msg.C2S_PassengerProfileSortChangeMessage]: proto.S2C_PassengerProfileSortChangeMessage,
    [Msg.C2S_CreatePayOrderMessage]: proto.S2C_CreatePayOrderMessage,
    [Msg.C2S_VerifyPayOrderMessage]: proto.S2C_VerifyPayOrderMessage,
    [Msg.C2S_GetPayRewardsMessage]: proto.S2C_GetPayRewardsMessage,
    [Msg.C2S_TrainNavigationMessage]: proto.S2C_TrainNavigationResultMessage,
    [Msg.C2S_GetPlanetMoveSurplusTimeMessage]: proto.S2C_GetPlanetMoveSurplusTimeRespMessage,
    [Msg.C2S_LandPlanetMessage]: proto.S2C_LandPlanetRespMessage,
    [Msg.C2S_ChapterPassMessage]: proto.S2C_ChapterPassResultMessage,
    [Msg.C2S_PassBranchPlanetNodeMessage]: proto.S2C_PassBranchPlanetNodeMessage,
    [Msg.C2S_ClaimBranchPlanetNodeRewardMessage]: proto.S2C_ClaimBranchPlanetNodeRewardMessage,
    [Msg.C2S_CancelMoveToPlanetMessage]: proto.S2C_CancelMoveToPlanetMessage,
    [Msg.C2S_UnlockProfileMessage]: proto.S2C_UnlockProfileMessage,
    [Msg.C2S_ProfileCollectRewardMessage]: proto.S2C_ProfileCollectRewardMessage,
    [Msg.C2S_PlanetProfileSortChangeMessage]: proto.S2C_PlanetProfileSortChangeMessage,
    [Msg.C2S_ChapterPassRandomBoxMessage]: proto.S2C_ChapterPassRandomBoxMessage,
    [Msg.C2S_ChapterStartTimeLimitBoxMessage]: proto.S2C_ChapterStartTimeLimitBoxMessage,
    [Msg.C2S_ChapterSyncTimeLimitBoxMessage]: proto.S2C_ChapterSyncTimeLimitBoxMessage,
    [Msg.C2S_ChapterPassTimeLimitBoxMessage]: proto.S2C_ChapterPassTimeLimitBoxMessage,
    [Msg.C2S_ChapterPassMonsterBoxMessage]: proto.S2C_ChapterPassMonsterBoxMessage,
    [Msg.C2S_ChapterPassToolBlessMessage]: proto.S2C_ChapterPassToolBlessMessage,
    [Msg.C2S_ChapterPassRageModeMessage]: proto.S2C_ChapterPassRageModeMessage,
    [Msg.C2S_DoPublicityMessage]: proto.S2C_DoPublicityMessage,
    [Msg.C2S_GetPublicityRewardMessage]: proto.S2C_GetPublicityRewardMessage,
    [Msg.C2S_ProfileBranchPassNodeMessage]: proto.S2C_ProfileBranchPassNodeMessage,
    [Msg.C2S_ProfileBranchQuestionMessage]: proto.S2C_ProfileBranchQuestionMessage,
    [Msg.C2S_ProfileBranchSyncEnergyMessage]: proto.S2C_ProfileBranchSyncEnergyMessage,
    [Msg.C2S_ProfileBranchUnlockMessage]: proto.S2C_ProfileBranchUnlockMessage,
    [Msg.C2S_UpdateFormationMessage]: proto.S2C_UpdateFormationMessage,
    [Msg.C2S_GetRankListMessage]: proto.S2C_GetRankListMessage,
    [Msg.C2S_GetRivalMessage]: proto.S2C_GetRivalMessage,
    [Msg.C2S_PvpFightMessage]: proto.S2C_PvpFightMessage,
    [Msg.C2S_PvpBattleRecordListMessage]: proto.S2C_PvpBattleRecordListMessage,
    [Msg.C2S_PvpBattleReplayMessage]: proto.S2C_PvpBattleReplayMessage,
    [Msg.C2S_PvpModuleDataMessage]: proto.S2C_PvpModuleDataMessage,
    [Msg.C2S_SetResonanceRoleMessage]: proto.S2C_SetResonanceRoleMessage,
    [Msg.C2S_StoreRefreshMessage]: proto.S2C_StoreRefreshMessage,
    [Msg.C2S_StoreBuyMessage]: proto.S2C_StoreBuyMessage,
    [Msg.C2S_SyncStoreMessage]: proto.S2C_SyncStoreMessage,
    [Msg.C2S_ToolMakeMessage]: proto.S2C_ToolMakeRespMessage,
    [Msg.C2S_FurnaceUpgradeMessage]: proto.S2C_FurnaceUpgradeRespMessage,
    [Msg.C2S_TowerBattleMessage]: proto.S2C_TowerBattleMessage,
    [Msg.C2S_TowerBattleWinMessage]: proto.S2C_TowerBattleWinMessage,
    [Msg.C2S_BuyCarriageMessage]: proto.S2C_BuyCarriageResultMessage,
    [Msg.C2S_GetCarriageBuildInfoMessage]: proto.S2C_GetCarriageBuildInfoResMessage,
    [Msg.C2S_OpenCarriageDoorMessage]: proto.S2C_OpenCarriageDoorResMessage,
    [Msg.C2S_BuildLevelUpMessage]: proto.S2C_BuildLevelUpMessage,
    [Msg.C2S_ChangeBuildSkinMessage]: proto.S2C_ChangeBuildSkinMessage,
    [Msg.C2S_CarriageThemeLvUpMessage]: proto.S2C_CarriageThemeLvUpMessage,
    [Msg.C2S_UnlockGoodsMessage]: proto.S2C_UnlockGoodsMessage,
    [Msg.C2S_LevelUpGoodsMessage]: proto.S2C_LevelUpGoodsMessage,
    [Msg.C2S_GetTrainActivityMessage]: proto.S2C_GetTrainActivityMessage,
    [Msg.C2S_ArrangeTrainActivityMessage]: proto.S2C_ArrangeTrainActivityMessage,
    [Msg.C2S_GetTrainActivityRewardMessage]: proto.S2C_GetTrainActivityRewardMessage,
    [Msg.C2S_SyncAllTrainDailyTaskMessage]: proto.S2C_SyncAllTrainDailyTaskMessage,
    [Msg.C2S_StartTrainDailyTaskMessage]: proto.S2C_StartTrainDailyTaskMessage,
    [Msg.C2S_ClaimTrainDailyTaskRewardMessage]: proto.S2C_ClaimTrainDailyTaskRewardMessage,
    [Msg.C2S_TrainTechUpgradeMessage]: proto.S2C_TrainTechUpgradeMessage,
    [Msg.C2S_TransportStartMessage]: proto.S2C_TransportStartMessage,
    [Msg.C2S_TransportFightMessage]: proto.S2C_TransportFightMessage,
    [Msg.C2S_TransportRewardGetMessage]: proto.S2C_TransportRewardGetMessage,
    [Msg.C2S_TransportBackMessage]: proto.S2C_TransportBackMessage,
    [Msg.C2S_SyncTransportMessage]: proto.S2C_SyncTransportMessage,
    [Msg.C2S_SyncWantedMessage]: proto.S2C_SyncWantedMessage,
    [Msg.C2S_RefrehWantedMessage]: proto.S2C_RefrehWantedMessage,
    [Msg.C2S_StartWantedMessage]: proto.S2C_StartWantedMessage,
    [Msg.C2S_ClaimWantedRewardMessage]: proto.S2C_ClaimWantedRewardMessage,
    [Msg.C2S_SyncAllWantedMessage]: proto.S2C_SyncAllWantedMessage,
    [Msg.C2S_RegisterMessage]: proto.S2C_RegisterResultMessage,
    [Msg.C2S_LoginAccountMessage]: proto.S2C_LoginResultMessage,
    [Msg.C2S_LoginByTokenMessage]: proto.S2C_LoginResultMessage,
    [Msg.C2S_LoginGuestMessage]: proto.S2C_LoginResultMessage,
    [Msg.C2S_LoginFBMessage]: proto.S2C_LoginResultMessage,
    [Msg.C2S_BindFBMessage]: proto.S2C_BindResultMessage,
    [Msg.C2S_LoginAppleMessage]: proto.S2C_LoginResultMessage,
    [Msg.C2S_BindAppleMessage]: proto.S2C_BindResultMessage,
    [Msg.C2S_LoginGoogleMessage]: proto.S2C_LoginResultMessage,
    [Msg.C2S_BindGoogleMessage]: proto.S2C_BindResultMessage,
    [Msg.C2S_LoginWxMessage]: proto.S2C_LoginResultMessage,
    [Msg.C2S_LoginWxAppMessage]: proto.S2C_LoginResultMessage,
    [Msg.C2S_LoginTapTapMessage]: proto.S2C_LoginResultMessage,
    [Msg.C2S_CertificationMessage]: proto.S2C_CertificationResultMessage,
    [Msg.C2S_EnterGameServerMessage]: proto.S2C_EnterGameServerResultMessage,
    [Msg.C2S_CancelSignOutMessage]: proto.S2C_CancelSignOutRespMessage,
    [Msg.S2C_ErrorResultMessage]: proto.S2C_ErrorResultMessage,
    [Msg.S2C_LoginResultMessage]: proto.S2C_LoginResultMessage,
    [Msg.S2C_BindResultMessage]: proto.S2C_BindResultMessage,
    [Msg.S2C_NoticeMessage]: proto.S2C_NoticeMessage,
    [Msg.S2C_CurrencyChangeMessage]: proto.S2C_CurrencyChangeMessage,
    [Msg.S2C_BagItemChangeMessage]: proto.S2C_BagItemChangeMessage,
    [Msg.S2C_JackpotRspMessage]: proto.S2C_JackpotRspMessage,
    [Msg.S2C_SyncMessage]: proto.S2C_SyncMessage,
    [Msg.S2C_LogoutMessage]: proto.S2C_LogoutMessage,
    [Msg.S2C_OnNewMailMessage]: proto.S2C_OnNewMailMessage,
    [Msg.S2C_OnGetBurstTaskMessage]: proto.S2C_OnGetBurstTaskMessage,

}